using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Services.SpreedlyService;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace FlexCharge.ApiGateway.Public.Controllers;

[Route("v1/")]
[ApiController]
public class TransactionController : ControllerBase
{
    [HttpPost("transmit")]
    [ProducesResponseType(typeof(TransmitResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> Transmit(TransmitRequest request, CancellationToken token)
    {
        return Ok();
    }

    [HttpPost("evaluate")]
    [ProducesResponseType(typeof(EvaluateResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> EvaluatePost(EvaluateRequest request,
        CancellationToken token)
    {
        return Ok();
    }

    [HttpPost("outcome")]
    [ProducesResponseType(typeof(OutcomeResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> OutcomePost(OutcomeRequest request, CancellationToken token)
    {
        return Ok();
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="request"></param>
    /// <param name="token"></param>
    /// <returns></returns>
    [HttpPost("tokenize")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(CreatePaymentInstrumentDTO), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> Tokenize(Guid? mid, string environment, CreatePaymentInstrumentRequest payload,
        CancellationToken token)
    {
        return Ok();
    }
    
}