using FlexCharge.Tracking.Entities;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Npgsql;
using System;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

namespace FlexCharge.Tracking
{
    public class PostgreSQLDbContext : DbContext
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        public PostgreSQLDbContext(DbContextOptions<PostgreSQLDbContext> options, IHttpContextAccessor httpContextAccessor)
            : base(options)
        {
            _httpContextAccessor = httpContextAccessor;
        }
        protected override void OnModelCreating(ModelBuilder builder)
        {
            builder.Entity<Entities.Tracking>().Property<bool>("IsDeleted");
            builder.Entity<Entities.Tracking>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);
            
            builder.Entity<Entities.Configuration>().Property<bool>("IsDeleted");
            builder.Entity<Entities.Configuration>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);
            
            builder.Entity<Entities.Module>().Property<bool>("IsDeleted");
            builder.Entity<Entities.Module>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);
            
            builder.Entity<Entities.ArchivedTracking>().Property<bool>("IsDeleted");
            builder.Entity<Entities.ArchivedTracking>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            builder.Entity<Entities.WebCrawling>().Property<bool>("IsDeleted");
            builder.Entity<Entities.WebCrawling>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);
            
            builder.Entity<Entities.Merchant>().Property<bool>("IsDeleted");
            builder.Entity<Entities.Merchant>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);
            
            builder.Entity<Entities.FrameEvent>().Property<bool>("IsDeleted");
            builder.Entity<Entities.FrameEvent>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);
        }
        
        public override int SaveChanges()
        {
            UpdateSoftDeleteStatuses();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = default(CancellationToken))
        {
            UpdateSoftDeleteStatuses();
            return await base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        }

        private void UpdateSoftDeleteStatuses()
        {
            var user = _httpContextAccessor?.HttpContext?.User?.Claims.SingleOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
            foreach (var entry in ChangeTracker.Entries())
            {
                switch (entry.State)
                {
                    case EntityState.Modified:
                        {
                            if (user != null)
                            {
                                if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedBy"))
                                    entry.Property("ModifiedBy").CurrentValue = user.Value;
                            }


                            if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedOn"))
                                entry.Property("ModifiedOn").CurrentValue = DateTime.Now.ToUniversalTime();
                            entry.CurrentValues["IsDeleted"] = false;
                            break;
                        }
                    case EntityState.Added:
                        {
                            if (user != null)
                            {
                                if (entry.Properties.Any(o => o.Metadata.Name == "CreatedBy"))
                                    entry.Property("CreatedBy").CurrentValue = user.Value;
                                if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedBy"))
                                    entry.Property("ModifiedBy").CurrentValue = user.Value;
                            }

                            if (entry.Properties.Any(o => o.Metadata.Name == "CreatedOn"))
                                entry.Property("CreatedOn").CurrentValue = DateTime.Now.ToUniversalTime();
                            if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedOn"))

                                entry.Property("ModifiedOn").CurrentValue = DateTime.Now.ToUniversalTime();
                            entry.CurrentValues["IsDeleted"] = false;
                            break;
                        }

                    case EntityState.Deleted:
                        entry.State = EntityState.Modified;
                        entry.CurrentValues["IsDeleted"] = true;
                        break;
                }
            }
        }

        public DbSet<Entities.Configuration> Configurations { get; set; }
        
        public DbSet<Entities.Tracking> Tracking { get; set; }
        public DbSet<Entities.WebCrawling> WebCrawling { get; set; }
        public DbSet<Entities.FrameEvent> FrameEvents { get; set; }
        
        public DbSet<Entities.Module> Modules { get; set; }
        
        public DbSet<Entities.ArchivedTracking> TrackingArchive { get; set; }
        public DbSet<Entities.Merchant> Merchants { get; set; }
    }
}
