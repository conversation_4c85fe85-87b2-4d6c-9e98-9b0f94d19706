using System;
using FlexCharge.Common.Cache;
using Microsoft.Extensions.Caching.Distributed;

namespace FlexCharge.Tracking.DistributedCache;

public class CacheKeyFactory : CacheKeyFactoryBase
{
    // public static CacheKey CreateMerchantConfigurationKey(Guid mid) =>
    //     CreateScopedKey($"Tracking_MerchantConfiguration_{mid}",
    //         new DistributedCacheEntryOptions() {SlidingExpiration = TimeSpan.FromDays(1)});


    #region Tracking Heartbeat Information

    public static CacheKey CreateKey_HeartbeatListByCustomerIp(string ip, Guid siteId) =>
        CreateScopedKey($"HBbyIP_{siteId}_{ip.ToLowerInvariant()}",
            new DistributedCacheEntryOptions() {SlidingExpiration = TimeSpan.FromMinutes(15)});

    public static CacheKey CreateKey_SenseKeyListByEmail(string email, Guid siteId) =>
        CreateScopedKey($"SKbyEmail_{siteId}_{email.ToLowerInvariant()}",
            new DistributedCacheEntryOptions() {SlidingExpiration = TimeSpan.FromMinutes(15)});

    #endregion
}