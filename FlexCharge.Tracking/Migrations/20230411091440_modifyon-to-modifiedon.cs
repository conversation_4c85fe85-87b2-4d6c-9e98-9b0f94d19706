using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Tracking.Migrations
{
    /// <inheritdoc />
    public partial class modifyontomodifiedon : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ModalCornersRadius",
                table: "Configurations");

            migrationBuilder.RenameColumn(
                name: "ModifyOn",
                table: "Tracking",
                newName: "ModifiedOn");

            migrationBuilder.RenameColumn(
                name: "ModifyOn",
                table: "Configurations",
                newName: "ModifiedOn");

            migrationBuilder.AddColumn<bool>(
                name: "IsCloseButtonVisible",
                table: "Configurations",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "Recorders",
                table: "Configurations",
                type: "jsonb",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsCloseButtonVisible",
                table: "Configurations");

            migrationBuilder.DropColumn(
                name: "Recorders",
                table: "Configurations");

            migrationBuilder.RenameColumn(
                name: "ModifiedOn",
                table: "Tracking",
                newName: "ModifyOn");

            migrationBuilder.RenameColumn(
                name: "ModifiedOn",
                table: "Configurations",
                newName: "ModifyOn");

            migrationBuilder.AddColumn<string>(
                name: "ModalCornersRadius",
                table: "Configurations",
                type: "text",
                nullable: true);
        }
    }
}
