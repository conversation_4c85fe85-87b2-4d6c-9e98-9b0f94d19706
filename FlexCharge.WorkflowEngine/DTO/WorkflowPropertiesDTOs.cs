using System;
using System.Collections.Generic;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.Response;
using FlexCharge.Common.Shared.UIBuilder.DTO;

namespace FlexCharge.WorkflowEngine.DTO;

public class GetWorkflowPropertiesRequest
{
    //public Dictionary<string, string>? Parameters { get; set; }
    public object Data { get; set; }
}

public class GetWorkflowPropertiesResponse
{
    //public Guid Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }

    //public Dictionary<string, string>? Parameters { get; set; }
    public object Data { get; set; }
    public List<ChallengeItemDTO>? Editor { get; set; }
}