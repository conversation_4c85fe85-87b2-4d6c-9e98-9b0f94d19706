using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.WorkflowEngine.Migrations
{
    /// <inheritdoc />
    public partial class addedWorkflowIdcolumntoWorkflowstable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "WorkflowId",
                table: "Workflows",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_Workflows_ModifiedOn",
                table: "Workflows",
                column: "ModifiedOn");

            migrationBuilder.CreateIndex(
                name: "IX_Workflows_WorkflowId",
                table: "Workflows",
                column: "WorkflowId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Workflows_ModifiedOn",
                table: "Workflows");

            migrationBuilder.DropIndex(
                name: "IX_Workflows_WorkflowId",
                table: "Workflows");

            migrationBuilder.DropColumn(
                name: "WorkflowId",
                table: "Workflows");
        }
    }
}
