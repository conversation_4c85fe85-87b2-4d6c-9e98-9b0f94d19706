{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:5091", "sslPort": 0}}, "$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": false, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "DB_HOST": "localhost", "DB_PORT": "5432", "DB_DATABASE": "fc.workflow", "DB_USERNAME": "workflow-engine-service-staging", "DB_PASSWORD": "11111", "SNS_IAM_REGION": "us-east-1", "SNS_IAM_ACCESS_KEY": "********************", "SNS_IAM_SECRET_KEY": "uPO48OgTDXReduu6gz7QzqtuQalSgLnkB6ZHDRtW"}}, "FlexCharge.WorkflowEngine": {"commandName": "Project", "launchBrowser": false, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "DB_HOST": "localhost", "DB_PORT": "5432", "DB_DATABASE": "fc.workflow", "DB_USERNAME": "workflow-engine-service-staging", "DB_PASSWORD": "11111", "SNS_IAM_REGION": "us-east-1", "SNS_IAM_ACCESS_KEY": "********************", "SNS_IAM_SECRET_KEY": "uPO48OgTDXReduu6gz7QzqtuQalSgLnkB6ZHDRtW"}, "applicationUrl": "https://localhost:5130;http://localhost:5131"}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": false, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/swagger", "publishAllPorts": true, "environmentVariables": {"DB_HOST": "host.docker.internal", "DB_PORT": "5432", "DB_DATABASE": "fc.workflow", "DB_USERNAME": "workflow-engine-service-staging", "DB_PASSWORD": "11111", "SNS_IAM_REGION": "us-east-1", "SNS_IAM_ACCESS_KEY": "********************", "SNS_IAM_SECRET_KEY": "uPO48OgTDXReduu6gz7QzqtuQalSgLnkB6ZHDRtW"}}}}