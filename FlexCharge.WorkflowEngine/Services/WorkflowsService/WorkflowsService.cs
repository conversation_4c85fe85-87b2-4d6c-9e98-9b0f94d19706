using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands.Workflows;
using FlexCharge.WorkflowEngine.Common.Workflows.Designer;
using FlexCharge.WorkflowEngine.Common.Workflows.Designer.Model;
using FlexCharge.WorkflowEngine.DTO;
using FlexCharge.WorkflowEngine.Entities;
using FlexCharge.WorkflowEngine.Workflows;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using ParameterValidationError = FlexCharge.Contracts.Commands.Workflows.ParameterValidationError;
using WorkflowDefinition = FlexCharge.WorkflowEngine.Workflows.WorkflowDefinition;

namespace FlexCharge.WorkflowEngine.Services.WorkflowsService;

public class WorkflowsService : IWorkflowsService
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IRequestClient<GetWorkflowBlockEditorCommand> _getWorkflowBlockEditorCommandRequestClient;
    private IMapper _mapper;

    private readonly IRequestClient<UpdateWorkflowBlockParametersCommand>
        _updateWorkflowBlockParametersCommandRequestClient;

    public WorkflowsService(PostgreSQLDbContext dbContext,
        IMapper mapper,
        IRequestClient<GetWorkflowBlockEditorCommand> getWorkflowBlockEditorCommandRequestClient,
        IRequestClient<UpdateWorkflowBlockParametersCommand> updateWorkflowBlockParametersCommandRequestClient)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _getWorkflowBlockEditorCommandRequestClient = getWorkflowBlockEditorCommandRequestClient;
        _updateWorkflowBlockParametersCommandRequestClient = updateWorkflowBlockParametersCommandRequestClient;
    }

    private async Task<Entities.Workflow?> GetWorkflowByIdOrDefaultAsync(Guid id)
    {
        var workflowDefinition = await _dbContext.Workflows
            .FirstOrDefaultAsync(x => x.Id == id);

        return workflowDefinition;
    }

    public async Task<Entities.Workflow?> GetActualWorkflowIdOrDefaultAsync(Guid workflowId)
    {
        var workflowDefinition = await _dbContext.Workflows
            .Where(x => x.WorkflowId == workflowId)
            .OrderByDescending(x => x.Version)
            .FirstOrDefaultAsync();

        return workflowDefinition;
    }

    public async Task<WorkflowDefinition?> GetWorkflowDefinitionOrDefaultAsync(Guid id)
    {
        var workflowDefinitionEntity = await GetWorkflowByIdOrDefaultAsync(id);

        return workflowDefinitionEntity != null
            ? WorkflowDefinition.Deserialize(workflowDefinitionEntity.Definition)
            : null;
    }

    public BlockDefinition GetBlockDefinitionOrDefault(WorkflowDefinition workflowDefinition, string nodeName)
    {
        var blockReferenceId = NodeIdParser.GetNodeId(nodeName);
        return GetBlockDefinitionOrDefault(workflowDefinition, blockReferenceId);
    }

    private BlockDefinition? GetBlockDefinitionOrDefault(WorkflowDefinition workflowDefinition,
        int blockReferenceId)
    {
        return workflowDefinition.GetBlockDefinitionByReferenceId(blockReferenceId);
    }

    public async Task<BlockEditor> GetBlockEditorByNodeOrDefaultAsync(WorkflowDefinition workflowDefinition,
        string nodeName, Dictionary<string, string> blockParameters)
    {
        var blockReferenceId = NodeIdParser.GetNodeId(nodeName);
        return await GetBlockEditorOrDefaultAsync(workflowDefinition, blockReferenceId, blockParameters);
    }

    public async Task<(IDictionary<string, string> Parameters, IList<ParameterValidationError> ValidationErrors)>
        UpdateBlockParametersAsync(WorkflowDefinition workflowDefinition, string blockFullName,
            IDictionary<Guid, string> answers)
    {
        var response = await _updateWorkflowBlockParametersCommandRequestClient
            .RunCommandAsync<UpdateWorkflowBlockParametersCommand, UpdateWorkflowBlockParametersCommandResponse>(
                new UpdateWorkflowBlockParametersCommand(blockFullName, answers));

        //var updatedParameters = response.Message.Parameters;

        // if (response.Message.ValidationErrors?.Any() != true)
        // {
        //     blockDefinition.Parameters = updatedParameters?.ToDictionary(x => x.Key, x => x.Value);
        //     await _dbContext.SaveChangesAsync();
        // }

        return new
        (
            response.Message.Parameters,
            response.Message.ValidationErrors
        );
    }

    public async Task<WorkflowsQueryResponse> GetWorkflows(string? query, List<WorkflowStatuses>? status,
        List<string>? domains, DateTime? from, DateTime? to, DateTime? modifiedFrom, DateTime? modifiedTo,
        DateTime? runFrom, DateTime? runTo, int pageSize, int pageNumber)
    {
        using var workspan = Workspan.Start<WorkflowsService>()
            .LogEnterAndExit();

        try
        {
            WorkflowsQueryResponse response = new WorkflowsQueryResponse();
            var workflows = _dbContext.Workflows
                .AsQueryable();

            if (!string.IsNullOrEmpty(query))
            {
                workflows = workflows.Where(x => x.Name.Contains(query) || x.Description.Contains(query));
            }

            if (status != null && status.Any())
            {
                workflows = workflows.Where(x =>
                    status.Contains((WorkflowStatuses) Enum.Parse(typeof(WorkflowStatuses), x.Status)));
            }

            if (domains != null && domains.Any())
            {
                workflows = workflows.Where(x => domains.Contains(x.Domain));
            }

            if (from != null)
            {
                workflows = workflows.Where(x => x.CreatedOn >= from);
            }

            if (to != null)
            {
                workflows = workflows.Where(x => x.CreatedOn <= to);
            }

            if (modifiedFrom != null)
            {
                workflows = workflows.Where(x => x.ModifiedOn >= modifiedFrom);
            }

            if (modifiedTo != null)
            {
                workflows = workflows.Where(x => x.ModifiedOn <= modifiedTo);
            }

            if (runFrom != null)
            {
                workflows = workflows.Where(x => x.LastRun >= runFrom);
            }

            if (runTo != null)
            {
                workflows = workflows.Where(x => x.LastRun <= runTo);
            }

            var workflowGroups = workflows
                .GroupBy(x => x.WorkflowId)
                .Select(g => g.OrderByDescending(x => x.Version).ToList())
                .ToList();

            var entity = workflowGroups
                .OrderByDescending(x => x.First().Version)
                .Select(x => new WorkflowsQueryResponse.WorkflowQueryDTO
                {
                    WorkflowId = x.First().WorkflowId,
                    Id = x.First().Id,
                    Name = x.First().Name,
                    Description = x.First().Description,
                    Version = x.First().Version,
                    Domain = x.First().Domain,
                    LastRun = x.First().LastRun,
                    Status = x.First().Status,
                    PublishedVersion = x.Any(y => y.Status == WorkflowStatuses.published.ToString())
                        ? x.FirstOrDefault(y => y.Status == WorkflowStatuses.published.ToString()).Version
                        : null,
                    WorkflowItems = x
                        .Select(y => new WorkflowsQueryResponse.WorkflowItemQueryDTO
                        {
                            Id = y.Id,
                            WorkflowId = y.WorkflowId,
                            Name = y.Name,
                            Description = y.Description,
                            Version = y.Version,
                            Status = y.Status,
                            CreatedOn = y.CreatedOn,
                            ModifiedBy = y.ModifiedBy
                        })
                        .OrderByDescending(x => x.Version)
                        .ToList()
                })
                .ToList();

            response.Workflows =
                _mapper
                    .Map<IPagedList<WorkflowsQueryResponse.WorkflowQueryDTO>,
                        PagedDTO<WorkflowsQueryResponse.WorkflowQueryDTO>>(entity.ToPagedList(pageNumber, pageSize));

            return response;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: WorkflowsService => Unable to query workflows");
            throw;
        }
    }

    public async Task CreateWorkflowAsync(WorkflowCreateDTO payload)
    {
        using var workspan = Workspan.Start<WorkflowsService>()
            .LogEnterAndExit();

        try
        {
            var definition = new
            {
                Blocks = new List<BlockDefinition>()
            };

            var workflow = new Workflow
            {
                Name = payload.Name,
                Description = payload.Description,
                Domain = payload.Domain,
                Definition = JsonConvert.SerializeObject(definition),
                Status = WorkflowStatuses.draft.ToString(),
                WorkflowId = Guid.NewGuid(),
            };

            _dbContext.Workflows.Add(workflow);
            await _dbContext.SaveChangesAsync(CancellationToken.None);
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: WorkflowsService => Unable to create workflow");
            throw;
        }
    }

    public async Task UpdateWorkflowAsync(WorkflowUpdateDTO payload)
    {
        using var workspan = Workspan.Start<WorkflowsService>()
            .LogEnterAndExit();

        try
        {
            var workflow = await _dbContext.Workflows.FirstOrDefaultAsync(x => x.Id == payload.Id);

            if (workflow == null)
            {
                throw new Exception($"Workflow with id {payload.Id} not found");
            }

            if (payload.Name != null)
            {
                workflow.Name = payload.Name;
            }

            if (payload.Description != null)
            {
                workflow.Description = payload.Description;
            }

            if (payload.Status != null)
            {
                workflow.Status = payload.Status;
            }

            if (payload.Domain != null)
            {
                workflow.Domain = payload.Domain;
            }

            _dbContext.Workflows.Update(workflow);
            await _dbContext.SaveChangesAsync(CancellationToken.None);
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: WorkflowsService => Unable to update workflow");
            throw;
        }
    }

    public async Task DeleteWorkflowAsync(Guid id)
    {
        using var workspan = Workspan.Start<WorkflowsService>()
            .LogEnterAndExit();

        try
        {
            var workflow = await _dbContext.Workflows.FirstOrDefaultAsync(x => x.Id == id);

            if (workflow == null)
            {
                throw new Exception($"Workflow with id {id} not found");
            }

            _dbContext.Workflows.Remove(workflow);
            await _dbContext.SaveChangesAsync(CancellationToken.None);
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: WorkflowsService => Unable to update workflow");
            throw;
        }
    }

    public async Task PublishWorkflowAsync(Guid id)
    {
        using var workspan = Workspan.Start<WorkflowsService>()
            .LogEnterAndExit();

        try
        {
            var workflow = await _dbContext.Workflows.FirstOrDefaultAsync(x => x.Id == id);

            if (workflow == null)
            {
                throw new Exception($"Workflow with id {id} not found");
            }

            var allWorkflows = await _dbContext.Workflows.Where(x => x.WorkflowId == workflow.WorkflowId).ToListAsync();

            foreach (var workflowEntity in allWorkflows)
            {
                if (workflowEntity.Status == WorkflowStatuses.published.ToString())
                {
                    workflowEntity.Status = WorkflowStatuses.reviewed.ToString();
                    _dbContext.Workflows.Update(workflowEntity);
                }
            }

            workflow.Status = WorkflowStatuses.published.ToString();

            _dbContext.Workflows.Update(workflow);
            await _dbContext.SaveChangesAsync(CancellationToken.None);
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: DomainsService => Unable to update workflow");
            throw;
        }
    }

    public async Task<Guid> SaveWorkflowAsync(WorkflowSaveDTO payload)
    {
        using var workspan = Workspan.Start<WorkflowsService>()
            .LogEnterAndExit();

        try
        {
            var workflowDefinitionSerializationSettings = new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore,
                Converters = new List<JsonConverter>()
                {
                    // Convert enums values to strings
                    new StringEnumConverter()
                }
            };

            var version = _dbContext.Workflows
                .Where(x => x.WorkflowId == payload.WorkflowId)
                .Max(x => x.Version);

            var workflow = new Workflow
            {
                Name = payload.Name,
                Description = payload.Description,
                Domain = payload.Domain,
                Definition =
                    JsonConvert.SerializeObject(WorkflowDefinition.DeserializeDesign(payload.WorkflowDefinition),
                        workflowDefinitionSerializationSettings),
                Status = WorkflowStatuses.draft.ToString(),
                Version = version + 1,
                WorkflowId = Guid.Parse("3f3dcb71-fc05-4a78-951d-22d7e80f12bc") == payload.WorkflowId
                    ? Guid.NewGuid()
                    : payload.WorkflowId, // TODO - use existing workflow id
            };

            var newWorkflow = _dbContext.Workflows.Add(workflow);
            await _dbContext.SaveChangesAsync(CancellationToken.None);

            return newWorkflow.Entity.Id;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: DomainsService => Unable to update workflow");
            throw;
        }
    }

    public async Task<Workflow> GetWorkflowByIdAsync(Guid id)
    {
        using var workspan = Workspan.Start<WorkflowsService>()
            .LogEnterAndExit();
        try
        {
            var workflow = await _dbContext.Workflows.FirstOrDefaultAsync(x => x.Id == id);

            return workflow;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: DomainsService => Unable to get workflow");
            throw;
        }
    }

    public async Task<Workflow> GetWorkflowByWorkflowIdAsync(Guid workflowId)
    {
        using var workspan = Workspan.Start<WorkflowsService>()
            .LogEnterAndExit();
        try
        {
            var workflow = await _dbContext.Workflows
                .Where(x => x.WorkflowId == workflowId)
                .OrderByDescending(x => x.Version)
                .FirstOrDefaultAsync();

            return workflow;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: DomainsService => Unable to get workflow");
            throw;
        }
    }

    private async Task<BlockEditor> GetBlockEditorOrDefaultAsync(WorkflowDefinition workflowDefinition,
        int blockReferenceId, Dictionary<string, string> blockParameters)
    {
        var blockDefinition = workflowDefinition.GetBlockDefinitionByReferenceId(blockReferenceId);

        return await GetBlockEditorOrDefaultAsync(workflowDefinition, blockDefinition.FullName, blockParameters);
    }

    public async Task<BlockEditor> GetBlockEditorOrDefaultAsync(WorkflowDefinition workflowDefinition,
        string blockFullName, Dictionary<string, string> blockParameters)
    {
        var getWorkflowBlockEditorCommandResponse = (await _getWorkflowBlockEditorCommandRequestClient
            .RunCommandAsync<GetWorkflowBlockEditorCommand, GetWorkflowBlockEditorCommandResponse>(
                new GetWorkflowBlockEditorCommand(blockFullName, blockParameters))).Message;


        var serializedBlockEditor = getWorkflowBlockEditorCommandResponse.SerializedBlockEditor;

        var editor = serializedBlockEditor != null
            ? JsonConvert.DeserializeObject<BlockEditor>(serializedBlockEditor)
            : null;


        return editor;
    }

    public async Task<List<ExternalControlDefinition>> GetExternalControlsOrDefaultAsync(Guid id)
    {
        var workflowDefinitionEntity = await GetWorkflowByIdAsync(id);

        return workflowDefinitionEntity != null
            ? JsonConvert.DeserializeObject<List<ExternalControlDefinition>>(workflowDefinitionEntity.ExternalControls)
            : null;
    }
}