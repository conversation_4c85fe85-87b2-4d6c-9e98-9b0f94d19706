<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <RootNamespace>FlexCharge.Eligibility.Adapters.Common</RootNamespace>
        <AssemblyName>FlexCharge.Eligibility.Adapters.Common</AssemblyName>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\FlexCharge.Common\FlexCharge.Common.csproj"/>
        <ProjectReference Include="..\FlexCharge.WorkflowEngine.Common\FlexCharge.WorkflowEngine.Common.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="DistributedCache\"/>
    </ItemGroup>

</Project>
