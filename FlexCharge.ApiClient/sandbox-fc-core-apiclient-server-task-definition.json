{"containerDefinitions": [{"name": "core-apiclient", "image": "556663010871.dkr.ecr.us-east-1.amazonaws.com/fc-core-server-apiclient:97fee549a1a0d04d6a050cc40c90e3d1dc824825", "cpu": 0, "portMappings": [{"containerPort": 80, "hostPort": 80, "protocol": "tcp"}], "essential": true, "environment": [{"name": "DB_USERNAME", "value": "apiclient_service_sandbox"}, {"name": "DB_DATABASE", "value": "fc_apiclient"}, {"name": "DB_HOST", "value": "flexcharge-sandbox.ctwfhnhdjewu.us-east-1.rds.amazonaws.com"}, {"name": "DB_PORT", "value": "5432"}, {"name": "ASPNETCORE_ENVIRONMENT", "value": "Sandbox"}, {"name": "AWS_COGNITO_USER_POOL_ID", "value": "us-east-1_7Snu9L5Rt"}, {"name": "SNS_IAM_REGION", "value": "us-east-1"}, {"name": "NEW_RELIC_APP_NAME", "value": "Apiclient-sandbox"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:SANDBOX_DB_APICLIENT_PASSWORD-ZQ5yes"}, {"name": "AWS_IAM_COGNITO_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:STG_AWS_IAM_COGNITO_KEY-SVc81A"}, {"name": "AWS_IAM_COGNITO_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:STG_AWS_IAM_COGNITO_SECRET-7KOA9j"}, {"name": "AWS_IAM_COGNITO_CLIENT_ID", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:SANDBOX_COGNITO_CLIENT_ID-flykbv"}, {"name": "SNS_IAM_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:SANDBOX_SNS_IAM_ACCESS_KEY-vt5dWo"}, {"name": "SNS_IAM_SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:SANDBOX_SNS_IAM_SECRET_KEY-iDGfTK"}, {"name": "API_CLIENT_JWT_SIGNING_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:SANDBOX-API-CLIENT-JWT-SIGNING-KEY-HlgILt"}, {"name": "STRIPE_APP_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:sandbox/integrations/stripe-apps/app-secret-UAvjmi"}, {"name": "STRIPE_APPS_STRIPE_API_KEY_FOR_OAUTH", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:sandbox/integrations/stripe-apps/stripe-api-key-for-oauth-GuKp2Q"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/fc-core-apiclient-server-sandbox", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs", "awslogs-create-group": "true"}}}], "family": "fc-core-apiclient-server-sandbox", "taskRoleArn": "arn:aws:iam::556663010871:role/ecsTaskExecutionWithSecretAccess-Sandbox-Role", "executionRoleArn": "arn:aws:iam::556663010871:role/ecsTaskExecutionWithSecretAccess-Sandbox-Role", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024"}