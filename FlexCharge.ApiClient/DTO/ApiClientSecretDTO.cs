namespace FlexCharge.ApiClient.DTO;

public class ApiClientSecretDTO
{
    public Guid Id { get; set; }
    public string Description { get; set; }
    public string Key { get; set; }
    public string Value { get; set; }
    public TimeSpan Expiration { get; set; }
    public DateTime? RevokedAt { get; private set; }
    public string Type { get; set; }
    public Guid ClientId { get; set; }
    public DateTime LastUsed { get; set; }
    public List<Guid> Scopes { get; set; }
    public string? Note { get; set; }
}