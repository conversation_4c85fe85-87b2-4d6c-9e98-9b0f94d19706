using FlexCharge.ApiClient;
using FlexCharge.ApiClient.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace FlexCharge.ApiClient.Services
{
    public class ClaimsProvider : IClaimsProvider
    {
        private readonly PostgreSQLDbContext _context;

        public ClaimsProvider(PostgreSQLDbContext context)
        {
            _context = context;
        }


        public async Task<List<Claim>> GetAsync(Guid user)
        {
            //var claims = await _context.ApiScopes.Include(x=>x.Claims).ToDictionaryAsync(x=>x.Name);

            //return claims;
            //claims.Add("full_name",new ApiScope{  })
            var claims = new List<Claim>();

            // claims.Add(new Claim("full_name", string.IsNullOrEmpty(user.FullName) ? "" : user.FullName));
            //
            //
            // foreach (var claim in user.Claims)
            // {
            //     claims.Add(new Claim(claim.Type, claim.Value));
            // }

            return await Task.FromResult(claims);
        }

        public async Task<List<Claim>> GetApiClientClaimsAsync(Guid clientId)
        {
            return await _context.ApiClientClaims.Where(x => x.ApiClientId == clientId)
                .Select(claim => new Claim(claim.Type, claim.Value)).ToListAsync();
        }

        public async Task<List<Claim>> GetApiScopesClaimsAsync()
        {
            return await _context
                .ApiScopeClaims
                .Select(x => new Claim(x.Type, x.Value))
                .ToListAsync();
        }
    }
}