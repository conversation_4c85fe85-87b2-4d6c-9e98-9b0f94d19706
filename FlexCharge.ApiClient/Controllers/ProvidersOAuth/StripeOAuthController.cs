using System.Text;
using System.Text.Json;
using FlexCharge.ApiClient.DTO;
using FlexCharge.ApiClient.Services;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Cloud.SecretsManager;
using FlexCharge.Common.Shared.Authentication.OAuth;
using FlexCharge.Common.Shared.Authentication.OAuth.Services.ProviderOAuthService;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Common;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Stripe;

namespace FlexCharge.ApiClient.Controllers.ProvidersOAuth
{
    [Route("[controller]")]
    [ApiController]
    [JwtAuth]
    public class StripeOAuthController : ProviderOAuthControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly IProviderOAuthService _providerOAuthService;
        private readonly IApiIdentityService _apiIdentityService;
        private readonly ISecretsManager _secretsManager;

        public StripeOAuthController(IServiceProvider serviceProvider,
            IConfiguration configuration,
            IProviderOAuthService providerOAuthService,
            IApiIdentityService apiIdentityService,
            ISecretsManager secretsManager) : base(serviceProvider)
        {
            _configuration = configuration;
            _providerOAuthService = providerOAuthService;
            _apiIdentityService = apiIdentityService;
            _secretsManager = secretsManager;
        }


        [HttpGet()]
        [ProducesResponseType(200)]
        [AllowAnonymous]
        public async Task<IActionResult> IncomingOAuthRequest()
        {
            var successRedirectUrl = _configuration.GetValue<string>("Stripe:SuccessRedirectUrl");
            var failureRedirectUrl = _configuration.GetValue<string>("Stripe:FailureRedirectUrl");

            return await ProcessIncomingAuthRequest(OAuthProvider.Stripe, successRedirectUrl, failureRedirectUrl);
        }

        [HttpPost("token")]
        [ProducesResponseType(200, Type = typeof(StripeTokenResponseDTO))]
        [AllowAnonymous]
        public async Task<IActionResult> GetTokenRequest()
        {
            using var workspan = Workspan.Start<StripeOAuthController>();

            if (!ModelState.IsValid)
                return ValidationProblem();

            try
            {
                // Use the secret key to verify the signature

                // Find your app's secret in your app settings page in the Developers Dashboard.
                // App's page -> View Signing Secret (at the top right corner)
                // absec_ is the prefix for the secret
                var stripeAppSecret = Environment.GetEnvironmentVariable("STRIPE_APP_SECRET");


#if DEBUG
                string stagingStripeAppSecretArn =
                    "arn:aws:secretsmanager:us-east-1:556663010871:secret:staging/integrations/stripe-apps/app-secret-xsbuk4";

                stripeAppSecret = await _secretsManager.GetSecretValueAsync(stagingStripeAppSecretArn);
#endif

                var signatureHeader = HttpContext.Request.Headers["Stripe-Signature"].FirstOrDefault();

                var requestBody = await GetRequestBodyAsync(HttpContext);

                try
                {
                    EventUtility.ValidateSignature(requestBody,
                        signatureHeader, stripeAppSecret);
                }
                catch (Exception e)
                {
                    #region Observability

                    var headerString = new StringBuilder();

                    foreach (var header in HttpContext.Request.Headers)
                    {
                        headerString.AppendLine($"{header.Key}: {header.Value}");
                    }

                    workspan
                        .Tag("RequestHeaders", headerString.ToString())
                        .Tag("RequestBody", requestBody)
                        .RecordFatalException(e, "Error validating Stripe signature");

                    #endregion

#if !DEBUG
                    return BadRequest("Error 23566");
#endif
                }

                var request = JsonSerializer.Deserialize<StripeTokenRequest>(requestBody);


                var stripeAccountId = request.account_id;

                workspan.Baggage("StripeAccountId", stripeAccountId);

                var accessToken = await _providerOAuthService.GetLatestAccessTokenAsync(OAuthProvider.Stripe,
                    ExternalEntityType.Account, stripeAccountId,
                    RelatedEntityType.Merchant);

                if (accessToken == null)
                {
                    workspan.Log
                        .Information("No access token found for the provided account id");

                    ModelState.AddModelError("account_id", "Error 63478");
                    return ValidationProblem();
                }

                var mid = accessToken.RelatedEntityId;

                workspan.Baggage("Mid", mid);

                var keys = await _apiIdentityService.GetKeys(mid, null, null);

                var keyPair = keys.Keys.Single();

                var jwt = await _apiIdentityService.Authenticate(keyPair.Key, keyPair.Secret);

                return Ok(jwt);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return BadRequest("Error processing token request");
            }
        }
    }
}