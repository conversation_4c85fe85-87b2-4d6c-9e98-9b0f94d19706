#if DEBUG
using System.Net.Http.Headers;
using System.Text;
using FlexCharge.Common.Shared.Authentication.OAuth;
using FlexCharge.Common.Shared.Authentication.OAuth.Services.ProviderOAuthService;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Common;
using MassTransit;
using Microsoft.AspNetCore.Mvc;


namespace FlexCharge.ApiClient.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class TestController : ControllerBase
    {
        private readonly IProviderOAuthService _providerOAuthService;
        private readonly IPublishEndpoint _publisher;

        public TestController(IProviderOAuthService providerOAuthService, IPublishEndpoint publisher)
        {
            _providerOAuthService = providerOAuthService;
            _publisher = publisher;
        }


        // [HttpGet("test-stripe-oauth")]
        // [ProducesResponseType(200)]
        // public async Task<ActionResult> Get(string acCode)
        // {
        //     using var workspan = Workspan.Start<TestController>();
        //
        //     var provider = "Stripe";
        //     
        //     _oAuthService.ProcessAuthorizationRequestAsync(
        //         provider, provider, new Dictionary<string, List<string>>(), new Dictionary<string, List<string>>(), acCode);
        //
        //     // OAuth 2.0 Authorization flow with Stripe
        //     // see: https://docs.stripe.com/stripe-apps/api-authentication/oauth#url-parameters
        //     var httpClient = new HttpClient();
        //
        //     //curl -X POST https://api.stripe.com/v1/oauth/token \
        //     // -u sk_live_***: \
        //     // -d code=ac_*** \
        //     // -d grant_type=authorization_code
        //     var clientSecret =
        //         "sk_test_51QTk2PCB02KSPRUm1Fqeix5wJrb1MTb9TiqG7Zudji2CPA6Q8bWNGcwUzjokD8tHLzzKdyGuKVQO2HjWT4nQgXx800cnULFyEM";
        //
        //     var request = new HttpRequestMessage(HttpMethod.Post, "https://api.stripe.com/v1/oauth/token");
        //     request.Headers.Authorization = new AuthenticationHeaderValue("Basic",
        //         Convert.ToBase64String(Encoding.ASCII.GetBytes($"{clientSecret}:")));
        //     request.Content = new FormUrlEncodedContent(new Dictionary<string, string>
        //     {
        //         {"code", acCode},
        //         {"grant_type", "authorization_code"}
        //     });
        //
        //     var response = await httpClient.SendAsync(request);
        //
        //     var responseContent = await response.Content.ReadAsStringAsync();
        //
        //     return Ok(responseContent);
        // }

        [HttpPut("remove-stripe-oauth-connection")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> Post(Guid mid, string stripeAccount)
        {
            using var workspan = Workspan.Start<TestController>();

            await _publisher.Publish(new ExternalProviderApplicationDeAuthorizedEvent(
                mid, OAuthProvider.Stripe, stripeAccount,
                "Id", "App Name"));

            return Ok();
        }
    }
}
#endif