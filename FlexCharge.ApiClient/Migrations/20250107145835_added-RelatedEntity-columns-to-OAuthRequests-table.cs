using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.ApiClient.Migrations
{
    /// <inheritdoc />
    public partial class addedRelatedEntitycolumnstoOAuthRequeststable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Mid",
                table: "OAuthRequests",
                newName: "RelatedEntityId");

            migrationBuilder.AddColumn<string>(
                name: "RelatedEntityType",
                table: "OAuthRequests",
                type: "text",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RelatedEntityType",
                table: "OAuthRequests");

            migrationBuilder.RenameColumn(
                name: "RelatedEntityId",
                table: "OAuthRequests",
                newName: "Mid");
        }
    }
}
