using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FlexCharge.ApiClient.Entities
{
    public class ApiClientClaim : AuditableEntity
    {
        public string Value { get; set; }
        public string Type { get; set; }
        
        public Guid ApiScopeClaimID  { get; set; }
        
        public ApiClient ApiClient { get; set; }
        public Guid ApiClientId { get; set; }
        public Guid? ApiScopeId { get; set; }
    }
}
