using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CsvHelper.Configuration.Attributes;
using FlexCharge.Common.Telemetry;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Activity.Services.InternalReporting.Reports.Implementations;

[Report("Net Fees Report", "6572AD09-7556-4A9C-8457-F872228F0844")]
class NetFeesReport : InternalReportBase
{
    [Name("Day")] public string Date { get; set; }

    [Name("Total Value Gross Fees")] public decimal TotalValueGrossFees { get; set; }

    //[Ignore] 
    public decimal TotalOrdersValue { get; set; }

    [Name("% Bad Debt and Fraud after 60 Days")]
    public string BadDebtAndFraudAçfter60DaysPercent =>
        CalculatePercentOrZero(BadDebtAndFraudCostAfter60Days, TotalOrdersValue);

    [Ignore] public decimal TotalRepaidValueAfter60Days { get; set; }

    [Ignore] public decimal FixedCosts { get; set; }

    [Ignore] public decimal PayoutProcessingCosts { get; set; }

    [Ignore] public decimal RepaymentProcessingCosts { get; set; }

    [Ignore] public decimal Eligibility3rdPartyServicesCosts { get; set; }

    [Name("Bad Debt and Fraud Cost")]
    public decimal BadDebtAndFraudCostAfter60Days => TotalOrdersValue - TotalRepaidValueAfter60Days;

    [Name("ProcessingCost")] public decimal ProcessingCost { get; set; }

    [Name("NetFees")] public decimal NetFees => TotalValueGrossFees - BadDebtAndFraudCostAfter60Days - ProcessingCost;


    #region Report Generation

    public override async Task<string> GenerateReportAsync(Workspan workspan,
        ReportGenerationStatistics reportGenerationStatistics, IQueryable<Entities.Activity> activities,
        DateTime startDate,
        DateTime endDate,
        Guid? mid = null,
        int? mcc = null)
    {
        var report = new List<NetFeesReport>();

        var activitiesFilteredByMid = activities.FilterByMid(mid);


        var reportActivities = await
            activitiesFilteredByMid
                .FilterByDate(startDate, endDate)
                .Where(x =>
                    x.Name == ActivityNames.Order_Placed).ToListAsync();

        var reportActivitiesGroupedByDate =
            reportActivities
                .GroupBy(x => x.ActionTimestamp.Date).OrderByDescending(x => x.Key);

        foreach (var activitiesOnThisDay in reportActivitiesGroupedByDate)
        {
            var capturesForThisDayOrdersAfter60Days =
                ( //Get all eligible orders on this day
                    from thisDayOfferEligibleActivity in activitiesOnThisDay.Where(x =>
                        x.Name == ActivityNames.Order_Placed)
                    //Get all captures in 60 days after this day 
                    from capturePaymentActivity in activitiesFilteredByMid.Where(x =>
                        x.ActionTimestamp.Date >= activitiesOnThisDay.Key.Date &&
                        x.ActionTimestamp.Date <= activitiesOnThisDay.Key.AddDays(60).Date &&
                        x.Name == ActivityNames.PayIn_Payment_Capture_Succeeded)
                    //Use only captures for orders on this day
                    where capturePaymentActivity.CorrelationId == thisDayOfferEligibleActivity.CorrelationId
                    select capturePaymentActivity)
                .ToList();

            try
            {
                #region Calculating Processing Cost

                //E.g. 3rd-party Fraud, Bureau calls fees 
                decimal eligibility3rdPartyServicesCosts =
                    activitiesFilteredByMid //we need to scan use all types of activities for processing costs
                        .Where(x =>
                            x.ActionTimestamp.Date == activitiesOnThisDay.Key.Date &&
                            x.Name != ActivityNames
                                .PayIn_Payment_Capture_Succeeded) //excluding payment capture activities
                        .CalculateTotalProcessingCosts();

                //Commissions for charging customer's credit card on each capture
                decimal repaymentProcessingCosts = capturesForThisDayOrdersAfter60Days.CalculateTotalProcessingCosts();

                //E.g. Bank Transfer cost
                decimal payoutProcessingCosts = 0;
                //E.g. Amazon AWS, Software licenses, e.g.
                decimal fixedCostsForThisDay = 0;

                decimal processingCost = eligibility3rdPartyServicesCosts + repaymentProcessingCosts +
                                         payoutProcessingCosts + fixedCostsForThisDay;

                #endregion

                report.Add(new NetFeesReport()
                {
                    Date = FormatDate(activitiesOnThisDay.Key),
                    TotalValueGrossFees = activitiesOnThisDay.Where(x => x.Name == ActivityNames.Order_Placed)
                        .CalculateTotalFee(),
                    TotalOrdersValue = activitiesOnThisDay.Where(x => x.Name == ActivityNames.Order_Placed)
                        .CalculateTotalAmount(),
                    TotalRepaidValueAfter60Days = capturesForThisDayOrdersAfter60Days.CalculateTotalAmount(),
                    ProcessingCost = processingCost,
                    Eligibility3rdPartyServicesCosts = eligibility3rdPartyServicesCosts,
                    RepaymentProcessingCosts = repaymentProcessingCosts,
                    PayoutProcessingCosts = payoutProcessingCosts,
                    FixedCosts = fixedCostsForThisDay
                });

                reportGenerationStatistics.RowsActuallyGenerated++;
            }
            catch (Exception e)
            {
                workspan.RecordException(e, "Cannot create report row");
            }

            reportGenerationStatistics.TotalRowsRequested++;
        }

        return GenerateCSVFromRows(report.ToList());
    }

    #endregion
}