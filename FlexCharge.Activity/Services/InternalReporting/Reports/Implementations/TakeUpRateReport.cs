using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CsvHelper.Configuration.Attributes;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Telemetry;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Activity.Services.InternalReporting.Reports.Implementations;

[Report("Take Up Rate Report", "C22B509F-20E6-44CC-A01E-7AA6175C33A5")]
class TakeUpRateReport : InternalReportBase
{
    [Name("Day")] public string Date { get; set; }

    [Name("# Total Offers")] public int TotalOffers { get; set; }

    [Ignore] public int NonConditionalOffers => TotalOffers - ConditionalOffers;

    [Name("% Non Conditional Offers / Total Offers")]
    public string NonConditionalOffersOfTotalOffersPercent => CalculatePercentOrZero(NonConditionalOffers, TotalOffers);

    [Name("% Take up Non Conditional Offers – Flow1")]
    public string TakeUpNonConditionalOffers_Flow1Percent => "";

    [Name("% Take up Non Conditional Offers – Flow2")]
    public string TakeUpNonConditionalOffers_Flow2Percent => "";

    [Name("% Take up Non Conditional Offers – Flow3")]
    public string TakeUpNonConditionalOffers_Flow3Percent => "";

    [Ignore] public int ConditionalOffers { get; set; }

    [Name("% Conditional Offers / Total Offers")]
    public string ConditionalOffersOfTotalOffersPercent => CalculatePercentOrZero(ConditionalOffers, TotalOffers);


    #region Report Generation

    public override async Task<string> GenerateReportAsync(Workspan workspan,
        ReportGenerationStatistics reportGenerationStatistics, IQueryable<Entities.Activity> activities, DateTime startDate,
        DateTime endDate,
        Guid? mid = null,
        int? mcc = null)
    {
        var activitiesToSelect =
            activities
                .FilterByMid(mid)
                .FilterByDate(startDate, endDate);


        var reportActivities = await
            activitiesToSelect
                .Where(x =>
                    x.Name == ActivityNames.Offer_Created ||
                    x.Name == ActivityNames.Cures_UserChallenge || 
                    x.Name == ActivityNames.UserChallenge_CureExecutionStarted
                )
                .ToListAsync();

        var offerCreatedOrCapturedActivitiesGroupedByDate =
            reportActivities
                .GroupBy(x => x.ActionTimestamp.Date).OrderByDescending(x => x.Key);

        List<string> columns = new List<string>();
        columns.Add("Date");
        columns.Add("# Total Offers");

        columns.Add("% Non Conditional Offers / Total Offers");

        columns.Add("% Take up Non Conditional Offers – Flow1");
        columns.Add("% Take up Non Conditional Offers – Flow2");
        columns.Add("% Take up Non Conditional Offers – Flow3");

        columns.Add("% Conditional Offers / Total Offers");

        Dictionary<string, int> userChallengeCureNameToColumn = new();

        ReportBuilder report = new ReportBuilder(columns);

        foreach (var activitiesOnThisDay in offerCreatedOrCapturedActivitiesGroupedByDate)
        {
            try
            {
                int totalOffers = activitiesOnThisDay.Count(x => x.Name == ActivityNames.Offer_Created);
                int conditionalOffers = activitiesOnThisDay.Count(x => x.Name == ActivityNames.Cures_UserChallenge);
                int nonConditionalOffers = totalOffers - conditionalOffers; // activitiesOnThisDay.Count(x => x.Name == ActivityNames.Cures_UserChallenge);

                var allUserChallengeActivities =
                    activitiesOnThisDay.Where(x => x.Name == ActivityNames.Cures_UserChallenge).ToList();

                var allUserChallengeTakenUpActivities =
                    activitiesOnThisDay.Where(x => x.Name == ActivityNames.UserChallenge_CureExecutionStarted).ToList();

                var requestedUserChallengesGroupedByCureId =
                    allUserChallengeActivities
                        .GroupBy(x => new PayloadMetadata(x.Meta, true).AsString("CureId"));

                var takenUpUserChallengesGroupedByCureId =
                    allUserChallengeTakenUpActivities
                        .GroupBy(x => new PayloadMetadata(x.Meta, true).AsString("CureId"))
                        .ToDictionary(x => x.Key, y => y);

                Dictionary<string, (int UserAskedCount, int UserRespondedCount)> userChallengeCuresTakeUpStatistics =
                    new();

                foreach (var requestedUserChallenges in requestedUserChallengesGroupedByCureId)
                {
                    var cureId = requestedUserChallenges.Key;
                    var requestedChallengesCount = requestedUserChallenges.Count();


                    var respondedChallengesCount = 0;
                    if (takenUpUserChallengesGroupedByCureId.TryGetValue(cureId,
                            out var takenUpUserChallengesForThisCure))
                    {
                        respondedChallengesCount = takenUpUserChallengesForThisCure.Count();
                    }

                    userChallengeCuresTakeUpStatistics[cureId] =
                        new(requestedChallengesCount, respondedChallengesCount);
                }

                int column = 0;
                using (var row = report.StartRow())
                {
                    //Date
                    row[column++] = FormatDate(activitiesOnThisDay.Key);
                    //# Total Offers
                    row[column++] = totalOffers.ToString();

                    //% Non Conditional Offers / Total Offers
                    row[column++] = CalculatePercentOrZero(nonConditionalOffers, totalOffers);

                    //% Take up Non Conditional Offers – Flow1
                    row[column++] = "";
                    //% Take up Non Conditional Offers – Flow2
                    row[column++] = "";
                    //% Take up Non Conditional Offers – Flow3
                    row[column++] = "";

                    //% Conditional Offers / Total Offers
                    row[column++] = CalculatePercentOrZero(conditionalOffers, totalOffers);

                    int startOfTakupByCuresColumns = column;
                    foreach (var userChallengeCureStatistics in userChallengeCuresTakeUpStatistics)
                    {
                        var cureId = userChallengeCureStatistics.Key;
                        
                        int columnId;
                        #region Get or add column for cure to report

                        if (!userChallengeCureNameToColumn.TryGetValue(cureId, out columnId))
                        {
                            columns.Add(cureId);
                            columnId = columns.Count - 1;
                            userChallengeCureNameToColumn.Add(cureId, columnId);
                        }

                        #endregion

                        if (columnId < column) throw new Exception("Wrong column for cure - check static columns");

                        row[columnId] = CalculatePercentOrZero(
                            userChallengeCureStatistics.Value.UserRespondedCount,
                            userChallengeCureStatistics.Value.UserAskedCount);
                    }

                    // for (int i = startOfTakupByCuresColumns; i < columns.Count; i++)
                    // {
                    //     if (string.IsNullOrEmpty(row[i])) row[i] = "0%";
                    // }
                }

                reportGenerationStatistics.RowsActuallyGenerated++;
            }
            catch (Exception e)
            {
                workspan.RecordException(e, "Cannot create report row");
            }

            reportGenerationStatistics.TotalRowsRequested++;
        }

        return  report.ToString();
    }

    #endregion
}