using System;
using System.Linq;

namespace FlexCharge.Activity.Services.InternalReporting.Reports;

static class ActivityQueryExtensions
{
    public static IQueryable<FlexCharge.Activity.Entities.Activity> FilterByMid(this IQueryable<FlexCharge.Activity.Entities.Activity> activities, Guid? mid)
    {
        return mid != null ? activities.Where(x => x.TenantId == mid) : activities;
    }
    
    public static IQueryable<FlexCharge.Activity.Entities.Activity> FilterByDate(this IQueryable<FlexCharge.Activity.Entities.Activity> activities, DateTime startTime, DateTime endTime)
    {
        var startUniversalTime = startTime.ToUniversalTime();
        var endUniversalTime = endTime.ToUniversalTime();
        return activities.Where(x => x.ActionTimestamp >= startUniversalTime && x.ActionTimestamp <= endUniversalTime);
    }
    
    public static IQueryable<FlexCharge.Activity.Entities.Activity> FilterByDate(this IQueryable<FlexCharge.Activity.Entities.Activity> activities, DateTime dayToSelect)
    {
        return activities.Where(x => x.ActionTimestamp.Date == dayToSelect.Date);
    }
}