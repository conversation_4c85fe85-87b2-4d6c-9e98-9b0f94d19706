using System;
using System.Collections.Generic;
using System.Linq;
using FlexCharge.Common.Activities;
using FlexCharge.Utils;

namespace FlexCharge.Activity.Services.InternalReporting.Reports;

static class ActivityEnumerableExtensions
{
    public static IEnumerable<FlexCharge.Activity.Entities.Activity> FilterByRepaymentRange(this IEnumerable<FlexCharge.Activity.Entities.Activity> activities, DateTime day,
        int repaymentIntervalDays)
    {
        return activities.Where(x => x.ActionTimestamp.Date <= day.AddDays(repaymentIntervalDays));
    }

    public static decimal CalculateTotalAmount(this IEnumerable<FlexCharge.Activity.Entities.Activity> activities)
    {
        return Formatters.LongToDecimal(activities
            .Select(x => new PayloadMetadata(x.Meta, createEmptyIfMetadataIsNull: true).AsLong("Amount"))
            .Sum());
    }
    
    public static long CalculateTotalAmountInCents(this IEnumerable<FlexCharge.Activity.Entities.Activity> activities)
    {
        return activities
            .Select(x => 
                new PayloadMetadata(x.Meta, createEmptyIfMetadataIsNull: true).AsLong("Amount"))
            .Sum();
    }
    
    
    public static decimal CalculateTotalFee(this IEnumerable<FlexCharge.Activity.Entities.Activity> activities)
    {
        return Formatters.LongToDecimal(activities
            .Select(x => new PayloadMetadata(x.Meta, createEmptyIfMetadataIsNull: true).AsLong("Fee"))
            .Sum());
    }
    
    public static decimal CalculateTotalProcessingCosts(this IEnumerable<FlexCharge.Activity.Entities.Activity> activities)
    {
        return Formatters.LongToDecimal(activities
            .Select(x => new PayloadMetadata(x.Meta, createEmptyIfMetadataIsNull: true)
            .ProcessingCostValueOrDefault).Sum());
    }
    
    
    public static decimal CalculateTotalAmountForRepaymentRange(this IEnumerable<FlexCharge.Activity.Entities.Activity> activities, DateTime day,
        int repaymentIntervalDays)
    {
        return activities.FilterByRepaymentRange(day, repaymentIntervalDays).CalculateTotalAmount();
    }
   
}

static class ActivityExtensions
{
    public static bool  IsEqualAmount(this FlexCharge.Activity.Entities.Activity activity1, 
        FlexCharge.Activity.Entities.Activity activity2)
    {
        var amount1 = new PayloadMetadata(activity1.Meta).AsLong("Amount");
        var amount2 = new PayloadMetadata(activity2.Meta).AsLong("Amount");

        return amount1 == amount2;
    }
}