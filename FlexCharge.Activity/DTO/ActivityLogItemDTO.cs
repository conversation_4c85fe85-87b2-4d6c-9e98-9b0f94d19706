using System;

namespace FlexCharge.Activity.DTO;

public class ActivityRequestDTO
{
    public Guid Id { get; set; }
    public DateTime Date { get; set; }
    public string Environment { get; set; }
    public string? Payload { get; set; }
    public Guid OrderId { get; set; }
}

public class ActivityResponseDTO
{
    public long Latency { get; set; }
    public string Status { get; set; }
    public int StatusCode { get; set; }
    public string Description { get; set; }
    public string Message { get; set; }
    public string? Payload { get; set; }
}

public class ActivityLogItemDTO
{
    public Guid Id { get; set; }
    public string Status { get; set; }
    public int StatusCode { get; set; }
    public DateTime Date { get; set; }
    public string Environment { get; set; }
    public string Type { get; set; }
    public string Description { get; set; }
    public string Name { get; set; }
    public ActivityRequestDTO Request { get; set; }
    public ActivityResponseDTO Response { get; set; }
    public string Path { get; set; }
    public string Method { get; set; }
}