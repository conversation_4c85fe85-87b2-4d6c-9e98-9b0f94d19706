using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Identity.Migrations
{
    /// <inheritdoc />
    public partial class addmfaperiod : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsMFAPermanent",
                table: "Users",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsMFAPermanent",
                table: "Users");
        }
    }
}
