using FlexCharge.Common.Activities.Attributes;
using FlexCharge.Common.Shared.Activities;
using FlexCharge.Contracts.Activities;

namespace FlexCharge.Identity.Activities;

[Category(ActivityCategories.Identity_AuditLog, ActivityInformationLevelFlags.Information)]
public enum IdentityActivities
{
    Identity_SignIn_PasswordResetRequired,
    Identity_SignIn_NewPasswordRequired,
    Identity_SignIn_PasswordChangeRequired,
    Identity_SignIn_Ended,
    Identity_AdminSignUp_Ended,
    Identity_SignUp_Ended,
    Identity_ResendInvitation_Ended,
    Identity_ChangePassword_Ended,
    Identity_ForgotPassword_Ended,
    Identity_ConfirmForgotPassword_Ended,
    Identity_SignOut_Ended,
    Identity_DisableUser_Ended,
    Identity_DeleteUser_Ended,
    Identity_UpdateUser_Ended,
    Identity_SignUpError,
    Identity_VerifyMFATokenAsync_Ended,
    Identity_SetUserMFAPreference_Ended,
    Identity_SignInChallengeAsync_Ended,
    Identity_AssociateMFATokenAsync_Ended,
    Identity_UpdateUserContactInformation_Ended,
    Identity_AdminChangePasswordAsync_Ended,
    Identity_AdminSetUserMFAPreference_Ended,
    Identity_ResetUserPassword_Ended,
}

[Category(ActivityCategories.Identity_Errors, ActivityInformationLevelFlags.Error)]
public enum IdentityErrorActivities
{
    Identity_SignIn_UserNotFound,
    Identity_SignIn_UserNotConfirmed,
    Identity_SignIn_NotAuthorized,
    Identity_SignIn_PasswordResetRequired,
    Identity_SignIn_Error,
    Identity_SignUp_UsernameNotExists,
    Identity_SignUp_UserAlreadyExistsIDP,
    Identity_SignUp_UserNotFound,
    Identity_SignUp_UserNotConfirmed,
    Identity_SignUp_UserNotAuthorized,
    Identity_SignUp_Error,
    Identity_SignUp_PasswordResetRequired,
    Identity_ResendInvitation_NotFound,
    Identity_ResendInvitation_UnsupportedUserState,
    Identity_ForgotPassword_CodeMismatch,
    Identity_ForgotPassword_ExpiredCode,
    Identity_ForgotPassword_Error,
    Identity_ConfirmForgotPassword_CodeMismatch,
    Identity_ConfirmForgotPassword_ExpiredCode,
    Identity_ConfirmForgotPassword_InvalidPassword,
    Identity_ConfirmForgotPassword_Error,
    Identity_SignOut_Error,
    Identity_DisableUser_Error,
    Identity_DeleteUser_Error,
    Identity_UpdateUser_Error,
    Identity_GetUserStatus_Error,
    Identity_ResendInvitation_Error,
    IdentitySetMFASettings_Error,
    Identity_VerifyMFATokenAsync_CodeMismatch,
    Identity_VerifyMFATokenAsync_Error,
    Identity_SetMFASettings_Error,
    Identity_SignInChallengeAsync_CodeMismatch,
    Identity_AssociateMFATokenAsync_Error,
    Identity_SignInChallengeAsync_UserNotAuthorized,
    Identity_UpdateUserContactInformation_Error,
    Identity_VerifyMFATokenAsync_UserNotAuthorized,
    Identity_AdminChangePasswordAsync_Error,
    Identity_AdminSetUserMFAPreference_Error,
    Identity_ResetUserPassword_Error,
}

[Category(ActivityCategories.Identity_API_Errors, ActivityInformationLevelFlags.Error)]
public enum ApiErrorActivities
{
    ModelStateError,
    Identity_SignIn_ModelStateError,
    EndpointCriticalApiError,
}


[Category(ActivityCategories.Identity_Security, ActivityInformationLevelFlags.Security | ActivityInformationLevelFlags.Error)]
public enum IdentitySecurityActivities
{
}
