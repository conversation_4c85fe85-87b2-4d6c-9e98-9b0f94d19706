using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Amazon;
using Amazon.AspNetCore.Identity.Cognito;
using Amazon.CognitoIdentityProvider;
using Amazon.CognitoIdentityProvider.Model;
using Amazon.Extensions.CognitoAuthentication;
using Amazon.Runtime;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Identity.Activities;
using FlexCharge.Identity.DTO;
using FlexCharge.Identity.Entities;
using MassTransit;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;


namespace FlexCharge.Identity.Services;

public class CognitoIdentityService : IIdentityService
{
    //https://github.com/aws/aws-aspnet-cognito-identity-provider/blob/249500b6690ab1f07abac142d01807220bd738af/docs/1-Setup.md
    //https://github.com/aws/aws-sdk-net-extensions-cognito/blob/master/src/Amazon.Extensions.CognitoAuthentication/CognitoUser.cs#L40
    //https://github.com/aws/aws-aspnet-cognito-identity-provider/blob/master/src/Amazon.AspNetCore.Identity.Cognito/CognitoSigninManager.cs#L141

    private const int MFA_INTERVAL = 30;

    private const string UserStatusForceChangePassword = "FORCE_CHANGE_PASSWORD";
    private const string UserStatusNewPasswordRequired = "NEW_PASSWORD_REQUIRED";
    private const string UserStatusResetRequired = "RESET_REQUIRED";
    private const string UserStatusConfirmed = "CONFIRMED";
    private SignInManager<CognitoUser> _signInManager;
    private UserManager<CognitoUser> _userManager;
    private readonly CognitoUserPool _pool;
    private readonly AmazonCognitoIdentityProviderClient _client;
    private readonly IHttpContextAccessor _httpContext;
    private readonly IPublishEndpoint _publisher;
    private readonly PostgreSQLDbContext _postgreSqlDbContext;
    private readonly IActivityService _activityService;
    private readonly IUsersService _usersService;

    public CognitoIdentityService(SignInManager<CognitoUser> signInManager,
        CognitoUserPool pool, UserManager<CognitoUser> userManager, IHttpContextAccessor httpContext,
        IPublishEndpoint publisher, PostgreSQLDbContext postgreSqlDbContext, IActivityService activityService,
        IUsersService usersService)
    {
        _signInManager = signInManager;
        _pool = pool;
        _userManager = userManager;
        _httpContext = httpContext;
        _publisher = publisher;
        _activityService = activityService;
        _postgreSqlDbContext = postgreSqlDbContext;
        _usersService = usersService;
    }


    public async Task<AdminSignUpResponseDTO> AdminSignUpAsync(AdminSignUpRequestDTO payload, MessageAction? messageAction)
    {
        using var workspan = Workspan.Start<CognitoIdentityService>();

        var response = new AdminSignUpResponseDTO();

        try
        {
            // var user = _pool.GetUser(payload.UserName);
            // user.Attributes.Add(CognitoAttribute.Email.AttributeName, payload.Email);

            using (AmazonCognitoIdentityProviderClient _client =
                   new AmazonCognitoIdentityProviderClient(
                       new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                           Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                       RegionEndpoint.USEast1))
            {
                var adminCreateUserRequest = new AdminCreateUserRequest
                {
                    Username = payload.UserName,
                    UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID"),
                    DesiredDeliveryMediums = new List<string>()
                    {
                        "EMAIL"
                    },
                };
                
                if (messageAction != null)
                {
                    adminCreateUserRequest.MessageAction = messageAction.ToString();
                }
                
                var createUserResponse = await _client.AdminCreateUserAsync(adminCreateUserRequest);

                workspan.Log.Information("createUserResponse: {CreateUserResponse}",
                    JsonConvert.SerializeObject(createUserResponse));

                var userAttributes = new List<AttributeType>()
                {
                    new()
                    {
                        Name = "email_verified",
                        Value = "true"
                    },
                    new()
                    {
                        Name = MyClaimTypes.ACCOUNT_ID,
                        Value = payload.AccountId.ToString()
                    },
                    new()
                    {
                        Name = CognitoAttribute.GivenName.ToString(),
                        Value = payload.FirstName
                    },
                    new()
                    {
                        Name = CognitoAttribute.FamilyName.ToString(),
                        Value = payload.LastName
                    },
                    new()
                    {
                        Name = CognitoAttribute.EmailVerified.ToString(),
                        Value = true.ToString()
                    },
                    new()
                    {
                        Name = MyClaimTypes.IS_PII_ENABLED,
                        Value = payload.IsPiiMasked.HasValue ? payload.IsPiiMasked.Value.ToString() : "true"
                    }
                };

                if (payload.Group == SuperAdminGroups.PARTNER_ADMIN ||
                    payload.Group == SuperAdminGroups.PARTNER_ADMIN)
                {
                    userAttributes.Add(new AttributeType()
                    {
                        Name = MyClaimTypes.PARTNER_ID,
                        Value = payload.PartnerId.ToString()
                    });
                }

                if (payload.Group == MerchantGroups.MERCHANT_ADMIN ||
                    payload.Group == MerchantGroups.MERCHANT_FINANCE ||
                    payload.Group == MerchantGroups.MERCHANT_SUPPORT ||
                    payload.Group == MerchantGroups.MERCHANT_DEVELOPER ||
                    payload.Group == MerchantGroups.MERCHANT_SUPPORT_ADMIN
                   )
                {
                    userAttributes.Add(new AttributeType()
                    {
                        Name = MyClaimTypes.MERCHANT_ID,
                        Value = payload.MerchantId.ToString()
                    });
                }

                if (!string.IsNullOrEmpty(payload.Phone))
                {
                    userAttributes.Add(new AttributeType()
                    {
                        Name = "phone_number",
                        Value = payload.Phone
                    });
                }

                // we use update because we unable to create claims during creation
                await _client.AdminUpdateUserAttributesAsync(new AdminUpdateUserAttributesRequest
                {
                    UserAttributes = userAttributes,
                    Username = payload.Email,
                    UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID")
                });

                await _client.AdminAddUserToGroupAsync(new AdminAddUserToGroupRequest
                {
                    GroupName = payload.Group,
                    Username = createUserResponse.User.Username,
                    UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID")
                });

                if (createUserResponse.HttpStatusCode != HttpStatusCode.OK)
                    response.AddError(Consts.GeneralError);

                response.Email = createUserResponse.User.Attributes.SingleOrDefault(x => x.Name == "email")?.Value;
                response.UserId =
                    Guid.Parse(createUserResponse.User.Attributes.SingleOrDefault(x => x.Name == "sub")?.Value);
                response.Role = payload.Group;

                try
                {
                    var userFromDb = await _usersService.GetUserByEmail(payload.Email);

                    if (userFromDb == null)
                    {
                        var users = await _postgreSqlDbContext.Users.Where(x => x.MerchantId == payload.MerchantId)
                            .ToListAsync();
                        var user = new User()
                        {
                            // Id = response.UserId,
                            Email = response.Email?.ToLower(),
                            FullName = $"{payload.FirstName} {payload.LastName}",
                            PhoneNumber = payload.Phone,
                            FirstName = payload.FirstName,
                            LastName = payload.LastName,
                            Status = UserStatusNewPasswordRequired,
                            MerchantId = payload.MerchantId,
                            AccountId = payload.AccountId,
                            IsPiiMasked = payload.IsPiiMasked ?? true,
                            IsEnforceMFAEnabled = users.Any() && users.First().IsEnforceMFAEnabled,
                            Group = payload.Group,
                            PartnerId = payload.PartnerId,
                        };
                        if (_httpContext.HttpContext != null)
                        {
                            user.LastDeviceMatch = _httpContext.HttpContext.Request.Headers["User-Agent"].ToString();
                        }

                        await _postgreSqlDbContext.Users.AddAsync(user);
                        var userSaved = await _postgreSqlDbContext.SaveChangesAsync();
                    }
                }
                catch (Exception e)
                {
                    workspan.Log.Fatal(e, "User created in Cognito but not in local DB");
                    throw;
                }

                //test
                await _publisher.Publish(new UserSignedUpEvent
                {
                    UserId = response.UserId,
                    Email = response.Email,
                    Group = response.Role,
                    Mid = payload.MerchantId,
                    Pid = payload.PartnerId,
                    Aid = payload.AccountId,
                    MerchantState = payload.MerchantState
                });

                await _activityService.CreateActivityAsync(
                    IdentityActivities.Identity_AdminSignUp_Ended,
                    update => update
                        .TenantId(payload.MerchantId)
                        .Meta(meta => meta
                            .SetValue("Response", JsonConvert.SerializeObject(response))
                        ));

                return response;
            }
        }
        catch (UsernameExistsException e)
        {
            workspan.Log.Error(e, $"UsernameExistsException: SignUpAsync {payload.Email}");

            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_SignUp_UserAlreadyExistsIDP,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", payload.Email)
                    ));

            response.AddError(Consts.GeneralError);
            return response;
        }
        catch (UserNotFoundException e)
        {
            workspan.Log.Error(e, $"UserNotFoundException: SignUpAsync {payload.Email}");

            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_SignUp_UserNotFound,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", payload.Email)
                    ));

            response.AddError(e.Message);
            return response;
        }
        catch (UserNotConfirmedException e)
        {
            workspan.Log.Error(e, $"UserNotConfirmedException: SignUpAsync {payload.Email}");

            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_SignUp_UserNotConfirmed,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", payload.Email)
                    ));

            response.AddError(e.Message);
            return response;
        }
        catch (NotAuthorizedException e)
        {
            workspan.Log.Error(e, $"NotAuthorizedException: SignUpAsync {payload.Email}");

            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_SignUp_UserNotAuthorized,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", payload.Email)
                    ));

            response.AddError(e.Message);
            return response;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);

            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_SignUp_Error,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", payload.Email)
                    ));

            throw;
        }
    }

    public async Task<SignUpResponseDTO> SignUpAsync(SignUpRequestDTO payload)
    {
        using var workspan = Workspan.Start<CognitoIdentityService>();
        workspan.Log.Information($"ENTERED: SignUpAsync {payload.Email}");

        var response = new SignUpResponseDTO();

        try
        {
            var user = _pool.GetUser(payload.UserName);
            user.Attributes.Add(CognitoAttribute.Email.AttributeName, payload.Email);
            user.Attributes.Add(MyClaimTypes.MERCHANT_ID, GetMID().ToString());
            user.Attributes.Add("custom:isPiiMasked",
                payload.IsPiiMasked.HasValue ? payload.IsPiiMasked.Value.ToString() : true.ToString());

            var result = await _userManager.CreateAsync(user, payload.Password);

            if (result.Succeeded)
            {
                var _client = new AmazonCognitoIdentityProviderClient(
                    new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                        Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                    RegionEndpoint.USEast1);
                await _client.AdminAddUserToGroupAsync(new AdminAddUserToGroupRequest
                {
                    GroupName = MerchantGroups.MERCHANT_ADMIN,
                    Username = user.UserID,
                    UserPoolId = _pool.PoolID
                });

                try
                {
                    var userFromDb = await _usersService.GetUserByEmail(payload.Email);

                    if (userFromDb == null)
                    {
                        var newUser = new User()
                        {
                            // Id = response.UserId,
                            Email = response.Email?.ToLower(),
                            FullName = $"{payload.FirstName} {payload.LastName}",
                            PhoneNumber = payload.Phone,
                            FirstName = payload.FirstName,
                            LastName = payload.LastName,
                            Status = UserStatusNewPasswordRequired,
                            MerchantId = GetMID(),
                            PartnerId = GetPID(),
                            IsPiiMasked = payload.IsPiiMasked ?? true,
                            Group = MerchantGroups.MERCHANT_ADMIN,
                        };
                        if (_httpContext.HttpContext != null)
                        {
                            newUser.LastDeviceMatch = _httpContext.HttpContext.Request.Headers["User-Agent"].ToString();
                        }

                        await _postgreSqlDbContext.Users.AddAsync(newUser);
                        await _postgreSqlDbContext.SaveChangesAsync();
                    }
                }
                catch (Exception e)
                {
                    workspan.RecordException(e);
                }


                workspan.Log.Information("User created a new account with password.");
                //await _signInManager.SignInAsync(user, isPersistent: false);

                return response;
            }

            foreach (var error in result.Errors)
            {
                response.AddError(string.Empty, error.Description);
                workspan.Log.Error($"ERROR: EMAIL: {payload.Email} ERROR:{error.Description}");

                await _activityService.CreateActivityAsync(
                    IdentityActivities.Identity_SignUpError,
                    data: error.Description,
                    update => update
                        .TenantId(GetMID())
                        .Meta(meta => meta
                            .SetValue("Username", payload.Email)
                        ));
            }

            await _activityService.CreateActivityAsync(
                IdentityActivities.Identity_SignUp_Ended,
                update => update
                    .TenantId(GetMID())
                    .Meta(meta => meta
                        .SetValue("Response", JsonConvert.SerializeObject(response))
                    ));

            return response;
        }
        catch (UsernameExistsException e)
        {
            workspan.Log.Error($"ERROR: UsernameExistsException EMAIL: {payload.Email}");

            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_SignUp_UsernameNotExists,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", payload.Email)
                    ));

            response.AddError(Consts.GeneralError);
            return response;
        }
        catch (UserNotFoundException e)
        {
            workspan.Log.Error($"ERROR: UserNotFoundException EMAIL: {payload.Email}");

            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_SignUp_UserNotFound,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", payload.Email)
                    ));

            response.AddError(e.Message);
            return response;
        }
        catch (UserNotConfirmedException e)
        {
            workspan.Log.Error($"ERROR: UserNotConfirmedException EMAIL: {payload.Email}");

            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_SignUp_UserNotConfirmed,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", payload.Email)
                    ));

            response.AddError(e.Message);
            return response;
        }
        catch (NotAuthorizedException e)
        {
            workspan.Log.Error($"ERROR: NotAuthorizedException EMAIL: {payload.Email}");

            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_SignUp_UserNotAuthorized,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", payload.Email)
                    ));

            response.AddError(e.Message);
            return response;
        }
        catch (PasswordResetRequiredException e)
        {
            workspan.Log.Error($"ERROR: PasswordResetRequiredException EMAIL: {payload.Email}");

            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_SignUp_PasswordResetRequired,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", payload.Email)
                    ));

            response.AddError(Consts.PasswordResetRequired, Consts.PasswordResetRequired_code);
            return response;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);

            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_SignUp_Error,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", payload.Email)
                    ));

            throw;
        }
    }

    public async Task AdminResendInvitation(User user)
    {
        using var workspan = Workspan.Start<CognitoIdentityService>();
        workspan.Log.Information(
            "ENTERED: AdminResendInvitation Email: {Username} Mid: {MerchantId} Pid: {PartnerId}",
            user.Email, user.MerchantId, user.PartnerId);

        var response = new AdminSignUpResponseDTO();
        
        Guid? tenantId = null;
                    
        if (user.MerchantId != null && user.MerchantId != Guid.Empty)
        {
            tenantId = user.MerchantId;
        }
        else if (user.PartnerId != null && user.PartnerId != Guid.Empty)
        {
            tenantId = user.PartnerId;
        }

        try
        {
            using (AmazonCognitoIdentityProviderClient _client =
                   new AmazonCognitoIdentityProviderClient(
                       new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                           Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                       RegionEndpoint.USEast1))
            {
                try
                {
                    var resendInvitation = await _client.AdminCreateUserAsync(new AdminCreateUserRequest
                    {
                        MessageAction = "RESEND",
                        Username = user.Email,
                        UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID"),
                        DesiredDeliveryMediums = new List<string>()
                        {
                            "EMAIL"
                        },
                    });

                    if (resendInvitation.HttpStatusCode != HttpStatusCode.OK)
                        throw new BadHttpRequestException("Error resending invitation");
                    
                    response.Email = user.Email;
                    response.UserId =
                        Guid.Parse(resendInvitation.User.Attributes.SingleOrDefault(x => x.Name == "sub")?.Value);
                    response.Role = resendInvitation.User.Attributes.SingleOrDefault(x => x.Name == "custom:group")
                        ?.Value;

                    await _publisher.Publish(new ApplicationResendInviteRequestedEvent
                    {
                        Email = user.Email,
                        MerchantId = user.MerchantId ?? Guid.Empty,
                        PartnerId = user.PartnerId ?? Guid.Empty,
                    });

                    workspan.Log.Information("AdminResendInvitation: {CreateUserResponse}",
                        JsonConvert.SerializeObject(resendInvitation));
                    
                    await _activityService.CreateActivityAsync(
                        IdentityActivities.Identity_ResendInvitation_Ended,
                        data: new
                        {
                            UserId = response.UserId,
                            Email = response.Email,
                            Group = response.Role,
                            Mid = user.MerchantId,
                            Pid = user.PartnerId,
                        },
                        update => update
                            .TenantId(tenantId));
                }
                catch (UserNotFoundException e)
                {
                        await _activityService.CreateActivityAsync(
                            IdentityErrorActivities.Identity_ResendInvitation_NotFound,
                            data: e,
                            update => update
                                .TenantId(tenantId)
                                .Meta(meta => meta
                                    .SetValue("UserName", response.Email)
                                ));

                        throw new BadHttpRequestException("Not found");
                    
                }
            }
        }
        catch (UnsupportedUserStateException e)
        {
            workspan.Log.Fatal(e, $"UnsupportedUserStateException: AdminResendInvitation {user.Email}");

            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_ResendInvitation_UnsupportedUserState,
                data: e,
                update => update
                    .TenantId(tenantId)
                    .Meta(meta => meta
                        .SetValue("UserName", response.Email)
                    ));

            throw;
        }
        catch (Exception e)
        {
            workspan.Log.Fatal(e, $"Exception: AdminResendInvitation {user.Email}");

            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_ResendInvitation_Error,
                data: e,
                update => update
                    .TenantId(tenantId)
                    .Meta(meta => meta
                        .SetValue("UserName", response.Email)
                    ));

            throw;
        }
    }

    public async Task<JsonWebToken> SignInAsync(string username, string password)
    {
        using var workspan = Workspan.Start<CognitoIdentityService>()
            .Baggage("Username", username)
            .LogEnterAndExit();

        var response = new JsonWebToken();

        try
        {
            var userFromDb =
                await _postgreSqlDbContext.Users.FirstOrDefaultAsync(x => x.Email.ToLower() == username.ToLower());
            if (userFromDb?.IsUserBlocked != null) // user is blocked
            {
                response.AddError(Consts.TooManyLoginAttempts);
                return response;
            }

            using var _client =
                new AmazonCognitoIdentityProviderClient(
                    new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                        Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                    RegionEndpoint.USEast1);

            var authRequest = new InitiateAuthRequest()
            {
                AuthFlow = AuthFlowType.USER_PASSWORD_AUTH,
                ClientId = Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_CLIENT_ID"),
            };

            authRequest.AuthParameters.Add("USERNAME", username);
            authRequest.AuthParameters.Add("PASSWORD", password);

            var authResponse = await _client.InitiateAuthAsync(authRequest);

            if (authResponse.ChallengeName == ChallengeNameType.SOFTWARE_TOKEN_MFA)
            {
                response.Session = authResponse.Session;

                return response;
            }

            if (userFromDb != null)
            {
                if (!userFromDb.IsActive && authResponse.AuthenticationResult.AccessToken != null)
                {
                    await _usersService.UpdateLocalUserAsync(new UpdateLocalUserDTO()
                    {
                        Email = username,
                        IsActive = true,
                    });
                }

                var isMFAEnabled = userFromDb.TwoFactorEnabled != null;
                var isVerificationCodeExists = userFromDb.VerificationCode != null;
                var isDeviceMatch = _httpContext.HttpContext != null && userFromDb.LastDeviceMatch ==
                    _httpContext.HttpContext?.Request?.Headers["User-Agent"].ToString();
                var isTimeoutPassed = userFromDb.LastMFADate == null ||
                                      userFromDb.LastMFADate.GetValueOrDefault().AddDays(MFA_INTERVAL) <
                                      DateTime.UtcNow;

                var MFAEnforceDays = 7;
                var MFAEnforceGraceDate = 3;

                if (userFromDb.IsEnforceMFAEnabled && !isMFAEnabled && userFromDb.MFAEnforceDate == null)
                {
                    await SetMFAEnforceDate(userFromDb.MerchantId);

                    response.DaysToEnforceMFA = MFAEnforceDays + MFAEnforceGraceDate;
                    response.SkipAvailable = true;
                }
                else if (userFromDb.IsEnforceMFAEnabled &&
                         !isMFAEnabled && userFromDb.MFAEnforceDate != null &&
                         userFromDb.MFAEnforceDate.GetValueOrDefault().AddDays(MFAEnforceDays + MFAEnforceGraceDate) <
                         DateTime.UtcNow)
                {
                    response.AddError(Consts.MFANotEnabled);
                }
                else if (userFromDb.IsEnforceMFAEnabled && !isMFAEnabled)
                {
                    response.DaysToEnforceMFA = (MFAEnforceDays + MFAEnforceGraceDate) -
                                                (DateTime.UtcNow - userFromDb.MFAEnforceDate.GetValueOrDefault()).Days;
                    response.SkipAvailable = response.DaysToEnforceMFA > MFAEnforceGraceDate;
                }

                if (userFromDb.RememberMFA && isMFAEnabled && isVerificationCodeExists && !isDeviceMatch ||
                    userFromDb.RememberMFA && isMFAEnabled && isVerificationCodeExists && isTimeoutPassed)
                {
                    await SetUserMFAPreference(authResponse.AuthenticationResult.AccessToken, username, false, true);

                    var newAuthRequest = new InitiateAuthRequest()
                    {
                        AuthFlow = AuthFlowType.USER_PASSWORD_AUTH,
                        ClientId = Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_CLIENT_ID"),
                    };
                    newAuthRequest.AuthParameters.Add("USERNAME", username);
                    newAuthRequest.AuthParameters.Add("PASSWORD", password);

                    var newAuthResponse = await _client.InitiateAuthAsync(newAuthRequest);

                    if (newAuthResponse.ChallengeName == ChallengeNameType.SOFTWARE_TOKEN_MFA)
                    {
                        response.Session = newAuthResponse.Session;

                        return response;
                    }
                    else
                    {
                        authResponse =
                            newAuthResponse; // if no MFA challenge, then proceed with the new auth response, something wrong with mfa settings
                    }
                }
            }

            var deviceId = "";
            if (_httpContext.HttpContext != null)
                deviceId = _httpContext.HttpContext.Request.Headers["User-Agent"].ToString();

            if (authResponse.AuthenticationResult == null &&
                authResponse.ChallengeName.Equals(UserStatusNewPasswordRequired))
            {
                await _activityService.CreateActivityAsync(
                    IdentityActivities.Identity_SignIn_NewPasswordRequired,
                    data: username,
                    update => update
                        .Meta(meta => meta
                            .SetValue("UserName", username)
                        ));

                response.AddError(Consts.PasswordChangeRequired_code, Consts.PasswordChangeRequired);
                return response;
            }

            if (authResponse.AuthenticationResult == null &&
                authResponse.ChallengeName.Equals(UserStatusResetRequired))
            {
                await _activityService.CreateActivityAsync(
                    IdentityActivities.Identity_SignIn_PasswordChangeRequired,
                    data: username,
                    update => update
                        .Meta(meta => meta
                            .SetValue("UserName", username)
                        ));

                response.AddError(Consts.PasswordChangeRequired, Consts.PasswordChangeRequired_code);
                return response;
            }

            if (authResponse.AuthenticationResult == null &&
                authResponse.ChallengeName.Equals(UserStatusResetRequired))
            {
                response.AddError(Consts.PasswordResetRequired, Consts.PasswordResetRequired_code);

                #region Observability

                workspan.Log.Information("SignInAsync: {Username} {Response}",
                    username, "PasswordResetRequired");

                await _activityService.CreateActivityAsync(
                    IdentityActivities.Identity_SignIn_PasswordResetRequired,
                    data: username,
                    update => update
                        //.TenantId(request.Mid)
                        //.CorrelationId(username)
                        .Meta(meta => meta
                            .SetValue("UserName", username)
                            .SetValue("DeviceId", deviceId)
                        ));

                #endregion

                return response;
            }


            response.AccessToken = authResponse.AuthenticationResult.AccessToken;
            response.Id = authResponse.AuthenticationResult.IdToken;
            response.Expires = authResponse.AuthenticationResult.ExpiresIn;
            response.RefreshToken = authResponse.AuthenticationResult.RefreshToken;

            await _activityService.CreateActivityAsync(
                IdentityActivities.Identity_SignIn_Ended,
                data: username,
                update => update
                    //.TenantId(request.Mid)
                    //.CorrelationId(username)
                    .Meta(meta => meta
                        .SetValue("UserName", username)
                        .SetValue("DeviceId", deviceId)
                    ));

            try
            {
                if (userFromDb == null)
                {
                    await _usersService.CreateLocalUserFromCognitoAsync(username,
                        GetMIDFromJwt(authResponse.AuthenticationResult.IdToken),
                        GetPIDFromJwt(authResponse.AuthenticationResult.IdToken),
                        true);
                }
                else
                {
                    var userToUpdate = new UpdateLocalUserDTO();

                    userToUpdate.LastLogin = DateTime.UtcNow;
                    userToUpdate.Email = userFromDb.Email;
                    userToUpdate.FailedLoginAttempts = 0;
                    userToUpdate.IsUserBlocked = null;

                    if (_httpContext.HttpContext != null)
                    {
                        userToUpdate.LastDeviceMatch =
                            _httpContext.HttpContext.Request.Headers["User-Agent"].ToString();
                    }

                    if (userFromDb.MerchantId == null || userFromDb.MerchantId == Guid.Empty)
                    {
                        userToUpdate.MerchantId = GetMIDFromJwt(authResponse.AuthenticationResult.IdToken);
                    }

                    await _usersService.UpdateLocalUserAsync(userToUpdate);
                }
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
            }

            return response;
        }
        catch (UserNotFoundException e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_SignIn_UserNotFound,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", username)
                    ));
            response.AddError(e.Message);
            return response;
        }
        catch (UserNotConfirmedException e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_SignIn_UserNotConfirmed,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", username)
                    ));

            response.AddError(e.Message);
            return response;
        }
        catch (NotAuthorizedException e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_SignIn_NotAuthorized,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", username)
                    ));

            var userFromDb =
                await _postgreSqlDbContext.Users.FirstOrDefaultAsync(x => x.Email.ToLower() == username.ToLower());
            if (userFromDb != null)
            {
                userFromDb.FailedLoginAttempts += 1;

                if (userFromDb.FailedLoginAttempts >= 5)
                {
                    userFromDb.IsUserBlocked = DateTime.UtcNow;
                }

                _postgreSqlDbContext.Users.Update(userFromDb);
                await _postgreSqlDbContext.SaveChangesAsync();
            }

            response.AddError(e.Message);
            return response;
        }
        catch (PasswordResetRequiredException e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_SignIn_PasswordResetRequired,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", username)
                    ));

            response.AddError(Consts.PasswordResetRequired, Consts.PasswordResetRequired_code);
            return response;
        }
        catch (Exception e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_SignIn_Error,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", username)
                    ));

            workspan.RecordException(e);
            throw;
        }
    }

    public async Task<JsonWebToken> ChangePasswordAsync(string username, string currentPassword, string newPassword)
    {
        using var workspan = Workspan.Start<CognitoIdentityService>();
        JsonWebToken response = new JsonWebToken();

        using var _client =
            new AmazonCognitoIdentityProviderClient(
                new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                    Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                RegionEndpoint.USEast1);

        var authRequest = new InitiateAuthRequest()
        {
            AuthFlow = AuthFlowType.USER_PASSWORD_AUTH,
            ClientId = Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_CLIENT_ID"),
        };

        authRequest.AuthParameters.Add("USERNAME", username);
        authRequest.AuthParameters.Add("PASSWORD", currentPassword);

        var authResponse = await _client.InitiateAuthAsync(authRequest);

        if (authResponse.ChallengeName == ChallengeNameType.NEW_PASSWORD_REQUIRED)
        {
            var authChallengeResponse = await _client.RespondToAuthChallengeAsync(new RespondToAuthChallengeRequest
            {
                ChallengeName = ChallengeNameType.NEW_PASSWORD_REQUIRED,
                ChallengeResponses = new Dictionary<string, string>()
                {
                    {"USERNAME", username},
                    {"NEW_PASSWORD", newPassword}
                },
                ClientId = Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_CLIENT_ID"),
                Session = authResponse.Session,
            });

            response.AccessToken = authChallengeResponse.AuthenticationResult.AccessToken;
            response.Id = authChallengeResponse.AuthenticationResult.IdToken;
            response.Expires = authChallengeResponse.AuthenticationResult.ExpiresIn;
            response.RefreshToken = authChallengeResponse.AuthenticationResult.RefreshToken;

            try
            {
                var userFromDb = await _usersService.GetUserByEmail(username);

                if (userFromDb != null)
                {
                    var userToUpdate = new UpdateLocalUserDTO();

                    userToUpdate.Email = username;
                    userToUpdate.Activated = DateTime.UtcNow;
                    userToUpdate.EmailConfirmed = DateTime.UtcNow;
                    userToUpdate.Status = UserStatusConfirmed;
                    userToUpdate.MerchantId = GetMIDFromJwt(authChallengeResponse.AuthenticationResult.IdToken);
                    if (_httpContext.HttpContext != null)
                    {
                        userToUpdate.LastDeviceMatch =
                            _httpContext.HttpContext.Request.Headers["User-Agent"].ToString();
                    }

                    await _usersService.UpdateLocalUserAsync(userToUpdate);
                }
                else
                {
                    await _usersService.CreateLocalUserFromCognitoAsync(username,
                        GetMIDFromJwt(authResponse.AuthenticationResult.IdToken),
                        GetPIDFromJwt(authResponse.AuthenticationResult.IdToken),
                        false);
                }
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
            }

            await _publisher.Publish(new UserTemporaryPasswordChangedEvent
            {
                Username = username
            });

            await _activityService.CreateActivityAsync(
                IdentityActivities.Identity_ChangePassword_Ended,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", username)
                    ));

            return response;
        }

        if (authResponse.AuthenticationResult != null)
        {
            var changePasswordResponse = await _client.ChangePasswordAsync(new ChangePasswordRequest
            {
                AccessToken = authResponse.AuthenticationResult.AccessToken,
                PreviousPassword = currentPassword,
                ProposedPassword = newPassword
            });

            response.AccessToken = authResponse.AuthenticationResult.AccessToken;
            response.Id = authResponse.AuthenticationResult.IdToken;
            response.Expires = authResponse.AuthenticationResult.ExpiresIn;
            response.RefreshToken = authResponse.AuthenticationResult.RefreshToken;

            await _publisher.Publish<UserPasswordChangedEvent>(new
            {
                Username = username
            });

            return response;
        }

        response.AddError(Consts.PasswordMismatch, nameof(Consts.GeneralError));
        return response;
    }

    public async Task ForgotPasswordAsync(ForgotPasswordDTO payload)
    {
        var workspan = Workspan.Start<CognitoIdentityService>();

        JsonWebToken response = new JsonWebToken();

        using var _client =
            new AmazonCognitoIdentityProviderClient(
                new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                    Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                RegionEndpoint.USEast1);

        try
        {
            var resetResponse = await _client.ForgotPasswordAsync(new ForgotPasswordRequest()
            {
                ClientId = Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_CLIENT_ID"),
                Username = payload.Username
            });

            workspan.Log.Information("Reset Password Response: {Response}", JsonConvert.SerializeObject(resetResponse));

            await _activityService.CreateActivityAsync(
                IdentityActivities.Identity_ForgotPassword_Ended,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", payload.Username)
                    ));

            // if (resetResponse.HttpStatusCode == HttpStatusCode.OK)
            // {
            //     workspan.Log.Information("Response: {Response}", JsonConvert.SerializeObject(resetResponse));
            // }
        }
        catch (CodeMismatchException e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_ForgotPassword_CodeMismatch,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", payload.Username)
                    ));

            response.AddError(Consts.PasswordMismatch, Consts.PasswordMismatch_code);
            //return response;
        }
        catch (ExpiredCodeException e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_ForgotPassword_ExpiredCode,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", payload.Username)
                    ));

            response.AddError(Consts.PasswordResetExpired, Consts.PasswordResetExpired_code);
            //return response;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }


        //return null;
    }

    public async Task<JsonWebToken> ConfirmForgotPasswordAsync(ConfirmForgotPasswordDTO payload)
    {
        using var workspan = Workspan.Start<CognitoIdentityService>();
        JsonWebToken response = new JsonWebToken();

        using var _client =
            new AmazonCognitoIdentityProviderClient(
                new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                    Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                RegionEndpoint.USEast1);

        try
        {
            var resetResponse = await _client.ConfirmForgotPasswordAsync(new ConfirmForgotPasswordRequest
            {
                ClientId = Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_CLIENT_ID"),
                ConfirmationCode = payload.ResetCode,
                Password = payload.NewPassword,
                Username = payload.Username
            });

            if (resetResponse.HttpStatusCode == HttpStatusCode.OK)
            {
                await _activityService.CreateActivityAsync(
                    IdentityActivities.Identity_ConfirmForgotPassword_Ended,
                    update => update
                        .Meta(meta => meta
                            .SetValue("UserName", payload.Username)
                        ));

                return response;
            }
        }
        catch (CodeMismatchException e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_ConfirmForgotPassword_CodeMismatch,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", payload.Username)
                    ));

            response.AddError(Consts.PasswordMismatch, Consts.PasswordMismatch_code, "General");
            return response;
        }
        catch (ExpiredCodeException e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_ConfirmForgotPassword_ExpiredCode,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", payload.Username)
                    ));

            response.AddError(Consts.PasswordResetExpired, Consts.PasswordResetExpired_code, "General");
            return response;
        }
        catch (InvalidPasswordException e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_ConfirmForgotPassword_InvalidPassword,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", payload.Username)
                    ));

            response.AddError(Consts.PasswordNotValid, e.Message);
            return response;
        }
        catch (Exception e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_ConfirmForgotPassword_Error,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", payload.Username)
                    ));

            workspan.RecordException(e);
            throw;
        }


        return null;
    }

    public async Task AdminChangePasswordAsync(string username, string email, string password)
    {
        using var workspan = Workspan.Start<CognitoIdentityService>();

        try
        {
            using var _client =
                new AmazonCognitoIdentityProviderClient(
                    new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                        Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                    RegionEndpoint.USEast1);

            await _client.AdminSetUserPasswordAsync(new AdminSetUserPasswordRequest
            {
                Username = username,
                Password = password,
                Permanent = true,
                UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID")
            });
            
            var user = await _postgreSqlDbContext.Users.FirstOrDefaultAsync(x => x.Email == email);
            
            if (user != null)
            {
                // We want to reset the user's failed login attempts and blocked status
                // as well as disable 2FA and Authenticator App
                user.FailedLoginAttempts = 0;
                user.IsUserBlocked = null;
                
                _postgreSqlDbContext.Users.Update(user);
                await _postgreSqlDbContext.SaveChangesAsync();
            }
            else
            {
                workspan.Log.Fatal($"AdminChangePasswordAsync > User not found: {email}");
            }

            await _activityService.CreateActivityAsync(
                IdentityActivities.Identity_AdminChangePasswordAsync_Ended,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", username)
                    ));
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_AdminChangePasswordAsync_Error,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", username)
                    ));
            throw;
        }
    }

    public async Task AdminSetPasswordAsync(string email, string password)
    {
        using var workspan = Workspan.Start<CognitoIdentityService>()
            .Baggage("Email", email)
            .LogEnterAndExit();

        try
        {
            using var _client =
                new AmazonCognitoIdentityProviderClient(
                    new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                        Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                    RegionEndpoint.USEast1);
            
            var user = await _client.AdminGetUserAsync(new AdminGetUserRequest
            {
                Username = email,
                UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID")
            });
            
            await AdminChangePasswordAsync(user.Username, email, password);
        }
        catch (Exception e)
        {
            workspan.Log.Fatal(e, $"AdminSetPasswordAsync > Error setting password for user: {email}");
            throw;
        }
    }

    public async Task SignOutAsync(Guid userId, string token)
    {
        using var workspan = Workspan.Start<CognitoIdentityService>();
        try
        {
            using var _client =
                new AmazonCognitoIdentityProviderClient(
                    new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                        Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                    RegionEndpoint.USEast1);

            await _activityService.CreateActivityAsync(
                IdentityActivities.Identity_SignOut_Ended,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserId", userId)
                    ));

            await _client.GlobalSignOutAsync(new GlobalSignOutRequest
            {
                AccessToken = token
            });
        }
        catch (Exception e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_SignOut_Error,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserId", userId)
                    ));

            workspan.RecordException(e);
            throw;
        }
    }

    public async Task DisableUser(string username)
    {
        using var workspan = Workspan.Start<CognitoIdentityService>()
            .Baggage("Email", username);

        try
        {
            workspan.Log.Information($"ENTERED: DisableUser username: {username}");

            using var _client =
                new AmazonCognitoIdentityProviderClient(
                    new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                        Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                    RegionEndpoint.USEast1);

            var response = await _client.AdminDisableUserAsync(new AdminDisableUserRequest
            {
                Username = username,
                UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID")
            });

            if (response.HttpStatusCode != HttpStatusCode.OK)
            {
                workspan.Log.Error($"ERROR: Disable username: {username}, response: {response}",
                    JsonConvert.SerializeObject(response));

                await _activityService.CreateActivityAsync(
                    IdentityActivities.Identity_DisableUser_Ended,
                    update => update
                        .Meta(meta => meta
                            .SetValue("UserName", username)
                        ));
            }
        }
        catch (Exception e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_DisableUser_Error,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", username)
                    ));

            workspan.RecordException(e);
            throw;
        }
    }

    public async Task DeleteUser(string username, Guid? merchantId, Guid? partnerId)
    {
        using var workspan = Workspan.Start<CognitoIdentityService>()
            .Baggage("Email", username)
            .Baggage("MerchantId", merchantId);

        try
        {
            workspan.Log.Information($"ENTERED: DeleteUser for username: {username}");

            using var _client =
                new AmazonCognitoIdentityProviderClient(
                    new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                        Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                    RegionEndpoint.USEast1);

            var user = await _client.AdminGetUserAsync(new AdminGetUserRequest
            {
                Username = username,
                UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID")
            });

            var email = user.UserAttributes.Find(x => x.Name == CognitoAttribute.Email.AttributeName)?.Value;

            var userFromDb = await _usersService.GetUserByEmail(email);

            if (userFromDb == null)
            {
                throw new Exception("User not found");
            }

            if (merchantId != null && merchantId != Guid.Empty && userFromDb.MerchantId != merchantId)
            {
                workspan.Log.Error($"DeleteUser username: {username} MerchantId: {merchantId}");
                return;
            }

            if (partnerId != null && partnerId != Guid.Empty && userFromDb.PartnerId != partnerId)
            {
                workspan.Log.Error($"DeleteUser username: {username} PartnerId: {partnerId}");
                return;
            }

            var response = await _client.AdminDeleteUserAsync(new AdminDeleteUserRequest
            {
                Username = username,
                UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID")
            });

            await _usersService.DeleteLocalUserAsync(email);

            if (response.HttpStatusCode != HttpStatusCode.OK)
            {
                workspan.Log.Error($"ERROR: DeleteUser username: {username}, response: {response}",
                    JsonConvert.SerializeObject(response));

                await _activityService.CreateActivityAsync(
                    IdentityActivities.Identity_DeleteUser_Ended,
                    update => update
                        .Meta(meta => meta
                            .SetValue("UserName", username)
                        ));
            }
        }
        catch (Exception e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_DeleteUser_Error,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", username)
                    ));

            workspan.RecordException(e);
            throw;
        }
    }

    public async Task AdminDeleteUser(string username)
    {
        using var workspan = Workspan.Start<CognitoIdentityService>()
            .Baggage("Email", username);

        try
        {
            using var _client =
                new AmazonCognitoIdentityProviderClient(
                    new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                        Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                    RegionEndpoint.USEast1);

            var user = await _client.AdminGetUserAsync(new AdminGetUserRequest
            {
                Username = username,
                UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID")
            });

            var response = await _client.AdminDeleteUserAsync(new AdminDeleteUserRequest
            {
                Username = username,
                UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID")
            });

            await _usersService.DeleteLocalUserAsync(user.UserAttributes
                .Find(x => x.Name == CognitoAttribute.Email.AttributeName)?.Value);

            if (response.HttpStatusCode != HttpStatusCode.OK)
            {
                workspan.Log.Error($"ERROR: AdminDeleteUser username: {username}, response: {response}",
                    JsonConvert.SerializeObject(response));

                await _activityService.CreateActivityAsync(
                    IdentityActivities.Identity_DeleteUser_Ended,
                    update => update
                        .Meta(meta => meta
                            .SetValue("UserName", username)
                        ));
            }
        }
        catch (Exception e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_DeleteUser_Error,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", username)
                    ));

            workspan.RecordException(e);
            throw;
        }
    }

    public async Task UpdateUserAsync(UpdateUserRequestDTO request, Guid? merchantId, Guid? partnerId)
    {
        using var workspan = Workspan.Start<CognitoIdentityService>();

        try
        {
            AmazonCognitoIdentityProviderClient _client =
                new AmazonCognitoIdentityProviderClient(
                    new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                        Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                    RegionEndpoint.USEast1);

            var user = await _client.AdminGetUserAsync(new AdminGetUserRequest
            {
                Username = request.Email,
                UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID")
            });

            var userFromDb =
                await _postgreSqlDbContext.Users.FirstOrDefaultAsync(x =>
                    x.Email.ToLower() == request.Email.ToLower());

            if (userFromDb == null ||
                merchantId != null && (userFromDb.MerchantId == Guid.Empty || merchantId != userFromDb.MerchantId) ||
                partnerId != null && (userFromDb.PartnerId == Guid.Empty || partnerId != userFromDb.PartnerId))
            {
                workspan.Log.Error(
                    $"UpdateUser username: {request.Email} MerchantId: {merchantId} UserMerchantId: {merchantId}");
                return;
            }

            var userAttributes = new List<AttributeType>();

            if (!string.IsNullOrEmpty(request.FirstName))
            {
                userAttributes.Add(new AttributeType()
                {
                    Name = "given_name",
                    Value = request.FirstName
                });
            }

            if (!string.IsNullOrEmpty(request.LastName))
            {
                userAttributes.Add(new AttributeType()
                {
                    Name = "family_name",
                    Value = request.LastName
                });
            }

            if (!string.IsNullOrEmpty(request.Phone))
            {
                userAttributes.Add(new AttributeType()
                {
                    Name = "phone_number",
                    Value = request.Phone
                });
            }

            if (request.IsPiiMasked.HasValue)
            {
                userAttributes.Add(new AttributeType()
                {
                    Name = MyClaimTypes.IS_PII_ENABLED,
                    Value = request.IsPiiMasked.Value.ToString()
                });
            }

            // if (!string.IsNullOrEmpty(request.Email))
            // {
            //     userAttributes.Add(new AttributeType()
            //     {
            //         Name = "email",
            //         Value = request.Email
            //     });
            // }

            if (userAttributes.Count > 0)
            {
                await _client.AdminUpdateUserAttributesAsync(new AdminUpdateUserAttributesRequest
                {
                    UserAttributes = userAttributes,
                    Username = request.Email,
                    UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID"),
                });
            }

            if (!string.IsNullOrEmpty(request.Group))
            {
                var groups = await _client.AdminListGroupsForUserAsync(new AdminListGroupsForUserRequest
                {
                    Username = request.Email,
                    UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID")
                });

                foreach (var group in groups.Groups)
                {
                    await _client.AdminRemoveUserFromGroupAsync(new AdminRemoveUserFromGroupRequest
                    {
                        GroupName = group.GroupName,
                        Username = request.Email,
                        UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID")
                    });
                }

                await _client.AdminAddUserToGroupAsync(new AdminAddUserToGroupRequest
                {
                    GroupName = request.Group,
                    Username = request.Email,
                    UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID")
                });
            }

            await _activityService.CreateActivityAsync(
                IdentityActivities.Identity_UpdateUser_Ended,
                update => update
                    .Meta(meta => meta
                        .SetValue("Request", JsonConvert.SerializeObject(request))
                    ));

            try
            {
                if (_httpContext.HttpContext != null)
                {
                    userFromDb.LastDeviceMatch = _httpContext.HttpContext.Request.Headers["User-Agent"].ToString();
                }

                if (request.VerificationCode != null)
                {
                    userFromDb.VerificationCode = request.VerificationCode;
                }

                if (!string.IsNullOrEmpty(request.LastName))
                {
                    userFromDb.LastName = request.LastName;
                }

                if (!string.IsNullOrEmpty(request.FirstName))
                {
                    userFromDb.FullName = $"{request.FirstName} {request.LastName}";
                }

                if (!string.IsNullOrEmpty(request.Phone))
                {
                    userFromDb.PhoneNumber = request.Phone;
                }

                if (request.MerchantId != null && request.MerchantId != Guid.Empty &&
                    userFromDb.MerchantId != request.MerchantId)
                {
                    userFromDb.MerchantId = request.MerchantId;
                }

                if (request.IsPiiMasked.HasValue)
                {
                    userFromDb.IsPiiMasked = request.IsPiiMasked.Value;
                }

                // if (request.SMSAuthentificationEnabled == true || request.AuthenticatorAppEnabled == true || userFromDb.TwoFactorEnabled != null)
                // {
                //     if (userFromDb.LastMFADate == null || userFromDb.LastMFADate.GetValueOrDefault().Date.AddDays(MFA_INTERVAL) < DateTime.UtcNow.Date)
                //     {
                //         await SetUserMFAPreference(
                //             request.AccessToken, 
                //             request.Email, 
                //             request.SMSAuthentificationEnabled.GetValueOrDefault(), 
                //             request.AuthenticatorAppEnabled.GetValueOrDefault());
                //     }
                // }

                _postgreSqlDbContext.Users.Update(userFromDb);
                await _postgreSqlDbContext.SaveChangesAsync();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
            }
        }
        catch (Exception e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_UpdateUser_Error,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", request.Email)
                    ));

            workspan.RecordException(e);
            throw;
        }
    }

    public async Task AdminUpdateUserAsync(UpdateUserRequestDTO request)
    {
        using var workspan = Workspan.Start<CognitoIdentityService>();

        try
        {
            AmazonCognitoIdentityProviderClient _client =
                new AmazonCognitoIdentityProviderClient(
                    new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                        Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                    RegionEndpoint.USEast1);

            var userAttributes = new List<AttributeType>();

            if (!string.IsNullOrEmpty(request.FirstName))
            {
                userAttributes.Add(new AttributeType()
                {
                    Name = "given_name",
                    Value = request.FirstName
                });
            }

            if (!string.IsNullOrEmpty(request.LastName))
            {
                userAttributes.Add(new AttributeType()
                {
                    Name = "family_name",
                    Value = request.LastName
                });
            }

            if (!string.IsNullOrEmpty(request.Phone))
            {
                userAttributes.Add(new AttributeType()
                {
                    Name = "phone_number",
                    Value = request.Phone
                });
            }

            if (request.IsPiiMasked.HasValue)
            {
                userAttributes.Add(new AttributeType()
                {
                    Name = MyClaimTypes.IS_PII_ENABLED,
                    Value = request.IsPiiMasked.Value.ToString()
                });
            }

            // if (!string.IsNullOrEmpty(request.Email))
            // {
            //     userAttributes.Add(new AttributeType()
            //     {
            //         Name = "email",
            //         Value = request.Email
            //     });
            // }

            if (userAttributes.Count > 0)
            {
                await _client.AdminUpdateUserAttributesAsync(new AdminUpdateUserAttributesRequest
                {
                    UserAttributes = userAttributes,
                    Username = request.Email,
                    UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID"),
                });
            }

            if (!string.IsNullOrEmpty(request.Group))
            {
                var groups = await _client.AdminListGroupsForUserAsync(new AdminListGroupsForUserRequest
                {
                    Username = request.Email,
                    UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID")
                });

                foreach (var group in groups.Groups)
                {
                    await _client.AdminRemoveUserFromGroupAsync(new AdminRemoveUserFromGroupRequest
                    {
                        GroupName = group.GroupName,
                        Username = request.Email,
                        UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID")
                    });
                }

                await _client.AdminAddUserToGroupAsync(new AdminAddUserToGroupRequest
                {
                    GroupName = request.Group,
                    Username = request.Email,
                    UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID")
                });
            }

            await _activityService.CreateActivityAsync(
                IdentityActivities.Identity_UpdateUser_Ended,
                update => update
                    .Meta(meta => meta
                        .SetValue("Request", JsonConvert.SerializeObject(request))
                    ));

            try
            {
                var userFromDb =
                    await _postgreSqlDbContext.Users.FirstOrDefaultAsync(x =>
                        x.Email.ToLower() == request.Email.ToLower());
                if (userFromDb == null)
                {
                    await _usersService.CreateLocalUserFromCognitoAsync(request.Email, null, null, false);
                }
                else
                {
                    if (_httpContext.HttpContext != null)
                    {
                        userFromDb.LastDeviceMatch = _httpContext.HttpContext.Request.Headers["User-Agent"].ToString();
                    }

                    if (request.VerificationCode != null)
                    {
                        userFromDb.VerificationCode = request.VerificationCode;
                    }

                    if (!string.IsNullOrEmpty(request.LastName))
                    {
                        userFromDb.LastName = request.LastName;
                    }

                    if (!string.IsNullOrEmpty(request.FirstName))
                    {
                        userFromDb.FullName = $"{request.FirstName} {request.LastName}";
                    }

                    if (!string.IsNullOrEmpty(request.Phone))
                    {
                        userFromDb.PhoneNumber = request.Phone;
                    }

                    if (request.MerchantId != null && request.MerchantId != Guid.Empty &&
                        userFromDb.MerchantId != request.MerchantId)
                    {
                        userFromDb.MerchantId = request.MerchantId;
                    }

                    if (request.IsPiiMasked.HasValue)
                    {
                        userFromDb.IsPiiMasked = request.IsPiiMasked.Value;
                    }

                    // if (request.SMSAuthentificationEnabled == true || request.AuthenticatorAppEnabled == true || userFromDb.TwoFactorEnabled != null)
                    // {
                    //     if (userFromDb.LastMFADate == null || userFromDb.LastMFADate.GetValueOrDefault().Date.AddDays(MFA_INTERVAL) < DateTime.UtcNow.Date)
                    //     {
                    //         await SetUserMFAPreference(
                    //             request.AccessToken, 
                    //             request.Email, 
                    //             request.SMSAuthentificationEnabled.GetValueOrDefault(), 
                    //             request.AuthenticatorAppEnabled.GetValueOrDefault());
                    //     }
                    // }

                    _postgreSqlDbContext.Users.Update(userFromDb);
                    await _postgreSqlDbContext.SaveChangesAsync();
                }
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
            }
        }
        catch (Exception e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_UpdateUser_Error,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", request.Email)
                    ));

            workspan.RecordException(e);
            throw;
        }
    }

    public async Task<string> GetUserStatusAsync(string userName)
    {
        using var workspan = Workspan.Start<UsersService>();

        try
        {
            AmazonCognitoIdentityProviderClient _client =
                new AmazonCognitoIdentityProviderClient(
                    new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                        Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                    RegionEndpoint.USEast1);

            var user = await _client.AdminGetUserAsync(new AdminGetUserRequest
            {
                Username = userName,
                UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID")
            });

            return user?.UserStatus;
        }
        catch (Exception e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_GetUserStatus_Error,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", userName)
                    ));

            workspan.RecordException(e);
            throw;
        }
    }

    public async Task<string> AssociateMFATokenAsync(string accessToken)
    {
        try
        {
            using var _client =
                new AmazonCognitoIdentityProviderClient(
                    new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                        Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                    RegionEndpoint.USEast1);

            var associateSoftwareTokenRequest = new AssociateSoftwareTokenRequest
            {
                AccessToken = accessToken
            };
            var associateSoftwareTokenResponse =
                await _client.AssociateSoftwareTokenAsync(associateSoftwareTokenRequest);
            var qrCodeImageUrl = associateSoftwareTokenResponse.SecretCode;

            await _activityService.CreateActivityAsync(IdentityActivities.Identity_AssociateMFATokenAsync_Ended);

            return associateSoftwareTokenResponse.SecretCode;
        }
        catch (Exception e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_AssociateMFATokenAsync_Error,
                data: e);
            throw;
        }
    }

    public async Task<VerifyMFAResponseDTO> VerifyMFATokenAsync(string accessToken, string userCode)
    {
        using var workspan = Workspan.Start<UsersService>();

        var response = new VerifyMFAResponseDTO();
        try
        {
            using var _client =
                new AmazonCognitoIdentityProviderClient(
                    new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                        Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                    RegionEndpoint.USEast1);

            var verifySoftwareTokenRequest = new VerifySoftwareTokenRequest
            {
                AccessToken = accessToken,
                UserCode = userCode,
            };
            var verifySoftwareTokenResponse = await _client.VerifySoftwareTokenAsync(verifySoftwareTokenRequest);

            await _activityService.CreateActivityAsync(IdentityActivities.Identity_VerifyMFATokenAsync_Ended);

            response.IsValid = verifySoftwareTokenResponse.Status == VerifySoftwareTokenResponseType.SUCCESS;

            return response;
        }
        catch (EnableSoftwareTokenMFAException e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_VerifyMFATokenAsync_CodeMismatch,
                data: e);

            workspan.RecordException(e);

            response.AddError(Consts.VerificationCodeError);
            return response;
        }
        catch (NotAuthorizedException e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_VerifyMFATokenAsync_UserNotAuthorized,
                data: e);

            workspan.RecordException(e);

            response.AddError(e.Message);
            return response;
        }
        catch (Exception e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_VerifyMFATokenAsync_Error,
                data: e);

            workspan.RecordException(e);

            response.AddError(Consts.GeneralError);
            return response;
        }
    }

    public async Task<SignInResponseDTO> PhoneSignInAsync(string phone, string androidAppHash)
    {
        throw new NotImplementedException();
    }

    public async Task<JsonWebToken> VerifyCode(string phone, string code)
    {
        throw new NotImplementedException();
    }

    public async Task<JsonWebToken> AuthClient(string ApiClientId, string AppSecret)
    {
        throw new NotImplementedException();
    }

    public async Task<JsonWebToken> GenerateKey(Guid clientId, string description, int expiration)
    {
        throw new NotImplementedException();
    }

    public async Task SetUserMFAPreference(string accessToken, string userName, bool SMSAuthentificationEnabled,
        bool authenticatorAppEnabled)
    {
        using var workspan = Workspan.Start<UsersService>();

        if (accessToken == null)
        {
            return;
        }

        try
        {
            AmazonCognitoIdentityProviderClient _client =
                new AmazonCognitoIdentityProviderClient(
                    new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                        Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                    RegionEndpoint.USEast1);

            var newMFAPreference = await _client.SetUserMFAPreferenceAsync(new SetUserMFAPreferenceRequest
            {
                AccessToken = accessToken,
                SMSMfaSettings = new SMSMfaSettingsType
                {
                    PreferredMfa = SMSAuthentificationEnabled,
                    Enabled = SMSAuthentificationEnabled
                },
                SoftwareTokenMfaSettings = new SoftwareTokenMfaSettingsType
                {
                    PreferredMfa = !SMSAuthentificationEnabled && authenticatorAppEnabled,
                    Enabled = authenticatorAppEnabled
                },
            });

            await _activityService.CreateActivityAsync(
                IdentityActivities.Identity_SetUserMFAPreference_Ended,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", userName)
                        .SetValue("SMSAuthentificationEnabled", $"{SMSAuthentificationEnabled}")
                        .SetValue("authenticatorAppEnabled", $"{authenticatorAppEnabled}")
                    ));
        }
        catch (Exception e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_SetMFASettings_Error,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", userName)
                    ));

            workspan.RecordEndpointCriticalApiError(e);
            throw;
        }
    }

    public async Task AdminSetUserMFAPreferenceAsync(string userName, bool SMSAuthentificationEnabled,
        bool authenticatorAppEnabled)
    {
        using var workspan = Workspan.Start<UsersService>();

        try
        {
            AmazonCognitoIdentityProviderClient _client =
                new AmazonCognitoIdentityProviderClient(
                    new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                        Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                    RegionEndpoint.USEast1);

            var newMFAPreference = await _client.AdminSetUserMFAPreferenceAsync(new AdminSetUserMFAPreferenceRequest()
            {
                Username = userName,
                UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID"),
                SMSMfaSettings = new SMSMfaSettingsType
                {
                    PreferredMfa = SMSAuthentificationEnabled,
                    Enabled = SMSAuthentificationEnabled
                },
                SoftwareTokenMfaSettings = new SoftwareTokenMfaSettingsType
                {
                    PreferredMfa = !SMSAuthentificationEnabled && authenticatorAppEnabled,
                    Enabled = authenticatorAppEnabled
                },
            });

            await _activityService.CreateActivityAsync(
                IdentityActivities.Identity_AdminSetUserMFAPreference_Ended,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", userName)
                    ));
        }
        catch (Exception e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_AdminSetUserMFAPreference_Error,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", userName)
                    ));

            workspan.RecordEndpointCriticalApiError(e);
            throw;
        }
    }

    public async Task ResetUserPasswordAsync(string userName)
    {
        using var workspan = Workspan.Start<UsersService>();

        try
        {
            AmazonCognitoIdentityProviderClient _client =
                new AmazonCognitoIdentityProviderClient(
                    new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                        Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                    RegionEndpoint.USEast1);

            await _client.AdminResetUserPasswordAsync(new AdminResetUserPasswordRequest()
            {
                Username = userName,
                UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID")
            });
            
            var user = await _postgreSqlDbContext.Users.FirstOrDefaultAsync(x => x.Email.ToLower() == userName.ToLower());
            
            if (user != null)
            {
                user.FailedLoginAttempts = 0;
                user.IsUserBlocked = null;
                
                _postgreSqlDbContext.Users.Update(user);
                await _postgreSqlDbContext.SaveChangesAsync();
            }
            else
            {
                workspan.Log.Error($"ResetUserPasswordAsync username: {userName} not found in loacl DB");
            }

            await _activityService.CreateActivityAsync(
                IdentityActivities.Identity_ResetUserPassword_Ended,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", userName)
                    ));
        }
        catch (Exception e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_ResetUserPassword_Error,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("UserName", userName)
                    ));

            workspan.RecordEndpointCriticalApiError(e);
            throw;
        }
    }

    public async Task<JsonWebToken> SignInChallengeAsync(string email, string session, string code, bool? rememberMFA)
    {
        using var workspan = Workspan.Start<CognitoIdentityService>();

        var response = new JsonWebToken();

        try
        {
            AmazonCognitoIdentityProviderClient _client =
                new AmazonCognitoIdentityProviderClient(
                    new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                        Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                    RegionEndpoint.USEast1);

            var respondToAuthChallengeResponse = await _client.RespondToAuthChallengeAsync(
                new RespondToAuthChallengeRequest
                {
                    ClientId = Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_CLIENT_ID"),
                    ChallengeName = ChallengeNameType.SOFTWARE_TOKEN_MFA,
                    Session = session,
                    ChallengeResponses = new Dictionary<string, string>
                    {
                        {"SOFTWARE_TOKEN_MFA_CODE", code},
                        {"USERNAME", email}
                    }
                });

            response.AccessToken = respondToAuthChallengeResponse.AuthenticationResult.AccessToken;
            response.RefreshToken = respondToAuthChallengeResponse.AuthenticationResult.RefreshToken;
            response.Expires = respondToAuthChallengeResponse.AuthenticationResult.ExpiresIn;
            response.Id = respondToAuthChallengeResponse.AuthenticationResult.IdToken;

            var userToUpdate = new UpdateLocalUserDTO();
            userToUpdate.Email = email;
            userToUpdate.LastMFADate = DateTime.UtcNow;
            userToUpdate.LastLogin = DateTime.UtcNow;
            // userToUpdate.AuthenticatorAppEnabled = false;
            userToUpdate.LastLoginDate = DateTime.UtcNow;
            {
                userToUpdate.LastDeviceMatch = _httpContext.HttpContext.Request.Headers["User-Agent"].ToString();
            }

            var user = await _usersService.GetUserByEmail(email);
            if (user?.RememberMFA == true || rememberMFA == true)
            {
                userToUpdate.RememberMFA = true;
                await SetUserMFAPreference(respondToAuthChallengeResponse.AuthenticationResult.AccessToken, email,
                    false, false);
            }

            await _usersService.UpdateLocalUserAsync(userToUpdate);

            await _activityService.CreateActivityAsync(
                IdentityActivities.Identity_SignInChallengeAsync_Ended,
                update => update
                    .Meta(meta => meta
                        .SetValue("Email", email)
                        .SetValue("Response", JsonConvert.SerializeObject(response))
                    ));

            return response;
        }
        catch (CodeMismatchException e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_SignInChallengeAsync_CodeMismatch,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("Email", email)
                    ));

            response.AddError(Consts.PasswordMismatch, Consts.VerificationCodeError, "General");
            return response;
        }
        catch (NotAuthorizedException e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_SignInChallengeAsync_UserNotAuthorized,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("Email", email)
                    ));

            response.AddError(e.Message);
            return response;
        }
        catch (Exception e)
        {
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_DeleteUser_Error,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("Email", email)
                    ));
            throw;
        }
    }

    public async Task UpdateUserContactInformationAsync(UpdateUserContactsRequestDTO payload)
    {
        using var workspan = Workspan.Start<CognitoIdentityService>();

        try
        {
            AmazonCognitoIdentityProviderClient _client =
                new AmazonCognitoIdentityProviderClient(
                    new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                        Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                    RegionEndpoint.USEast1);

            var user = await _client.AdminGetUserAsync(new AdminGetUserRequest
            {
                Username = payload.Email,
                UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID")
            });

            var userAttributes = new List<AttributeType>();

            if (!string.IsNullOrEmpty(payload.FirstName))
            {
                userAttributes.Add(new AttributeType()
                {
                    Name = "given_name",
                    Value = payload.FirstName
                });
            }

            if (!string.IsNullOrEmpty(payload.LastName))
            {
                userAttributes.Add(new AttributeType()
                {
                    Name = "family_name",
                    Value = payload.LastName
                });
            }

            if (!string.IsNullOrEmpty(payload.Phone))
            {
                userAttributes.Add(new AttributeType()
                {
                    Name = "phone_number",
                    Value = payload.Phone
                });
            }

            if (!string.IsNullOrEmpty(payload.NewEmail))
            {
                userAttributes.Add(new AttributeType()
                {
                    Name = "email",
                    Value = payload.NewEmail
                });
            }

            if (userAttributes.Count > 0)
            {
                await _client.AdminUpdateUserAttributesAsync(new AdminUpdateUserAttributesRequest
                {
                    UserAttributes = userAttributes,
                    Username = payload.Email,
                    UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID"),
                });
            }

            await _activityService.CreateActivityAsync(
                IdentityActivities.Identity_UpdateUserContactInformation_Ended,
                update => update
                    .Meta(meta => meta
                        .SetValue("Email", payload.Email)
                        .SetValue("Payload", JsonConvert.SerializeObject(payload))
                    ));
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            await _activityService.CreateActivityAsync(
                IdentityErrorActivities.Identity_UpdateUserContactInformation_Error,
                data: e,
                update => update
                    .Meta(meta => meta
                        .SetValue("Email", payload.Email)
                        .SetValue("Payload", JsonConvert.SerializeObject(payload))
                    ));
            throw;
        }
    }

    protected async Task SetMFAEnforceDate(Guid? mid)
    {
        try
        {
            if (mid == null) return;

            var users = await _postgreSqlDbContext.Users
                .Where(u => u.MerchantId == mid)
                .ToListAsync();

            if (users.Any())
            {
                foreach (var user in users)
                {
                    user.MFAEnforceDate = DateTime.UtcNow;
                    _postgreSqlDbContext.Users.Update(user);
                    await _postgreSqlDbContext.SaveChangesAsync();
                }
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    protected Guid GetMID()
    {
        var IsValidMid = Guid.TryParse(_httpContext.HttpContext.User.Claims
            .Where(x => x.Type == MyClaimTypes.MERCHANT_ID)
            .Select(x => x.Value).SingleOrDefault(), out Guid mid);

        if (IsValidMid)
            return mid;

        return mid;
    }

    protected Guid GetPID()
    {
        var IsValidPid = Guid.TryParse(_httpContext.HttpContext.User.Claims
            .Where(x => x.Type == MyClaimTypes.PARTNER_ID)
            .Select(x => x.Value).SingleOrDefault(), out Guid pid);

        if (IsValidPid)
            return pid;

        return pid;
    }

    protected Guid GetMIDFromJwt(string jwt)
    {
        var parsedJwt = new JwtSecurityTokenHandler().ReadJwtToken(jwt);
        var IsValidMid = Guid.TryParse(parsedJwt.Claims
            .Where(x => x.Type == MyClaimTypes.MERCHANT_ID)
            .Select(x => x.Value).SingleOrDefault(), out Guid mid);

        if (IsValidMid)
            return mid;

        return mid;
    }

    protected Guid GetPIDFromJwt(string jwt)
    {
        var parsedJwt = new JwtSecurityTokenHandler().ReadJwtToken(jwt);
        var IsValidMid = Guid.TryParse(parsedJwt.Claims
            .Where(x => x.Type == MyClaimTypes.PARTNER_ID)
            .Select(x => x.Value).SingleOrDefault(), out Guid pid);

        if (IsValidMid)
            return pid;

        return pid;
    }
}