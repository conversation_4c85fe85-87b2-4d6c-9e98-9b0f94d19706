using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace FlexCharge.Identity.DTO
{
    public class ContactDTO
    {
        public Guid Id { get; set; }
        [Required]
        public string FirstName { get; set; }
        [Required]
        public string LastName { get; set; }
        [Required]
        public string Email { get; set; }
        [Required]
        public string Phone { get; set; }

        public Guid UserId { get; set; }
    }

    // public class RoomDTOValidator : AbstractValidator<RoomDTO>
    // {
    //     public RoomDTOValidator()
    //     {
    //
    //     }
    // }
}
