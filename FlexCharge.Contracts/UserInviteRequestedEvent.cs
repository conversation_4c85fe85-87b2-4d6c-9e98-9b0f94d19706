using System.Transactions;

namespace FlexCharge.Contracts
{
    public record UserInviteRequestedEvent
    {
        public Guid UserId  { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Group  { get; set; }
        public Guid Mid { get; set; }
        public Guid Pid { get; set; }
    }
}