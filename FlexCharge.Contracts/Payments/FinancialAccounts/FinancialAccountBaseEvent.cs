namespace FlexCharge.Contracts.FinancialAccounts;

public record FinancialAccountBaseEvent
{
    public Guid Id { get; set; }
    public string Name { get; set; }

    public string Description { get; set; }
    public string RelatedEntityType { get; set; }
    public Guid RelatedEntityId { get; set; }
    public string RelatedEntityDba { get; set; }
    public string AccountType { get; set; }
    public int CurrencyCode { get; set; }
    public string? Currency { get; set; }
}