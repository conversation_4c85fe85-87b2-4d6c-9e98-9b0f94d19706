namespace FlexCharge.Contracts;

public record CheckoutCardUpdatedEvent : ICardUpdatedEvent
{
    public Guid PaymentInstrumentId { get; set; }

    //public string ProviderName { get; set; }

    //[SensitiveData(ObfuscationType.CreditCardMaskAllDigits)]
    public string EncryptedCardNumber { get; set; }
    public int Month { get; set; }
    public int Year { get; set; }
    public string EncryptedPrivateKey { get; set; }
    public string KmsKey { get; set; }
}