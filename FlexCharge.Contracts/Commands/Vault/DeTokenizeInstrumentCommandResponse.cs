using FlexCharge.Contracts.CardBrands;
using FlexCharge.Contracts.Common;
using FlexCharge.Payments.BinChecker.CardBrands;

namespace FlexCharge.Contracts.Commands.Vault;

public record DeTokenizeInstrumentCommandResponse
{
    public Guid Id { get; set; }
    public string CardHolderFirstName { get; set; }
    public string CardHolderLastName { get; set; }
    public string Number { get; set; }
    public string VerificationValue { get; set; }
    public int ExpirationMonth { get; set; }
    public int ExpirationYear { get; set; }
    public string CardNumberMasked { get; set; }
    public string Bin { get; set; }
    public string Last4 { get; set; }

    public NetworkTokenInfo NetworkTokenInfo { get; set; }
    public CardBrand CardBrand { get; set; }

    public Address BillingAddress { get; set; }
    public Address ShippingAddress { get; set; }
    public string SenseKey { get; set; }
    public string Email { get; set; }
    public string PhoneNumber { get; set; }
    public bool ValidLuhn { get; set; }
    public string? CvvInputValidationResult { get; set; }
    public DateTime? AccountLastUpdatedAt { get; set; }

    public bool TokenIsDeleted { get; set; }

    public DateTime? ReturnedFromAccountUpdaterOn { get; set; }
    public DateTime? AccountUpdaterResponseReceivedOn { get; set; }
}

public class NetworkTokenInfo
{
    public string Token { get; set; }
    public string? Cryptogram { get; set; }
    public string? Eci { get; set; }
    public int ExpirationMonth { get; set; }
    public int ExpirationYear { get; set; }
    public bool AuthRequired { get; set; }
}