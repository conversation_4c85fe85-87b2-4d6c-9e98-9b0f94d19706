namespace FlexCharge.Contracts.Commands;

public class ThreeDS
{
    public string? ThreeDsVersion { get; set; }
    public string? EcommerceIndicator { get; set; }

    public string? AuthenticationValue { get; set; }

    public string? DirectoryServerTransactionId { get; set; }

    public string? Xid { get; set; }

    public string? AuthenticationValueAlgorithm { get; set; }

    public string? DirectoryResponseStatus { get; set; }

    public string? AuthenticationResponseStatus { get; set; }

    public bool? Enrolled { get; set; }
}