namespace FlexCharge.Contracts.Vault;

public class AccountUpdaterResultsReceivedEvent
{
    public Guid BatchId { get; set; }
    public int TotalItemsCount { get; set; }
    public int UpdatedItemsCount { get; set; }
    public DateTime TransactionDateTime { get; set; } = DateTime.UtcNow;
    public List<string> Stats { get; set; } = new();
    public DateTime SentAt { get; set; } 
    public string Environment { get; set; } 
}