using System;

namespace FlexCharge.Contracts.Activities;

/// <summary>
/// Activities are immutable
/// </summary>
public interface IActivity
{
    public Guid Id { get; set; }

    public DateTime ActionTimestamp { get; set; }

    /// <summary>
    /// True only for first activity
    /// </summary>
    public bool IsRoot { get; set; }

    /// <summary>
    /// Activities are immutable. If activity is an update for another activity - specify activity to update  id here
    /// </summary>
    public Guid PreviousActionId { get; set; }

    /// <summary>
    /// Microservice name
    /// </summary>
    public string Source { get; set; }

    /// <summary>
    /// E.g. MerchantID
    /// </summary>
    public System.Guid TenantId { get; set; }

    /// <summary>
    /// Event name, if activity produced one
    /// </summary>
    public string Event { get; set; }

    /// <summary>
    /// Domain, e.g. Eligibility
    /// </summary>
    public string Domain { get; set; }

    /// <summary>
    /// Correlates related activities
    /// </summary>
    public Guid CorrelationId { get; set; }

    public string Version { get; set; }

    public string Meta { get; set; }

    public string? Data { get; set; }


    public string Category { get; set; }
    public string SubCategory { get; set; }
    public string Name { get; set; }

    public string Value { get; set; }

    public string ActionResult { get; set; }

    /// <summary>
    /// E.g. SUPER_ADMIN
    /// </summary>
    public string AccessLevel { get; set; }

    /// <summary>
    /// Use ActivityInformationLevelFlags
    /// </summary>
    public int InformationLevel { get; set; }
}