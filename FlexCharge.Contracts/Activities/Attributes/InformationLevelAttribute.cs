using System;
using FlexCharge.Contracts.Activities;

namespace FlexCharge.Common.Activities.Attributes;

[AttributeUsage(AttributeTargets.Field, Inherited = false, AllowMultiple = false)]
public class InformationLevelAttribute : Attribute
{
    public ActivityInformationLevelFlags InformationLevelFlags { get; }

    public InformationLevelAttribute(ActivityInformationLevelFlags informationLevelFlags)
    {
        InformationLevelFlags = informationLevelFlags;
    }
}