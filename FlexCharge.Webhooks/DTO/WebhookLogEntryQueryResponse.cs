using System;
using System.ComponentModel.DataAnnotations;
using FlexCharge.Common.Response;

namespace FlexCharge.Webhooks.DTO
{
    public class WebhookLogEntryQueryResponse : BaseResponse
    {
        [Required]
        public Guid SubscriberId { get; set; }
        public Guid EventInvocationId { get; set; }
        [Required]
        public string EventName { get; set; }
        [Required]
        public DateTime TimeStamp { get; set; } //- use createdOn
        [Required]
        public string Payload { get; set; } // Json
        [Required]
        public bool IsSandbox { get; set; }
        [Required]
        public string Environment { get; set; }
        [Required]
        public Guid WebhookId { get; set; }

        [Required]
        public string EventDesciption { get; set; }

        public string Summary
        {
            get
            {
                //Construct from payload
                return EventDesciption;
            }
        }
    }

}
