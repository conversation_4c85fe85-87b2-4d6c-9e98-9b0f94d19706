using System;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Fabio;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.Telemetry.HttpRequests;
using FlexCharge.Contracts.Activities;
using FlexCharge.Utils;
using FlexCharge.Webhooks.Activities;
using FlexCharge.Webhooks.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace FlexCharge.Webhooks.Services;

public class WebhookService : IWebhookService
{
    private PostgreSQLDbContext _dbContext;
    private readonly IFabioHttpClient _httpClient;
    private readonly IActivityService _activityService;

    public WebhookService(PostgreSQLDbContext dbContext, IFabioHttpClient httpClient,
        IActivityService activityService)
    {
        _dbContext = dbContext;
        _httpClient = httpClient;
        _activityService = activityService;
    }

    public async Task<bool> InvokeWebhookAsync(Guid mid, Guid? pid, string eventName, string payload,
        Guid? correlationId)
    {
        using var workspan = Workspan.Start<WebhookService>()
            .Baggage("Mid", mid)
            .Baggage("EventName", eventName)
            .Baggage("CorrelationId", correlationId)
            .Payload(payload);

        workspan.Log.Information("Invoke Webhook Started");

        try
        {
            var webhooks = await _dbContext.Webhooks
                .Include(x => x.Event)
                .Include(x => x.Subscriber)
                .Where(webhook => webhook.Subscriber.Mid == mid && webhook.Event.EventName == eventName)
                .ToListAsync();

            workspan.Log.Information("Webhooks found: {WebhooksCount}", webhooks.Count);

            foreach (var webhook in webhooks)
            {
                Guid subscriberId = Guid.Empty;
                try
                {
                    subscriberId = (await GetSubscriberAsync(mid, pid)).Id; // TODO: add pid from orders consumers

                    string subscriberKey = webhook.Subscriber.Key;


                    // Specify the 'x-ms-date' header as the current UTC timestamp according to the RFC1123 standard
                    var date = DateTimeOffset.UtcNow.ToString("r", CultureInfo.InvariantCulture);
                    // Get the host name corresponding with the 'host' header.
                    var host = webhook.EndpointUrl.Host;
                    var contentHash = HMACSHA512.ComputeContentHash(payload);

                    //create random nonce for each request
                    string nonce = Guid.NewGuid().ToString("N");


                    //var stringToSignWithoutContentHash = $"POST\n{webhook.EndpointUrl.PathAndQuery}\n{nonce};{date};{host};";
                    var stringToSignWithoutContentHash = $"POST\n{nonce};{date};{host};";
                    var stringToSign = stringToSignWithoutContentHash + $"{contentHash}";


                    var signature = HMACSHA512.ComputeSignature(stringToSign, subscriberKey);
                    var authorizationHeader =
                        $"HMAC-SHA512 SignedHeaders=x-fc-nonce;x-fc-date;host;x-fc-content-sha512&Signature={signature}";

                    var requestMessage = new HttpRequestMessage
                    {
                        Content = new StringContent(payload, Encoding.UTF8),
                        Method = HttpMethod.Post,
                        RequestUri = webhook.EndpointUrl,
                    };

                    var headers = requestMessage.Headers;

                    //headers.Add("x-fc-nonce", nonce);

                    // Add a nonce header (see: https://en.wikipedia.org/wiki/Cryptographic_nonce).
                    headers.Add("x-fc-nonce", nonce);

                    // Add a date header.
                    headers.Add("x-fc-date", date);

                    // Add a host header.
                    // In C#, the 'host' header is added automatically by the 'HttpClient'. However, this step may be required on other platforms such as Node.js.

                    // Add a content hash header.
                    headers.Add("x-fc-content-sha512", contentHash);

                    // Add an authorization header.
                    headers.Add("x-fc-authorization", authorizationHeader);


                    // Add simple body signature
                    headers.Add("x-fc-signature",
                        HMACSHA512.ComputeSignature(payload, subscriberKey));

                    workspan.Log.Information("Invoking webhook: Endpoint:{EndpointUrl}. Payload: {Payload}",
                        webhook.EndpointUrl, payload);

                    var url = requestMessage.RequestUri.ToString();

                    await _activityService.CreateActivityAsync(
                        WebhookMerchantNotificationActivities.Webhooks_WebhookInvocation_Started,
                        set => set
                            .TenantId(mid)
                            .CorrelationId(correlationId)
                            .Data(payload)
                            .Meta(meta => meta
                                .SetValue("EventName", eventName)
                                .SetValue("Url", url))
                    );

                    var stopwatch = Stopwatch.StartNew();

                    //use https://fctestwebhook.free.beeceptor.com/ to test endpoints
                    var response = await _httpClient.SendAsync(requestMessage);

                    stopwatch.Stop();
                    var latencyInMS = stopwatch.ElapsedMilliseconds;


#if DEBUG
                    bool webhookSignatureVerified = HMACSHA512.VerifyWebhookSignature(
                        webhook.EndpointUrl,
                        requestMessage.Headers, requestMessage.Content.ReadAsStringAsync().Result,
                        subscriberKey);

                    FlexCharge.Webhooks.SignatureVerificationSample.Program.TestMain(null);
#endif

                    if (response.IsSuccessStatusCode)
                    {
                        #region Observability

                        workspan.Log.Information(
                            "SUCCESS: WebhookService => InvokeWebhookAsync > {StatusCode} mid: {Mid} event: {EventName} url: {Url}",
                            response.StatusCode, mid, eventName, url);


                        await _activityService.CreateActivityAsync(
                            WebhookMerchantNotificationActivities.Webhooks_WebhookInvocation_Succeeded,
                            set => set
                                .TenantId(mid)
                                .CorrelationId(correlationId)
                                .Data(payload)
                                .Meta(meta => meta
                                    .SetValue("EventName", eventName)
                                    .SetValue("Url", url)
                                    .SetValue("StatusCode", response.StatusCode.ToString()))
                        );

                        #endregion
                    }
                    else
                    {
                        #region Observability

                        workspan.Log.Error(
                            "HTTP ERROR: WebhookService => Unable to invoke webhook > {StatusCode} mid: {Mid} event: {EventName} url: {url}",
                            response.StatusCode, mid, eventName, requestMessage.RequestUri.ToString());

                        await _activityService.CreateActivityAsync(
                            WebhookMerchantNotificationActivities.Webhooks_WebhookInvocation_Failed,
                            set => set
                                .TenantId(mid)
                                .CorrelationId(correlationId)
                                .Data(payload)
                                .Meta(meta => meta
                                    .SetValue("EventName", eventName)
                                    .SetValue("Url", url)
                                    .SetValue("StatusCode", response.StatusCode.ToString()))
                        );

                        #endregion
                    }

                    await _dbContext.WebhooksLog.AddAsync(new WebhookLogEntry()
                    {
                        EventName = eventName,
                        Status = (int) response.StatusCode,
                        ResponseMessage = SanitizeWebhookInvocationResponse(await response.Content.ReadAsStringAsync()),
                        TimeStamp = DateTime.UtcNow,
                        SubscriberId = subscriberId,
                        Payload = payload,
                        IsSandbox = !EnvironmentHelper.IsInProduction,
                        Environment = EnvironmentHelper.GetCurrentEnvironment().ToString(),
                        EventInvocationId = Guid.NewGuid(),
                        WebhookId = webhook.Id
                    });

                    await _dbContext.SaveChangesAsync();

                    #region Public API Log Activity

                    string requestContent = "";
                    try
                    {
                        // On redirect POST is always changed to GET by HttpClient
                        if (requestMessage.Method != HttpMethod.Get)
                        {
                            requestContent = await requestMessage.Content.ReadAsStringAsync();
                        }
                        else
                        {
                            workspan.Log.Information("Webhook request was redirected to GET method");
                        }
                    }
                    catch (Exception e)
                    {
                        workspan.Log.Warning(e, "Unable to read request content");
                    }

                    string responseContent = "";
                    try
                    {
                        if (response?.Content != null)
                        {
                            responseContent = await response.Content.ReadAsStringAsync();
                        }
                    }
                    catch (Exception e)
                    {
                        workspan.Log.Warning(e, "Unable to read response content");
                    }


                    var requestAndResponse = RequestTelemetryInformation.Create(
                        requestMessage, requestContent,
                        response, responseContent,
                        latencyInMS);

                    if (response.IsSuccessStatusCode)
                    {
                        workspan.Log.Information("Webhook invocation succeeded. Request: {RequestAndResponse}",
                            JsonConvert.SerializeObject(requestAndResponse));

                        await _activityService.CreateActivityAsync(
                            PublicApiActivities.PublicApi_Webhook_Fired,
                            set => set
                                .TenantId(mid)
                                .CorrelationId(correlationId)
                                .Data(requestAndResponse, storeTypeInformation: false)
                                .Meta(meta =>
                                    meta
                                        .Latency(latencyInMS)
                                        .Endpoint(requestAndResponse.RequestPath)
                                        //.DocumentationLink(attribute.DocumentationLink)
                                        .RequestMethod(requestAndResponse.Method)
                                        .ResponseCode(requestAndResponse.ResponseStatusCode)
                                ));
                    }
                    else
                    {
                        workspan.Log.Information("Webhook invocation error. Request: {RequestAndResponse}",
                            JsonConvert.SerializeObject(requestAndResponse));

                        await _activityService.CreateActivityAsync(
                            PublicApiErrorActivities.PublicApi_Webhook_Error,
                            set => set
                                .TenantId(mid)
                                .CorrelationId(correlationId)
                                .Data(requestAndResponse, storeTypeInformation: false)
                                .Meta(meta =>
                                        meta
                                            .Latency(latencyInMS)
                                            .Endpoint(requestAndResponse.RequestPath)
                                            //.DocumentationLink(attribute.DocumentationLink)
                                            .RequestMethod(requestAndResponse.Method)
                                            .ResponseCode(requestAndResponse.ResponseStatusCode)
                                            .SetValue("EventName", eventName)
                                            .SetValue("RedirectedToGet",
                                                requestAndResponse.Method ==
                                                HttpMethod.Get
                                                    .Method) // On redirect POST is always changed to GET by HttpClient
                                ));
                    }

                    #endregion

                    return response.IsSuccessStatusCode;
                }
                catch (Exception e)
                {
                    workspan.RecordException(e, $"Unable to invoke webhook");

                    await _activityService.CreateActivityAsync(WebhookErrorActivities.Webhooks_WebhookInvocation_Error,
                        set => set
                            .TenantId(mid)
                            .CorrelationId(correlationId)
                            .Data(e)
                            .Meta(meta =>
                                meta
                                    .SetValue("EventName", eventName)
                            ));

                    HttpRequestException httpException = e as HttpRequestException;

                    await _dbContext.WebhooksLog.AddAsync(new WebhookLogEntry()
                    {
                        EventName = eventName,
                        Status = httpException?.StatusCode != null ? (int) httpException.StatusCode : -1,
                        ResponseMessage = e.Message,
                        TimeStamp = DateTime.UtcNow,
                        SubscriberId = subscriberId,
                        Payload = payload,
                        IsSandbox = !EnvironmentHelper.IsInProduction,
                        Environment = EnvironmentHelper.GetCurrentEnvironment().ToString(),
                        EventInvocationId = Guid.NewGuid(),
                        WebhookId = webhook.Id
                    });

                    await _dbContext.SaveChangesAsync();
                }
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "Unable to invoke webhook");

            await _activityService.CreateActivityAsync(WebhookErrorActivities.Webhooks_WebhookInvocation_Error,
                set => set
                    .TenantId(mid)
                    .CorrelationId(correlationId)
                    .Data(e)
                    .Meta(meta =>
                        meta
                            .SetValue("EventName", eventName)
                    ));

            throw;
        }

        return false;
    }


    const int MAX_STORED_WEBHOOK_RESPONSE_LENGTH_IN_BYTES = 4 * 1024;

    private string SanitizeWebhookInvocationResponse(string response)
    {
        // To avoid possible script injection when shown on our Portal
        // Implement - convert possible Html tage in response to encoded html tags

        response = response.TrimStart();
        response = response.Substring(0, Math.Min(response.Length, MAX_STORED_WEBHOOK_RESPONSE_LENGTH_IN_BYTES));
        response = response.TrimEnd();
        response = response.Replace("<", "&lt;");
        response = response.Replace(">", "&gt;");

        return response;
    }

    public async Task<Subscriber> SetupSubscriberAsync(Guid mid, Guid? pid)
    {
        using var workspan = Workspan.Start<WebhookService>()
            .Baggage(nameof(pid), pid)
            .Baggage(nameof(mid), mid);

        try
        {
            // Subscriber subscriber;
            //
            // if (mid.HasValue && mid != Guid.Empty && pid.HasValue && pid != Guid.Empty)
            // {
            //     subscriber = await _dbContext.Subscribers.Where(x => x.Mid == mid && x.Pid == pid).SingleOrDefaultAsync();
            // }
            // else if (mid.HasValue && mid != Guid.Empty)
            // {
            //     subscriber = await _dbContext.Subscribers.Where(x => x.Mid == mid && x.Pid == null).SingleOrDefaultAsync();
            // }
            // else if (pid.HasValue && pid != Guid.Empty)
            // {
            //     subscriber = await _dbContext.Subscribers.Where(x => x.Pid == pid && x.Mid == null).SingleOrDefaultAsync();
            // }
            // else
            // {
            //     subscriber = null;
            // }

            var subscriber = await _dbContext.Subscribers.Where(x => x.Mid == mid && x.Pid == null)
                .SingleOrDefaultAsync();

            if (subscriber == null)
            {
                var key = CryptoSigningHelper.GenerateRandomKey();

                subscriber = new Subscriber()
                {
                    Key = key
                };

                subscriber.Mid = mid;

                if (pid.HasValue && pid != Guid.Empty)
                {
                    subscriber.Pid = pid;
                }

                _dbContext.Subscribers.Add(subscriber);
                await _dbContext.SaveChangesAsync();

                workspan.Log.Information(
                    $"SUCCESS: WebhookService => SetupSubscriber > Subscriber successfully created. Mid: {mid} key: {key}");
                await _activityService.CreateActivityAsync(
                    WebhookMerchantNotificationActivities.Webhooks_WebhookSetup_Succeeded,
                    set => set
                        .TenantId(mid)
                        .Data(key)
                );
            }
            else
            {
                workspan.Log.Information(
                    $"SUCCESS: WebhookService => SetupSubscriber > Subscriber already exists. Mid: {subscriber.Mid} key: {subscriber.Key}");
                await _activityService.CreateActivityAsync(
                    WebhookMerchantNotificationActivities.Webhooks_WebhookSetup_WebhookAlreadyExists,
                    set => set
                        .TenantId(mid)
                );
            }

            return subscriber;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, $"Unable to setup subscriber");

            await _activityService.CreateActivityAsync(WebhookErrorActivities.Webhooks_WebhookSetup_Error,
                set => set
                    .TenantId(mid)
            );

            throw;
        }
    }

    public async Task<Subscriber> GetSubscriberAsync(Guid mid, Guid? pid, CancellationToken token = default)
    {
        using var workspan = Workspan.Start<WebhookService>()
            .Baggage("Pid", pid)
            .Baggage("Mid", mid);

        try
        {
            // Subscriber subscriber;

            // if (mid.HasValue && mid != Guid.Empty && pid.HasValue && pid != Guid.Empty)
            // {
            //     subscriber = await _dbContext.Subscribers.Where(x => x.Mid == mid && x.Pid == pid).SingleOrDefaultAsync(token);
            // }
            // else if (mid.HasValue && mid != Guid.Empty)
            // {
            //     subscriber = await _dbContext.Subscribers.Where(x => x.Mid == mid && x.Pid == null).SingleOrDefaultAsync(token);
            // } 
            // else if (pid.HasValue && pid != Guid.Empty)
            // {
            //     subscriber = await _dbContext.Subscribers.Where(x => x.Pid == pid && x.Mid == null).SingleOrDefaultAsync(token);
            // }
            // else
            // {
            //     throw new Exception("Mid or Pid must be provided");
            // }

            var subscriber = await _dbContext.Subscribers.Where(x => x.Mid == mid).SingleOrDefaultAsync(token);

            if (subscriber is null)
            {
                subscriber = await SetupSubscriberAsync(mid, pid);
            }

            return subscriber;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "Can't find webhook subscriber by mid");
            throw;
        }
    }
}