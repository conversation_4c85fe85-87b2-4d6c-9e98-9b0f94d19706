// using System;
// using FlexCharge.Common.Telemetry;
// using FlexCharge.Contracts;
// using FlexCharge.Webhooks.Services;
// using MassTransit;
// using Microsoft.Extensions.Logging;
// using Newtonsoft.Json;
// using Task = System.Threading.Tasks.Task;
//
// namespace FlexCharge.Webhooks.Consumers;
//
// //[EntityName($"{nameof(TestEvent)}.fifo")]
// public interface TestEvent
// {
//     public string Message { get; set; }
// }
//
// public class TestEventConsumer : IConsumer<TestEvent>
// {
//         ILogger<TestEventConsumer> _logger;
//         private IWebhookService _webhookService;
//
//         public TestEventConsumer(ILogger<TestEventConsumer> logger, IWebhookService webhookService)
//         {
//             _logger = logger;
//             _webhookService = webhookService;
//         }
//
//         public async Task Consume(ConsumeContext<TestEvent> context)
//         {
//             using var workspan = Workspan.Start<TestEventConsumer>(nameof(Consume));
//
//             try
//             {
//             }
//             catch (Exception e)
//             {
//                 workspan.RecordException(e, true, "Error");
//             }
//         }
// }