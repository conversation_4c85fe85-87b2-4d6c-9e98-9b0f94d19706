using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Cache;
using FlexCharge.Common.DataStreaming;
using FlexCharge.Common.DistributedLock.Implementations.RedLock;
using FlexCharge.Common.Emails;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.PostgreSql;
using FlexCharge.Common.Swagger;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.Telemetry.PerformanceCounters;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using System;
using System.Threading;
using Amazon.SecretsManager;
using FlexCharge.Common.Cloud.BI.Amazon;
using FlexCharge.Risk.Services;

namespace FlexCharge.Risk
{
    public class Startup
    {
        public static EventWaitHandle StartupCompleted { get; } = new(false, EventResetMode.ManualReset);

        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddTelemetry();
            services.AddCloudWatchPerformanceCountersTelemetry<Startup>();

            services.AddTransient<IHttpContextAccessor, HttpContextAccessor>();
            services.AddTransient(typeof(IOptions<>), typeof(OptionsManager<>));

            services.AddTransient<IEmailSender, SendGridEmailSender>();
            services.AddTransient<IRiskAlertsService, RiskAlertsService>();

            services.AddSingleton<IAmazonSecretsManager, AmazonSecretsManagerClient>();
            services.AddAmazonSecretsManager();


            services.Configure<AppOptions>(Configuration.GetSection("app"));
            services.AddOptions();

            var connectionString =
                $@"Host={Environment.GetEnvironmentVariable("DB_HOST")};Port={Environment.GetEnvironmentVariable("DB_PORT")};Database={Environment.GetEnvironmentVariable("DB_DATABASE")};Username={Environment.GetEnvironmentVariable("DB_USERNAME")};Password='{Environment.GetEnvironmentVariable("DB_PASSWORD")}';";
#if DEBUG
            connectionString = "Host=localhost;Database=fc.risk;Username=risk-service-staging;Password=*****";
#endif

            services.AddEntityFrameworkNpgsql()
                .AddNpgsqlDbContext<PostgreSQLDbContext>(connectionString);


            #region Authorization Configuration

            services.AddJwt();

            services.AddAuthorization(options =>
            {
                options.AddPolicy(MyPolicies.SUPER_ADMINS_ONLY,
                    policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.SUPER_ADMIN));
                options.AddPolicy(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS,
                    policy =>
                    {
                        policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                            SuperAdminGroups.SUPER_ADMIN,
                            SuperAdminGroups.MERCHANT_ADMIN,
                            SuperAdminGroups.INTEGRATION_PARTNER_ADMIN,
                            SuperAdminGroups.PARTNER_ADMIN);
                    });
                options.AddPolicy(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS,
                    policy =>
                    {
                        policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                            SuperAdminGroups.SUPER_ADMIN,
                            SuperAdminGroups.PARTNER_ADMIN,
                            SuperAdminGroups.INTEGRATION_PARTNER_ADMIN,
                            MerchantGroups.MERCHANT_ADMIN,
                            MerchantGroups.MERCHANT_SUPPORT,
                            MerchantGroups.MERCHANT_SUPPORT_ADMIN,
                            MerchantGroups.MERCHANT_FINANCE,
                            MerchantGroups.MERCHANT_DEVELOPER);
                    });
            });

            #endregion

            services.AddAutoMapper(typeof(Startup));
            services.AddControllers();
            services.AddSwaggerDocs();

            services.AddMassTransit<Startup>();

            services.AddCors(options =>
            {
                options.AddPolicy("CorsPolicy", cors =>
                    cors.AllowAnyMethod()
                        .AllowAnyOrigin()
                        .AllowAnyHeader());
            });

            services.AddBackgroundWorkerService(Configuration);
            services.AddEmailClient();
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IHostApplicationLifetime applicationLifetime,
            IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                // Enable middleware to serve generated Swagger as a JSON endpoint.
            }

            app.UseCors("CorsPolicy");

            //app.UseHttpsRedirection();
            app.UseSwaggerDocs();

            app.UseRouting();
            app.UseAuthorization();
            app.UseAutoMigrations<PostgreSQLDbContext>();

            app.UseEndpoints(endpoints => { endpoints.MapControllers(); });

            app.UseMassTransit();

            StartupCompleted.Set();
        }
    }
}