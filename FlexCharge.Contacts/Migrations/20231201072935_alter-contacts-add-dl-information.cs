using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Contacts.Migrations
{
    /// <inheritdoc />
    public partial class altercontactsadddlinformation : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "DriverLicenseCountry",
                table: "Contacts",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DriverLicenseExpirationDate",
                table: "Contacts",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DriverLicenseNumber",
                table: "Contacts",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DriverLicenseState",
                table: "Contacts",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PassportCountry",
                table: "Contacts",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "PassportExpirationDate",
                table: "Contacts",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PassportNumber",
                table: "Contacts",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PatronDescription",
                table: "Contacts",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PatronId",
                table: "Contacts",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PatronLanguage",
                table: "Contacts",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PatronStatus",
                table: "Contacts",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PatronType",
                table: "Contacts",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DriverLicenseCountry",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "DriverLicenseExpirationDate",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "DriverLicenseNumber",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "DriverLicenseState",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "PassportCountry",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "PassportExpirationDate",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "PassportNumber",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "PatronDescription",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "PatronId",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "PatronLanguage",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "PatronStatus",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "PatronType",
                table: "Contacts");
        }
    }
}
