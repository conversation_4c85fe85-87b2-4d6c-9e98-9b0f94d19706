using AutoMapper;
using FlexCharge.Common;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Contacts.DTO;
using FlexCharge.Contacts.Entities;
using FlexCharge.Contacts.Messages;
using FlexCharge.Contracts;
using FlexCharge.Utils;

namespace FlexCharge.Contacts
{
    public class AutoMappings : Profile
    {
        public AutoMappings()
        {
            CreateMap<Contact, ContactDTO>();
            CreateMap<Contact, ContactQueryDTO>()
                .ForMember(dest => dest.CurrencySymbol, opt => opt.MapFrom(src => GetCurrencySymbol(src.Currency)));
            CreateMap<ContactDTO, Contact>();
            CreateMap<ContactUpdateDTO, Contact>()
            .ForMember(dest => dest.Id, opt => opt.Ignore());

            CreateMap<ContactUpdateImageDTO, Contact>()
            .ForMember(dest => dest.Id, opt => opt.Ignore());


            CreateMap<ApplicationCreatedEvent, Contact>().ReverseMap();
            CreateMap<ContactCreatedEvent, Contact>()
.ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ContactId))
                .ReverseMap();
            
            CreateMap<IPagedList<Contact>, PagedDTO<ContactQueryDTO>>()
                .ConvertUsing(new PagedListTypeConverter<Contact, ContactQueryDTO>());
        }
        
        private string GetCurrencySymbol(string currency)
        {
            if (string.IsNullOrEmpty(currency))
            {
                return string.Empty;
            }
            l18n.CurrencyTools.TryGetCurrencySymbol(currency, out var currencySymbol);
            return currencySymbol;
        }
    }
}
