// <auto-generated />
using System;
using FlexCharge.Payments.BinChecker;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using NpgsqlTypes;

#nullable disable

namespace FlexCharge.Payments.BinChecker.Migrations
{
    [DbContext(typeof(PostgreSQLDbContext))]
    partial class PostgreSQLDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("FlexCharge.Payments.BinChecker.Services.BinNumberValidationServices.BINDB.Bin", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Active")
                        .HasColumnType("integer");

                    b.Property<int>("BinID")
                        .HasColumnType("integer");

                    b.Property<int>("BinLength")
                        .HasColumnType("integer");

                    b.Property<string>("CardB2BProgram")
                        .HasColumnType("text");

                    b.Property<string>("CardBillingCurrency")
                        .HasColumnType("text");

                    b.Property<string>("CardBrand")
                        .HasColumnType("text");

                    b.Property<string>("CardClass")
                        .HasColumnType("text");

                    b.Property<string>("CardCountryCode")
                        .HasColumnType("text");

                    b.Property<string>("CardDebitNetworkParticipant")
                        .HasColumnType("text");

                    b.Property<string>("CardFastFunds")
                        .HasColumnType("text");

                    b.Property<string>("CardFundSource")
                        .HasColumnType("text");

                    b.Property<string>("CardIssueDetail")
                        .HasColumnType("text");

                    b.Property<string>("CardIssueType")
                        .HasColumnType("text");

                    b.Property<string>("CardIssuer")
                        .HasColumnType("text");

                    b.Property<string>("CardIssuerBin")
                        .HasColumnType("text");

                    b.Property<string>("CardIssuerPhone")
                        .HasColumnType("text");

                    b.Property<string>("CardIssuerRegulated")
                        .HasColumnType("text");

                    b.Property<string>("CardIssuerWebsite")
                        .HasColumnType("text");

                    b.Property<string>("CardIssuingCountry")
                        .HasColumnType("text");

                    b.Property<string>("CardMoneySendIndicator")
                        .HasColumnType("text");

                    b.Property<string>("CardMoneyTransferIndicator")
                        .HasColumnType("text");

                    b.Property<string>("CardOnlineGamblingIndicator")
                        .HasColumnType("text");

                    b.Property<string>("CardOriginalCreditIndicator")
                        .HasColumnType("text");

                    b.Property<string>("CardProcessingIndicator")
                        .HasColumnType("text");

                    b.Property<string>("CardProductSubType")
                        .HasColumnType("text");

                    b.Property<string>("CardReloadable")
                        .HasColumnType("text");

                    b.Property<string>("CardSubType")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Day")
                        .HasColumnType("integer");

                    b.Property<string>("FSAIndicator")
                        .HasColumnType("text");

                    b.Property<string>("HighAccount")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("LargeTicketIndicator")
                        .HasColumnType("text");

                    b.Property<string>("LowAccount")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Month")
                        .HasColumnType("integer");

                    b.Property<int>("PanLength")
                        .HasColumnType("integer");

                    b.Property<int>("PanMaximumLength")
                        .HasColumnType("integer");

                    b.Property<int>("PanMinimumLength")
                        .HasColumnType("integer");

                    b.Property<string>("PrepaidIndicator")
                        .HasColumnType("text");

                    b.Property<string>("TokenBinIndicator")
                        .HasColumnType("text");

                    b.Property<int>("Year")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CardIssuerBin")
                        .IsUnique();

                    b.ToTable("Bins");
                });

            modelBuilder.Entity("FlexCharge.Payments.BinChecker.Services.BinNumberValidationServices.BINDB.BinRange", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Active")
                        .HasColumnType("integer");

                    b.Property<int>("BinID")
                        .HasColumnType("integer");

                    b.Property<int>("BinLength")
                        .HasColumnType("integer");

                    b.Property<string>("CardB2BProgram")
                        .HasColumnType("text");

                    b.Property<string>("CardBillingCurrency")
                        .HasColumnType("text");

                    b.Property<string>("CardBrand")
                        .HasColumnType("text");

                    b.Property<string>("CardClass")
                        .HasColumnType("text");

                    b.Property<string>("CardCountryCode")
                        .HasColumnType("text");

                    b.Property<string>("CardDebitNetworkParticipant")
                        .HasColumnType("text");

                    b.Property<string>("CardFastFunds")
                        .HasColumnType("text");

                    b.Property<string>("CardFundSource")
                        .HasColumnType("text");

                    b.Property<string>("CardIssueDetail")
                        .HasColumnType("text");

                    b.Property<string>("CardIssueType")
                        .HasColumnType("text");

                    b.Property<string>("CardIssuer")
                        .HasColumnType("text");

                    b.Property<string>("CardIssuerPhone")
                        .HasColumnType("text");

                    b.Property<string>("CardIssuerRegulated")
                        .HasColumnType("text");

                    b.Property<string>("CardIssuerWebsite")
                        .HasColumnType("text");

                    b.Property<string>("CardIssuingCountry")
                        .HasColumnType("text");

                    b.Property<string>("CardMoneySendIndicator")
                        .HasColumnType("text");

                    b.Property<string>("CardMoneyTransferIndicator")
                        .HasColumnType("text");

                    b.Property<NpgsqlRange<long>>("CardNumberRange")
                        .HasColumnType("int8range");

                    b.Property<string>("CardOnlineGamblingIndicator")
                        .HasColumnType("text");

                    b.Property<string>("CardOriginalCreditIndicator")
                        .HasColumnType("text");

                    b.Property<string>("CardProcessingIndicator")
                        .HasColumnType("text");

                    b.Property<string>("CardProductSubType")
                        .HasColumnType("text");

                    b.Property<string>("CardReloadable")
                        .HasColumnType("text");

                    b.Property<string>("CardSubType")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DataProvider")
                        .HasColumnType("text");

                    b.Property<int>("Day")
                        .HasColumnType("integer");

                    b.Property<string>("FSAIndicator")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsValid")
                        .HasColumnType("boolean");

                    b.Property<string>("LargeTicketIndicator")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Month")
                        .HasColumnType("integer");

                    b.Property<int>("PanLength")
                        .HasColumnType("integer");

                    b.Property<int>("PanMaximumLength")
                        .HasColumnType("integer");

                    b.Property<int>("PanMinimumLength")
                        .HasColumnType("integer");

                    b.Property<string>("PrepaidIndicator")
                        .HasColumnType("text");

                    b.Property<string>("TokenBinIndicator")
                        .HasColumnType("text");

                    b.Property<int>("Year")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CardNumberRange");

                    b.ToTable("BinRanges");
                });
#pragma warning restore 612, 618
        }
    }
}
