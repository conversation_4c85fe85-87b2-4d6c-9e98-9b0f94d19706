using System;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common.Telemetry;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace FlexCharge.Payments.BinChecker.Services.BinNumberValidationServices;

public class SeonBinNumberValidationService : IBinNumberValidationService
{
    private readonly HttpClient _httpClient;
    private readonly IMapper _mapper;

    public SeonBinNumberValidationService(HttpClient httpClient,
        IMapper mapper)
    {
        _httpClient = httpClient;
        _mapper = mapper;
    }

    public async Task<BinNumberValidationResponse> ValidateAsync(BinNumberValidationRequest payload)
    {
        using var workspan = Workspan.Start<SeonBinNumberValidationService>()
            .Payload(payload);

        var binValidationResponse = new BinNumberValidationResponse();
        try
        {
            var seonBinApiBaseUrl = Environment.GetEnvironmentVariable("BIN_SEON_BASE_URL");


            if (payload.BinNumber?.Length == 8 == false && payload.BinNumber?.Length == 6 == false)
            {
                const string validationError = "To validate Bin number with SEON it must contain 6 or 8 digits";
                workspan.Log.Error(validationError);
                binValidationResponse.AddError(validationError);

                return binValidationResponse;
            }

            var message = new HttpRequestMessage
            {
                //Content = new  StringContent(JsonConvert.SerializeObject(seonRequest),Encoding.UTF8,"application/json"),
                Method = HttpMethod.Get,
                RequestUri = new Uri(seonBinApiBaseUrl + $"/{payload.BinNumber}"),
            };

            message.Headers.Add("X-API-KEY",
                Environment.GetEnvironmentVariable("FRAUD_SEON_API_KEY")
            );

            var response = await _httpClient.SendAsync(message);

            //var a = await response.Content.ReadAsStringAsync();

            var seonBinApiResponse = await response.Content.ReadFromJsonAsync<SeonModels.SeonBinApiResponseModel>();

            workspan.Log.Information($"Seon bin api response: {JsonConvert.SerializeObject(seonBinApiResponse)}");


            if (!response.IsSuccessStatusCode)
            {
                workspan.Log.Error("SeonBinNumberValidationService > Validate > {ReasonPhrase}",
                    response.ReasonPhrase);
                binValidationResponse.AddError(response.ReasonPhrase, response.StatusCode.ToString(),
                    code: response.StatusCode.ToString());
                return binValidationResponse;
            }


            var data = seonBinApiResponse.data;
            binValidationResponse.Country = data.bin_country;
            binValidationResponse.BankName = data.bin_bank;
            binValidationResponse.BankPhone = data.bin_phone;
            binValidationResponse.BankWebsite = data.bin_website;
            binValidationResponse.CardIssuer = data.card_issuer;
            binValidationResponse.CardLevel = data.bin_level;
            binValidationResponse.CardNetwork = data.bin_card;
            binValidationResponse.CardType = data.bin_type;
            binValidationResponse.CountryCode = data.bin_country_code;
            binValidationResponse.IsCardValid = data.bin_valid == true;

            if (!CardBrands.CardBrandDetector.IsBinCheckCorrect(payload.BinNumber, binValidationResponse.CardNetwork,
                    out var brandName))
            {
                if (!string.IsNullOrWhiteSpace(brandName))
                    binValidationResponse.CardNetwork = brandName;

                workspan.Log.Warning("SEON BinCheck returned invalid card brand for {Bin}",
                    payload.BinNumber);
            }

            return binValidationResponse;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }
}