<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
		<UserSecretsId>99bbd081-7f55-4d77-9823-a712a2c8e7e0</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<!--<DockerComposeProjectPath>..\docker-compose.dcproj</DockerComposeProjectPath>-->
		<Configurations>Debug;Release;Staging</Configurations>
		<WarningsAsErrors>CS4014</WarningsAsErrors>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<DocumentationFile>bin\$(Configuration)\$(AssemblyName).xml</DocumentationFile>
		<NoWarn>1701;1702;1591;</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AWSSDK.KeyManagementService" Version="3.7.4.18" />
		<PackageReference Include="CreditCardValidator" Version="3.0.1" />
		<PackageReference Include="DeviceDetector.NET" Version="6.1.4" />
        <PackageReference Include="EntityFrameworkCore.Exceptions.PostgreSQL" Version="8.1.3"/>
		<PackageReference Include="jose-jwt" Version="4.1.0"/>
		<PackageReference Include="MediatR" Version="8.0.1" />
		<PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="8.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.2" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
		<PackageReference Include="Microsoft.Extensions.Http.Polly" Version="8.0.0-rc.2.23480.2" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.10.8" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="System.Net.Http" Version="4.3.4" />
		<PackageReference Include="System.Text.Json" Version="9.0.0"/>
		<PackageReference Include="TimeZoneConverter" Version="6.1.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\FlexCharge.Common\FlexCharge.Common.csproj" />
		<ProjectReference Include="..\FlexCharge.Contracts\FlexCharge.Contracts.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Services\Keys" />
	</ItemGroup>

	<ItemGroup>
	  <None Update="Services\AccountUpdaterServices\keys\tokenex-pgp-sandbox-public-outgoing-encryption-key.asc">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	  <None Update="Services\AccountUpdaterServices\keys\tokenex-pgp-public-key.asc">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	  <None Update="Services\AccountUpdaterServices\keys\tokenex-pgp-production-public-outgoing-encryption-key.asc">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	  <None Update="Services\AccountUpdaterServices\keys\tokenex-pgp-private-key.asc">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>

    <ItemGroup>
        <Compile Remove="Services\AccountUpdaterServices\TokeneExAccountUpdaterService.cs"/>
    </ItemGroup>

    <!--	<Choose>-->
<!--		<When Condition=" '$(Configuration)'=='Staging' ">-->
<!--			<ItemGroup>-->
<!--				<Content Remove="appsettings.Development.json" />-->

<!--				&lt;!&ndash; Other files you want to update in the scope of Debug &ndash;&gt;-->
<!--				<None Update="other_files">-->
<!--					<CopyToOutputDirectory>Never</CopyToOutputDirectory>-->
<!--				</None>-->
<!--			</ItemGroup>-->
<!--		</When>-->
<!--		<When Condition=" '$(Configuration)'=='Development' ">-->
<!--			<ItemGroup>-->
<!--				<Content Remove="appsettings.Staging.json" />-->

<!--				&lt;!&ndash; Other files you want to update in the scope of Debug &ndash;&gt;-->
<!--				<None Update="other_files">-->
<!--					<CopyToOutputDirectory>Never</CopyToOutputDirectory>-->
<!--				</None>-->
<!--			</ItemGroup>-->
<!--		</When>-->
<!--	</Choose>-->

</Project>
