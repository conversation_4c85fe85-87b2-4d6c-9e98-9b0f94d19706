using FlexCharge.Contracts.Vault;

namespace FlexCharge.Vault.Services;

public static class AccountUpdaterResponseMessage
{
    public static string GetMessage(AccountUpdaterResponseCode code)
    {
        switch (code)
        {
            case AccountUpdaterResponseCode.NewCard:
                return "New Card";
            case AccountUpdaterResponseCode.ValidAccountNoUpdate:
                return "Valid account; no update";
            case AccountUpdaterResponseCode.AccountExpirationDateUpdated:
                return "Account Expiration Date Updated";
            case AccountUpdaterResponseCode.AccountNumberUpdated:
                return "Account Number Updated";
            case AccountUpdaterResponseCode.AccountIsClosed:
                return "Account is Closed";
            case AccountUpdaterResponseCode.ContactCardholder:
                return "Contact Cardholder";
            case AccountUpdaterResponseCode.ErrorMerchantNotRegistered:
                return "Error - Merchant Not Registered";
            case AccountUpdaterResponseCode.NoMatch:
                return "No Match";
            case AccountUpdaterResponseCode.BlockedMerchantOrReportedFraud:
                return "Blocked Merchant or Reported Fraud";
            case AccountUpdaterResponseCode.InactiveCard:
                return "Inactive Card";
            case AccountUpdaterResponseCode.InactiveOrCanceledSeller:
                return "Inactive or Canceled Seller";
            case AccountUpdaterResponseCode.InvalidExpirationDate:
                return "Invalid Expiration Date";
            case AccountUpdaterResponseCode.InvalidAccountNumber:
                return "Invalid Account Number";
            case AccountUpdaterResponseCode.InvalidCardType:
                return "Invalid Card Type";
            case AccountUpdaterResponseCode.RemoveSuccessful:
                return "Remove Successful";
            case AccountUpdaterResponseCode.InvalidOrBlankSellerId:
                return "Invalid or Blank Seller ID";
            case AccountUpdaterResponseCode.CardRemovedPreviously:
                return "Card Removed Previously";
            case AccountUpdaterResponseCode.CardRecordNotFound:
                return "Card Record Not Found";
            default:
                return "Unknown";
        }
    }
}