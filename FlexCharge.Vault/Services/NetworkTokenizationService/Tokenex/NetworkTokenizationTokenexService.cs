using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using CreditCardValidator;
using EntityFramework.Exceptions.Common;
using FlexCharge.Common.Cards;
using FlexCharge.Common.Logging.LogSuppression;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.CardBrands;
using FlexCharge.Contracts.Commands.Tracking;
using FlexCharge.Contracts.Commands.Vault;
using FlexCharge.Contracts.Vault;
using FlexCharge.Utils;
using FlexCharge.Vault.DTO;
using FlexCharge.Vault.Entities;
using FlexCharge.Vault.Services.TokenExNetworkTokenization.Models;
using FlexCharge.Vault.Services.TokenExNetworkTokenization.Models.CardMetaDataUpdate;
using FlexCharge.Vault.Services.TokenExNetworkTokenization.Models.StateChange;
using FlexCharge.Vault.Services.TokenExNetworkTokenization.Models.StatusCodes;
using FlexCharge.Vault.Services.TokenExNetworkTokenization.Models.Tokenize.Request;
using FlexCharge.Vault.Services.TokenExNetworkTokenization.Models.Tokenize.Response;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using NetworkTokenRequestResponse = FlexCharge.Vault.Entities.NetworkTokenRequestResponse;
using FlexCharge.Vault.Services.TokenExNetworkTokenization.Models.LifeStyle;

namespace FlexCharge.Vault.Services.NetworkTokenizationervice;

public class NetworkTokenizationTokenexService : INetworkTokenizationService
{
    private readonly TokenExSDK _tokenExSdk;
    private readonly IPublishEndpoint _publishEndpoint;
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IRequestClient<GetBrowserInfoCommand> _getBrowserInformationRequest;
    private readonly NetworkTokenOptions _networkTokenOptions;
    private readonly JsonSerializerOptions _jsonSerializerOptions;

    public NetworkTokenizationTokenexService(TokenExSDK tokenExSdk,
        IPublishEndpoint publishEndpoint,
        PostgreSQLDbContext dbContext,
        IRequestClient<GetBrowserInfoCommand> getBrowserInformationRequest,
        IOptions<NetworkTokenOptions> networkTokenOptions)
    {
        _tokenExSdk = tokenExSdk;
        _publishEndpoint = publishEndpoint;
        _dbContext = dbContext;
        _getBrowserInformationRequest = getBrowserInformationRequest;
        _networkTokenOptions = networkTokenOptions.Value;
        _jsonSerializerOptions = new()
        {
            MaxDepth = 10,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            ReadCommentHandling = JsonCommentHandling.Skip,
            ReferenceHandler = ReferenceHandler.IgnoreCycles
        };
        _jsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
    }

    private async Task<TokenizationRequest> GetTokenRequestAsync(
        Contracts.Common.DeviceInfo? deviceInfo,
        FlexCharge.Contracts.Common.Address? billingAddress,
        PaymentInstrument instrument
    )
    {
        using var workspan = Workspan.Start<NetworkTokenizationTokenexService>();

        var vaultV2 = instrument.VaultV2;

        var cvv = instrument.Cvv is null ? null : await Encryptor.DecryptPciData(instrument.Cvv);
        var pan = await Encryptor.DecryptPciData(vaultV2.Token);


        var brandName = new CreditCardDetector(pan).Brand.ToString();
        if (!brandName.TryGetCardBrand(out var brand))
        {
            workspan.Log.Warning("Invalid card brand {Brand}", brandName);
            return null;
        }

        if (!AllowedCardBrands.Contains(brand.Value)) return null;

        string GetExpirationDate(int month, int year) => $"{year % 100:D2}{month:D2}";


        var result = new TokenizationRequest()
        {
            Cvv = cvv,
            CardholderBillingAddress = new()
            {
                City = billingAddress?.City,
                Country = billingAddress?.CountryCode,
                State = billingAddress?.StateCode,
                Zip = billingAddress?.Zip,
                Line1 = billingAddress?.Address1,
                Line2 = billingAddress?.Address2
            },
            Data = pan,
            ConsumerId = Encryptor.GenerateSlug(),
            AccountType = brand == CardBrand.Amex ? AccountType.credit_card : AccountType.WALLET,
            ExpirationDate = GetExpirationDate(vaultV2.ExpirationMonth, vaultV2.ExpirationYear),
            CardholderName =
                $"{instrument.CardHolderFirstName ?? string.Empty} {instrument.CardHolderLastName ?? string.Empty}",
            PanSource = PanSource
                .ONFILE, // string.IsNullOrEmpty(card.VerificationValue)? PanSource.ONFILE:PanSource.KEYENTERED,
            PresentationMode = new() {PresentationMode.ECOM},
            DeviceData = new()
            {
                //Language = "eng",
                Locale = deviceInfo?.Locale ?? "en-US", //todo
                Language = deviceInfo?.Language,
                Brand = deviceInfo?.Brand,
                DeviceType = deviceInfo?.DeviceType?.ToString(),
                Model = deviceInfo?.Model,
                DeviceName = deviceInfo?.Name,
                OsVersion = deviceInfo?.OsVersion,
                DevicePlatform = deviceInfo?.Platform,
                DeviceTimeZone = deviceInfo?.Timezone,
                DeviceIPv4 = deviceInfo?.IpAddress,
                //DeviceLocation = 
                WalletAccountEmailAddress = string.IsNullOrWhiteSpace(instrument.Email)
                    ? null
                    : instrument.Email
            },
        };

        return result;
    }

    /// <summary>
    /// get  token and set it in DB
    /// </summary>
    /// <param name="tokenRequest"></param>
    /// <param name="tokenId"></param>
    /// <returns></returns>
    async Task<NetworkToken> TokenizeAsync(
        TokenizationRequest tokenRequest,
        NetworkTokenRequestResponse requestResponseEntity,
        VaultV2 vault, CancellationToken ct)
    {
        /// block network tokenization temporarrily// todo- remove
        return null;

        using var workspan = Workspan.Start<NetworkTokenizationTokenexService>().LogEnterAndExit();

        NetworkToken token = null;
        requestResponseEntity.LastRequestAt = DateTime.UtcNow;

        try
        {
            var response = await _tokenExSdk.Tokenize(tokenRequest, ct);


            if (response is null)
            {
                workspan.Log.Error("TokenizeAsync failed for {vault}", vault.Id);
                return null;
            }

            requestResponseEntity.LastResponseAt = DateTime.UtcNow;
            requestResponseEntity.RequestCount++;

            workspan.Log.Information("TokenizeAsync {Response}", JsonSerializer.Serialize(response));

            var network = response.NetworkResponse;
            var expiry = response.NetworkResponse?.EncData?
                .PaymentBundle?.BundleElements?.FirstOrDefault(e => e.Type == BundleElementType.TOKEN_EXP)?.Value;

            requestResponseEntity.TxReferenceNumber = response?.ReferenceNumber;
            requestResponseEntity.Response = JsonSerializer.SerializeToDocument(response,
                _jsonSerializerOptions);
            requestResponseEntity.ResponseStatusCode = network?.StatusCode;
            requestResponseEntity.ResponseStatusMessage = network?.StatusMessage;
            requestResponseEntity.IsSuccess = response.Success
                                              && response.NetworkResponse?.StatusCode == "0000"
                                              && (response.NetworkResponse?.TokenizationDecision ==
                                                  TokenizationDecision.APPROVED
                                                  || response.NetworkResponse?.TokenizationDecision ==
                                                  TokenizationDecision.AUTH_REQ);
            var carddata = network?.EncData?.CardData;
            if (carddata is not null)
            {
                vault.PaymentAccountReference = carddata.PaymentAccountReference;
            }

            await _dbContext.SaveChangesAsync();


            //requestEntity.StatusMessage = network?.StatusMessage;

            if (requestResponseEntity.IsSuccess)
            {
                try
                {
                    using var _ = DatabaseLogSuppressor
                        .SuppressUniqueConstraintError<PostgreSQLDbContext>("NetworkTokens",
                            "IX_NetworkTokens_TokenReferenceId");

                    token = new()
                    {
                        TxToken = response.Token,
                        State = response.NetworkResponse.TokenState,
                        Token = network.EncData
                            .PaymentBundle.BundleElements.FirstOrDefault(e => e.Type == BundleElementType.TOKEN)
                            ?.Value, //todo
                        ExpirationMonth = int.Parse(expiry.Substring(2)),
                        ExpirationYear = int.Parse(expiry.Substring(0, 2)),
                        TokenRefId = network.TokenReferenceId,
                        CardInfo = carddata is null
                            ? null
                            : new()
                            {
                                CardName = carddata.CardName,
                                CardType = carddata.CardType,
                                CardCountryCode = carddata.CardCountryCode,
                                CardSuffix = carddata.CardSuffix,
                                CoBranded = carddata.CoBranded,
                                Cvv2Printed = carddata.Cvv2Printed,
                                CardLongDescription = carddata.CardLongDescription,
                                PaymentAccountReference = carddata.PaymentAccountReference,
                                CardShortDescription = carddata.CardShortDescription,
                                PanReferenceId = carddata.PanReferenceId,
                                CardTypeIndicator = carddata.CardTypeIndicator,
                                PanExpDate = carddata.PanExpDate,
                                CoBrandName = carddata.CoBrandName,
                                ExpDataPrinted = carddata.ExpDataPrinted,
                                CardHolderEmbossedName = carddata.CardHolderEmbossedName,
                                CardMetaInfo = network.CardMetaData is var mdata && mdata is null
                                    ? null
                                    : new()
                                    {
                                        IssuerData = mdata.IssuerData is var idata && idata is null
                                            ? null
                                            : new()
                                            {
                                                IssuerAddress = idata.IssuerAddress,
                                                IssuerEmail = idata.IssuerEmail,
                                                IssuerName = idata.IssuerEmail,
                                                IssuerWebsite = idata.IssuerWebsite,
                                                CustomerServiceTelephone = idata.CustomerServiceTelephone,
                                                IssuerLogoUrl = idata.IssuerLogoUrl,
                                                IssuerNotificationIcon = idata.IssuerNotificationIcon,
                                                IssuerPrivacyUrl = idata.IssuerPrivacyUrl,
                                                OnlineBankingUrl = idata.OnlineBankingUrl
                                            },
                                        IssuerAppData = mdata.IssuerAppData is var iadata && iadata is null
                                            ? null
                                            : new()
                                            {
                                                IssuerAppAddress = iadata.IssuerAppAddress,
                                                IssuerAppName = iadata.IssuerAppName,
                                                IssuerAppOSType = iadata.IssuerAppOSType
                                            },
                                        LabelColor = mdata.LabelColor,
                                        LongDescription = mdata.LongDescription,
                                        ShortDescription = mdata.ShortDescription,
                                        CardArtUrl = mdata.CardArtUrl,
                                        CardBackgroundColor = mdata.CardBackgroundColor,
                                        CardForegroundColor = mdata.CardForegroundColor,
                                        TermsAndConditionsId = mdata.TermsAndConditionsId,
                                        TermsAndConditionsUrl = mdata.TermsAndConditionsUrl,
                                        CardAssets = mdata.CardAssets is var assets && assets is null
                                            ? null
                                            : assets.Select(a => new CardAssetInfo()
                                            {
                                                Guid = a.Guid,
                                                AssetType = a.AssetType,
                                                MimeType = a.MimeType,
                                                PixelHeight = a.PixelHeight,
                                                PixelWidth = a.PixelWidth
                                            }).ToList()
                                    },
                            },
                        Provider = "TOKENEX",
                        TokenRequestorId = response.NetworkResponse.TokenRequestorId,
                        TokenReferenceId = response.NetworkResponse.TokenReferenceId,
                        Decision = network?.TokenizationDecision switch
                        {
                            TokenizationDecision.APPROVED => Decision.Approved,
                            TokenizationDecision.PCI_APPROVED => Decision.PCI_approved,
                            TokenizationDecision.AUTH_REQ => Decision.Auth_req,
                            _ => throw new Exception("Unknown decision value")
                        }
                    };
                    requestResponseEntity.NetworkToken = token;

                    // vault.PaymentAccountReference = carddata?.PaymentAccountReference;
                    await _dbContext.NetworkTokens.AddAsync(token);
                    await _dbContext.SaveChangesAsync();
                    workspan.Log.Information("new network token is received  > {Network tokenRefId}",
                        token.TokenReferenceId);
                }
                catch (UniqueConstraintException e) //when (token != null && e.Entries.Count == 1 &&
                    //  e.Entries[0].Entity == token)
                {
                    _dbContext.Entry(token).State = EntityState.Detached;
                    token = await _dbContext.NetworkTokens.SingleOrDefaultAsync(t =>
                        t.TokenReferenceId == response.NetworkResponse.TokenReferenceId);
                    token.State =
                        response.NetworkResponse.TokenState; // new request might bring updated token state
                    requestResponseEntity.NetworkToken = token;

                    workspan.Log.Information("token already exists > {Network tokenRefId}", token.TokenReferenceId);
                    //await _dbContext.SaveChangesAsync();
                }
            }
            else
            {
                workspan.Log.Information("Network Token TokenizeAsync failed with {Message}",
                    response.NetworkResponse?.StatusMessage);
            }
        }
        catch (Exception ex)
        {
            workspan.RecordException(ex);
            throw;
        }

        await _dbContext.SaveChangesAsync();
        return token;

        // static string GetCleanTokenizeResponse(TokenizationResponse response)
        // {
        //     var token = string.Empty;
        //     var tokenElem = response.NetworkResponse?.EncData?.PaymentBundle?.BundleElements?.FirstOrDefault(
        //         e => e.Type == BundleElementType.TOKEN);
        //     if (tokenElem is not null)
        //     {
        //         token = tokenElem.Value;
        //         tokenElem.Value = string.Empty;
        //     }
        //
        //     var responseJson = JsonSerializer.Serialize(response);
        //     var cleanResponse = JsonSerializer.Deserialize<TokenizationResponse>(responseJson);
        //     if (tokenElem is not null)
        //     {
        //         tokenElem.Value = token;
        //     }
        //
        //     return cleanResponse;
        // }
    }

    private async Task SetPaymentTokenAdvisoryLock(
        Guid paymentToken,
        bool exclusive,
        int? timeOutMs = null)
    {
        var lockString = $"paymentToken:{paymentToken.ToString()}";
        if (exclusive)
        {
            await _dbContext.SetPostgresExclusiveAdvisoryLockAsync(lockString, timeOutMs);
        }
        else
        {
            await _dbContext.SetPostgresSharedAdvisoryLockAsync(lockString, timeOutMs);
        }
    }


    /// <summary>
    ///  try and  read network token from DB,
    /// if it is not found immediately- wait, using lock, for network tokenization to finish
    /// </summary>
    /// <param name="paymentInstrumentId"></param>
    /// <param name="isCit"></param>
    /// <returns></returns>
    private async Task<NetworkToken> GetNetworkToken(Guid paymentInstrumentId, bool isCit)
    {
        NetworkToken result = null;

        var timeoutMs = isCit ? _networkTokenOptions.TimeoutCitMs : _networkTokenOptions.TimeoutMitMs;

        try
        {
            await using var trans = await _dbContext.Database.BeginTransactionAsync();


            //shared advisory lock, waiting for the exclusive advisory lock,
            // set during  network tokenezation
            // to be resolved
            await SetPaymentTokenAdvisoryLock(paymentInstrumentId, exclusive: false, timeoutMs);

            var instrument =
                await _dbContext.PaymentInstruments
                    .Include(p => p.NetworkToken)
                    .AsNoTracking()
                    .SingleOrDefaultAsync(p => p.Id == paymentInstrumentId,
                        CancellationToken.None);

            if (instrument is null)
            {
                Workspan.Current.RecordError("instrument is null");
            }
            else if (instrument.NetworkToken is not null)
            {
                result = instrument.NetworkToken;
            }
        }
        catch (OperationCanceledException oe) when (oe.InnerException is TimeoutException)
        {
            Workspan.Current.Log.Information("{Timeout} on network token select ", timeoutMs);
        }
        catch (Exception e)
        {
            if (e.Message.Contains("55P03:"))
            {
                Workspan.Current.Log.Information("Lock timeout when waiting for network token");
            }
            else
            {
                Workspan.Current.RecordException(e);
            }
        }


        if (string.IsNullOrWhiteSpace(result?.Token)) return null;
        return result;
    }

    public async Task<NetworkTokenInfo> GetTokenAndCryptogramResult(
        Guid paymentInstrumentId,
        string cardNumber,
        int amount,
        bool isCit,
        string eci,
        string cavv)
    {
        return null; ///todo-temporarily disabled!

        using var workspan = Workspan.Start<NetworkTokenizationTokenexService>()
            .Tag("paymentInstrumentId", paymentInstrumentId);

        NetworkTokenInfo result = null;

        //todo- eligibility parameters for timeout
        var timeoutMs = isCit ? _networkTokenOptions.TimeoutCitMs : _networkTokenOptions.TimeoutMitMs;

#if DEBUG
        if (_networkTokenOptions.TestCards.Contains(cardNumber)) timeoutMs = 6000;
#endif

        try
        {
            string cryptogram = null;
            var networkToken = await GetNetworkToken(paymentInstrumentId, isCit);
            if (networkToken is null)
            {
                workspan.Log.Information("Network token not found");
                return null;
            }

            if (networkToken.State != TokenState.ACTIVE)
            {
                workspan.Log.Information("Active network token not found");
                return null;
            }

            var networkTokenValue = networkToken?.Token;

            workspan.Log.Information("Active network token found");

            if (true) //isCit) // cryptogram only for CIT , but temporary enabled for MIT
            {
                var sam = amount.ToString();
                var stringAm = amount == 0
                    ? "0.00"
                    : $"{sam.Substring(0, sam.Length - 2)}.{sam.Substring(sam.Length - 1)}";


                ///todo - timeout for this request
                var response = await _tokenExSdk.GetPaymentBundle(new PaymentBundleRequest()
                {
                    Token = networkToken.TxToken,
                    Amount = stringAm,
                    TransactionInitiationType =
                        isCit ? TransactionInitiationType.CONSUMER : TransactionInitiationType.MERCHANT,
                    TransactionType = TransactionType.ECOM,
                    ECI = eci,
                    CryptogramValue = cavv
                }, CancellationToken.None);
                if (response.Success && response.NetworkResponse.StatusCode == "0000")
                {
                    cryptogram = response.NetworkResponse.DecodedEncData.PaymentBundle
                        .BundleElements.FirstOrDefault(b => b.Type == BundleElementType.ECOM_CRYPTOGRAM)?.Value;
                    eci = eci ?? response.NetworkResponse.DecodedEncData.PaymentBundle
                        .BundleElements.FirstOrDefault(b => b.Type == BundleElementType.ECI)?.Value;

                    workspan.Log.Information("Network token cryptogram received");
                }
                else
                {
                    workspan.Log.Information("Network token cryptogram not received");
                    return null;
                }
            }

            result = new()
            {
                Token = networkTokenValue,
                Cryptogram = cryptogram,
                ExpirationMonth = networkToken.ExpirationMonth,
                ExpirationYear = networkToken.ExpirationYear,
                Eci = eci,
                AuthRequired = networkToken.Decision == Decision.Auth_req
            };


            workspan.Log.Information(" GetTokenAndCryptogram got result ");
        }
        catch (OperationCanceledException oe) when (oe.InnerException is TimeoutException)
        {
            workspan.Log.Information("Timeout on network token select ");
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
        }

        return result;
    }


    public async Task UpdateTokenState(NetworkTokenStateChangeNotification notification)
    {
        using var workspan = Workspan.Start<NetworkTokenizationTokenexService>().LogEnterAndExit();
        await _dbContext.NetworkTokens
            .Where(t => t.TokenRefId == notification.TokenReferenceId)
            .ExecuteUpdateAsync(settings => settings
                .SetProperty(a => a.State,
                    a => notification.CurrentTokenState));

        workspan.Log.Information(" UpdateTokenState updated {TokenState}", notification.CurrentTokenState);
    }


    void UpdateTokenCardData(NetworkToken token,
        FlexCharge.Vault.Services.TokenExNetworkTokenization.Models.GetCardMetaData.NetworkResponse network)
    {
        using var workspan = Workspan.Start<NetworkTokenizationTokenexService>().LogEnterAndExit();


        var expiry = network.TokenMetadata.TokenExpDate;
        token.ExpirationMonth = int.Parse(expiry.Substring(2));
        token.ExpirationYear = int.Parse(expiry.Substring(0, 2));
        //token.TokenRefId = network.TokenReferenceId;
        var carddata = network.EncData.CardData;

        token.CardInfo = carddata is null
            ? null
            : new()
            {
                CardName = carddata.CardName,
                CardType = carddata.CardType,
                CardCountryCode = carddata.CardCountryCode,
                CardSuffix = carddata.CardSuffix,
                CoBranded = carddata.CoBranded,
                Cvv2Printed = carddata.Cvv2Printed,
                CardLongDescription = carddata.CardLongDescription,
                PaymentAccountReference = carddata.PaymentAccountReference,
                CardShortDescription = carddata.CardShortDescription,
                PanReferenceId = carddata.PanReferenceId,
                CardTypeIndicator = carddata.CardTypeIndicator,
                PanExpDate = carddata.PanExpDate,
                CoBrandName = carddata.CoBrandName,
                ExpDataPrinted = carddata.ExpDataPrinted,
                CardHolderEmbossedName = carddata.CardHolderEmbossedName,
                CardMetaInfo = network.CardMetaData is var mdata && mdata is null
                    ? null
                    : new()
                    {
                        IssuerData = mdata.IssuerData is var idata && idata is null
                            ? null
                            : new()
                            {
                                IssuerAddress = idata.IssuerAddress,
                                IssuerEmail = idata.IssuerEmail,
                                IssuerName = idata.IssuerEmail,
                                IssuerWebsite = idata.IssuerWebsite,
                                CustomerServiceTelephone = idata.CustomerServiceTelephone,
                                IssuerLogoUrl = idata.IssuerLogoUrl,
                                IssuerNotificationIcon = idata.IssuerNotificationIcon,
                                IssuerPrivacyUrl = idata.IssuerPrivacyUrl,
                                OnlineBankingUrl = idata.OnlineBankingUrl
                            },
                        IssuerAppData = mdata.IssuerAppData is var iadata && iadata is null
                            ? null
                            : new()
                            {
                                IssuerAppAddress = iadata.IssuerAppAddress,
                                IssuerAppName = iadata.IssuerAppName,
                                IssuerAppOSType = iadata.IssuerAppOSType
                            },
                        LabelColor = mdata.LabelColor,
                        LongDescription = mdata.LongDescription,
                        ShortDescription = mdata.ShortDescription,
                        CardArtUrl = mdata.CardArtUrl,
                        CardBackgroundColor = mdata.CardBackgroundColor,
                        CardForegroundColor = mdata.CardForegroundColor,
                        TermsAndConditionsId = mdata.TermsAndConditionsId,
                        TermsAndConditionsUrl = mdata.TermsAndConditionsUrl,
                        CardAssets = mdata.CardAssets is var assets && assets is null
                            ? null
                            : assets.Select(a => new CardAssetInfo()
                            {
                                Guid = a.Guid,
                                AssetType = a.AssetType,
                                MimeType = a.MimeType,
                                PixelHeight = a.PixelHeight,
                                PixelWidth = a.PixelWidth
                            }).ToList()
                    },
            };
        //     
        // token.CardName = carddata.CardName;
        // token.CardType = carddata.CardType;
        // token.CountryCode = carddata.CardCountryCode;
        // token.PaymentAccountReference = carddata.PaymentAccountReference;
        // token.StatusMessage = network.StatusMessage;
        // token.StatusCode = network.StatusCode;
    }


    /// <summary>
    /// wait for answer from Tokenex
    /// </summary>
    /// <param name="notification"></param>
    /// <exception cref="NotImplementedException"></exception>
    public async Task UpdateCardMetaData(NetworkTokenCardUpdateNotification notification)
    {
        using var workspan = Workspan.Start<NetworkTokenizationTokenexService>().LogEnterAndExit();

        try
        {
            var token = await _dbContext.NetworkTokens.SingleOrDefaultAsync(t =>
                t.TokenRefId == notification.TokenReferenceId);

            var response = await _tokenExSdk.GetCardMetadata(new GetCardMetadataRequest()
            {
                Token = token.Token
            }, CancellationToken.None);
            UpdateTokenCardData(token, response.NetworkResponse);
            // response.NetworkResponse.TokenMetadata
            await _dbContext.SaveChangesAsync();
            workspan.Log.Information("Card metadata updated {Data}", JsonSerializer.Serialize(notification));
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
        }
    }


    public bool GlobalNetworkTokenizationIsEnabled(Guid? mid, CreditCardDTO card, Workspan workspan)
    {
        if (!mid.HasValue)
        {
            workspan.Log.Warning("Cannot tokenize card without merchant id");
            return false;
        }

        if (card.BillingAddress is null)
        {
            workspan.Log.Warning("Cannot tokenize card without billing address");
            return false;
        }

        if (string.IsNullOrWhiteSpace(card.Email))
        {
            workspan.Log.Warning("Cannot tokenize card without email");
            return false;
        }

        return true;
    }

    public async Task<bool> UnsubscribeFromNetworkTokenization(string token)
    {
        using var workspan = Workspan.Start<NetworkTokenizationTokenexService>().LogEnterAndExit();

        var existingToken = await _dbContext.NetworkTokens.SingleOrDefaultAsync(t => t.Token == token);

        if (existingToken is null)
        {
            workspan.Log.Warning("Token not found in DB");
            return false;
        }

        if (existingToken.State == TokenState.DELETED)
        {
            workspan.Log.Warning("Token already deleted");
            return false;
        }

        workspan.Log.Information("Unsubscribing from network tokenization for token {Token}", token);

        var result = await _tokenExSdk.LifeCycle(new LifecycleManagementRequest
        {
            Token = token,
            LifecycleManagementAction = LifecycleManagementAction.DELETE_TOKEN,
            RequestReason = "Unsubscribe from network tokenization"
        }, CancellationToken.None);

        if (result.Success)
        {
            workspan.Log.Information("Unsubscribed from network tokenization");

            existingToken.State = TokenState.DELETED;

            _dbContext.NetworkTokens.Update(existingToken);
            await _dbContext.SaveChangesAsync();
            return true;
        }

        workspan.Log.Error("Unsubscribe from network tokenization failed");
        return false;
    }

    public async Task<TokenStatusResponse> GetNetworkTokenStatus(string token)
    {
        using var workspan = Workspan.Start<NetworkTokenizationTokenexService>().LogEnterAndExit();

        // var existingToken = await _dbContext.NetworkTokens.SingleOrDefaultAsync(t => t.Token == token);
        //
        // if (existingToken is null)
        // {
        //     workspan.Log.Warning("Token not found in DB");
        //     return null;
        // }

        var result = await _tokenExSdk.GetStatus(new TokenStatusRequest
        {
            Token = token
        }, CancellationToken.None);

        return result;
    }


    private async Task<Contracts.Common.DeviceInfo> GetDeviceDataAsync(string senseKey, Guid? mid, Guid? orderId)
    {
        var deviceInfoResponse = await _getBrowserInformationRequest.GetResponse<GetBrowserInfoCommandResponse>(new()
        {
            SenseKey = senseKey,
            Mid = mid,
            OrderId = orderId
        });

        var result = deviceInfoResponse.Message.DeviceInfo;

        return result;
    }


    async Task<(TokenizationRequest, string)> GetTokenizationRequestAndPciJson(
        Contracts.Common.DeviceInfo? deviceInfo,
        FlexCharge.Contracts.Common.Address? billingAddress,
        PaymentInstrument instrument)
    {
        deviceInfo = deviceInfo ?? new Contracts.Common.DeviceInfo() {Locale = "en-US"};

        var tokenRequest = await GetTokenRequestAsync(deviceInfo, billingAddress, instrument);

        if (tokenRequest is null)
            return (null, null);

        var pan = tokenRequest.Data;
        var panFingerprint = CryptoSigningHelper.ComputeContentHash(pan);
        var cvv = tokenRequest.Cvv;

        string cvvFingerprint = cvv is null
            ? null
            : CryptoSigningHelper.ComputeContentHash(cvv);

        var consumerId = tokenRequest.ConsumerId;
        tokenRequest.Cvv = cvvFingerprint;
        tokenRequest.Data = panFingerprint;
        tokenRequest.ConsumerId = string.Empty;

        var jsonRequest = JsonSerializer.Serialize(tokenRequest, _jsonSerializerOptions);

        tokenRequest.Cvv = cvv;
        tokenRequest.Data = pan;
        tokenRequest.ConsumerId = consumerId;

        return (tokenRequest, jsonRequest);
    }

    bool RequestIsRetryable(string code)
    {
        if (code is null) return true;

        var retryCodes = new HashSet<string>()
        {
            VisaStatusCodes.UnknownError,
            VisaStatusCodes.InternalServerError,
            AmexStatusCodes.UpstreamTimeout,
            AmexStatusCodes.ServerTimeOut,
            MasterCardStatusCodes.UpstreamTimeout,
            MasterCardStatusCodes.ServerTimeOut
        };
        return retryCodes.Contains(code);
    }

    private async Task UpdatePaymentInstrumentInformationAsync(NetworkTokenRequestedEvent networkTokenRequestedEvent,
        PaymentInstrument instrument)
    {
        if (instrument.DeviceInfo is null && !string.IsNullOrWhiteSpace(networkTokenRequestedEvent.SenseKey))
        {
            try
            {
                var deviceInfo = await GetDeviceDataAsync(networkTokenRequestedEvent.SenseKey,
                    networkTokenRequestedEvent.Mid, networkTokenRequestedEvent.OrderId);

                instrument.SenseKey = networkTokenRequestedEvent.SenseKey;
                instrument.DeviceInfo = deviceInfo;

                if (deviceInfo != null)
                {
                    Workspan.Current!.Log.Information("Device info for network token received {Device}",
                        JsonSerializer.Serialize(deviceInfo));
                }
                else
                {
                    Workspan.Current!.Log.Warning("Device info for network token is null");
                }
            }
            catch (Exception e)
            {
                Workspan.Current!.RecordException(e, "Device info request failed");
            }
        }

        if (networkTokenRequestedEvent.BillingAddress != null)
        {
            if (instrument.BillingAddress is null)
            {
                instrument.BillingAddress = networkTokenRequestedEvent.BillingAddress;
            }
            else
            {
                if (!string.IsNullOrWhiteSpace(networkTokenRequestedEvent.BillingAddress.Address1))
                {
                    instrument.BillingAddress.Address1 = networkTokenRequestedEvent.BillingAddress.Address1;
                }

                if (!string.IsNullOrWhiteSpace(networkTokenRequestedEvent.BillingAddress.Address2))
                {
                    instrument.BillingAddress.Address2 = networkTokenRequestedEvent.BillingAddress.Address2;
                }

                if (!string.IsNullOrWhiteSpace(networkTokenRequestedEvent.BillingAddress.CountryCode))
                {
                    instrument.BillingAddress.CountryCode = networkTokenRequestedEvent.BillingAddress.CountryCode;
                }

                if (!string.IsNullOrWhiteSpace(networkTokenRequestedEvent.BillingAddress.City))
                {
                    instrument.BillingAddress.City = networkTokenRequestedEvent.BillingAddress.City;
                }

                if (!string.IsNullOrWhiteSpace(networkTokenRequestedEvent.BillingAddress.Phone))
                {
                    instrument.BillingAddress.Phone = networkTokenRequestedEvent.BillingAddress.Phone;
                }

                if (!string.IsNullOrWhiteSpace(networkTokenRequestedEvent.BillingAddress.Zip))
                {
                    instrument.BillingAddress.Zip = networkTokenRequestedEvent.BillingAddress.Zip;
                }

                if (!string.IsNullOrWhiteSpace(networkTokenRequestedEvent.BillingAddress.StateCode))
                {
                    instrument.BillingAddress.StateCode = networkTokenRequestedEvent.BillingAddress.StateCode;
                }
            }
        }

        if (!string.IsNullOrWhiteSpace(networkTokenRequestedEvent.Email))
        {
            instrument.Email = networkTokenRequestedEvent.Email;
        }

        if (!string.IsNullOrWhiteSpace(networkTokenRequestedEvent.CardHolderFirstName))
        {
            instrument.CardHolderFirstName = networkTokenRequestedEvent.CardHolderFirstName;
        }

        if (!string.IsNullOrWhiteSpace(networkTokenRequestedEvent.CardHolderLastName))
        {
            instrument.CardHolderLastName = networkTokenRequestedEvent.CardHolderLastName;
        }

        await _dbContext.SaveChangesAsync();

        Workspan.Current!.Log.Information("Instrument updated for network token");
    }

    public async Task<NetworkToken> TokenizeCardAsync(NetworkTokenRequestedEvent networkTokenRequestedEvent)
    {
        using var workspan = Workspan.Start<NetworkTokenizationTokenexService>();

        NetworkToken token = null;
        try
        {
            //start transaction to ensure that only one request is sent for given PaymentToken
            await using var trans = await _dbContext.Database.BeginTransactionAsync();
            //await _dbContext.SetLockTimeoutAsync(timeoutMs);

            //exclusive advisory lock, waiting for network tokenization request via HTTP
            // to be resolved
            // We need it because there is no suitable table to use for row lock
            // As a bonus-this lock is much faster then a row-level lock
            // The lock is released when transaction is committed or rolled back
            await SetPaymentTokenAdvisoryLock(networkTokenRequestedEvent.PaymentInstrumentToken, exclusive: true);

            var instrument =
                await _dbContext.PaymentInstruments
                    .Include(i => i.NetworkToken)
                    .SingleOrDefaultAsync(i => i.Id == networkTokenRequestedEvent.PaymentInstrumentToken);


            if (instrument is null)
            {
                workspan.Log.Error("TokenizeCardAsync: instrument with {Token} is null",
                    networkTokenRequestedEvent.PaymentInstrumentToken);
                return null;
            }

            if (instrument.NetworkToken is not null)
            {
                workspan.Log.Warning("TokenizeCardAsync instrument.NetworkToken is not null");
                return instrument.NetworkToken;
            }

            await UpdatePaymentInstrumentInformationAsync(networkTokenRequestedEvent, instrument);

            var email = instrument.Email;

            if (string.IsNullOrWhiteSpace(email))
            {
                workspan.Log.Warning("TokenizeCardAsync: instrument.Email is null");
                return null;
            }

            var (tokenizationRequest, requestJson) = await GetTokenizationRequestAndPciJson(
                instrument.DeviceInfo,
                networkTokenRequestedEvent.BillingAddress,
                instrument);

            if (tokenizationRequest is null)
            {
                workspan.Log.Warning("Network tokenization request is null");
                return null;
            }

            workspan.Log.Information("TokenizeCardAsync preparing {Request}", requestJson);

            NetworkTokenRequestResponse tokenRequestResponseEntity = null;

            bool requestExists = true;
            try
            {
                using var _ = DatabaseLogSuppressor
                    .SuppressUniqueConstraintError<PostgreSQLDbContext>("NetworkTokenRequests",
                        "IX_NetworkTokenRequests_Request");

                tokenRequestResponseEntity = new()
                {
                    Request = requestJson, //JsonSerializer.SerializeToDocument(requestJson),
                };
                await _dbContext.NetworkTokenRequests.AddAsync(tokenRequestResponseEntity);
                await _dbContext.SaveChangesAsync();
                requestExists = false;
                workspan.Log.Information("TokenizeCardAsync prepared {Request}", requestJson);
            }
            catch (UniqueConstraintException e) //when (tokenRequestEntity != null && e.Entries.Count == 1 &&
                // e.Entries[0].Entity == tokenRequestEntity)
            {
                _dbContext.Entry(tokenRequestResponseEntity).State = EntityState.Detached;
                workspan.Log.Information("Network token request already exists");
            }
            catch (Exception ex)
            {
                workspan.RecordException(ex, "Network token request failed {Request}", requestJson);
            }

            if (requestExists && !RequestIsRetryable(tokenRequestResponseEntity?.ResponseStatusCode))
            {
                return null;
            }

            try
            {
                token = await TokenizeAsync(
                    tokenizationRequest,
                    tokenRequestResponseEntity,
                    instrument.VaultV2,
                    CancellationToken.None);


                if (token is not null)
                {
                    tokenRequestResponseEntity.NetworkToken = token;
                    instrument.NetworkToken = token;
                }

                await _dbContext.SaveChangesAsync();
                await trans.CommitAsync();

                workspan.Log.Information("TokenizeCardAsync > Success > {NetworkToken}", token?.Token);
            }
            catch (Exception e)
            {
                await trans.RollbackAsync();
                workspan.RecordException(e, "TokenizeCardAsync > Failed");
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "Network token request failed");
        }

        return token;
    }

    public TokenizationProvider Provider => TokenizationProvider.Tokenex;

    public CardBrand[] AllowedCardBrands =>
        new[] {CardBrand.Visa, CardBrand.MasterCard, CardBrand.Amex};
}