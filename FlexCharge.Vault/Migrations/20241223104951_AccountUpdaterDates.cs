using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Vault.Migrations
{
    /// <inheritdoc />
    public partial class AccountUpdaterDates : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "AccountUpdaterResponseReceivedOn",
                table: "VaultsV2",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ReturnedFromAccountUpdaterOn",
                table: "VaultsV2",
                type: "timestamp with time zone",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AccountUpdaterResponseReceivedOn",
                table: "VaultsV2");

            migrationBuilder.DropColumn(
                name: "ReturnedFromAccountUpdaterOn",
                table: "VaultsV2");
        }
    }
}
