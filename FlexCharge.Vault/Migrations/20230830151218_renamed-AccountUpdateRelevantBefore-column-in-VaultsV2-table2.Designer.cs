// <auto-generated />
using System;
using FlexCharge.Vault;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace FlexCharge.Vault.Migrations
{
    [DbContext(typeof(PostgreSQLDbContext))]
    [Migration("20230830151218_renamed-AccountUpdateRelevantBefore-column-in-VaultsV2-table2")]
    partial class renamedAccountUpdateRelevantBeforecolumninVaultsV2table2
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("FlexCharge.Vault.Entities.AccountUpdaterBatch", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Format")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsEnrypted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifyOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasColumnType("text");

                    b.Property<string>("RequestId")
                        .HasColumnType("text");

                    b.Property<string>("RequestName")
                        .HasColumnType("text");

                    b.Property<DateTime>("RequestedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AccountUpdaterBatches");
                });

            modelBuilder.Entity("FlexCharge.Vault.Entities.AccountUpdaterBatchItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("BatchId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifyOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("SequenceNumber")
                        .HasColumnType("integer");

                    b.Property<Guid>("VaultId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("BatchId", "VaultId")
                        .IsUnique();

                    b.ToTable("AccountUpdaterBatchItems");
                });

            modelBuilder.Entity("FlexCharge.Vault.Entities.PaymentInstrument", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CardCountry")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderFirstName")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderFirstNameNormalized")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderLastName")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderLastNameNormalized")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderName")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Cvv")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifyOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("VaultV2Id")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("VaultV2Id");

                    b.ToTable("PaymentInstruments");
                });

            modelBuilder.Entity("FlexCharge.Vault.Entities.Vault", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("AuditTimestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Bin")
                        .HasColumnType("text");

                    b.Property<string>("CardCountry")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderFirstName")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderFirstNameNormalized")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderLastName")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderLastNameNormalized")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderName")
                        .HasColumnType("text");

                    b.Property<string>("CardNumberMasked")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Cvv")
                        .HasColumnType("text");

                    b.Property<int>("ExpirationMonth")
                        .HasColumnType("integer");

                    b.Property<int>("ExpirationYear")
                        .HasColumnType("integer");

                    b.Property<string>("Fingerprint")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Last4")
                        .HasColumnType("text");

                    b.Property<Guid?>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifyOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Number")
                        .HasColumnType("text");

                    b.Property<string>("Token")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Vaults");
                });

            modelBuilder.Entity("FlexCharge.Vault.Entities.VaultConfig", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsAccountUpdateDisabled")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.ToTable("config", t =>
                        {
                            t.HasCheckConstraint("CK_ONE_ROW", "\"Id\" = 1");
                        });

                    b.HasData(
                        new
                        {
                            Id = 1,
                            IsAccountUpdateDisabled = false
                        });
                });

            modelBuilder.Entity("FlexCharge.Vault.Entities.VaultV2", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("AccountUpdateRelevantBefore")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("AccountUpdaterEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("AccountUpdaterMessage")
                        .HasColumnType("text");

                    b.Property<DateTime?>("AccountUpdaterRequestedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("AuditTimestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Bin")
                        .HasColumnType("text");

                    b.Property<string>("CardNumberMasked")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ExpirationMonth")
                        .HasColumnType("integer");

                    b.Property<int>("ExpirationYear")
                        .HasColumnType("integer");

                    b.Property<string>("Fingerprint")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Last4")
                        .HasColumnType("text");

                    b.Property<DateTime>("LastUsed")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifyOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("NextVaultId")
                        .HasColumnType("uuid");

                    b.Property<string>("Number")
                        .HasColumnType("text");

                    b.Property<bool>("PendingUpdate")
                        .HasColumnType("boolean");

                    b.Property<string>("Token")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("IsDeleted");

                    b.HasIndex(new[] { "Fingerprint", "CardNumberMasked", "ExpirationYear", "ExpirationMonth" }, "FingerPrint_CardNumberMasked_ExpirationYear_ExpirationMonth_Idx")
                        .IsUnique()
                        .HasFilter("\"IsDeleted\"=false");

                    b.ToTable("VaultsV2");
                });

            modelBuilder.Entity("FlexCharge.Vault.Entities.AccountUpdaterBatchItem", b =>
                {
                    b.HasOne("FlexCharge.Vault.Entities.AccountUpdaterBatch", "Batch")
                        .WithMany("Items")
                        .HasForeignKey("BatchId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Batch");
                });

            modelBuilder.Entity("FlexCharge.Vault.Entities.PaymentInstrument", b =>
                {
                    b.HasOne("FlexCharge.Vault.Entities.VaultV2", "VaultV2")
                        .WithMany("PaymentTokens")
                        .HasForeignKey("VaultV2Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("VaultV2");
                });

            modelBuilder.Entity("FlexCharge.Vault.Entities.AccountUpdaterBatch", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("FlexCharge.Vault.Entities.VaultV2", b =>
                {
                    b.Navigation("PaymentTokens");
                });
#pragma warning restore 612, 618
        }
    }
}
