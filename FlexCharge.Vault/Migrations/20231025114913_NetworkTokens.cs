using System;
using FlexCharge.Contracts.Commands.Tracking;
using FlexCharge.Vault.Entities;
using FlexCharge.Vault.Services.TokenExNetworkTokenization.Models.Tokenize.Request;
using FlexCharge.Vault.Services.TokenExNetworkTokenization.Models.Tokenize.Response;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Vault.Migrations
{
    /// <inheritdoc />
    public partial class NetworkTokens : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<bool>(
                name: "Valid<PERSON>uhn",
                table: "VaultsV2",
                type: "boolean",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "boolean",
                oldDefaultValue: true);

            migrationBuilder.AddColumn<string>(
                name: "PaymentAccountReference",
                table: "VaultsV2",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DeviceInfo>(
                name: "DeviceInfo",
                table: "PaymentInstruments",
                type: "jsonb",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "NetworkTokenId",
                table: "PaymentInstruments",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "NetworkTokens",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Token = table.Column<string>(type: "text", nullable: true),
                    TokenReferenceId = table.Column<string>(type: "text", nullable: true),
                    ExpirationMonth = table.Column<int>(type: "integer", nullable: false),
                    ExpirationYear = table.Column<int>(type: "integer", nullable: false),
                    CardInfo = table.Column<CardInfo>(type: "jsonb", nullable: true),
                    State = table.Column<string>(type: "text", nullable: false),
                    Provider = table.Column<string>(type: "text", nullable: true),
                    TxToken = table.Column<string>(type: "text", nullable: true),
                    TokenRefId = table.Column<string>(type: "text", nullable: true),
                    TokenRequestorId = table.Column<string>(type: "text", nullable: true),
                    Decision = table.Column<int>(type: "integer", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NetworkTokens", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "NetworkTokenRequests",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TokenexRequest = table.Column<TokenizationRequest>(type: "jsonb", nullable: true),
                    RequestString = table.Column<string>(type: "text", nullable: true),
                    TokenexResponse = table.Column<TokenizationResponse>(type: "jsonb", nullable: true),
                    TokenexStatusMessage = table.Column<string>(type: "text", nullable: true),
                    TokenexStatusCode = table.Column<string>(type: "text", nullable: true),
                    NetworkTokenId = table.Column<Guid>(type: "uuid", nullable: true),
                    PaymentInstrumentId = table.Column<Guid>(type: "uuid", nullable: false),
                    TxReferenceNumber = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NetworkTokenRequests", x => x.Id);
                    table.ForeignKey(
                        name: "FK_NetworkTokenRequests_NetworkTokens_NetworkTokenId",
                        column: x => x.NetworkTokenId,
                        principalTable: "NetworkTokens",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_NetworkTokenRequests_PaymentInstruments_PaymentInstrumentId",
                        column: x => x.PaymentInstrumentId,
                        principalTable: "PaymentInstruments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "PaymentAccountReference_Idx",
                table: "VaultsV2",
                column: "PaymentAccountReference");

            migrationBuilder.CreateIndex(
                name: "IX_PaymentInstruments_NetworkTokenId",
                table: "PaymentInstruments",
                column: "NetworkTokenId");

            migrationBuilder.CreateIndex(
                name: "IX_NetworkTokenRequests_NetworkTokenId",
                table: "NetworkTokenRequests",
                column: "NetworkTokenId");

            migrationBuilder.CreateIndex(
                name: "IX_NetworkTokenRequests_PaymentInstrumentId",
                table: "NetworkTokenRequests",
                column: "PaymentInstrumentId");

            migrationBuilder.CreateIndex(
                name: "IX_NetworkTokenRequests_RequestString",
                table: "NetworkTokenRequests",
                column: "RequestString",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_NetworkTokens_IsDeleted",
                table: "NetworkTokens",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_NetworkTokens_TokenReferenceId",
                table: "NetworkTokens",
                column: "TokenReferenceId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_NetworkTokens_TokenRefId",
                table: "NetworkTokens",
                column: "TokenRefId");

            migrationBuilder.AddForeignKey(
                name: "FK_PaymentInstruments_NetworkTokens_NetworkTokenId",
                table: "PaymentInstruments",
                column: "NetworkTokenId",
                principalTable: "NetworkTokens",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PaymentInstruments_NetworkTokens_NetworkTokenId",
                table: "PaymentInstruments");

            migrationBuilder.DropTable(
                name: "NetworkTokenRequests");

            migrationBuilder.DropTable(
                name: "NetworkTokens");

            migrationBuilder.DropIndex(
                name: "PaymentAccountReference_Idx",
                table: "VaultsV2");

            migrationBuilder.DropIndex(
                name: "IX_PaymentInstruments_NetworkTokenId",
                table: "PaymentInstruments");

            migrationBuilder.DropColumn(
                name: "PaymentAccountReference",
                table: "VaultsV2");

            migrationBuilder.DropColumn(
                name: "DeviceInfo",
                table: "PaymentInstruments");

            migrationBuilder.DropColumn(
                name: "NetworkTokenId",
                table: "PaymentInstruments");

            migrationBuilder.AlterColumn<bool>(
                name: "ValidLuhn",
                table: "VaultsV2",
                type: "boolean",
                nullable: false,
                defaultValue: true,
                oldClrType: typeof(bool),
                oldType: "boolean");
        }
    }
}
