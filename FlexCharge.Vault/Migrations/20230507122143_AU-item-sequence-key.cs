using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Vault.Migrations
{
    /// <inheritdoc />
    public partial class AUitemsequencekey : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateSequence<int>(
                name: "Sequence",
                minValue: -2000000L,
                maxValue: 2000000L,
                cyclic: true);

            migrationBuilder.AlterColumn<int>(
                name: "SequenceNumber",
                table: "AccountUpdaterItems",
                type: "integer",
                nullable: false,
                defaultValueSql: "nextval('\"Sequence\"')",
                oldClrType: typeof(int),
                oldType: "integer");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropSequence(
                name: "Sequence");

            migrationBuilder.AlterColumn<int>(
                name: "SequenceNumber",
                table: "AccountUpdaterItems",
                type: "integer",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer",
                oldDefaultValueSql: "nextval('\"Sequence\"')");
        }
    }
}
