// using FlexCharge.Common.BackgroundJobs;
// using FlexCharge.Contracts;
// using MassTransit;
//
// namespace FlexCharge.Scheduler.Consumers.Test;
//
// public class TestRunScheduledJobEventConsumer : IConsumer<SchedulerRunJobEvent>
// {
//         ILogger<TestRunScheduledJobEventConsumer> _logger;
//         private ISchedulerService _schedulerService;
//
//         public TestRunScheduledJobEventConsumer(ILogger<TestRunScheduledJobEventConsumer> logger, ISchedulerService schedulerService)
//         {
//             _logger = logger;
//             _schedulerService = schedulerService;
//         }
//
//         public async Task Consume(ConsumeContext<SchedulerRunJobEvent> context)
//         {
//              try
//              {
//                  if (context.Message.Recipient == "test")
//                  {
//                      Console.WriteLine("Hooray!!!");
//                  }
//
//                  //_schedulerService.RemoveScheduledJob();
//                  //_logger.LogInformation("Value: {Value}", context.Message.Id);
//              }
//              catch (Exception e)
//              {
//                  _logger.LogError(e, $"EXCEPTION: TestRunScheduledJobEventConsumer > FAILED");
//              }
//         }
// }