// using System;
// using System.Globalization;
// using System.IO;
// using System.Threading;
// using System.Threading.Tasks;
// using FlexCharge.Common;
// using FlexCharge.Common.BackgroundJobs;
// using FlexCharge.Contracts;
// using FlexCharge.Contracts.Commands.Vault;
// using FlexCharge.Payments.Entities;
// using FlexCharge.Payments.Services.PaymentInstrumentsServices;
// using FlexCharge.Payments.Services.PaymentServices;
// using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
// using FlexCharge.Utils;
// using MassTransit;
// using Microsoft.AspNetCore.Http;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.Extensions.Configuration;
// using Moq;
// using NUnit.Framework;
// using Merchant = FlexCharge.Payments.Entities.Merchant;
// using PaymentMethodType = FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels.PaymentMethodType;
// using Transaction = FlexCharge.Payments.Entities.Transaction;
// namespace FlexCharge.Payments.Tests;
//
// public class PaymentOrchestratorTests
// {
//     private Mock<IPaymentProvider> _paymentProvider;
//     private Mock<IServiceProvider> _serviceProvider;
//     private Mock<PostgreSQLDbContext> _context;
//     
//     private Mock<IPublishEndpoint> _publisher;
//     private PaymentOrchestrator _orchestrator;
//     private Mock<IRequestClient<DeTokenizeInstrumentCommand>> _detokenizeInstrumentRequestClient;
//     private Mock<IBackgroundWorkerCommandQueue> _backgroundWorkerCommandQueue;
//     private Mock<IPaymentInstrumentsService> _paymentInstrumentsService;
//     private Mock<IHttpContextAccessor> _httpContextAccessorMock;
//     private Mock<DbSet<Transaction>> _transactions;
//
//     public IConfiguration _config { get; set; }
//
//     [SetUp]
//     public void Setup()
//     {
//         _config = new ConfigurationBuilder()
//             .SetBasePath(Directory.GetCurrentDirectory())
//             .AddJsonFile(@"appsettings.Staging.json", false, false)
//             .AddEnvironmentVariables().Build();
//         
//         var options = new DbContextOptionsBuilder<PostgreSQLDbContext>()
//             .UseInMemoryDatabase("")
//             .Options;
//         
//         _httpContextAccessorMock = new Mock<IHttpContextAccessor>();
//
//         _context = new Mock<PostgreSQLDbContext>(options,_httpContextAccessorMock.Object);
//
//         _paymentProvider = new Mock<IPaymentProvider>();
//         _publisher = new Mock<IPublishEndpoint>();
//         _detokenizeInstrumentRequestClient = new Mock<IRequestClient<DeTokenizeInstrumentCommand>>();
//         _backgroundWorkerCommandQueue = new Mock<IBackgroundWorkerCommandQueue>();
//         _paymentInstrumentsService = new Mock<IPaymentInstrumentsService>();
//         _serviceProvider = new Mock<IServiceProvider>();
//
//         _orchestrator = new PaymentOrchestrator(_context.Object, _serviceProvider.Object,
//             _detokenizeInstrumentRequestClient.Object, _publisher.Object, _backgroundWorkerCommandQueue.Object,
//             _paymentInstrumentsService.Object);
//     }
//
//     [Test]
//     public async Task SaleAsync_Should_Throw_ArgumentNullException_When_Gateway_Is_Null()
//     {
//         // Arrange
//         var payload = new SaleRequest {Mid = Guid.NewGuid()};
//         Gateway? gateway = null;
//         var token = new CancellationToken();
//
//         // Act & Assert
//         Assert.ThrowsAsync<ArgumentNullException>(() =>
//             _orchestrator.SaleAsync(payload, gateway, token));
//     }
//
//     [Test]
//     public async Task SaleAsync_Should_Throw_NotSupportedException_When_PaymentProvider_Does_Not_Support_CreditCards()
//     {
//         // Arrange
//         var payload = new SaleRequest {Mid = Guid.NewGuid()};
//         var gateway = new Gateway();
//         var token = new CancellationToken();
//         _paymentProvider.Setup(p => p.SupportsCreditCards).Returns(false);
//
//         // Act & Assert
//         Assert.ThrowsAsync<NotSupportedException>(() =>
//             _orchestrator.SaleAsync(payload, gateway, token));
//     }
//
//     [Test]
//     public async Task SaleAsync_Should_Store_Transaction_In_Database_And_Publish_Event_When_Payment_Succeeds()
//     {
//         // Arrange
//         var payload = new SaleRequest {Mid = Guid.NewGuid()};
//         var gateway = new Gateway();
//         var token = new CancellationToken();
//         var merchant = new Merchant {Mid = payload.Mid};
//         var response = new SaleResult {Success = true};
//         var trx = new Transaction();
//         _paymentProvider.Setup(p => p.SupportsCreditCards).Returns(true);
//         _paymentProvider.Setup(p => p.SaleAsync(payload, token)).ReturnsAsync(response);
//         _context.Setup(c => c.Transactions.AddAsync(It.IsAny<Transaction>(), token))
//             .Callback<Transaction, CancellationToken>((t, _) => trx = t);
//         _context.Setup(c => c.SaveChangesAsync(token)).ReturnsAsync(1);
//         _context.Setup(c => c.Merchants.FindAsync(It.IsAny<object[]>(), token)).ReturnsAsync(merchant);
//         _publisher.Setup(p => p.Publish<PaymentDebitedEvent>(It.IsAny<object>(), token)).Returns(Task.CompletedTask);
//
//         // Act
//         var result = await _orchestrator.SaleAsync(payload, gateway, token);
//
//         // Assert
//         Assert.AreEqual(response, result);
//         Assert.IsNotNull(trx.Id);
//         Assert.AreEqual(payload.PaymentInstrumentId, trx.PaymentMethodId);
//         Assert.AreEqual(response.ProviderTransactionToken, trx.ProviderTransactionToken);
//         Assert.AreEqual(payload.Descriptor.Name, trx.DynamicDescriptor);
//         Assert.AreEqual(payload.Amount, trx.Amount);
//         Assert.AreEqual(payload.CurrencyCode, trx.Currency);
//         Assert.AreEqual(response.ProviderResponseCode, trx.ResponseCode);
//         Assert.AreEqual(response.ProviderResponseMessage, trx.ResponseMessage);
//         Assert.AreEqual(response.AvsCode, trx.AvsResultCode);
//         Assert.AreEqual(response.CvvCode, trx.CvvResultCode);
//         Assert.AreEqual(response.CavvCode, trx.CavvResultCode);
//         Assert.AreEqual(response.AuthorizationCode, trx.AuthorizationId);
//         Assert.AreEqual(TransactionType.Debit, trx.Type);
//         Assert.AreEqual(nameof(PaymentMethodType.CreditCard), trx.PaymentType);
//         Assert.AreEqual(response.Success ? TransactionStatus.Completed : TransactionStatus.Failed, trx.Status);
//         Assert.AreEqual(_paymentProvider.Object.CurrentPaymentProvider, trx.ProviderName);
//         Assert.AreEqual(gateway?.Name, trx.ProcessorName);
//         Assert.AreEqual(payload.PayerId ?? Guid.Empty, trx.PayerId);
//         Assert.AreEqual(merchant, trx.Merchant);
//         Assert.AreEqual(payload.OrderId, trx.OrderId);
//         Assert.AreEqual(false, trx.IsRecurring);
//         Assert.AreEqual(response.RawResult, trx.Meta);
//         _publisher.Verify(p => p.Publish<PaymentDebitedEvent>(It.Is<object>(o =>
//             o.GetType().GetProperty("OrderId").GetValue(o).ToString() == payload.OrderId.ToString()
//             && o.GetType().GetProperty("TransactionId").GetValue(o).ToString() == trx.Id.ToString()
//             && o.GetType().GetProperty("Mid").GetValue(o).ToString() == merchant.Mid.ToString()
//             && o.GetType().GetProperty("Type").GetValue(o).ToString() == TransactionType.Debit.ToString()
//             && o.GetType().GetProperty("Description").GetValue(o).ToString()
//                 .Contains(payload.CreditCard.Number.GetLast(4))
//             && o.GetType().GetProperty("AmountFormatted").GetValue(o).ToString() ==
//             Formatters.LongToDecimal(payload.Amount).ToString(CultureInfo.InvariantCulture)
//             && o.GetType().GetProperty("Amount").GetValue(o).ToString() == trx.Amount.ToString()
//             && o.GetType().GetProperty("FeeAmount").GetValue(o).ToString() == trx.FeeAmount.ToString()
//             && o.GetType().GetProperty("PaymentDate").GetValue(o).ToString() == trx.CreatedOn.ToString()
//             && o.GetType().GetProperty("Provider").GetValue(o).ToString() ==
//             _paymentProvider.Object.CurrentPaymentProvider
//             && o.GetType().GetProperty("ProviderResponseCode").GetValue(o).ToString() == trx.ResponseCode
//             && o.GetType().GetProperty("ProviderResponseDescription").GetValue(o).ToString() == trx.ResponseMessage
//             && o.GetType().GetProperty("CvvCode").GetValue(o).ToString() == trx.CvvResultCode
//             && o.GetType().GetProperty("AvsCode").GetValue(o).ToString() == trx.AvsResultCode
//             && o.GetType().GetProperty("ProcessorResponseCode").GetValue(o) == null
//             && o.GetType().GetProperty("ProcessorResponseDescription").GetValue(o) == null
//             && o.GetType().GetProperty("Processor").GetValue(o).ToString() == (gateway == null ? string.Empty : gateway.Name)
//         ), token));
//     }
//
//     [Test]
//     public async Task SaleAsync_Should_Store_Transaction_In_Database_And_Publish_Event_When_Payment_Fails()
//     {
//         // Arrange
//         var payload = new SaleRequest {Mid = Guid.NewGuid()};
//         var gateway = new Gateway();
//         var token = new CancellationToken();
//         var merchant = new Merchant {Mid = payload.Mid};
//         var response = new SaleResult {Success = false};
//         var trx = new Transaction();
//         _paymentProvider.Setup(p => p.SupportsCreditCards).Returns(true);
//         _paymentProvider.Setup(p => p.SaleAsync(payload, token)).ReturnsAsync(response);
//         _context.Setup(c => c.Transactions.AddAsync(It.IsAny<Transaction>(), token))
//             .Callback<Transaction, CancellationToken>((t, _) => trx = t);
//         _context.Setup(c => c.SaveChangesAsync(token)).ReturnsAsync(1);
//         _context.Setup(c => c.Merchants.FindAsync(It.IsAny<object[]>(), token)).ReturnsAsync(merchant);
//         _publisher.Setup(p => p.Publish<PaymentDebitFailedEvent>(It.IsAny<object>(), token))
//             .Returns(Task.CompletedTask);
//
//         // Act
//         var result = await _orchestrator.SaleAsync(payload, gateway, token);
//         // Assert
//         Assert.AreEqual(response, result);
//         Assert.IsNotNull(trx.Id);
//         Assert.AreEqual(payload.PaymentInstrumentId, trx.PaymentMethodId);
//         Assert.AreEqual(response.ProviderTransactionToken, trx.ProviderTransactionToken);
//         Assert.AreEqual(payload.Descriptor.Name, trx.DynamicDescriptor);
//         Assert.AreEqual(payload.Amount, trx.Amount);
//         Assert.AreEqual(payload.CurrencyCode, trx.Currency);
//         Assert.AreEqual(response.ProviderResponseCode, trx.ResponseCode);
//         Assert.AreEqual(response.ProviderResponseMessage, trx.ResponseMessage);
//         Assert.AreEqual(response.AvsCode, trx.AvsResultCode);
//         Assert.AreEqual(response.CvvCode, trx.CvvResultCode);
//         Assert.AreEqual(response.CavvCode, trx.CavvResultCode);
//         Assert.AreEqual(response.AuthorizationCode, trx.AuthorizationId);
//         Assert.AreEqual(TransactionType.Debit, trx.Type);
//         Assert.AreEqual(nameof(PaymentMethodType.CreditCard), trx.PaymentType);
//         Assert.AreEqual(response.Success ? TransactionStatus.Completed : TransactionStatus.Failed, trx.Status);
//         Assert.AreEqual(_paymentProvider.Object.CurrentPaymentProvider, trx.ProviderName);
//         Assert.AreEqual(gateway?.Name, trx.ProcessorName);
//         Assert.AreEqual(payload.PayerId ?? Guid.Empty, trx.PayerId);
//         Assert.AreEqual(merchant, trx.Merchant);
//         Assert.AreEqual(payload.OrderId, trx.OrderId);
//         Assert.AreEqual(false, trx.IsRecurring);
//         Assert.AreEqual(response.RawResult, trx.Meta);
//         _publisher.Verify(p => p.Publish<PaymentDebitFailedEvent>(It.Is<object>(o =>
//             o.GetType().GetProperty("OrderId").GetValue(o).ToString() == payload.OrderId.ToString()
//             && o.GetType().GetProperty("TransactionId").GetValue(o).ToString() == trx.Id.ToString()
//             && o.GetType().GetProperty("Mid").GetValue(o).ToString() == merchant.Mid.ToString()
//             && o.GetType().GetProperty("Type").GetValue(o).ToString() == TransactionType.Debit.ToString()
//             && o.GetType().GetProperty("Description").GetValue(o).ToString()
//                 .Contains(payload.CreditCard.Number.GetLast(4))
//             && o.GetType().GetProperty("AmountFormatted").GetValue(o).ToString() ==
//             Formatters.LongToDecimal(payload.Amount).ToString(CultureInfo.InvariantCulture)
//             && o.GetType().GetProperty("Amount").GetValue(o).ToString() == trx.Amount.ToString()
//             && o.GetType().GetProperty("FeeAmount").GetValue(o).ToString() == trx.FeeAmount.ToString()
//             && o.GetType().GetProperty("PaymentDate").GetValue(o).ToString() == trx.CreatedOn.ToString()
//             && o.GetType().GetProperty("Provider").GetValue(o).ToString() ==
//             _paymentProvider.Object.CurrentPaymentProvider
//             && o.GetType().GetProperty("ProviderResponseCode").GetValue(o).ToString() == trx.ResponseCode
//             && o.GetType().GetProperty("ProviderResponseDescription").GetValue(o).ToString() == trx.ResponseMessage
//             && o.GetType().GetProperty("CvvCode").GetValue(o).ToString() == trx.CvvResultCode
//             && o.GetType().GetProperty("AvsCode").GetValue(o).ToString() == trx.AvsResultCode
//             && o.GetType().GetProperty("ProcessorResponseCode").GetValue(o) == null
//             && o.GetType().GetProperty("ProcessorResponseDescription").GetValue(o) == null
//             && o.GetType().GetProperty("Processor").GetValue(o).ToString() == (gateway == null ? string.Empty : gateway.Name)
//         ), token));
//     }
// }