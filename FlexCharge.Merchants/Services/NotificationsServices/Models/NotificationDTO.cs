using System;
using System.Collections.Generic;
using System.Linq;

namespace FlexCharge.Merchants.Services.NotificationsServices.Models
{
    public class NotificationsDTO
    {
        public int UnseenCounter
        {
            get { return Notifications.Count(x => !x.Seen); }
        }

        public IEnumerable<NotificationDTO> Notifications { get; set; }
    }

    public class NotificationDTO
    {
        public string Title { get; set; }
        public string Message { get; set; }
        public bool Seen { get; set; }
        public bool Read { get; set; }
        public string Date { get; set; }
        public int Category { get; set; }
        public Guid Id { get; set; }
    }

    public class NotificationMessage
    {
        public string Title { get; set; }
        public string Content { get; set; }
    }
}