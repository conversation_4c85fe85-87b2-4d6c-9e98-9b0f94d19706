using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Merchants.Migrations
{
    /// <inheritdoc />
    public partial class addIsSenseJsOptionalandUseDefaultSiteForUnknownMerchantUrlsEnabled : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsSenseJsOptional",
                table: "Merchants",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "UseDefaultSiteForUnknownMerchantUrlsEnabled",
                table: "Merchants",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsSenseJsOptional",
                table: "Merchants");

            migrationBuilder.DropColumn(
                name: "UseDefaultSiteForUnknownMerchantUrlsEnabled",
                table: "Merchants");
        }
    }
}
