using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Merchants.Migrations
{
    /// <inheritdoc />
    public partial class addsmsandemailnotificationstopartnerstable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "BccEmail",
                table: "Partners",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NotificationSenderEmail",
                table: "Partners",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NotificationSenderPhone",
                table: "Partners",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ReplyToEmail",
                table: "Partners",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "BccEmail",
                table: "Partners");

            migrationBuilder.DropColumn(
                name: "NotificationSenderEmail",
                table: "Partners");

            migrationBuilder.DropColumn(
                name: "NotificationSenderPhone",
                table: "Partners");

            migrationBuilder.DropColumn(
                name: "ReplyToEmail",
                table: "Partners");
        }
    }
}
