using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Merchants.Migrations
{
    public partial class integrationTypeMerchantApplicants : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "FeeConfigurations",
                keyColumn: "Id",
                keyValue: new Guid("26fbe175-4b5f-40ed-9cd1-bc3b3fb5316b"));

            migrationBuilder.DeleteData(
                table: "FeeConfigurations",
                keyColumn: "Id",
                keyValue: new Guid("7520c4a5-06b8-48fe-9cc1-0aded00c315e"));

            migrationBuilder.DeleteData(
                table: "FeeConfigurations",
                keyColumn: "Id",
                keyValue: new Guid("c9ca862f-7a02-4d34-abb1-d66f0fbf570a"));

            migrationBuilder.AddColumn<int>(
                name: "IntegrationType",
                table: "Merchants",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "IntegrationType",
                table: "Applications",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IntegrationType",
                table: "Merchants");

            migrationBuilder.DropColumn(
                name: "IntegrationType",
                table: "Applications");

            migrationBuilder.InsertData(
                table: "FeeConfigurations",
                columns: new[] { "Id", "Amount", "ChargeType", "CreatedBy", "CreatedOn", "Description", "IsDeleted", "IsStandard", "ModifiedBy", "ModifiedOn", "Name", "Type" },
                values: new object[,]
                {
                    { new Guid("26fbe175-4b5f-40ed-9cd1-bc3b3fb5316b"), 1490, 1, null, new DateTime(2022, 3, 31, 8, 5, 45, 37, DateTimeKind.Utc).AddTicks(9220), "Percentage", false, true, null, new DateTime(2022, 3, 31, 8, 5, 45, 37, DateTimeKind.Utc).AddTicks(9280), "Percentage", 2 },
                    { new Guid("7520c4a5-06b8-48fe-9cc1-0aded00c315e"), 20, 1, null, new DateTime(2022, 3, 31, 8, 5, 45, 37, DateTimeKind.Utc).AddTicks(9330), "Fee Amount", false, true, null, new DateTime(2022, 3, 31, 8, 5, 45, 37, DateTimeKind.Utc).AddTicks(9340), "Fee Amount", 1 },
                    { new Guid("c9ca862f-7a02-4d34-abb1-d66f0fbf570a"), 1000, 3, null, new DateTime(2022, 3, 31, 8, 5, 45, 37, DateTimeKind.Utc).AddTicks(9360), "Chargeback Fee", false, true, null, new DateTime(2022, 3, 31, 8, 5, 45, 37, DateTimeKind.Utc).AddTicks(9370), "Chargeback Fee", 1 }
                });
        }
    }
}
