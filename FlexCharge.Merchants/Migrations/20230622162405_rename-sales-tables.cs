using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Merchants.Migrations
{
    /// <inheritdoc />
    public partial class renamesalestables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Applications_Partners_SalesAgencyAdminId",
                table: "Applications");

            migrationBuilder.DropForeignKey(
                name: "FK_Merchants_SalesAgencyAdmins_SalesAgencyAdminId",
                table: "Merchants");

            migrationBuilder.DropTable(
                name: "SalesAgencyAgents");

            migrationBuilder.DropTable(
                name: "SalesAgencyAdmins");

            migrationBuilder.RenameColumn(
                name: "SalesAgencyAdminId",
                table: "Merchants",
                newName: "SalesAgencyId");

            migrationBuilder.RenameIndex(
                name: "IX_Merchants_SalesAgencyAdminId",
                table: "Merchants",
                newName: "IX_Merchants_SalesAgencyId");

            migrationBuilder.RenameColumn(
                name: "SalesAgencyAdminId",
                table: "Applications",
                newName: "SalesAgencyId");

            migrationBuilder.RenameIndex(
                name: "IX_Applications_SalesAgencyAdminId",
                table: "Applications",
                newName: "IX_Applications_SalesAgencyId");

            migrationBuilder.CreateTable(
                name: "SalesAgencies",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SalesAgencies", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SalesAgents",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    ContactId = table.Column<Guid>(type: "uuid", nullable: true),
                    SalesAgencyId = table.Column<Guid>(type: "uuid", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SalesAgents", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SalesAgents_Contacts_ContactId",
                        column: x => x.ContactId,
                        principalTable: "Contacts",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_SalesAgents_SalesAgencies_SalesAgencyId",
                        column: x => x.SalesAgencyId,
                        principalTable: "SalesAgencies",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_SalesAgents_ContactId",
                table: "SalesAgents",
                column: "ContactId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesAgents_SalesAgencyId",
                table: "SalesAgents",
                column: "SalesAgencyId");

            migrationBuilder.AddForeignKey(
                name: "FK_Applications_SalesAgencies_SalesAgencyId",
                table: "Applications",
                column: "SalesAgencyId",
                principalTable: "SalesAgencies",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Merchants_SalesAgencies_SalesAgencyId",
                table: "Merchants",
                column: "SalesAgencyId",
                principalTable: "SalesAgencies",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Applications_SalesAgencies_SalesAgencyId",
                table: "Applications");

            migrationBuilder.DropForeignKey(
                name: "FK_Merchants_SalesAgencies_SalesAgencyId",
                table: "Merchants");

            migrationBuilder.DropTable(
                name: "SalesAgents");

            migrationBuilder.DropTable(
                name: "SalesAgencies");

            migrationBuilder.RenameColumn(
                name: "SalesAgencyId",
                table: "Merchants",
                newName: "SalesAgencyAdminId");

            migrationBuilder.RenameIndex(
                name: "IX_Merchants_SalesAgencyId",
                table: "Merchants",
                newName: "IX_Merchants_SalesAgencyAdminId");

            migrationBuilder.RenameColumn(
                name: "SalesAgencyId",
                table: "Applications",
                newName: "SalesAgencyAdminId");

            migrationBuilder.RenameIndex(
                name: "IX_Applications_SalesAgencyId",
                table: "Applications",
                newName: "IX_Applications_SalesAgencyAdminId");

            migrationBuilder.CreateTable(
                name: "SalesAgencyAdmins",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ContactId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true),
                    ModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SalesAgencyAdmins", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SalesAgencyAdmins_Contacts_ContactId",
                        column: x => x.ContactId,
                        principalTable: "Contacts",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "SalesAgencyAgents",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ContactId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true),
                    ModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    SalesAgencyAdminId = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SalesAgencyAgents", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SalesAgencyAgents_Contacts_ContactId",
                        column: x => x.ContactId,
                        principalTable: "Contacts",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_SalesAgencyAgents_SalesAgencyAdmins_SalesAgencyAdminId",
                        column: x => x.SalesAgencyAdminId,
                        principalTable: "SalesAgencyAdmins",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_SalesAgencyAdmins_ContactId",
                table: "SalesAgencyAdmins",
                column: "ContactId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesAgencyAgents_ContactId",
                table: "SalesAgencyAgents",
                column: "ContactId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesAgencyAgents_SalesAgencyAdminId",
                table: "SalesAgencyAgents",
                column: "SalesAgencyAdminId");

            migrationBuilder.AddForeignKey(
                name: "FK_Applications_Partners_SalesAgencyAdminId",
                table: "Applications",
                column: "SalesAgencyAdminId",
                principalTable: "Partners",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Merchants_SalesAgencyAdmins_SalesAgencyAdminId",
                table: "Merchants",
                column: "SalesAgencyAdminId",
                principalTable: "SalesAgencyAdmins",
                principalColumn: "Id");
        }
    }
}
