using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Merchants.Migrations
{
    /// <inheritdoc />
    public partial class makesalesagentsnulable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SalesAgent_Contacts_ContactId",
                table: "SalesAgent");

            migrationBuilder.DropForeignKey(
                name: "FK_SalesAgent_Merchants_MerchantId",
                table: "SalesAgent");

            migrationBuilder.DropForeignKey(
                name: "FK_SalesUser_Contacts_ContactId",
                table: "SalesUser");

            migrationBuilder.DropForeignKey(
                name: "FK_SalesUser_SalesAgent_SalesAgentId",
                table: "SalesUser");

            migrationBuilder.DropPrimaryKey(
                name: "PK_SalesUser",
                table: "SalesUser");

            migrationBuilder.DropPrimaryKey(
                name: "PK_SalesAgent",
                table: "SalesAgent");

            migrationBuilder.RenameTable(
                name: "SalesUser",
                newName: "SalesUsers");

            migrationBuilder.RenameTable(
                name: "SalesAgent",
                newName: "SalesAgents");

            migrationBuilder.RenameIndex(
                name: "IX_SalesUser_SalesAgentId",
                table: "SalesUsers",
                newName: "IX_SalesUsers_SalesAgentId");

            migrationBuilder.RenameIndex(
                name: "IX_SalesUser_ContactId",
                table: "SalesUsers",
                newName: "IX_SalesUsers_ContactId");

            migrationBuilder.RenameIndex(
                name: "IX_SalesAgent_MerchantId",
                table: "SalesAgents",
                newName: "IX_SalesAgents_MerchantId");

            migrationBuilder.RenameIndex(
                name: "IX_SalesAgent_ContactId",
                table: "SalesAgents",
                newName: "IX_SalesAgents_ContactId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_SalesUsers",
                table: "SalesUsers",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_SalesAgents",
                table: "SalesAgents",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_SalesAgents_Contacts_ContactId",
                table: "SalesAgents",
                column: "ContactId",
                principalTable: "Contacts",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_SalesAgents_Merchants_MerchantId",
                table: "SalesAgents",
                column: "MerchantId",
                principalTable: "Merchants",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_SalesUsers_Contacts_ContactId",
                table: "SalesUsers",
                column: "ContactId",
                principalTable: "Contacts",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_SalesUsers_SalesAgents_SalesAgentId",
                table: "SalesUsers",
                column: "SalesAgentId",
                principalTable: "SalesAgents",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SalesAgents_Contacts_ContactId",
                table: "SalesAgents");

            migrationBuilder.DropForeignKey(
                name: "FK_SalesAgents_Merchants_MerchantId",
                table: "SalesAgents");

            migrationBuilder.DropForeignKey(
                name: "FK_SalesUsers_Contacts_ContactId",
                table: "SalesUsers");

            migrationBuilder.DropForeignKey(
                name: "FK_SalesUsers_SalesAgents_SalesAgentId",
                table: "SalesUsers");

            migrationBuilder.DropPrimaryKey(
                name: "PK_SalesUsers",
                table: "SalesUsers");

            migrationBuilder.DropPrimaryKey(
                name: "PK_SalesAgents",
                table: "SalesAgents");

            migrationBuilder.RenameTable(
                name: "SalesUsers",
                newName: "SalesUser");

            migrationBuilder.RenameTable(
                name: "SalesAgents",
                newName: "SalesAgent");

            migrationBuilder.RenameIndex(
                name: "IX_SalesUsers_SalesAgentId",
                table: "SalesUser",
                newName: "IX_SalesUser_SalesAgentId");

            migrationBuilder.RenameIndex(
                name: "IX_SalesUsers_ContactId",
                table: "SalesUser",
                newName: "IX_SalesUser_ContactId");

            migrationBuilder.RenameIndex(
                name: "IX_SalesAgents_MerchantId",
                table: "SalesAgent",
                newName: "IX_SalesAgent_MerchantId");

            migrationBuilder.RenameIndex(
                name: "IX_SalesAgents_ContactId",
                table: "SalesAgent",
                newName: "IX_SalesAgent_ContactId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_SalesUser",
                table: "SalesUser",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_SalesAgent",
                table: "SalesAgent",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_SalesAgent_Contacts_ContactId",
                table: "SalesAgent",
                column: "ContactId",
                principalTable: "Contacts",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_SalesAgent_Merchants_MerchantId",
                table: "SalesAgent",
                column: "MerchantId",
                principalTable: "Merchants",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_SalesUser_Contacts_ContactId",
                table: "SalesUser",
                column: "ContactId",
                principalTable: "Contacts",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_SalesUser_SalesAgent_SalesAgentId",
                table: "SalesUser",
                column: "SalesAgentId",
                principalTable: "SalesAgent",
                principalColumn: "Id");
        }
    }
}
