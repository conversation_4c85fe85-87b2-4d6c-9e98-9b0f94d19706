using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Merchants.Migrations
{
    public partial class addIsStandardFee : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "<PERSON>Standar<PERSON>",
                table: "ApplicationFee",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsStandard",
                table: "ApplicationFee");
        }
    }
}
