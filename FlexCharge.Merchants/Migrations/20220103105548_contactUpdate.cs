using System;
using FlexCharge.Merchants.Enums;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Merchants.Migrations
{
    public partial class contactUpdate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "StoreName",
                table: "Applications",
                newName: "CompanyName");

            migrationBuilder.AlterDatabase()
                .Annotation("Npgsql:Enum:active_in_active", "active,inactive")
                .Annotation("Npgsql:Enum:application_status", "draft,cancelled,submitted,in_review,approved,declined")
                .OldAnnotation("Npgsql:Enum:active_in_active", "active,inactive");

            migrationBuilder.AddColumn<bool>(
                name: "Primary",
                table: "Contacts",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "Role",
                table: "Contacts",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "PrimaryContactId",
                table: "Applications",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<ApplicationStatus>(
                name: "Status",
                table: "Applications",
                type: "application_status",
                nullable: false,
                defaultValue: ApplicationStatus.DRAFT);

            migrationBuilder.CreateIndex(
                name: "IX_Applications_PrimaryContactId",
                table: "Applications",
                column: "PrimaryContactId");

            migrationBuilder.AddForeignKey(
                name: "FK_Applications_Contacts_PrimaryContactId",
                table: "Applications",
                column: "PrimaryContactId",
                principalTable: "Contacts",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Applications_Contacts_PrimaryContactId",
                table: "Applications");

            migrationBuilder.DropIndex(
                name: "IX_Applications_PrimaryContactId",
                table: "Applications");

            migrationBuilder.DropColumn(
                name: "Primary",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "Role",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "PrimaryContactId",
                table: "Applications");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "Applications");

            migrationBuilder.RenameColumn(
                name: "CompanyName",
                table: "Applications",
                newName: "StoreName");

            migrationBuilder.AlterDatabase()
                .Annotation("Npgsql:Enum:active_in_active", "active,inactive")
                .OldAnnotation("Npgsql:Enum:active_in_active", "active,inactive")
                .OldAnnotation("Npgsql:Enum:application_status", "draft,cancelled,submitted,in_review,approved,declined");
        }
    }
}
