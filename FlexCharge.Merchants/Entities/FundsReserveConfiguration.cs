using System;
using FlexCharge.Merchants.Enums;
using MassTransit.Futures.Contracts;
using VaultSharp.V1.SecretsEngines;

namespace FlexCharge.Merchants.Entities;

public class FundsReserveConfiguration : AuditableEntity
{
    public string Name { get; set; }
    public int DisputeRateMin { get; set; }
    public int DisputeRateMax { get; set; }

    public FundReserveType Type { get; set; } // Fixed or Rolling

    // Percentage or Fixed
    public int ReserveRate { get; set; }
    public FundReserveRateType ReserveRateType { get; set; } // Percentage or Fixed
    public int Period { get; set; }
    public bool IsActive { get; set; } = true;
}