using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using FlexCharge.Merchants.Enums;

namespace FlexCharge.Merchants.DTO;

public class BusinessInformationDTO
{
    public Guid? PartnerId { get; set; }
    public Guid? IntegrationPartnerId { get; set; }
    public Guid? SalesAgencyId { get; set; }

    [Required]
    [StringLength(50,
        ErrorMessage = "Legal entity must be between 3 and 50 character in length.")]
    [DisplayName("Legal entity name")]
    public string LegalEntityName { get; set; }

    public string CompanyName { get; set; }

    [StringLength(50,
        ErrorMessage = "Dba must be between 3 and 50 character in length.")]
    public string Dba { get; set; }

    public string Type { get; set; }
    public string TaxId { get; set; }
    [Required] public string Descriptor { get; set; }
    public string BusinessEstablishedDate { get; set; }
    public string Mcc { get; set; }
    public string Industry { get; set; }
    public bool Pcidss { get; set; }
    public string EcommercePlatform { get; set; }
    public string Website { get; set; }

    [Required] public ApplicationStatus Status { get; set; }

    [Required]
    [DisplayName("Integration type")]
    public MerchantIntegrationTypes IntegrationType { get; set; }

    public string Description { get; set; }
    public bool IsSiteValidated { get; set; }
    public bool IsIntegrationGuideEnabled { get; set; }
    public Guid? AccountId { get; set; }
    public string? ProductsSold { get; set; }
    public bool? IntegrationPartnerParticipateSale { get; set; }
    public int AnnualSalesVolume { get; set; }
}