using System;

namespace FlexCharge.Merchants.DTO;

public class AuditLogResponseDTO
{
    public string TableName { get; set; }
    public string PrimaryKey { get; set; }
    public string Operation { get; set; }
    public object OldValues { get; set; }
    public object NewValues { get; set; }
    public string AffectedColumns { get; set; }

    public string UserId { get; set; }
    public string UserFirstName { get; set; }
    public string UserLastName { get; set; }
    public string UserRole { get; set; }
    public string UserEmail { get; set; }
    public string UserIp { get; set; }
    public DateTime CreatedOn { get; set; }
}