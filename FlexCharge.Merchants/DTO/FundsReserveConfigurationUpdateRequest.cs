using System;
using Elasticsearch.Net.Specification.CrossClusterReplicationApi;
using FlexCharge.Merchants.Enums;

namespace FlexCharge.Merchants.DTO;

public class FundsReserveConfigurationUpdateRequest
{
    public Guid Id { get; set; }

    public string Name { get; set; }
    public decimal? DisputeRateMin { get; set; }
    public decimal? DisputeRateMax { get; set; }

    public FundReserveType? Type { get; set; } // Fixed or Rolling

    // Percentage or Fixed
    public int? ReserveRate { get; set; }
    public FundReserveRateType? ReserveRateType { get; set; } // Percentage or Fixed
    public int? Period { get; set; }
}