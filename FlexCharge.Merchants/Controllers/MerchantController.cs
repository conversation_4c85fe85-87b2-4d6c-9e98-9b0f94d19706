using AutoMapper;
using FlexCharge.Common;
using FlexCharge.Merchants.DTO;
using FlexCharge.Merchants.Enums;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Elasticsearch.Net;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Shared.Partners;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Merchants.Entities;
using FlexCharge.Merchants.Exceptions;
using FlexCharge.Merchants.Services.ApplicationServices;
using FlexCharge.Merchants.Services.MerchantServices;
using FlexCharge.Orders.Services;
using MassTransit;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.ModelBinding.Metadata;
using Microsoft.Extensions.Caching.Distributed;
using Newtonsoft.Json;
using Merchant = FlexCharge.Merchants.Entities.Merchant;
using MerchantFee = FlexCharge.Merchants.Entities.MerchantFee;
using HtmlAgilityPack;

namespace FlexCharge.Merchants.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [JwtAuth]
    public class MerchantController : BaseController
    {
        private PostgreSQLDbContext _context { get; set; }
        private readonly AppOptions _globalData;
        private readonly IMapper _mapper;

        private readonly IPublishEndpoint _publisher;

        private readonly IDistributedCache _cache;
        private readonly ISecurityCheckService _securityCheckService;
        private readonly IMerchantService _merchantService;

        public MerchantController(PostgreSQLDbContext context, IMapper mapper,
            IOptions<AppOptions> globalData, IPublishEndpoint publisher, IDistributedCache cache,
            ISecurityCheckService securityCheckService, IMerchantService merchantService)
        {
            _context = context;
            _mapper = mapper;

            _publisher = publisher;
            _cache = cache;
            _globalData = globalData.Value;
            _securityCheckService = securityCheckService;
            _merchantService = merchantService;
        }

        // [HttpPost()]
        // [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        // [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        // [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        // [ProducesResponseType(StatusCodes.Status404NotFound)]
        // [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        // public async Task<IActionResult> Post(MerchantRequest request, CancellationToken token)
        // {
        //     try
        //     {
        //         _logger.LogInformation(
        //             $"ENTERED: {_globalData.Name} => POST {HttpContext.Request.Path + HttpContext.Request.QueryString} ");
        //
        //         if (!ModelState.IsValid)
        //             return ValidationProblem();
        //
        //         var mer = _context.Merchants.Add(_mapper.Map<Entities.Merchant>(request));
        //         await _context.SaveChangesAsync();
        //
        //         var response = _mapper.Map<MerchantResponse>(mer);
        //         response.Result = ResponseResult.SUCCESS.ToString();
        //
        //         _logger.LogInformation(
        //             $"EXIT: {_globalData.Name} => POST {HttpContext.Request.Path + HttpContext.Request.QueryString} ");
        //         return Ok(response);
        //     }
        //     catch (Exception e)
        //     {
        //         _logger.LogError(e,
        //             $"EXCEPTION: POST {_globalData.Name} => API ERROR {HttpContext.Request.Path + HttpContext.Request.QueryString} ");
        //         return StatusCode(StatusCodes.Status500InternalServerError, "Failed creating a merchant");
        //     }
        // }

        [HttpGet()] // GET ALL
        [Authorize(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(PagedDTO<Merchant>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Get(CancellationToken token, string q, ActiveInActive? status,
            Guid? mid, Guid? pid, Guid? integrationPartnerId,
            string sort,
            int pageNumber = 1, int pageSize = 10)
        {
            using var workspan = Workspan.Start<MerchantController>();
            try
            {
                workspan.Log.Information(
                    "ENTERED: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString} ",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);

                var merchants = _context.Merchants
                    .Include(x => x.Address)
                    .Include(x => x.Partner)
                    .Include(x => x.IntegrationPartner)
                    .Include(x => x.PrimaryContact)
                    .Include(x => x.SalesAgency)
                    .Include(x => x.FundsReserveConfigurations)
                    .Include(x => x.Documents)
                    .Include(x => x.Fees).AsQueryable();

                if (mid != null)
                {
                    merchants = merchants.Where(x => x.Id == mid);
                }

                if (pid != null)
                {
                    merchants = merchants.Where(x => x.PartnerId == pid);
                }

                if (integrationPartnerId != null)
                {
                    merchants = merchants.Where(x => x.IntegrationPartnerId == integrationPartnerId);
                }

                if (HttpContext.IsUserInGroup(SuperAdminGroups.MERCHANT_ADMIN))
                {
                    merchants = merchants.Where(x => x.Id == GetMID());
                }

                if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    merchants = merchants.Where(x => x.PartnerId == GetPID());
                }

                if (HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    merchants = merchants.Where(x => x.IntegrationPartnerId == GetPID());
                }

                if (status != null)
                    merchants = merchants.Where(x => x.Status == status);

                if (!string.IsNullOrEmpty(q))
                {
                    var search = q.ToLower();

                    merchants = merchants.Where(x =>
                        x.CompanyName.ToLower().Contains(search) ||
                        x.Dba.ToLower().Contains(search) ||
                        x.Id.ToString().ToLower().Contains(search) ||
                        x.LegalEntityName.ToLower().Contains(search));
                }

                merchants = merchants.OrderByDescending(x => x.ModifiedOn);

                var pagedMerchants = await merchants.ToPagedListAsync(pageNumber, pageSize);

                workspan.Log.Information("EXIT: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);

                return Ok(_mapper.Map<IPagedList<Merchant>, PagedDTO<MerchantResponse>>(pagedMerchants));
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }

        [HttpGet("list")] // GET ALL
        [Authorize(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(PagedDTO<Merchant>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetList()
        {
            using var workspan = Workspan.Start<MerchantController>();
            try
            {
                workspan.Log.Information(
                    "ENTERED: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString} ",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);

                var merchants = _context.Merchants
                    .Include(x => x.Partner)
                    .Include(x => x.SalesAgency)
                    .Include(x => x.Owners)
                    .ThenInclude(owner => owner.Address)
                    .AsQueryable();

                if (HttpContext.User.Claims.Any(x =>
                        x.Type == MyClaimTypes.COGNITO_GROUP && x.Value == SuperAdminGroups.MERCHANT_ADMIN))
                    merchants = merchants.Where(x => x.Id == GetMID());

                if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    var pid = GetPID();
                    if (pid == Guid.Empty)
                        return BadRequest("Contact support");

                    var partner = await _context.Partners
                        .SingleOrDefaultAsync(x => x.Id == pid);

                    var isIntegrationPartner = HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN);

                    merchants = isIntegrationPartner
                        ? merchants.Where(x => x.IntegrationPartnerId == pid)
                        : merchants.Where(x => x.PartnerId == pid);
                }


                workspan.Log.Information("EXIT: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);

                return Ok(await merchants.Select(x => new
                {
                    Name = $"{x.Dba}{(x.Status == ActiveInActive.ACTIVE ? "" : " (Inactive)")}",
                    CompanyName = x.CompanyName,
                    Id = x.Id,
                    IsActive = x.Status == ActiveInActive.ACTIVE,
                    PartnerId = x.PartnerId,
                    PartnerName = x.Partner.Name,
                    SalesAgencyId = x.SalesAgencyId,
                    SalesAgencyName = x.SalesAgency.Name,
                }).ToListAsync());
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }

        [HttpGet()] // GET BY ID
        [Route("{id:guid}")]
        [Authorize(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Get([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.Start<MerchantController>()
                .LogEnterAndExit();
            try
            {
                var query = _context.Merchants
                    .Include(x => x.Address)
                    .Include(x => x.PrimaryContact)
                    .Include(x => x.DeveloperContact)
                    .Include(x => x.Partner)
                    .Include(x => x.IntegrationPartner)
                    .Include(x => x.Fees)
                    .Include(x => x.SalesAgency)
                    .Include(x => x.Documents)
                    .Include(x => x.Owners)
                    .ThenInclude(owner => owner.Address)
                    .Include(x => x.FundsReserveConfigurations)
                    .ThenInclude(x => x.FundsReserveConfiguration)
                    .AsQueryable();

                Merchant record;

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    // var cached = await _cache.GetStringAsync($"{id}", token: token);
                    // if (!string.IsNullOrEmpty(cached))
                    //     return Ok(JsonConvert.DeserializeObject<MerchantResponse>(cached));
                    record = await query.SingleOrDefaultAsync(x => x.Id == id, cancellationToken: token);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) ||
                         HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    var pid = GetPID();
                    if (pid == null || pid == Guid.Empty)
                        return BadRequest("Contact support");

                    var isIntegrationPartner = HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN);

                    record = isIntegrationPartner
                        ? await query.SingleOrDefaultAsync(x => x.Id == id && x.IntegrationPartnerId == pid,
                            cancellationToken: token)
                        : await query.SingleOrDefaultAsync(x => x.Id == id && x.PartnerId == pid,
                            cancellationToken: token);
                }
                else
                {
                    // var cached = await _cache.GetStringAsync($"{id}", token: token);
                    // if (!string.IsNullOrEmpty(cached))
                    //     return Ok(JsonConvert.DeserializeObject<MerchantResponse>(cached));
                    record = await query.SingleOrDefaultAsync(x => x.Id == GetMID(), cancellationToken: token);
                }

                if (record == null)
                    return NotFound("Merchant not found");

                var historicalFees = record.Fees.Where(x => !x.IsActive);

                var historicalFeesSortedDictionary =
                    historicalFees.Where(x => !x.IsActive).Select(x => _mapper.Map<FeeConfigurationResponse>(x))
                        .GroupBy(x => x.CreatedOn.Date)
                        .ToDictionary(x => x.Key.ToShortDateString(),
                            x => x);

                record.Fees.RemoveAll(x => !x.IsActive);

                var mapped = _mapper.Map<MerchantResponse>(record);

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    mapped.BankAccountInformation.AccountNumber = record.AccountNumber;
                    mapped.BankAccountInformation.RoutingNumber = record.RoutingNumber;
                    mapped.BankAccountInformation.BankAccountVerified = record.BankAccountVerified;
                    mapped.BankAccountInformation.BankName = record.BankName;
                    mapped.BankAccountInformation.Currency = record.Currency;
                }

                mapped.UploadedDocuments = record.Documents?.Select(x => new DocumentDTO
                {
                    Id = x.Id,
                    Name = x.Name?.Length > 37 ? x.Name.Substring(37) : x.Name,
                    Type = x.Type,
                    Path = x.Path,
                    Description = x.Description,
                    CreatedOn = x.CreatedOn,
                }).ToList();

                mapped.FeesHistory = historicalFeesSortedDictionary;

                return Ok(mapped);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching Merchant");
            }
        }

        [HttpGet()]
        [Route("{id:guid}/fees")]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetFees([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.Start<MerchantController>().LogEnterAndExit();
            try
            {
                var query = _context.MerchantFees
                    .AsQueryable();

                IEnumerable<Entities.MerchantFee> records;

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    records = await query.Where(x => x.RelatedMerchant.Id == id).ToListAsync(cancellationToken: token);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) ||
                         HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    var pid = GetPID();
                    if (pid == null || pid == Guid.Empty)
                        return BadRequest("Contact support");

                    var isIntegrationPartner = HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN);

                    records = isIntegrationPartner
                        ? await query.Where(x =>
                                x.RelatedMerchant.Id == id && x.RelatedMerchant.IntegrationPartnerId == pid)
                            .ToListAsync(cancellationToken: token)
                        : await query.Where(x => x.RelatedMerchant.PartnerId == id)
                            .ToListAsync(cancellationToken: token);
                }
                else
                {
                    records = await query.Where(x => x.Id == GetMID()).ToListAsync(cancellationToken: token);
                }

                var mapped = _mapper.Map<IEnumerable<FeeConfigurationResponse>>(records);

                return Ok(mapped);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching fees");
            }
        }

        [HttpPut("{id:guid}/funds-reserve-config")] // UPDATE
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Put([FromRoute] Guid id, MerchantFundsReserveConfigurationUpdateDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<MerchantController>(this, id, _globalData);
            try
            {
                workspan.Log.Information("ENTERED: {GlobalDataName} => PUT {RequestQueryString} ", _globalData.Name,
                    HttpContext.Request.Path + HttpContext.Request.QueryString);

                if (!ModelState.IsValid)
                    return ValidationProblem();

                //get merchant
                var merchant = await _context.Merchants
                    .Include(x => x.FundsReserveConfigurations)
                    .Where(x => x.Id == id).SingleOrDefaultAsync(cancellationToken: token);

                //throw not found if merchant not found
                if (merchant == null) return BadRequest("Merchant not found");

                if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) && merchant.PartnerId != GetPID() ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN) &&
                    merchant.IntegrationPartnerId != GetPID() ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.MERCHANT_ADMIN) && merchant.Id != GetMID())
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                var reserveConfig = await _context.FundsReserveConfigurations
                    .Where(x => x.Id == payload.FundsReserveConfigurationId)
                    .SingleOrDefaultAsync(cancellationToken: token);

                var merchantFundsReserves = await _context.MerchantFundsReserveConfiguration
                    .Where(x => x.MerchantId == id).ToListAsync(token);

                //delete all existing reserve configs
                merchantFundsReserves.Clear();

                //add reserve config to merchant
                var newMerchantReserve =
                    new MerchantFundsReserveConfiguration(merchant.Id, reserveConfig.Id);

                await _context.MerchantFundsReserveConfiguration.AddAsync(newMerchantReserve, token);

                var saveResult = await _context.SaveChangesAsync(token);
                if (saveResult == 0)
                    return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");

                await PublishUpdateMerchantEvent(merchant.Id, token);
                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");
            }
        }

        [HttpDelete("{id:guid}/funds-reserve-config")] // DELETE
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Delete([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<MerchantController>(this, id, _globalData);
            try
            {
                workspan.Log.Information("ENTERED: {GlobalDataName} => DELETE {RequestQueryString} ", _globalData.Name,
                    HttpContext.Request.Path + HttpContext.Request.QueryString);

                //get merchant
                var merchant = await _context.Merchants
                    .Include(x => x.FundsReserveConfigurations)
                    .Where(x => x.Id == id).SingleOrDefaultAsync(cancellationToken: token);

                //throw not found if merchant not found
                if (merchant == null) return BadRequest("Merchant not found");

                if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) && merchant.PartnerId != GetPID() ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN) &&
                    merchant.IntegrationPartnerId != GetPID() ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.MERCHANT_ADMIN) && merchant.Id != GetMID())
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                var merchantFundsReserves = await _context.MerchantFundsReserveConfiguration
                    .Where(x => x.MerchantId == id).ToListAsync(token);

                merchantFundsReserves.ForEach(x => _context.MerchantFundsReserveConfiguration.Remove(x));

                var saveResult = await _context.SaveChangesAsync(token);
                if (saveResult == 0)
                    return StatusCode(StatusCodes.Status500InternalServerError,
                        "Failed delete funds reserve configuration");

                await PublishUpdateMerchantEvent(merchant.Id, token);
                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    "Failed delete funds reserve configuration");
            }
        }

        [HttpGet()]
        [Route("{id:guid}/funds-reserve-config")]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetFundsReserveConfiguration([FromRoute] Guid id,
            CancellationToken token)
        {
            using var workspan = Workspan.Start<MerchantController>().LogEnterAndExit();
            try
            {
                var query = _context.MerchantFundsReserveConfiguration
                    .Include(x => x.Merchant)
                    .Where(x => x.IsDeleted == false)
                    .AsQueryable();

                IEnumerable<Entities.MerchantFundsReserveConfiguration> records;

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    records = await query.Where(x => x.Merchant.Id == id).ToListAsync(cancellationToken: token);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    records = await query.Where(x => x.Merchant.Id == id && x.Merchant.PartnerId == GetPID())
                        .ToListAsync(cancellationToken: token);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    records = await query.Where(x => x.Merchant.Id == id && x.Merchant.IntegrationPartnerId == GetPID())
                        .ToListAsync(cancellationToken: token);
                }
                else
                {
                    records = await query.Where(x => x.Id == GetMID()).ToListAsync(cancellationToken: token);
                }

                var mapped = _mapper.Map<IEnumerable<MerchantFundsReserveConfigurationDTO>>(records);

                return Ok(mapped);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching reserve configuration");
            }
        }

        [HttpPost("{id:guid}/deactivate")]
        [Authorize(Policy = MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Deactivate([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<MerchantController>(this, id, _globalData).LogEnterAndExit();
            try
            {
                if (!ModelState.IsValid)
                    return ValidationProblem();

                var pid = GetPID();
                Merchant record = null;
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    record = await _context.Merchants
                        .SingleOrDefaultAsync(x => x.Id == id, cancellationToken: token);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) && pid != null && pid != Guid.Empty)
                {
                    record = await _context.Merchants
                        .SingleOrDefaultAsync(x => x.Id == id && x.PartnerId == pid, cancellationToken: token);
                }

                if (record == null)
                {
                    return NotFound("Merchant not found");
                }

                if (record is {Status: ActiveInActive.INACTIVE})
                    throw new FlexValidationException("INACTIVE merchant cannot be Deactivated.");

                record.Status = ActiveInActive.INACTIVE;

                _context.Update(record);
                await _context.SaveChangesAsync(token);

                await _publisher.Publish(new MerchantDeactivatedEvent
                {
                    Mid = record.Id,
                    TimeStamp = DateTime.Now.ToUniversalTime()
                }, token);

                var response = new MerchantResponse
                {
                    Status = ActiveInActive.INACTIVE
                };

                return Ok(response);
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed deactivating merchant");
            }
        }

        [HttpPost("{id:guid}/activate")]
        [Authorize(Policy = MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Activate([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<MerchantController>(this, id, _globalData).LogEnterAndExit();
            try
            {
                if (!ModelState.IsValid)
                    return ValidationProblem();

                var pid = GetPID();
                Merchant record = null;

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    record = await _context.Merchants
                        .SingleOrDefaultAsync(x => x.Id == id, cancellationToken: token);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) && pid != null && pid != Guid.Empty)
                {
                    record = await _context.Merchants
                        .SingleOrDefaultAsync(x => x.Id == id && x.PartnerId == pid, cancellationToken: token);
                }

                if (record == null)
                {
                    return NotFound("Merchant not found");
                }

                if (record.Status == ActiveInActive.ACTIVE)
                    throw new FlexValidationException("ACTIVE merchant cannot be activated.");

                record.Status = ActiveInActive.ACTIVE;

                _context.Update(record);
                await _context.SaveChangesAsync(token);

                await _publisher.Publish(new MerchantActivatedEvent
                {
                    Mid = record.Id,
                    PartnerId = record.PartnerId,
                    IntegrationType = record.IntegrationType.ToString(),
                    Mcc = record.Mcc
                }, token);

                return Ok(new MerchantResponse
                {
                    Status = ActiveInActive.ACTIVE
                });
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed activating merchant");
            }
        }

        [HttpPost("{id:guid}/lock")]
        [Authorize(Policy = MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Lock([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<MerchantController>(this, id, _globalData).LogEnterAndExit();
            try
            {
                if (!ModelState.IsValid)
                    return ValidationProblem();

                var record = await _context.Merchants
                    .SingleOrDefaultAsync(x => x.Id == id, cancellationToken: token);

                if (record.Locked)
                    throw new FlexValidationException("Merchant already locked");

                record.Locked = true;

                _context.Update(record);
                await _context.SaveChangesAsync(token);

                await _publisher.Publish(new MerchantLockedEvent
                {
                    Mid = record.Id,
                    PartnerId = record.PartnerId,
                    ModifiedBy = record.ModifiedBy
                }, token);

                return Ok();
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed locking merchant");
            }
        }

        [HttpPost("{id:guid}/unlock")]
        [Authorize(Policy = MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Unlock([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<MerchantController>(this, id, _globalData).LogEnterAndExit();
            try
            {
                if (!ModelState.IsValid)
                    return ValidationProblem();

                var record = await _context.Merchants
                    .SingleOrDefaultAsync(x => x.Id == id, cancellationToken: token);

                if (!record.Locked)
                    throw new FlexValidationException("Merchant already unlocked");

                record.Locked = false;

                _context.Update(record);
                await _context.SaveChangesAsync(token);

                await _publisher.Publish<MerchantUnlockedEvent>(new
                {
                    Mid = record.Id,
                    PartnerId = record.PartnerId,
                    ModifiedBy = record.ModifiedBy
                }, token);

                return Ok();
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed unlocking merchant");
            }
        }

        [HttpPut("{id:guid}/customer-support")] // UPDATE
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Put([FromRoute] Guid id, MerchantCustomerSupportInformationDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<MerchantController>(this, payload, _globalData);
            try
            {
                workspan.Log.Information("ENTERED: {GlobalDataName} => PUT {RequestQueryString} ", _globalData.Name,
                    HttpContext.Request.Path + HttpContext.Request.QueryString);

                if (!HttpContext.IsUserInOneOfGroups(
                        SuperAdminGroups.SUPER_ADMIN,
                        SuperAdminGroups.PARTNER_ADMIN,
                        MerchantGroups.MERCHANT_ADMIN
                    ))
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                if (!ModelState.IsValid)
                    return ValidationProblem();

                var merchant = await _context.Merchants
                    .Where(x => x.Id == id).SingleOrDefaultAsync(cancellationToken: token);

                if (merchant == null) return NotFound();

                _securityCheckService.EnsureHasAccessToMerchantUpdateApi(merchant.Id, GetMID(), merchant.PartnerId,
                    GetPID());

                merchant.CustomerSupportLink = payload.CustomerSupportLink;
                merchant.CustomerSupportEmail = payload.CustomerSupportEmail;
                merchant.CustomerSupportName = payload.CustomerSupportName;
                merchant.CustomerSupportPhone = payload.CustomerSupportPhone;

                _context.Merchants.Update(merchant);
                var saveResult = await _context.SaveChangesAsync(token);
                if (saveResult == 0)
                    return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");

                await PublishUpdateMerchantEvent(merchant.Id, token);
                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");
            }
        }

        [HttpPut("{id:guid}/developer-details")] // UPDATE
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Put([FromRoute] Guid id, DeveloperContact payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<MerchantController>(this, payload, _globalData);
            try
            {
                if (!HttpContext.IsUserInOneOfGroups(
                        SuperAdminGroups.SUPER_ADMIN,
                        SuperAdminGroups.PARTNER_ADMIN,
                        MerchantGroups.MERCHANT_ADMIN
                    ))
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                if (!ModelState.IsValid)
                    return ValidationProblem();

                var merchant = await _context.Merchants
                    .Include(x => x.DeveloperContact)
                    .Where(x => x.Id == id).SingleOrDefaultAsync(cancellationToken: token);

                if (merchant == null) return NotFound();

                _securityCheckService.EnsureHasAccessToMerchantUpdateApi(merchant.Id, GetMID(), merchant.PartnerId,
                    GetPID());

                var merchantDeveloperContact = merchant.DeveloperContact;

                if (merchantDeveloperContact == null && !string.IsNullOrEmpty(payload.DeveloperEmail))
                {
                    merchant.DeveloperContact = new Contact
                    {
                        FirstName = payload.DeveloperFirstName,
                        LastName = payload.DeveloperLastName,
                        Phone = payload.DeveloperPhone,
                        Email = payload.DeveloperEmail
                    };
                }
                else if (!string.IsNullOrEmpty(payload.DeveloperEmail))
                {
                    merchantDeveloperContact.FirstName = payload.DeveloperFirstName;
                    merchantDeveloperContact.LastName = payload.DeveloperLastName;
                    merchantDeveloperContact.Phone = payload.DeveloperPhone;
                    merchantDeveloperContact.Email = payload.DeveloperEmail;
                }

                _context.Merchants.Update(merchant);
                var saveResult = await _context.SaveChangesAsync(token);
                if (saveResult == 0)
                    return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");

                await PublishUpdateMerchantEvent(merchant.Id, token);
                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");
            }
        }

        [HttpPut("{id:guid}/business-information")] // UPDATE
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Put([FromRoute] Guid id, BusinessInformationDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<MerchantController>(this, payload, _globalData);
            try
            {
                if (!HttpContext.IsUserInOneOfGroups(
                        SuperAdminGroups.SUPER_ADMIN,
                        SuperAdminGroups.PARTNER_ADMIN,
                        MerchantGroups.MERCHANT_ADMIN
                    ))
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                if (!ModelState.IsValid)
                    return ValidationProblem();

                var merchant = await _context.Merchants
                    .Include(x => x.Partner)
                    .Include(x => x.IntegrationPartner)
                    .Include(x => x.Account)
                    .Where(x => x.Id == id).SingleOrDefaultAsync(cancellationToken: token);

                if (merchant == null) return NotFound();

                _securityCheckService.EnsureHasAccessToMerchantUpdateApi(merchant.Id, GetMID(), merchant.PartnerId,
                    GetPID());

                merchant.CompanyName = payload.CompanyName;
                merchant.LegalEntityName = payload.LegalEntityName;
                merchant.Dba = payload.Dba;
                // merchant.PartnerId = payload.PartnerId;
                merchant.IntegrationPartnerId = payload.IntegrationPartnerId;
                merchant.SalesAgencyId = payload.SalesAgencyId;
                merchant.TaxId = payload.TaxId;
                merchant.Website = payload.Website;
                merchant.Industry = payload.Industry;
                merchant.Descriptor = payload.Descriptor;
                merchant.Type = payload.Type;
                merchant.EcommercePlatform = payload.EcommercePlatform;
                merchant.Mcc = payload.Mcc;
                merchant.Pcidss = payload.Pcidss;
                merchant.BusinessEstablishedDate = payload.BusinessEstablishedDate;
                merchant.IsIntegrationGuideEnabled = payload.IsIntegrationGuideEnabled;
                merchant.IsSiteValidated = payload.IsSiteValidated;
                merchant.ProductsSold = payload.ProductsSold;
                merchant.AnnualSalesVolume = payload.AnnualSalesVolume;
                
                if (payload.IntegrationPartnerParticipateSale.HasValue)
                {
                    merchant.IntegrationPartnerParticipateSale = payload.IntegrationPartnerParticipateSale.Value;
                }

                if (payload.PartnerId != null)
                {
                    merchant.PartnerId = payload.PartnerId;

                    if (merchant.Account != null)
                    {
                        merchant.Account.PartnerId = payload.PartnerId;
                    }
                    else
                    {
                        merchant.Account = new Account
                        {
                            PartnerId = payload.PartnerId,
                            IntegrationPartnerId = payload.IntegrationPartnerId,
                            Name = merchant.CompanyName,
                        };
                    }
                }

                if (payload.IntegrationPartnerId == null)
                {
                    merchant.IntegrationPartner = null;
                    merchant.IntegrationPartnerId = null;

                    if (merchant.Account != null)
                    {
                        merchant.Account.IntegrationPartnerId = null;
                    }
                }
                else
                {
                    merchant.IntegrationPartnerId = payload.IntegrationPartnerId;

                    if (merchant.Account != null)
                    {
                        merchant.Account.IntegrationPartnerId = payload.IntegrationPartnerId;
                    }
                    else
                    {
                        merchant.Account = new Account
                        {
                            IntegrationPartnerId = payload.IntegrationPartnerId,
                            PartnerId = payload.PartnerId,
                            Name = merchant.CompanyName,
                        };
                    }
                }

                if (payload.SalesAgencyId == null)
                    merchant.SalesAgency = null;

                if (payload.AccountId != null &&
                    merchant.AccountId == null) // Don't change accountId if it's already set
                    merchant.AccountId = null;

                _context.Merchants.Update(merchant);
                var saveResult = await _context.SaveChangesAsync(token);

                if (saveResult == 0)
                    return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");

                await PublishUpdateMerchantEvent(merchant.Id, token);
                return Ok();
            }
            catch (MerchantsSecurityException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");
            }
        }

        [HttpPut("{id:guid}/address")] // UPDATE
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Put([FromRoute] Guid id, AddressDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<MerchantController>(this, payload, _globalData);
            try
            {
                if (!HttpContext.IsUserInOneOfGroups(
                        SuperAdminGroups.SUPER_ADMIN,
                        SuperAdminGroups.PARTNER_ADMIN,
                        MerchantGroups.MERCHANT_ADMIN
                    ))
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                if (!ModelState.IsValid)
                    return ValidationProblem();

                var merchant = await _context.Merchants
                    .Include(x => x.Address)
                    .Where(x => x.Id == id).SingleOrDefaultAsync(cancellationToken: token);

                if (merchant == null) return NotFound();

                _securityCheckService.EnsureHasAccessToMerchantUpdateApi(merchant.Id, GetMID(), merchant.PartnerId,
                    GetPID());

                merchant.Address = _mapper.Map<AddressDTO, Address>(payload);

                _context.Merchants.Update(merchant);
                var saveResult = await _context.SaveChangesAsync(token);

                if (saveResult == 0)
                    return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");

                await PublishUpdateMerchantEvent(merchant.Id, token);
                return Ok();
            }
            catch (MerchantsSecurityException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");
            }
        }

        [HttpPut("{id:guid}/contact-information")] // UPDATE
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Put([FromRoute] Guid id, PrimaryContact payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<MerchantController>(this, payload, _globalData);
            try
            {
                if (!HttpContext.IsUserInOneOfGroups(
                        SuperAdminGroups.SUPER_ADMIN,
                        SuperAdminGroups.PARTNER_ADMIN,
                        MerchantGroups.MERCHANT_ADMIN
                    ))
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                if (!ModelState.IsValid)
                    return ValidationProblem();

                var merchant = await _context.Merchants
                    .Include(x => x.PrimaryContact)
                    .Where(x => x.Id == id).SingleOrDefaultAsync(cancellationToken: token);

                if (merchant == null) return NotFound();

                _securityCheckService.EnsureHasAccessToMerchantUpdateApi(merchant.Id, GetMID(), merchant.PartnerId,
                    GetPID());

                merchant.PrimaryContact = _mapper.Map<PrimaryContact, Contact>(payload);

                _context.Merchants.Update(merchant);
                var saveResult = await _context.SaveChangesAsync(token);

                if (saveResult == 0)
                    return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");

                await PublishUpdateMerchantEvent(merchant.Id, token);
                return Ok();
            }
            catch (MerchantsSecurityException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");
            }
        }

        [HttpPut("{id:guid}/bank")] // UPDATE
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Put([FromRoute] Guid id, BankAccountInformationDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<MerchantController>(this, payload, _globalData);
            try
            {
                if (!HttpContext.IsUserInOneOfGroups(
                        SuperAdminGroups.SUPER_ADMIN,
                        SuperAdminGroups.PARTNER_ADMIN,
                        MerchantGroups.MERCHANT_ADMIN
                    ))
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                if (!ModelState.IsValid)
                    return ValidationProblem();

                var merchant = await _context.Merchants
                    .Where(x => x.Id == id).SingleOrDefaultAsync(cancellationToken: token);

                if (merchant == null) return NotFound();

                _securityCheckService.EnsureHasAccessToMerchantUpdateApi(merchant.Id, GetMID(), merchant.PartnerId,
                    GetPID());

                merchant.DdaType = payload.DdaType;
                merchant.BankName = payload.BankName;
                merchant.Currency = payload.Currency;

                if (payload.AccountNumber != null)
                    merchant.AccountNumber = payload.AccountNumber;
                if (payload.RoutingNumber != null)
                    merchant.RoutingNumber = payload.RoutingNumber;
                if (payload.BankAccountVerified != null)
                    merchant.BankAccountVerified = payload.BankAccountVerified;

                _context.Merchants.Update(merchant);
                var saveResult = await _context.SaveChangesAsync(token);

                if (saveResult == 0)
                    return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");

                await PublishUpdateMerchantEvent(merchant.Id, token);
                return Ok();
            }
            catch (MerchantsSecurityException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");
            }
        }

        [HttpPut("{id:guid}/special-terms")] // UPDATE
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Put([FromRoute] Guid id, SpecialTermsDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<MerchantController>(this, payload, _globalData);
            try
            {
                if (!HttpContext.IsUserInOneOfGroups(
                        SuperAdminGroups.SUPER_ADMIN,
                        SuperAdminGroups.PARTNER_ADMIN,
                        MerchantGroups.MERCHANT_ADMIN
                    ))
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                if (!ModelState.IsValid)
                    return ValidationProblem();

                var merchant = await _context.Merchants
                    .Where(x => x.Id == id).SingleOrDefaultAsync(cancellationToken: token);

                if (merchant == null) return NotFound();

                _securityCheckService.EnsureHasAccessToMerchantUpdateApi(merchant.Id, GetMID(), merchant.PartnerId,
                    GetPID());

                merchant.SpecialTerms = payload.SpecialTerms;

                _context.Merchants.Update(merchant);
                var saveResult = await _context.SaveChangesAsync(token);

                if (saveResult == 0)
                    return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");

                await PublishUpdateMerchantEvent(merchant.Id, token);
                return Ok();
            }
            catch (MerchantsSecurityException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");
            }
        }

        [HttpPut("{id:guid}/configurations")] // UPDATE
        [Authorize(MyPolicies.ADMINS_AND_MERACHANTS_ADMINS)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Put([FromRoute] Guid id, MerchantConfigurationsDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<MerchantController>(this, payload, _globalData);
            try
            {
                if (!HttpContext.IsUserInOneOfGroups(
                        SuperAdminGroups.SUPER_ADMIN,
                        MerchantGroups.MERCHANT_ADMIN
                    ))
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                if (!ModelState.IsValid)
                    return ValidationProblem();

                var merchant = await _context.Merchants
                    .Where(x => x.Id == id).SingleOrDefaultAsync(cancellationToken: token);

                if (merchant == null) return NotFound();

                if (HttpContext.User.Claims.Any(x =>
                        x.Type == MyClaimTypes.COGNITO_GROUP && x.Value == SuperAdminGroups.MERCHANT_ADMIN) &&
                    merchant.Id != GetMID())
                {
                    return NotFound();
                }

                #region [Commented] Not used for now - can be requeired later

                // merchant.GhostModeThrottlePercentage = payload.GhostModeThrottlePercentage;
                // merchant.DynamicAuthorizationDiscountThrottlePercentage =
                //     payload.DynamicAuthorizationDiscountThrottlePercentage;
                // merchant.Offer_NSF_RequestsThrottle_Percentage = payload.Offer_NSF_RequestsThrottle_Percentage;

                #endregion

                merchant.OfferRequestsRateLimitIntervalMS = payload.OfferRequestsRateLimitIntervalMS;
                merchant.OfferRequestsMaxPerDay = payload.OfferRequestsMaxPerDay;
                merchant.OfferRequestsRateLimitCount = payload.OfferRequestsRateLimitCount;
                merchant.OfferRequestsThrottlePercentage = payload.OfferRequestsThrottlePercentage;
                merchant.Orders_MaxMonthlyAmount = payload.Orders_MaxMonthlyAmount;
                merchant.MinOrderAmount = Utils.Formatters.DecimalToInt(payload.MinOrderAmount);
                merchant.MaxOrderAmount = Utils.Formatters.DecimalToInt(payload.MaxOrderAmount);

                if (payload.RiskLevel != null)
                {
                    merchant.RiskLevel = payload.RiskLevel;
                }

                if (payload.RiskLevel_Visa != null)
                {
                    merchant.RiskLevel_Visa = payload.RiskLevel_Visa;
                }

                if (payload.RiskTier != null)
                {
                    merchant.RiskTier = payload.RiskTier;
                }

                _context.Merchants.Update(merchant);
                var saveResult = await _context.SaveChangesAsync(token);

                if (saveResult == 0)
                    return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");

                await PublishUpdateMerchantEvent(merchant.Id, token);
                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");
            }
        }

        [HttpPut("{id:guid}/general")] // UPDATE
        [Authorize(MyPolicies.ADMINS_AND_MERACHANTS_ADMINS)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(void), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Put([FromRoute] Guid id, MerchantGeneralSettingsDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<MerchantController>(this, payload, _globalData);
            try
            {
                if (!HttpContext.IsUserInOneOfGroups(
                        SuperAdminGroups.SUPER_ADMIN,
                        MerchantGroups.MERCHANT_ADMIN
                    ))
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                if (!ModelState.IsValid)
                    return ValidationProblem();

                var merchant = await _context.Merchants
                    .Where(x => x.Id == id).SingleOrDefaultAsync(cancellationToken: token);

                if (merchant == null) return NotFound();

                if (HttpContext.User.Claims.Any(x =>
                        x.Type == MyClaimTypes.COGNITO_GROUP && x.Value == SuperAdminGroups.MERCHANT_ADMIN) &&
                    merchant.Id != GetMID())
                {
                    return NotFound();
                }

                merchant.Locked = payload.Locked;
                merchant.PayoutsEnabled = payload.PayoutsEnabled;
                merchant.IsMitEnabled = payload.IsMitEnabled;

                merchant.MITEvaluateAsync = payload.IsMITEvaluateAsync;
                merchant.CITEvaluateAsync = payload.IsCITEvaluateAsync;

                merchant.VirtualTerminalEnabled = payload.VirtualTerminalEnabled;
                merchant.BillingInformationOptional = payload.BillingInformationOptional;
                merchant.MITGetSiteByDynamicDescriptorEnabled = payload.MITGetSiteByDynamicDescriptorEnabled;
                merchant.MITConsumerNotificationsEnabled = payload.MITConsumerNotificationsEnabled;
                merchant.MITConsumerCuresEnabled = payload.MITConsumerCuresEnabled;
                merchant.UIWidgetOptional = payload.UIWidgetOptional;
                merchant.CITConsumerNotificationsEnabled = payload.CITConsumerNotificationsEnabled;

                merchant.CITClickToRefundEnabled = payload.CITClickToRefundEnabled;
                merchant.MITClickToRefundEnabled = payload.MITClickToRefundEnabled;
                // merchant.MITAgreedExpiryHours = payload.MITAgreedExpiryHours;
                merchant.UseDefaultSiteForUnknownMerchantUrlsEnabled =
                    payload.UseDefaultSiteForUnknownMerchantUrlsEnabled;
                merchant.IsSenseJsOptional = payload.IsSenseJsOptional;

                merchant.AllowBinCheckOnTokenization = payload.AllowBinCheckOnTokenization;
                merchant.EnableGlobalNetworkTokenization = payload.EnableGlobalNetworkTokenization;

                merchant.AccountUpdaterEnabled = payload.AccountUpdaterEnabled;

                merchant.IsCrawlingEnabled = payload.IsCrawlingEnabled;
                merchant.IsIframeMessagesCollectEnabled = payload.IsIframeMessagesCollectEnabled;
                merchant.IsEnforceMFAEnabled = payload.IsEnforceMFAEnabled;

                merchant.CaptureRequired = payload.CaptureRequired;

                merchant.Global3DSEnabled = payload.Global3DSEnabled;
                merchant.InformationalOnly3DS = payload.InformationalOnly3DS;

                merchant.ConsumerOrderNotificationChannel = payload.ConsumerOrderNotificationChannel;

                merchant.SchemeTransactionIdEnabled = payload.SchemeTransactionIdEnabled;
                merchant.MITImmediateRetryEnabled = payload.MITImmediateRetryEnabled;

                merchant.IsAvsRequired = payload.IsAvsRequired;
                merchant.IsCvvRequired = payload.IsCvvRequired;

                merchant.IgnoreSiteIdFromClient = payload.IgnoreSiteIdFromClient;

                merchant.PayerEnabled = payload.PayerEnabled;
                merchant.RedactIpEnabled = payload.RedactIpEnabled;

                if (payload.SupportedCountries != null)
                {
                    merchant.SupportedCountries = JoinSupportedCountriesList(payload.SupportedCountries);
                }

                _context.Merchants.Update(merchant);
                var saveResult = await _context.SaveChangesAsync(token);

                if (saveResult == 0)
                    return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");

                await PublishUpdateMerchantEvent(merchant.Id, token);
                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");
            }
        }

        [HttpPut("{id:guid}/partners-general")] // UPDATE
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdatePartnersMerchantGeneral([FromRoute] Guid id,
            MerchantGeneralBaseSettingsDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<MerchantController>(this, payload, _globalData);
            try
            {
                if (!HttpContext.IsUserInOneOfGroups(
                        SuperAdminGroups.SUPER_ADMIN,
                        SuperAdminGroups.PARTNER_ADMIN
                    ))
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                if (!ModelState.IsValid)
                    return ValidationProblem();

                var merchant = await _context.Merchants
                    .Where(x => x.Id == id).SingleOrDefaultAsync(cancellationToken: token);

                if (merchant == null) return NotFound();

                if (HttpContext.User.Claims.Any(x =>
                        x.Type == MyClaimTypes.COGNITO_GROUP && x.Value == SuperAdminGroups.PARTNER_ADMIN) &&
                    merchant.PartnerId != GetPID())
                {
                    return NotFound();
                }

                merchant.Locked = payload.Locked;
                merchant.PayoutsEnabled = payload.PayoutsEnabled;

                _context.Merchants.Update(merchant);
                var saveResult = await _context.SaveChangesAsync(token);

                if (saveResult == 0)
                    return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");

                await PublishUpdateMerchantEvent(merchant.Id, token);
                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");
            }
        }

        [HttpPut("{id:guid}/fees")] // UPDATE
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Put([FromRoute] Guid id, MerchantFeesDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<MerchantController>(this, payload, _globalData);
            try
            {
                if (!HttpContext.IsUserInOneOfGroups(
                        SuperAdminGroups.SUPER_ADMIN,
                        SuperAdminGroups.PARTNER_ADMIN,
                        MerchantGroups.MERCHANT_ADMIN
                    ))
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                if (!ModelState.IsValid)
                    return ValidationProblem();

                var merchant = await _context.Merchants
                    .Include(x => x.Fees)
                    .Where(x => x.Id == id).SingleOrDefaultAsync(cancellationToken: token);

                if (merchant == null) return NotFound();

                _securityCheckService.EnsureHasAccessToMerchantUpdateApi(merchant.Id, GetMID(), merchant.PartnerId,
                    GetPID());

                foreach (var fee in payload.Fees)
                {
                    //Add new fee
                    var dbFee = merchant.Fees.Find(x => x.Id == fee.Id);

                    if (dbFee == null)
                    {
                        var defaultFee =
                            await _context.FeeConfigurations.SingleOrDefaultAsync(x => x.Id == fee.Id,
                                cancellationToken: token);
                        var newFee = new Entities.MerchantFee
                        {
                            Name = defaultFee.Name,
                            Type = defaultFee.Type,
                            ChargeType = defaultFee.ChargeType,
                            Amount = fee.Amount,
                            IsActive = true,
                            MinimumFeeAmount = fee.MinimumFeeAmount,
                            RelatedMerchant = merchant
                        };
                        await _context.MerchantFees.AddAsync(newFee, token);
                    }
                    else if (dbFee.IsActive)
                    {
                        dbFee.IsActive = false;
                        dbFee.IsDeleted = true;

                        var updatedFee = new Entities.MerchantFee
                        {
                            Name = dbFee.Name,
                            Type = dbFee.Type,
                            ChargeType = dbFee.ChargeType,
                            Amount = fee.Amount != dbFee.Amount ? fee.Amount : dbFee.Amount,
                            MinimumFeeAmount = fee.MinimumFeeAmount != dbFee.MinimumFeeAmount
                                ? fee.MinimumFeeAmount
                                : dbFee.MinimumFeeAmount,
                            IsActive = true,
                            RelatedMerchant = dbFee.RelatedMerchant
                        };
                        await _context.MerchantFees.AddAsync(updatedFee, token);
                    }
                }

                _context.Merchants.Update(merchant);
                var saveResult = await _context.SaveChangesAsync(token);

                if (saveResult == 0)
                    return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");

                await PublishUpdateMerchantEvent(merchant.Id, token);
                return Ok();
            }
            catch (MerchantsSecurityException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");
            }
        }

        [HttpPut("{id:guid}/preferences")] // UPDATE
        [Authorize(MyPolicies.ADMINS_AND_MERACHANTS_ADMINS)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Put([FromRoute] Guid id, MerchantPreferencesDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<MerchantController>(this, payload, _globalData);
            try
            {
                if (!HttpContext.IsUserInOneOfGroups(
                        SuperAdminGroups.SUPER_ADMIN,
                        MerchantGroups.MERCHANT_ADMIN
                    ))
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                if (!ModelState.IsValid)
                    return ValidationProblem();

                var merchant = await _context.Merchants
                    .Include(x => x.Fees)
                    .Where(x => x.Id == id).SingleOrDefaultAsync(cancellationToken: token);

                if (merchant == null) return NotFound();

                if (HttpContext.User.Claims.Any(x =>
                        x.Type == MyClaimTypes.COGNITO_GROUP && x.Value == SuperAdminGroups.MERCHANT_ADMIN) &&
                    merchant.Id != GetMID())
                {
                    return NotFound();
                }

                merchant.Timezone = payload.Timezone;
                merchant.TimezoneName = payload.TimezoneName;
                merchant.Language = payload.Language;

                _context.Merchants.Update(merchant);
                var saveResult = await _context.SaveChangesAsync(token);

                if (saveResult == 0)
                    return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");

                await PublishUpdateMerchantEvent(merchant.Id, token);
                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");
            }
        }

        [HttpPut("{id:guid}/product")] // UPDATE
        [Authorize(MyPolicies.ADMINS_AND_MERACHANTS_ADMINS)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateProduct([FromRoute] Guid id, ProductUpdateDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<MerchantController>(this, payload, _globalData);
            try
            {
                if (!HttpContext.IsUserInOneOfGroups(
                        SuperAdminGroups.SUPER_ADMIN,
                        MerchantGroups.MERCHANT_ADMIN
                    ))
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                if (!ModelState.IsValid)
                    return ValidationProblem();

                var merchant = await _context.Merchants
                    .Where(x => x.Id == id).SingleOrDefaultAsync(cancellationToken: token);

                if (merchant == null) return NotFound();

                if (HttpContext.IsUserInGroup(SuperAdminGroups.MERCHANT_ADMIN) &&
                    merchant.Id != GetMID())
                {
                    return NotFound();
                }

                merchant.ProductsSold = payload.ProductsSold;

                _context.Merchants.Update(merchant);
                var saveResult = await _context.SaveChangesAsync(token);

                if (saveResult == 0)
                    return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");

                await PublishUpdateMerchantEvent(merchant.Id, token);
                return Ok();
            }
            catch (MerchantsSecurityException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");
            }
        }

        #region Sites

        [HttpGet("{id:guid}/sites")] // GET ALL
        [ProducesResponseType(typeof(PagedDTO<Merchant>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetSites(Guid id)
        {
            using var workspan = Workspan.Start<MerchantController>();
            try
            {
                workspan.Log.Information("ENTERED: {GlobalDataName} => {RequestMethod} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method,
                    HttpContext.Request.Path + HttpContext.Request.QueryString);

                IQueryable<Site> sites;

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    sites = _context.Sites
                        .Include(c => c.WhitelistedUrls)
                        .Where(x => x.MerchantId == id);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    sites = _context.Sites
                        .Include(c => c.WhitelistedUrls)
                        .Where(x => x.MerchantId == id && x.Merchant.PartnerId == GetPID());
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    sites = _context.Sites
                        .Include(c => c.WhitelistedUrls)
                        .Where(x => x.MerchantId == id && x.Merchant.IntegrationPartnerId == GetPID());
                }
                else
                {
                    sites = _context.Sites
                        .Include(c => c.WhitelistedUrls)
                        .Where(x => x.MerchantId == GetMID());
                }

                var sitesList = await sites
                    .OrderByDescending(x => x.ModifiedOn)
                    .Select(x => new SiteDTO
                    {
                        Id = x.Id,
                        Name = x.Name,
                        Descriptor = x.Descriptor,
                        DescriptorCity = x.DescriptorCity,
                        CustomerSupportName = x.CustomerSupportName,
                        CustomerSupportEmail = x.CustomerSupportEmail,
                        CustomerSupportPhone = x.CustomerSupportPhone,
                        CustomerSupportLink = x.CustomerSupportLink,
                        WhitelistedUrls = x.WhitelistedUrls.Select(y => y.Link),
                        Tags = x.Tags != null ? JsonConvert.DeserializeObject<List<string>>(x.Tags) : null,
                    }).ToListAsync();

                workspan.Log.Information("EXIT: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return Ok(sitesList);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }

        [HttpGet("{id:guid}/sites/{siteId:guid}")]
        [ProducesResponseType(typeof(PagedDTO<Merchant>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Get(Guid id, Guid siteId)
        {
            using var workspan = Workspan.Start<MerchantController>();
            try
            {
                workspan.Log.Information("ENTERED: {GlobalDataName} => {RequestMethod} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method,
                    HttpContext.Request.Path + HttpContext.Request.QueryString);

                IQueryable<Site> site;
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    site = _context.Sites.Where(x => x.MerchantId == id && x.Id == siteId);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    site = _context.Sites.Where(x => x.Merchant.PartnerId == GetPID() && x.Id == siteId);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    site = _context.Sites.Where(x => x.Merchant.IntegrationPartnerId == GetPID() && x.Id == siteId);
                }
                else
                {
                    site = _context.Sites.Where(x => x.MerchantId == GetMID() && x.Id == siteId);
                }

                var siteDto = await site.Select(x => new SiteDTO()
                {
                    Id = x.Id,
                    Name = x.Name,
                    Descriptor = x.Descriptor,
                    DescriptorCity = x.DescriptorCity,
                    CustomerSupportName = x.CustomerSupportName,
                    CustomerSupportEmail = x.CustomerSupportEmail,
                    CustomerSupportPhone = x.CustomerSupportPhone,
                    CustomerSupportLink = x.CustomerSupportLink,
                    WhitelistedUrls = x.WhitelistedUrls.Select(y => y.Link),
                    Tags = x.Tags != null ? JsonConvert.DeserializeObject<List<string>>(x.Tags) : null,
                }).SingleOrDefaultAsync();


                if (siteDto == null)
                    return NotFound("Site not found");

                workspan.Log.Information("EXIT: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return Ok(siteDto);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }

        [HttpPut("{mid:guid}/sites/{siteId:guid}")] // UPDATE
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(SiteDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateSite(Guid mid, Guid siteId, SiteDTO siteToUpdate)
        {
            using var workspan = Workspan.Start<MerchantController>();
            try
            {
                workspan.Log.Information("ENTERED: {GlobalDataName} => {RequestMethod} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method,
                    HttpContext.Request.Path + HttpContext.Request.QueryString);

                if (!HttpContext.IsUserInOneOfGroups(
                        SuperAdminGroups.SUPER_ADMIN,
                        SuperAdminGroups.PARTNER_ADMIN
                    ))
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                if (siteToUpdate.Tags != null)
                {
                    var existingTags = new Dictionary<string, string>();
                    var merchantSites =
                        await _context.Sites.Where(x => x.MerchantId == mid && x.Id != siteId).ToListAsync();

                    foreach (var merchantSite in merchantSites)
                    {
                        if (merchantSite.Tags != null)
                        {
                            var tags = JsonConvert.DeserializeObject<List<string>>(merchantSite.Tags);
                            foreach (var tag in tags)
                            {
                                if (siteToUpdate.Tags.Contains(tag) && !existingTags.ContainsKey(tag))
                                    existingTags.Add(tag, merchantSite.Name);
                            }
                        }
                    }

                    if (existingTags.Count > 0)
                    {
                        string alreadyExistingTags =
                            string.Join(", ", existingTags.Select(x => $"{x.Key} ({x.Value})"));
                        ModelState.AddModelError("Tags", $"Tags already exist: {alreadyExistingTags}");

                        return ValidationProblem();
                    }
                }

                var site = await _context.Sites
                    .Include(x => x.Merchant)
                    .Include(x => x.WhitelistedUrls)
                    .SingleOrDefaultAsync(x => x.MerchantId == mid && x.Id == siteId);

                if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    var partnerId = GetPID();

                    if (partnerId == null || partnerId == Guid.Empty)
                    {
                        return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                    }

                    var isIntegrationPartner = HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN);

                    if (site == null || (!isIntegrationPartner && site.Merchant?.PartnerId != partnerId) ||
                        (isIntegrationPartner && site.Merchant?.IntegrationPartnerId != partnerId))
                    {
                        return NotFound("Site not found");
                    }
                }

                if (site == null)
                    return NotFound("Site not found");

                if (!HttpContext.IsUserInGroup(SuperAdminGroups.MERCHANT_ADMIN) && siteToUpdate.State != null)
                {
                    site.State = siteToUpdate.State;
                }

                site.Name = siteToUpdate.Name;
                site.Descriptor = siteToUpdate.Descriptor;
                site.DescriptorCity = siteToUpdate.DescriptorCity;
                site.CustomerSupportEmail = siteToUpdate.CustomerSupportEmail;
                site.CustomerSupportLink = siteToUpdate.CustomerSupportLink;
                site.CustomerSupportName = siteToUpdate.CustomerSupportName;
                site.CustomerSupportPhone = siteToUpdate.CustomerSupportPhone;

                if (site.WhitelistedUrls.Any())
                {
                    foreach (var url in siteToUpdate.WhitelistedUrls)
                    {
                        if (site.WhitelistedUrls.Exists(x => x.Link == url)) continue;

                        var item = new SiteWhitelistedUrl
                        {
                            Link = url
                        };
                        site.WhitelistedUrls.Add(item);
                    }

                    foreach (var whitelistedUrls in site.WhitelistedUrls.Where(whitelistedUrls =>
                                 siteToUpdate.WhitelistedUrls.All(x => x != whitelistedUrls.Link)))
                    {
                        _context.SiteWhitelistedUrls.Remove(whitelistedUrls);
                    }
                }
                else
                {
                    foreach (var whitelistedUrl in siteToUpdate.WhitelistedUrls)
                    {
                        var item = new SiteWhitelistedUrl
                        {
                            Link = whitelistedUrl
                        };

                        site.WhitelistedUrls.Add(item);
                    }
                }

                if (siteToUpdate.Tags != null)
                {
                    var newTags = new List<string>();
                    foreach (var tag in siteToUpdate.Tags)
                    {
                        if (!newTags.Contains(tag))
                            newTags.Add(tag);
                    }

                    site.Tags = JsonConvert.SerializeObject(newTags);
                }
                else
                {
                    site.Tags = null;
                }

                _context.Sites.Update(site);
                await _context.SaveChangesAsync();

                await _publisher.Publish<MerchantSiteUpdatedEvent>(new
                {
                    Mid = mid,
                    SiteId = site.Id,
                    Name = site.Name,
                    Descriptor = site.Descriptor,
                    DescriptorCity = site.DescriptorCity,
                    CustomerSupportName = site.CustomerSupportName,
                    CustomerSupportEmail = site.CustomerSupportEmail,
                    CustomerSupportPhone = site.CustomerSupportPhone,
                    CustomerSupportLink = site.CustomerSupportLink,
                    WhitelistedUrls = siteToUpdate.WhitelistedUrls,
                    Tags = siteToUpdate.Tags,
                    State = siteToUpdate.State,
                });

                workspan.Log.Information("EXIT: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return Ok(siteToUpdate);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed update");
            }
        }

        [HttpPost("sites")]
        [Authorize(MyPolicies.MERCHANTS)]
        [ProducesResponseType(typeof(SiteDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> MerchantsInsertSite(MerchantCreateSiteDTO payload)
        {
            using var workspan = Workspan.Start<MerchantController>()
                .Baggage("mid", GetMID());
            try
            {
                workspan.Log.Information("ENTERED: {GlobalDataName} => {RequestMethod} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method,
                    HttpContext.Request.Path + HttpContext.Request.QueryString);

                if (!ModelState.IsValid)
                    return ValidationProblem();

                var newSite = new Site
                {
                    MerchantId = GetMID(),
                    State = "Pending",
                    CustomerSupportLink = payload.CustomerSupportLink,
                };

                var site = _context.Sites.Add(newSite);

                await _context.SaveChangesAsync();

                await _publisher.Publish<MerchantSiteCreatedEvent>(new
                {
                    Mid = GetMID(),
                    SiteId = site.Entity.Id,
                    State = site.Entity.State,
                    CustomerSupportLink = site.Entity.CustomerSupportLink,
                }, CancellationToken.None);

                workspan.Log.Information("EXIT: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return Ok(site.Entity.Id);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed insert");
            }
        }

        [HttpPost("{mid:guid}/sites")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(SiteDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> InsertSite(Guid mid, SiteDTO payload)
        {
            using var workspan = Workspan.Start<MerchantController>()
                .Baggage(nameof(mid), mid)
                .Baggage("pid", GetPID());
            try
            {
                workspan.Log.Information("ENTERED: {GlobalDataName} => {RequestMethod} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method,
                    HttpContext.Request.Path + HttpContext.Request.QueryString);

                if (!HttpContext.IsUserInOneOfGroups(
                        SuperAdminGroups.SUPER_ADMIN,
                        SuperAdminGroups.PARTNER_ADMIN
                    ))
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                if (!ModelState.IsValid)
                    return ValidationProblem();

                payload.WhitelistedUrls = payload.WhitelistedUrls.DistinctBy(x => x.Host.Trim().ToLowerInvariant());

                if (payload.Tags != null)
                {
                    var existingTags = new Dictionary<string, string>();
                    var merchantSites = await _context.Sites.Where(x => x.MerchantId == mid).ToListAsync();

                    foreach (var merchantSite in merchantSites)
                    {
                        if (merchantSite.Tags != null)
                        {
                            var tags = JsonConvert.DeserializeObject<List<string>>(merchantSite.Tags);
                            foreach (var tag in tags)
                            {
                                if (payload.Tags.Contains(tag) && !existingTags.ContainsKey(tag))
                                    existingTags.Add(tag, merchantSite.Name);
                            }
                        }
                    }

                    if (existingTags.Count > 0)
                    {
                        string alreadyExistingTags =
                            string.Join(", ", existingTags.Select(x => $"{x.Key} ({x.Value})"));
                        ModelState.AddModelError("Tags", $"Tags already exist: {alreadyExistingTags}");

                        return ValidationProblem();
                    }
                }

                var site = _context.Sites.Add(new Site()
                {
                    MerchantId = mid,
                    Name = payload.Name,
                    Descriptor = payload.Descriptor,
                    DescriptorCity = payload.DescriptorCity,
                    CustomerSupportName = payload.CustomerSupportName,
                    CustomerSupportEmail = payload.CustomerSupportEmail,
                    CustomerSupportPhone = payload.CustomerSupportPhone,
                    CustomerSupportLink = payload.CustomerSupportLink,
                    WhitelistedUrls = new List<SiteWhitelistedUrl>(payload.WhitelistedUrls.Select(x =>
                        new SiteWhitelistedUrl
                        {
                            Link = x
                        })),
                    Tags = payload.Tags != null ? JsonConvert.SerializeObject(payload.Tags) : null,
                    State = "Approved"
                });

                await _context.SaveChangesAsync();

                await _publisher.Publish<MerchantSiteCreatedEvent>(new
                {
                    Mid = mid,
                    SiteId = site.Entity.Id,
                    Name = payload.Name,
                    Descriptor = payload.Descriptor,
                    DescriptorCity = payload.DescriptorCity,
                    CustomerSupportName = payload.CustomerSupportName,
                    CustomerSupportEmail = payload.CustomerSupportEmail,
                    CustomerSupportPhone = payload.CustomerSupportPhone,
                    CustomerSupportLink = payload.CustomerSupportLink,
                    WhitelistedUrls = payload.WhitelistedUrls,
                    Tags = payload.Tags,
                    State = "Approved"
                }, CancellationToken.None);

                workspan.Log.Information("EXIT: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return Ok(site.Entity.Id);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed insert");
            }
        }

        [HttpDelete("{id:guid}/sites/{siteId:guid}")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Delete([FromRoute] Guid id, [FromRoute] Guid siteId, CancellationToken token)
        {
            using var workspan = Workspan.Start<MerchantController>();
            try
            {
                workspan.Log.Information(
                    "ENTERED: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString} ",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);

                var site = await _context.Sites
                    .Include(x => x.Merchant)
                    .SingleOrDefaultAsync(x => x.MerchantId == id && x.Id == siteId,
                        cancellationToken: token) ?? throw new InvalidOperationException();

                if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    var partnerId = GetPID();

                    if (partnerId == null || partnerId == Guid.Empty)
                    {
                        return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                    }

                    var isIntegrationPartner = HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN);

                    if ((!isIntegrationPartner && site.Merchant.PartnerId != partnerId) ||
                        (isIntegrationPartner && site.Merchant.IntegrationPartnerId != partnerId))
                    {
                        return NotFound("Site not found");
                    }
                }

                _context.Sites.Remove(site);
                await _context.SaveChangesAsync(token);

                await _publisher.Publish<MerchantSiteDeletedEvent>(new
                {
                    Mid = id,
                    SiteId = siteId
                }, CancellationToken.None);

                workspan.Log.Information("EXIT: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return Ok();
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed deleting site");
            }
        }

        #endregion

        [HttpPost("widget-validation")]
        [Authorize(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Validate([FromBody] SenseJsValidationDTO payload, CancellationToken token)
        {
            using var workspan = Workspan.Start<MerchantController>();
            try
            {
                workspan.Log.Information(
                    "ENTERED: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString} ",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);

                if (!ModelState.IsValid)
                    return ValidationProblem();

                using var client = new HttpClient();
                using var result = await client.GetAsync(payload.Url);
                var statusCode = result.IsSuccessStatusCode;

                if (!statusCode)
                {
                    return BadRequest("Site can't be reached");
                }

                var html = await client.GetStringAsync(payload.Url);

                var doc = new HtmlDocument();
                doc.LoadHtml(html);

                var response = new SenseJsValidationResponseDTO();
                var scripts = doc.DocumentNode.SelectNodes("//script[not(@src)]");

                if (scripts == null)
                {
                    return Ok(response);
                }

                foreach (var script in scripts)
                {
                    if (script.InnerText.Contains(Environment.GetEnvironmentVariable("URL_SENSE_JS")) &&
                        script.InnerText.Contains(payload.Mid.ToString()) &&
                        script.InnerText.Contains(payload.SiteId.ToString()))
                    {
                        response.IsSenseJsExists = true;
                    }

                    if (script.InnerText.Contains(Environment.GetEnvironmentVariable("URL_UI_WIDGET")) &&
                        script.InnerText.Contains(payload.Mid.ToString()) &&
                        script.InnerText.Contains(payload.SiteId.ToString()))
                    {
                        response.IsSenseUIWidgetExists = true;
                    }

                    if (script.InnerText.Contains("fc.setOrderId"))
                    {
                        response.IsSetOrderIdCallExists = true;
                    }

                    if (script.InnerText.Contains("UIWidget.InitUi"))
                    {
                        response.IsInitUiCallExists = true;
                    }
                }

                workspan.Log.Information("EXIT: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return Ok(response);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                if (e.Message.Contains("nodename nor servname provided, or not known") ||
                    e.Message.Contains("Name or service not known"))
                {
                    return BadRequest("Site not found");
                }

                return StatusCode(StatusCodes.Status500InternalServerError,
                    "Failed updating site senseJs validation");
            }
        }

        [HttpPut("{id:guid}/owners")] // UPDATE
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateOwner([FromRoute] Guid id, MerchantOwnersDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<MerchantController>(this, payload, _globalData);
            try
            {
                if (!HttpContext.IsUserInOneOfGroups(
                        SuperAdminGroups.SUPER_ADMIN,
                        SuperAdminGroups.PARTNER_ADMIN,
                        MerchantGroups.MERCHANT_ADMIN
                    ))
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                if (!ModelState.IsValid)
                    return ValidationProblem();

                var merchant = await _context.Merchants
                    .Include(x => x.Owners)
                    .ThenInclude(x => x.Address)
                    .Where(x => x.Id == id).SingleOrDefaultAsync(cancellationToken: token);

                if (merchant == null) return NotFound();

                if (HttpContext.IsUserInGroup(SuperAdminGroups.MERCHANT_ADMIN) && merchant.Id != GetMID() ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) && merchant.PartnerId != GetPID() ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN) &&
                    merchant.IntegrationPartnerId != GetPID())
                {
                    return NotFound();
                }

                var newOwners = new List<Owner>();
                var sumOfPercentOwnership = 0; // Sum of all percent ownerships except the one we are updating

                foreach (var owner in merchant.Owners)
                {
                    if (owner.Id == payload.Id)
                    {
                        owner.FirstName = payload.FirstName;
                        owner.LastName = payload.LastName;
                        owner.Email = payload.Email;
                        owner.Phone = payload.Phone;
                        owner.DateOfBirth = payload.DateOfBirth;
                        owner.Address = payload.Address;
                        owner.Title = payload.Title;
                        owner.PercentOwnership = payload.PercentOwnership;
                        owner.PrincipalType = payload.PrincipalType;
                        owner.IndividualFormOfID = payload.IndividualFormOfID;
                        owner.IdentificationNumber = payload.IdentificationNumber;
                    }
                    else
                    {
                        sumOfPercentOwnership += owner.PercentOwnership;
                    }

                    newOwners.Add(owner);
                }

                if (newOwners.Sum(x => x.PercentOwnership) > 100)
                {
                    var response = new MerchantResponse();
                    response.AddError($"For this owner, the percentage cannot exceed {100 - sumOfPercentOwnership}",
                        "PercentOwnership",
                        "general");
                    return ReturnResponse(response);
                }

                merchant.Owners = newOwners;

                _context.Merchants.Update(merchant);
                var saveResult = await _context.SaveChangesAsync(token);

                if (saveResult == 0)
                    return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");

                await PublishUpdateMerchantEvent(merchant.Id, token);
                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");
            }
        }

        [HttpPost("{id:guid}/owners")] // CREATE
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS)]
        [ProducesResponseType(typeof(MerchantResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> CreateOwner([FromRoute] Guid id, MerchantOwnersDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<MerchantController>(this, payload, _globalData);

            try
            {
                if (!ModelState.IsValid)
                    return ValidationProblem();

                var merchant = await _context.Merchants
                    .Include(x => x.Owners)
                    .ThenInclude(x => x.Address)
                    .Where(x => x.Id == id).SingleOrDefaultAsync(cancellationToken: token);

                if (merchant == null) return NotFound();

                if (HttpContext.IsUserInGroup(SuperAdminGroups.MERCHANT_ADMIN) && merchant.Id != GetMID() ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) && merchant.PartnerId != GetPID() ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN) &&
                    merchant.IntegrationPartnerId != GetPID())
                {
                    return NotFound();
                }

                if (merchant.Owners.Sum(x => x.PercentOwnership) + payload.PercentOwnership > 100)
                {
                    var response = new MerchantResponse();
                    response.AddError(
                        $"For this owner, the percentage cannot exceed {100 - merchant.Owners.Sum(x => x.PercentOwnership)}",
                        "PercentOwnership",
                        "general");
                    return ReturnResponse(response);
                }

                var newOwner = new Owner
                {
                    Title = payload.Title,
                    FirstName = payload.FirstName,
                    LastName = payload.LastName,
                    Email = payload.Email,
                    Phone = payload.Phone,
                    DateOfBirth = payload.DateOfBirth,
                    Address = payload.Address,
                    PercentOwnership = payload.PercentOwnership,
                    PrincipalType = payload.PrincipalType,
                    IndividualFormOfID = payload.IndividualFormOfID,
                    IdentificationNumber = payload.IdentificationNumber
                };

                merchant.Owners.Add(newOwner);

                _context.Merchants.Update(merchant);
                var saveResult = await _context.SaveChangesAsync(token);

                if (saveResult == 0)
                    return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");

                await PublishUpdateMerchantEvent(merchant.Id, token);
                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating merchant");
            }
        }

        [HttpDelete("{id:guid}/owners/{ownerId:guid}")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteOwner([FromRoute] Guid id, [FromRoute] Guid ownerId,
            CancellationToken token)
        {
            using var workspan = Workspan.Start<MerchantController>();

            try
            {
                var merchant = await _context.Merchants
                    .Include(x => x.Owners)
                    .ThenInclude(x => x.Address)
                    .Where(x => x.Id == id).SingleOrDefaultAsync(cancellationToken: token);

                if (merchant == null) return NotFound();

                if (HttpContext.IsUserInGroup(SuperAdminGroups.MERCHANT_ADMIN) && merchant.Id != GetMID() ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) && merchant.PartnerId != GetPID() ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN) &&
                    merchant.IntegrationPartnerId != GetPID())
                {
                    return NotFound("Merchant not found");
                }

                var owner = merchant.Owners.SingleOrDefault(x => x.Id == ownerId);

                if (owner == null)
                    return NotFound("Owner not found");

                merchant.Owners.Remove(owner);

                _context.Merchants.Update(merchant);
                await _context.SaveChangesAsync(token);

                await PublishUpdateMerchantEvent(merchant.Id, token);

                return Ok();
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed deleting owner");
            }
        }

        [HttpPut("{id:guid}/documents")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateDocuments([FromRoute] Guid id,
            [FromForm] string documents,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, null, _globalData);
            try
            {
                var merchant = await _context.Merchants
                    .Include(x => x.Partner)
                    .ThenInclude(x => x.Address).Include(application => application.PrimaryContact)
                    .Include(x => x.IntegrationPartner)
                    .Include(x => x.Documents)
                    .SingleOrDefaultAsync(x => x.Id == id, token);

                if (merchant == null)
                {
                    return NotFound("Merchant not found.");
                }

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN) ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) && merchant.PartnerId == GetPID() ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.MERCHANT_ADMIN) && merchant.Id == GetMID())
                {
                    var request = JsonConvert.DeserializeObject<DocumentsUpdateDTO>(documents);
                    var files = new List<IFormFile>();
                    foreach (IFormFile file in Request.Form.Files)
                    {
                        files.Add(file);
                    }

                    await using var transaction = await _context.Database.BeginTransactionAsync();

                    // Remove unused documents
                    var documentIds = request.Documents.Select(d => d.Id).ToList();
                    var documentsToRemove = merchant.Documents.Where(d => !documentIds.Contains(d.Id)).ToList();
                    _context.Documents.RemoveRange(documentsToRemove);

                    // Add new documents from IFormFile list
                    foreach (var file in files)
                    {
                        await _merchantService.StoreDocumentAsync(merchant, file);
                    }

                    await _context.SaveChangesAsync(token);

                    await transaction.CommitAsync();
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                return Ok("Documents updated successfully.");
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed to update documents");
            }
        }

        private async Task PublishUpdateMerchantEvent(Guid mid, CancellationToken token)
        {
            var merchant = await _context.Merchants
                .Include(x => x.Address)
                .Include(x => x.Fees)
                .Include(x => x.PrimaryContact)
                .Include(x => x.DeveloperContact)
                .Include(x => x.Partner)
                .Include(x => x.SalesAgency)
                .Where(x => x.Id == mid).SingleOrDefaultAsync(cancellationToken: token);


            await _publisher.Publish(MerchantUpdateEventsFactory.ConstructMerchantUpdatedEvent(merchant, _mapper),
                token);
        }

        public static string JoinSupportedCountriesList(List<string> supportedCountries)
        {
            return string.Join(",", supportedCountries);
        }
    }
}