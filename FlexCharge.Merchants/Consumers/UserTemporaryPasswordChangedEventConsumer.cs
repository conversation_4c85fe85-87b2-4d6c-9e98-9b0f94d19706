using System;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Merchants.Entities;
using FlexCharge.Merchants.Enums;
using FlexCharge.Merchants.Services.ApplicationServices;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace FlexCharge.Merchants.Consumers;

public class UserTemporaryPasswordChangedEventConsumer : IConsumer<UserTemporaryPasswordChangedEvent>
{
    private readonly ILogger<UserTemporaryPasswordChangedEventConsumer> _logger;
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IApplicationService _applicationService;

    public UserTemporaryPasswordChangedEventConsumer(ILogger<UserTemporaryPasswordChangedEventConsumer> logger,
        PostgreSQLDbContext dbContext, IApplicationService applicationService)
    {
        _logger = logger;
        _dbContext = dbContext;
        _applicationService = applicationService;
    }


    public async Task Consume(ConsumeContext<UserTemporaryPasswordChangedEvent> context)
    {
        using var workspan = Workspan.Start<UserTemporaryPasswordChangedEventConsumer>()
            .Baggage("UserName", context.Message.Username)
            .LogEnterAndExit();

        try
        {
            // var contact = await _dbContext.Contacts.SingleOrDefaultAsync(x => x.Email == context.Message.Username);
            
            var applicant = await _dbContext.Applications
                .Include(x => x.PrimaryContact)
                .Include(x => x.DeveloperContact)
                .SingleOrDefaultAsync(x =>
                    x.PrimaryContact.Email.Trim().ToLower() == context.Message.Username.Trim().ToLower()  &&
                    x.Status != ApplicationStatus.CANCELLED);

            if (applicant == null)
            {
                var partner = await _dbContext.Partners
                    .Include(x => x.Contact)
                    .SingleOrDefaultAsync(x =>
                        x.Contact.Email == context.Message.Username);

                if (partner == null)
                {
                    ArgumentNullException.ThrowIfNull(partner);
                }
                
                partner.Contact.ConfirmedUser = true;
                
                _dbContext.Contacts.Update(partner.Contact);
                await _dbContext.SaveChangesAsync();
            }
            else
            {
                var primaryContact = applicant.PrimaryContact;
            
                ArgumentNullException.ThrowIfNull(primaryContact);

                primaryContact.ConfirmedUser = true;
            
                if (applicant.PrimaryContact != null)
                {
                    applicant.PrimaryContact.ConfirmedUser = true;
                }

                _dbContext.Contacts.Update(primaryContact);
                await _dbContext.SaveChangesAsync();
            }
            
            await _applicationService.ApplyApplicationActivity(applicant.Id, new ApplicationActivity
            {
                Department = "Onboarding",
                Category = "User",
                Activity = "Temporary Password Changed",
                UpdateMessage = $"Temporary password changed for {context.Message.Username}",
            });
        }
        catch (Exception e)
        {
            workspan.Log.Fatal(e, "Failed to change temporary password");
            
            await _applicationService.ApplyApplicationActivity(Guid.Empty, new ApplicationActivity
            {
                Department = "Onboarding",
                Category = "User",
                Activity = "Temporary Password Changed Failed",
                Note = e.Message,
                UpdateMessage = $"Failed to change temporary password for {context.Message.Username}"
            });
        }
    }
}