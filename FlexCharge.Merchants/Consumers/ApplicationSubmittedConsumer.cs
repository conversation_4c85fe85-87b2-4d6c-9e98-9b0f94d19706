using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using FlexCharge.Common.Emails;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Merchants.Entities;
using FlexCharge.Merchants.Migrations;
using FlexCharge.Merchants.Services.ApplicationServices;
using MassTransit;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SendGrid.Helpers.Mail;

namespace FlexCharge.Merchants.Consumers;

public class ApplicationSubmittedConsumer : IConsumer<ApplicationSubmittedEvent>
{
    private readonly ILogger<ApplicationSubmittedConsumer> _logger;
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IEmailSender _emailSender;
    private readonly IConfiguration _configuration;
    private readonly IApplicationService _applicationService;
    private readonly IHttpContextAccessor _httpContextAccessor;


    public ApplicationSubmittedConsumer(ILogger<ApplicationSubmittedConsumer> logger, PostgreSQLDbContext dbContext,
        IEmailSender emailSender, IConfiguration configuration, IApplicationService applicationService,
        IHttpContextAccessor httpContextAccessor)
    {
        _logger = logger;
        _dbContext = dbContext;
        _emailSender = emailSender;
        _configuration = configuration;
        _applicationService = applicationService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task Consume(ConsumeContext<ApplicationSubmittedEvent> context)
    {
        using var workspan = Workspan.Start<ApplicationSubmittedConsumer>()
            .Baggage("ApplicationId", context.Message.ApplicationId);

        try
        {
            var application =
                await _dbContext.Applications
                    .Include(x => x.PrimaryContact)
                    .Include(x => x.Address)
                    .Include(application => application.Partner)
                    .SingleOrDefaultAsync(x => x.Id == context.Message.ApplicationId);
            ArgumentNullException.ThrowIfNull(application);

            //get partner 
            var partner = await _dbContext.Partners
                .Include(x => x.Address)
                .SingleOrDefaultAsync(x => x.Id == application.PartnerId);
            if (partner == null)
            {
                workspan.Log.Fatal("Partner {ApplicationPartnerId} not found", application.PartnerId);
                throw new ArgumentNullException($"Partner {application.PartnerId} not found");
            }

            // Create Application Activity
            await _applicationService.ApplyApplicationActivity(application.Id, new ApplicationActivity
            {
                Category = "Application",
                Activity = "Application Submitted",
                Meta = JsonConvert.SerializeObject(new
                {
                    applicationId = application.Id,
                    applicationStatus = application.Status.ToString(),
                    applicationLegalEntityName = application.LegalEntityName
                }),
                UpdateMessage = $"Application {application.LegalEntityName} submitted",
            });

            var templateId = _configuration.GetValue<string>("email:application_Submitted_Internal_EmailTemplateId");

            // Send email to RISK
            // https://flex-charge.atlassian.net/wiki/spaces/PF/pages/582647816/Self+boarding+-+Application+Review+Notifications+flow#RISK%3A-Application-submitted-for-Review
            await _emailSender.SendEmailAsync(
                partner.RiskEmail ?? partner.SupportEmail,
                $"{application.LegalEntityName} has submitted their application", "Content", new
                {
                    partner_color = "e5575b",
                    applicant_first_name = application.PrimaryContact.FirstName,
                    aplicant_last_name = application.PrimaryContact.LastName,
                    applicant_dba = application.LegalEntityName,
                    application_status = application.Status.ToString(),
                    nudge = "Your prompt review is essential to keep the onboarding process moving forward.",
                    team = "Payments",
                    action_requiried = "Review the application and define an application status",
                    CTA = "Review The Application",
                    base_url = _configuration.GetValue<string>("email:baseUrl"),
                    path = "/accounts/applications/",
                    mid = application.Id,
                    slug = "",
                    email = partner.RiskEmail ?? partner.SupportEmail,
                    email_from = "<EMAIL>",

                    partner_address_line_1 = partner.Address?.Line1,
                    partner_address_line_2 = partner.Address?.Line2,
                    partner_city = partner.Address?.City,
                    partner_country = partner.Address?.Country,
                    partner_state_province_region = partner.Address?.State,
                    partner_postal_code = partner.Address?.ZipCode,
                    partner_site_url = partner.SiteUrl,
                    partner_privacy_policy_url = partner.PrivacyPolicyUrl,
                    partner_terms_url = partner.TermsAndConditionsUrl,
                    distribution = "internal",
                    template_id = templateId,
                },
                templateId,
                bcc: application.Partner.BccEmail,
                replyTo: application.Partner.ReplyToEmail,
                senderEmailOverride: application.Partner.NotificationSenderEmail,
                senderNameOverride: application.Partner.Name);

            try
            {
                var operationEmail = partner.OperationsEmail ?? partner.SupportEmail;
                if (operationEmail == null)
                {
                    workspan.Log.Fatal("Operations email not found for partner {PartnerId}", partner.Id);
                }
                else
                {
                    // Send email to Operations
                    // https://flex-charge.atlassian.net/wiki/spaces/PF/pages/582647816/Self+boarding+-+Application+Review+Notifications+flow#OPERATIONS-Application-submitted-for-Review
                    await _emailSender.SendEmailAsync(
                        partner.OperationsEmail ?? partner.SupportEmail,
                        $"{application.LegalEntityName} has submitted their application", "Content", new
                        {
                            partner_color = "e5575b",
                            applicant_first_name = application.PrimaryContact.FirstName,
                            aplicant_last_name = application.PrimaryContact.LastName,
                            applicant_dba = application.LegalEntityName,
                            application_status = application.Status.ToString(),
                            nudge = "our prompt review is essential to keep the onboarding process moving forward.",
                            team = "Operations",
                            action_requiried = "Certify the Applicant’s integration",
                            CTA = "Define Integration Status",
                            base_url = _configuration.GetValue<string>("email:baseUrl"),
                            path = $"/accounts/applications/",
                            mid = application.Id,
                            slug = "",
                            email_from = "<EMAIL>",
                            email = partner.OperationsEmail ?? partner.SupportEmail,

                            partner_address_line_1 = partner.Address?.Line1,
                            partner_address_line_2 = partner.Address?.Line2,
                            partner_city = partner.Address?.City,
                            partner_country = partner.Address?.Country,
                            partner_state_province_region = partner.Address?.State,
                            partner_postal_code = partner.Address?.ZipCode,
                            partner_site_url = partner.SiteUrl,
                            partner_privacy_policy_url = partner.PrivacyPolicyUrl,
                            partner_terms_url = partner.TermsAndConditionsUrl,

                            distribution = "internal",
                            template_id = templateId,
                        },
                        templateId,
                        bcc: application.Partner.BccEmail,
                        replyTo: application.Partner.ReplyToEmail,
                        senderEmailOverride: application.Partner.NotificationSenderEmail,
                        senderNameOverride: application.Partner.Name);
                }
            }
            catch (Exception e)
            {
                workspan.Log.Error(e, "Error sending email to operations");

                await _applicationService.ApplyApplicationActivity(application.Id, new ApplicationActivity
                {
                    Department = "Operations",
                    Category = "Application",
                    Activity = "Application Submitted Operations Email Error",
                    Note = e.Message,
                    UpdateMessage = $"Failed to send email to operations about that application {application.LegalEntityName} was submitted"
                });
            }

            try
            {
                // Send email to Applicant
                // https://flex-charge.atlassian.net/wiki/spaces/PF/pages/582647816/Self+boarding+-+Application+Review+Notifications+flow#APPLICANT%3A-Application-submitted-for-Review

                var applicantTemplateId =
                    _configuration.GetValue<string>("email:application_Submitted_EmailTemplateId");

                await _emailSender.SendEmailAsync(
                    application.PrimaryContact.Email,
                    "Your Application Has Been Submitted – What's Next?", "Content", new
                    {
                        applicant_first_name = application.PrimaryContact.FirstName,
                        aplicant_last_name = application.PrimaryContact.LastName,
                        applicant_dba = application.LegalEntityName,
                        application_status = application.Status.ToString(),
                        partner_color = "e5575b",
                        partner_name = partner.Name,
                        base_url_1 = _configuration.GetValue<string>("email:baseUrl"),
                        path_1 = "/auth/login",
                        base_url_2 = "https://guides.flexfactor.io/",
                        path_2 = "",
                        email_from = "<EMAIL>",
                        email = application.PrimaryContact.Email,

                        partner_address_line_1 = partner.Address?.Line1,
                        partner_address_line_2 = partner.Address?.Line2,
                        partner_city = partner.Address?.City,
                        partner_country = partner.Address?.Country,
                        partner_state_province_region = partner.Address?.State,
                        partner_postal_code = partner.Address?.ZipCode,
                        partner_site_url = partner.SiteUrl,
                        partner_privacy_policy_url = partner.PrivacyPolicyUrl,
                        partner_terms_url = partner.TermsAndConditionsUrl,

                        distribution = "internal",
                        template_id = applicantTemplateId
                    },
                    applicantTemplateId,
                    bcc: application.Partner.BccEmail,
                    replyTo: application.Partner.ReplyToEmail,
                    senderEmailOverride: application.Partner.NotificationSenderEmail,
                    senderNameOverride: application.Partner.Name);

                await _applicationService.ApplyApplicationActivity(application.Id, new ApplicationActivity
                {
                    Department = "Onboarding",
                    Category = "Application",
                    Activity = "Application Submitted Email Sent",
                    UpdateMessage = $"An email has been sent to applicant {application.PrimaryContact.Email} about that application {application.LegalEntityName} was submitted"
                });
            }
            catch (Exception e)
            {
                workspan.Log.Error(e, "Error sending email to applicant");

                await _applicationService.ApplyApplicationActivity(application.Id, new ApplicationActivity
                {
                    Department = "Onboarding",
                    Category = "Application",
                    Activity = "Application Submitted Sent Email Error",
                    Note = e.Message,
                    UpdateMessage = $"Failed to send email to applicant {application.PrimaryContact.Email} about that application {application.LegalEntityName} was submitted"
                });
            }
        }
        catch (Exception e)
        {
            workspan.Log.Fatal(e, "Error processing ApplicationSubmittedEvent");
        }
    }

    private string GetEmailFromJwt()
    {
        var email = _httpContextAccessor.HttpContext.User.Claims
            .FirstOrDefault(c => c.Type.ToString().Contains(ClaimTypes.Email))?.Value;
        return email;
    }
}