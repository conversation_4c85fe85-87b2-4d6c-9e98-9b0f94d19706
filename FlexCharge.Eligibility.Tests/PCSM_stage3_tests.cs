// using System;
// using System.Collections.Generic;
// using System.IO;
// using System.Security.Cryptography.Xml;
// using System.Threading.Tasks;
// using FlexCharge.Eligibility.Enums;
// using FlexCharge.Eligibility.Services.PCSMServices;
// using Microsoft.Extensions.Configuration;
// using NUnit.Framework;
//
// namespace FlexCharge.Eligibility.Tests;
//
// [TestFixture]
// public class TestsStage3
// {
//     //public IPcsmService _pcsmService { get; set; }
//     public IConfiguration _config { get; set; }
//
//     [SetUp]
//     public void Setup()
//     {
//         _config = new ConfigurationBuilder()
//             .SetBasePath(Directory.GetCurrentDirectory())
//             .AddJsonFile(@"appsettings.Staging.json", false, false)
//             .AddEnvironmentVariables().Build();
//     }
//
//     // [Test]
//     // public async Task Stage3_Do_Approved_with_high_bureau()
//     // {
//     //     var _pcsmService = new PcsmService(_config);
//     //
//     //     var fraudResponses = new Dictionary<string, string>();
//     //     fraudResponses.Add("KOUNT.Result", "0");
//     //     fraudResponses.Add("SEON.Result", "0");
//     //
//     //     var bureauResponses = new Dictionary<string, int>();
//     //     bureauResponses.Add(BureauProvidersEnum.EXPERIAN.ToString(), 800);
//     //
//     //     var request = new Stage3Request
//     //     {
//     //         FraudProviderResponses = fraudResponses,
//     //         BureauProviderResponses = bureauResponses,
//     //         ResponseCode = "100",
//     //         ResponseCodeSource = "NMI",
//     //         ZeroVerificationResponseCode = "",
//     //         // IsFullyAuthorized = true,
//     //         // IsCustomerInitiatedTransaction = true,
//     //         CvvResultCode = "M",
//     //         CavvResultCode = "Y",
//     //         AvsResultCode = "N"
//     //     };
//     //
//     //     var response = await _pcsmService.InvokeStage3(request);
//     //
//     //     Assert.AreEqual(OrderState.ELIGIBILITY_STAGE3_PASSED, response.OrderState);
//     // }
//     //
//     // [TestCase("0","0",0,"100","NMI","M","Y","M",ExpectedResult = OrderState.ELIGIBILITY_STAGE3_PASSED,TestName = "Do_Approved_all_passed(7)")]
//     // [TestCase("1","1",0,"100","NMI","N","Y","N",ExpectedResult = OrderState.ELIGIBILITY_STAGE3_PASSED,TestName = "Do_Approved_3ds_passed(8)")]
//     // [TestCase("0","0",800,"202","NMI","M","","",ExpectedResult = OrderState.ELIGIBILITY_STAGE3_PASSED,TestName = "Do_NSF_CVV_Fraud_3ds_passed(12)")]
//     // [TestCase("0","0",800,"202","NMI","","Y","",ExpectedResult = OrderState.ELIGIBILITY_STAGE3_PASSED,TestName = "Do_NSF_CVV_Fraud_3ds_passed(13)")]
//     // public async Task<OrderState> Stage3_Do_Approved_scenarios(string kount, string seon, int experianScore, string responseCode,
//     //     string ResponseCodeSource, string CvvResultCode, string CavvResultCode, string AvsResultCode)
//     // {
//     //     var _pcsmService = new PcsmService(_config);
//     //
//     //     var fraudResponses = new Dictionary<string, string>();
//     //     fraudResponses.Add("KOUNT.Result", kount);
//     //     fraudResponses.Add("SEON.Result", seon);
//     //
//     //     var bureauResponses = new Dictionary<string, int>();
//     //     bureauResponses.Add(BureauProvidersEnum.EXPERIAN.ToString(), experianScore);
//     //
//     //     var request = new Stage3Request
//     //     {
//     //         FraudProviderResponses = fraudResponses,
//     //         BureauProviderResponses = bureauResponses,
//     //         ResponseCode = responseCode,
//     //         ResponseCodeSource = ResponseCodeSource,
//     //         ZeroVerificationResponseCode = "",
//     //         // IsFullyAuthorized = true,
//     //         // IsCustomerInitiatedTransaction = true,
//     //         CvvResultCode = CvvResultCode,
//     //         CavvResultCode = CavvResultCode,
//     //         AvsResultCode = AvsResultCode
//     //     };
//     //
//     //     var response = await _pcsmService.InvokeStage3(request);
//     //
//     //     return response.OrderState;
//     //     //Assert.AreEqual( OrderState.ELIGIBILITY_STAGE3_PASSED, response.OrderState);
//     // }
//     //
//     // [TestCase("0","0",0,"100","NMI","N","","",ExpectedResult = "ST04",TestName = "Do_100_cvv_failed(16)")]
//     // [TestCase("1","0",0,"100","NMI","M","","",ExpectedResult = "FR02",TestName = "Do_100_fraud_failed(17)")]
//     // [TestCase("1","0",0,"100","NMI","N","","",ExpectedResult = "ST04",TestName = "Do_100_cvv_fraud_failed(18)")]
//     //
//     // [TestCase("0","0",400,"202","NMI","","","",ExpectedResult = "NE00",TestName = "Do_NSF_low_bureau(23)")]
//     // [TestCase("0","0",800,"202","NMI","N","","",ExpectedResult = "ST04",TestName = "Do_NSF_low_bureau(23)")]
//     // [TestCase("1","0",800,"202","NMI","M","","",ExpectedResult = "FR02",TestName = "Do_NSF_low_bureau(23)")]
//     // [TestCase("1","0",800,"202","NMI","N","","",ExpectedResult = "ST04",TestName = "Do_NSF_low_bureau(23)")]
//     // [TestCase("0","0",400,"203","NMI","","","",ExpectedResult = "NE00",TestName = "Do_NSF_low_bureau(28)")]
//     // [TestCase("0","0",800,"203","NMI","N","","",ExpectedResult = "ST04",TestName = "Do_NSF_low_bureau(28)")]
//     // [TestCase("1","0",800,"203","NMI","M","","",ExpectedResult = "FR02",TestName = "Do_NSF_low_bureau(28)")]
//     // [TestCase("1","0",800,"203","NMI","N","","",ExpectedResult = "ST04",TestName = "Do_NSF_low_bureau(28)")]
//     //
//     // [TestCase("0","0",0,"201","NMI","M","","M",ExpectedResult = "ST01",TestName = "Do_201_DNH_all_passed(31)")]
//     // [TestCase("0","0",0,"201","NMI","N","","M",ExpectedResult = "ST02",TestName = "Do_201_DNH_cvv_failed(32)")]
//     // [TestCase("0","0",0,"201","NMI","M","","N",ExpectedResult = "ST03",TestName = "Do_201_DNH_fraud_failed(33)")]
//     // [TestCase("0","0",0,"201","NMI","N","","N",ExpectedResult = "ST02",TestName = "Do_201_DNH_cvv_fraud_failed(34)")]
//     //
//     // [TestCase("0","0",0,"200","NMI","M","","M",ExpectedResult = "ST01",TestName = "Do_200_all_passed(36)")]
//     // [TestCase("0","0",0,"200","NMI","N","","M",ExpectedResult = "ST02",TestName = "Do_200_cvv_failed(36)")]
//     // [TestCase("0","0",0,"200","NMI","M","","N",ExpectedResult = "ST03",TestName = "Do_200_avs_failed(36)")]
//     // [TestCase("0","0",0,"200","NMI","N","","N",ExpectedResult = "ST02",TestName = "Do_200_cvv_avs_failed(36)")]
//     //
//     // [TestCase("0","0",0,"300","NMI","M","","M",ExpectedResult = "ST01",TestName = "Do_300_all_passed(36)")]
//     // [TestCase("0","0",0,"300","NMI","N","","M",ExpectedResult = "ST02",TestName = "Do_300_cvv_failed(36)")]
//     // [TestCase("0","0",0,"300","NMI","M","","N",ExpectedResult = "ST03",TestName = "Do_300_avs_failed(36)")]
//     // [TestCase("0","0",0,"300","NMI","N","","N",ExpectedResult = "ST02",TestName = "Do_300_cvv_avs_failed(36)")]
//     //
//     // [TestCase("0","0",0,"253","NMI","M","","M",ExpectedResult = "ST01",TestName = "Do_253_all_passed(40)")]
//     // [TestCase("0","0",0,"253","NMI","N","","M",ExpectedResult = "ST02",TestName = "Do_253_cvv_failed(40)")]
//     // [TestCase("0","0",0,"253","NMI","M","","N",ExpectedResult = "ST03",TestName = "Do_253_avs_failed(40)")]
//     // [TestCase("0","0",0,"253","NMI","N","","N",ExpectedResult = "ST02",TestName = "Do_253_cvv_avs_failed(40)")]
//     //
//     // [TestCase("0","0",0,"240","NMI","M","","M",ExpectedResult = "ST01",TestName = "Do_240_all_passed(45)")]
//     // [TestCase("0","0",0,"240","NMI","N","","M",ExpectedResult = "ST02",TestName = "Do_240_cvv_failed(45)")]
//     // [TestCase("0","0",0,"240","NMI","M","","N",ExpectedResult = "ST03",TestName = "Do_240_avs_failed(45)")]
//     // [TestCase("0","0",0,"240","NMI","N","","N",ExpectedResult = "ST02",TestName = "Do_240_cvv_avs_failed(45)")]
//     //
//     // [TestCase("0","0",0,"220","NMI","","","",ExpectedResult = "DI01",TestName = "Do_220_Incorrect_payment_information(48)")]
//     // [TestCase("0","0",0,"221","NMI","","","",ExpectedResult = "DI01",TestName = "Do_220_account_closed(49)")]
//     // [TestCase("0","0",0,"221","NMI","","","",ExpectedResult = "DI01",TestName = "Do_222_no_such_issuer(50)")]
//     //
//     // [TestCase("0","0",0,"250","NMI","","","",ExpectedResult = "NE00",TestName = "Do_250_Lost_or_stolen_card(56)")]
//     // [TestCase("0","0",0,"251","NMI","","","",ExpectedResult = "NE00",TestName = "Do_251_Lost_or_stolen_card(57)")]
//     // [TestCase("0","0",0,"252","NMI","","","",ExpectedResult = "NE00",TestName = "Do_252_Lost_or_stolen_card(58)")]
//     //
//     // // [TestCase("0","0",0,"225","NMI","","","",ExpectedResult = "DI05",TestName = "Do_225_cvv_error(60)")]
//     // [TestCase("0","0",0,"223","NMI","","","",ExpectedResult = "DI04",TestName = "Do_223_expired_non_active(63)")]
//     // [TestCase("0","0",0,"224","NMI","","","",ExpectedResult = "DI03",TestName = "Do_224_expired(67)")]
//     // [TestCase("0","0",0,"204","NMI","","","",ExpectedResult = "CI01",TestName = "Do_204_mcc_issue(69)")]
//     // [TestCase("0","0",0,"226","NMI","","","",ExpectedResult = "NE00",TestName = "Do_226_mcc_issue(74)")]
//     // [TestCase("0","0",0,"263","NMI","","","",ExpectedResult = "CI01",TestName = "Do_263_update_card_details_available(83)")]
//     // public async Task<string> Stage3_Do_cures_scenarios(string kount, string seon, int experianScore, string responseCode,
//     //     string ResponseCodeSource, string CvvResultCode, string CavvResultCode, string AvsResultCode)
//     // {
//     //     var _pcsmService = new PcsmService(_config);
//     //
//     //     var fraudResponses = new Dictionary<string, string>();
//     //     fraudResponses.Add("KOUNT.Result", kount);
//     //     fraudResponses.Add("SEON.Result", seon);
//     //
//     //     var bureauResponses = new Dictionary<string, int>();
//     //     bureauResponses.Add(BureauProvidersEnum.EXPERIAN.ToString(), experianScore);
//     //
//     //     var request = new Stage3Request
//     //     {
//     //         FraudProviderResponses = fraudResponses,
//     //         BureauProviderResponses = bureauResponses,
//     //         ResponseCode = responseCode,
//     //         ResponseCodeSource = ResponseCodeSource,
//     //         ZeroVerificationResponseCode = "",
//     //         // IsFullyAuthorized = true,
//     //         // IsCustomerInitiatedTransaction = true,
//     //         CvvResultCode = CvvResultCode,
//     //         CavvResultCode = CavvResultCode,
//     //         AvsResultCode = AvsResultCode
//     //     };
//     //
//     //     var response = await _pcsmService.InvokeStage3(request);
//     //
//     //     return response.Status;
//     //     //Assert.AreEqual( OrderState.ELIGIBILITY_STAGE3_PASSED, response.OrderState);
//     // }
//     //
//     // [Test]
//     // public async Task Do_simulate_exception()
//     // {
//     //     var _pcsmService = new PcsmService(_config);
//     //     Assert.ThrowsAsync<NullReferenceException>(async () => await _pcsmService.InvokeStage3(null));
//     // }
// }