using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Amazon;
using Amazon.DynamoDBv2.Model;
using Amazon.Kinesis;
using Amazon.Kinesis.Model;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using Newtonsoft.Json;
using DescribeStreamRequest = Amazon.Kinesis.Model.DescribeStreamRequest;
using DescribeStreamResponse = Amazon.Kinesis.Model.DescribeStreamResponse;
using GetRecordsRequest = Amazon.Kinesis.Model.GetRecordsRequest;
using GetShardIteratorRequest = Amazon.Kinesis.Model.GetShardIteratorRequest;
using GetShardIteratorResponse = Amazon.Kinesis.Model.GetShardIteratorResponse;
using ProvisionedThroughputExceededException = Amazon.Kinesis.Model.ProvisionedThroughputExceededException;
using Shard = Amazon.Kinesis.Model.Shard;

namespace FlexCharge.Common.DataStreaming;

public class KinesisService
{
    RegionEndpoint region = RegionEndpoint.USEast1;

    private AmazonKinesisConfig _config { get; set; }
    private AmazonKinesisClient _kinesisClient { get; set; }

    private string AwsKeyId { get; set; }
    private string AwsKeySecret { get; set; }

    public KinesisService()
    {
        _config = new AmazonKinesisConfig();
        _config.RegionEndpoint = Amazon.RegionEndpoint.USEast1;

        //https://davidpallmann.hashnode.dev/hello-kinesis-data-streams
        _kinesisClient = new AmazonKinesisClient(_config);
    }

    #region Put Record

    public async Task PutRecordAsync<T>(T Message, string streamName, string source)
    {
        using var workspan = Workspan.Start<KinesisService>();

        var env = EnvironmentHelper.GetCurrentEnvironment();

        workspan.Log.Debug("ENTERED: PutRecordAsync {Message}", JsonConvert.SerializeObject(Message));

        var serialized = JsonConvert.SerializeObject(Message);
        byte[] dataBytes = Encoding.UTF8.GetBytes(serialized + "\n");

        using MemoryStream ms = new MemoryStream(dataBytes);
        var stream = $"{streamName}-{env}".ToLowerInvariant();
        var request = new PutRecordRequest
        {
            StreamName = $"{streamName}-{env}".ToLowerInvariant(),
            PartitionKey = source,
            Data = ms
        };

        workspan.Log.Debug("Writing as source {Source} to Kinesis stream {StreamName}", source, stream);

        var sw = Stopwatch.StartNew();
        var response = await _kinesisClient.PutRecordAsync(request);
        sw.Stop();

        workspan.Log.Debug(
            "{S} [{HttpStatusCode}] [{ElapsedMilliseconds} ms] sequence number {SequenceNumber}: shard Id: {ShardId}",
            DateTime.Now, response.HttpStatusCode, sw.ElapsedMilliseconds, response.SequenceNumber, response.ShardId);
    }

    #endregion


    #region Put Records

    public record PutRecordsResult<T>
    {
        public List<T> FailedRecords { get; set; }
        public List<PutRecordsResultEntry> Results { get; set; }
    }

    public async Task<PutRecordsWithFailedRecords> PutRecordsAsync<T>(ICollection<T> messages, string streamName,
        string source)
    {
        using var workspan = Workspan.Start<KinesisService>();

        var env = EnvironmentHelper.GetCurrentEnvironment();

        var entries = new List<PutRecordsRequestEntry>();

        Dictionary<PutRecordsRequestEntry, T> mapPutRequestToMessage = new();
        foreach (var message in messages)
        {
            workspan.Log.Debug("PutRecordsAsync {Message}", JsonConvert.SerializeObject(message));

            var serialized = JsonConvert.SerializeObject(message);
            byte[] dataBytes = Encoding.UTF8.GetBytes(serialized + "\n");

            using MemoryStream ms = new MemoryStream(dataBytes);
            var requestEntry = new PutRecordsRequestEntry
            {
                PartitionKey = source,
                Data = ms
            };
            entries.Add(requestEntry);

            mapPutRequestToMessage.Add(requestEntry, message);
        }

        var stream = $"{streamName}-{env}".ToLowerInvariant();
        var request = new PutRecordsRequest
        {
            StreamName = stream,
            Records = entries
        };

        var result = await PutRecordsWithErrorHandlingAsync(request);

        #region Observability

        if (result.FailedRecordCount > 0)
        {
            workspan
                .Tag("FailedRecordCount", result.FailedRecordCount)
                .Log.Error("Failed to write records to Kinesis stream {StreamName}", stream);
        }

        if (result.ProcessedRecordsCount > 0)
        {
            workspan
                .Tag("ProcessedRecordsCount", result.ProcessedRecordsCount)
                .Log.Debug("Successfully wrote records to Kinesis stream {StreamName}", stream);
        }

        #endregion

        return result;
    }

    public record PutRecordsWithFailedRecords
    {
        public int ProcessedRecordsCount { get; set; }
        public int FailedRecordCount { get; set; }

        public List<(int Index, PutRecordsResultEntry Result)> FailedRecords;
    };

    async Task<PutRecordsWithFailedRecords> PutRecordsWithErrorHandlingAsync(PutRecordsRequest batchPutRequest)
    {
        int recordsToProcessCount = batchPutRequest.Records.Count;
        int processedRecordsCount = 0;

        var result = new PutRecordsWithFailedRecords();

        string exceptionName = null;
        string exceptionMessage = null;
        try
        {
            // AWS tries default retries, calling method can add more on top of that using result from this method
            // see: https://docs.aws.amazon.com/sdkref/latest/guide/feature-retry-behavior.html
            var batchPutResponse = await _kinesisClient.PutRecordsAsync(batchPutRequest);

            #region Commented test code

            // #if DEBUG
//             //throw new ProvisionedThroughputExceededException("AAA");
//             // if (batchPutResponse.Records.Count >= 2)
//             // {
//             //     batchPutResponse.FailedRecordCount = 2;
//             //     batchPutResponse.Records[0].ErrorCode = "ProvisionedThroughputExceededException";
//             //     batchPutResponse.Records[1].ErrorCode = "ThrottlingException";
//             // }
// #endif

            #endregion

            // Check for any unprocessed items and retry them,
            // as Kinesis may not process every item in a single batch
            // if you exceed the throughput limits
            //see: https://docs.aws.amazon.com/kinesis/latest/APIReference/API_PutRecords.html

            processedRecordsCount = batchPutResponse.Records.Count - batchPutResponse.FailedRecordCount;

            result.FailedRecords = new();
            if (batchPutResponse.FailedRecordCount > 0)
            {
                for (int i = 0; i < batchPutResponse.Records.Count; i++)
                {
                    if (batchPutResponse.Records[i].ErrorCode != null)
                    {
                        result.FailedRecords.Add((i, batchPutResponse.Records[i]));
                    }
                }
            }
        }
        catch (ProvisionedThroughputExceededException e)
        {
            Workspan.Current?.Log.Warning(
                "Kinesis PutRecordsWithRetriesAsync error > ProvisionedThroughputExceededException: {Exception}",
                e.Message);

            exceptionName = e.GetType().Name;
            exceptionMessage = e.Message;
        }
        catch (RequestLimitExceededException e)
        {
            Workspan.Current?.Log.Warning(
                "Kinesis PutRecordsWithRetriesAsync error > RequestLimitExceededException: {Exception}",
                e.Message);

            exceptionName = e.GetType().Name;
            exceptionMessage = e.Message;
        }
        catch (KMSThrottlingException e)
        {
            Workspan.Current?.Log.Warning(
                "Kinesis PutRecordsWithRetriesAsync error > KMSThrottlingException: {Exception}",
                e.Message);

            exceptionName = e.GetType().Name;
            exceptionMessage = e.Message;
        }
        catch (Exception e)
        {
            Workspan.Current?.Log.Error("Kinesis PutRecordsWithRetriesAsync error: {Exception}",
                e.Message);

            exceptionName = e.GetType().Name;
            exceptionMessage = e.Message;
        }

        result.ProcessedRecordsCount = processedRecordsCount;
        result.FailedRecordCount = recordsToProcessCount - processedRecordsCount;

        if (result.FailedRecordCount > 0 && result.FailedRecords == null) // exception occured -> all records failed
        {
            result.FailedRecords = batchPutRequest
                .Records
                .Select((x, i) => (i, new PutRecordsResultEntry()
                {
                    ErrorCode = exceptionName ?? "Unknown",
                    ErrorMessage = exceptionMessage ?? "Unknown",
                    SequenceNumber = null,
                    ShardId = null
                })).ToList();
        }


        return result;
    }

    #endregion

    // Add retries to this method!!!
    private async Task ReadFromKinesis(string streamName)
    {
        using var workspan = Workspan.Start<KinesisService>();

        //create config that points to Kinesis region
        AmazonKinesisConfig config = new AmazonKinesisConfig();
        config.RegionEndpoint = Amazon.RegionEndpoint.USEast1;

        //create new client object
        AmazonKinesisClient client = new AmazonKinesisClient(config);

        //Step #1 - describe stream to find out the shards it contains
        DescribeStreamRequest describeRequest = new DescribeStreamRequest();
        describeRequest.StreamName = streamName;

        DescribeStreamResponse describeResponse = await client.DescribeStreamAsync(describeRequest);
        List<Shard> shards = describeResponse.StreamDescription.Shards;
        foreach (Shard s in shards)
        {
            Console.WriteLine("shard: " + s.ShardId);
        }

        //grab the only shard ID in this stream
        string primaryShardId = shards[0].ShardId;

        //Step #2 - get iterator for this shard
        GetShardIteratorRequest iteratorRequest = new GetShardIteratorRequest();
        iteratorRequest.StreamName = streamName;
        iteratorRequest.ShardId = primaryShardId;
        iteratorRequest.ShardIteratorType = ShardIteratorType.TRIM_HORIZON;

        GetShardIteratorResponse iteratorResponse = await client.GetShardIteratorAsync(iteratorRequest);
        string iterator = iteratorResponse.ShardIterator;

        Console.WriteLine("Iterator: " + iterator);

        //Step #3 - get records in this iterator
        await GetShardRecords(client, iterator);
    }

    private async Task GetShardRecords(IAmazonKinesis client, string iteratorId)
    {
        while (true)
        {
            //create request
            GetRecordsRequest getRequest = new GetRecordsRequest();
            getRequest.Limit = 100;
            getRequest.ShardIterator = iteratorId;

            //call "get" operation and get everything in this shard range
            var getResponse = await client.GetRecordsAsync(getRequest);
            //get reference to next iterator for this shard
            var nextIterator = getResponse.NextShardIterator;
            //retrieve records
            var records = getResponse.Records;

            //print out each record's data value
            foreach (var r in records)
            {
                //pull out (JSON) data in this record
                string s = Encoding.UTF8.GetString(r.Data.ToArray());

                //DO SOMETHING WITH THE RECORDS
            }

            if (null != nextIterator)
            {
                //if there's another iterator, call operation again
                iteratorId = nextIterator;
                continue;
            }

            break;
        }
    }
}