// using Microsoft.Extensions.DependencyInjection;
// using Microsoft.Extensions.Logging;
// using OpenTracing;
// using OpenTracing.Util;
// using System;
// using System.Reflection;
//
// namespace FlexCharge.Common.Jaeger
// {
//     public static class JaegerServiceCollectionExtensions
//     {
//         public static IServiceCollection AddJaeger(this IServiceCollection services)
//         {
//
//             services.AddOpenTracing();
//
//             services.AddSingleton<ITracer>(serviceProvider =>
//             {
//                 string serviceName = Assembly.GetEntryAssembly().GetName().Name;
//
//                 ILoggerFactory loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
//
//                 var config = Configuration.FromEnv(loggerFactory);
//
//
//                 var tracer = config.GetTracer();
//                 GlobalTracer.Register(tracer);
//
//                 return tracer;
//             });
//
//
//             return services;
//         }
//
//     }
// }

