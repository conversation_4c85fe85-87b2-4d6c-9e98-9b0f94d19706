using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Amazon.SQS;
using Amazon.SQS.Model;
using FlexCharge.Common.Telemetry;
using FlexCharge.Utils.Concurrency;
using Microsoft.Extensions.Hosting;

namespace FlexCharge.Common.Cloud.Storage.Amazon;

abstract class AmazonSqsQueueListenerBackgroundServiceBase : BackgroundService
{
    private static ConcurrentBoolean IsRunning { get; set; } = new(false);


    protected async Task ProcessMessagesAsync(CancellationToken stoppingToken, string queueName, int pollingInterval)
    {
        //using (var client = AmazonS3ServiceBase.CreateAmazonS3Interface())
        //using (var sqsClient = AmazonS3ServiceBase.CreateAmazonSQSInterface())

        using (var sqsClient = new AmazonSQSClient())
        {
            var queueUrl = await GetQueueUrl(sqsClient, queueName);

            await MessageProcessingLoopAsync(sqsClient, queueUrl, pollingInterval, stoppingToken);
        }
    }

    private async Task MessageProcessingLoopAsync(IAmazonSQS client, string queueUrl, int pollingInterval,
        CancellationToken stoppingToken)
    {
        using var workspan = Workspan.Start<AmazonDTOs.AmazonS3StorageWatcherBackgroundService>();

        workspan.Log.Information("Starting polling queue at {QueueUrl}", queueUrl);

        while (!stoppingToken.IsCancellationRequested)
        {
            var messages = await ReceiveMessageAsync(client, queueUrl, 10);

            if (IsRunning.Value == true && Enumerable.Any(messages))
            {
                workspan.Log.Information("{MessagesCount} message(s) received", messages.Count);

                foreach (var msg in messages)
                {
                    var result = await ProcessMessageAsync(msg);

                    if (result)
                    {
                        workspan.Log.Information("S3 notification {MessageId} processed with success", msg.MessageId);
                        await DeleteMessageAsync(client, queueUrl, msg.ReceiptHandle);
                    }
                }
            }
            else
            {
                //Console.WriteLine("No message available");
                await Task.Delay(TimeSpan.FromMilliseconds(pollingInterval), stoppingToken);
            }
        }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="msg"></param>
    /// <returns>True if message should be removed from the queue</returns>
    protected abstract Task<bool> ProcessMessageAsync(Message msg);

    private static async Task<string> GetQueueUrl(IAmazonSQS client, string queueName)
    {
        try
        {
            var response = await client.GetQueueUrlAsync(new GetQueueUrlRequest
            {
                QueueName = queueName
            });

            return response.QueueUrl;
        }
        catch (QueueDoesNotExistException)
        {
            //You might want to add additional exception handling here because that may fail
            var response = await client.CreateQueueAsync(new CreateQueueRequest
            {
                QueueName = queueName
            });

            return response.QueueUrl;
        }

        return null;
    }

    private static async Task<List<Message>> ReceiveMessageAsync(IAmazonSQS client, string queueUrl,
        int maxMessages = 1)
    {
        var request = new ReceiveMessageRequest
        {
            QueueUrl = queueUrl,
            MaxNumberOfMessages = maxMessages
        };

        var messages = await client.ReceiveMessageAsync(request);

        return messages.Messages;
    }

    private static async Task DeleteMessageAsync(IAmazonSQS client, string queueUrl, string id)
    {
        var request = new DeleteMessageRequest
        {
            QueueUrl = queueUrl,
            ReceiptHandle = id
        };

        await client.DeleteMessageAsync(request);
    }

    public static void Start()
    {
        IsRunning = new(true);
    }
}