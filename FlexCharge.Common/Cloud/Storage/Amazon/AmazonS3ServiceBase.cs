namespace FlexCharge.Common.Cloud.Storage;

public abstract class AmazonS3ServiceBase
{
    // public static IAmazonS3 CreateAmazonS3Interface()
    // {
    //     // var awsCredentials = new BasicAWSCredentials(
    //     //     Environment.GetEnvironmentVariable("AWS_S3_IAM_ACCESS_KEY"),
    //     //     Environment.GetEnvironmentVariable("AWS_S3_IAM_SECRET_KEY"));
    //     //return new AmazonS3Client(awsCredentials, s3Region);
    //     
    //     var s3Region = RegionEndpoint.GetBySystemName(Environment.GetEnvironmentVariable("AWS_S3_IAM_REGION"));
    //     return new AmazonS3Client(s3Region);
    //     
    // }
    //
    // public static IAmazonSQS CreateAmazonSQSInterface()
    // {
    //     var s3Region = RegionEndpoint.GetBySystemName(Environment.GetEnvironmentVariable("AWS_S3_IAM_REGION"));
    //     return new AmazonSQSClient(s3Region);
    // }
    //
    // public static IAmazonSimpleNotificationService CreateAmazonSNSInterface()
    // {
    //     var s3Region = RegionEndpoint.GetBySystemName(Environment.GetEnvironmentVariable("AWS_S3_IAM_REGION"));
    //     return new AmazonSimpleNotificationServiceClient(s3Region);
    // }
}