using System;
using System.Text;
using System.Threading.Tasks;
using Amazon.SecretsManager;
using Amazon.SecretsManager.Model;
using FlexCharge.Common.Telemetry;

namespace FlexCharge.Common.Cloud.SecretsManager
{
    public class AwsSecretsManager : ISecretsManager
    {
        public async Task<string> GetSecretValueAsync(string secretName)
        {
            using var workspan = Workspan.Start<AwsSecretsManager>();

            try
            {
                IAmazonSecretsManager client = new AmazonSecretsManagerClient();

                GetSecretValueRequest request = new GetSecretValueRequest()
                {
                    SecretId = secretName,
                    VersionStage = "AWSCURRENT", // VersionStage defaults to AWSCURRENT if unspecified.
                };

                var response = await client.GetSecretValueAsync(request);

                return response.SecretString;
            }
            catch (AmazonSecretsManagerException e)
            {
                workspan.RecordFatalException(e, "Error occurred while fetching secret from AWS Secrets Manager");
                throw;
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "Error occurred while fetching secret from AWS Secrets Manager");
                throw;
            }
        }

        // String is not valid key format, so we need to format it
        public string FormatOpenSSHKey(string key, bool? isEd25519 = true)
        {
            var builder = new StringBuilder();
            builder.AppendLine("-----BEGIN OPENSSH PRIVATE KEY-----");

            string base64Key = key
                .Replace("-----BEGIN OPENSSH PRIVATE KEY-----", string.Empty)
                .Replace("-----END OPENSSH PRIVATE KEY-----", string.Empty)
                .Replace("\n", string.Empty)
                .Replace("\r", string.Empty)
                .Replace(" ", string.Empty)
                .Trim();

            int lineLength = 64;

            if (isEd25519 == true)
            {
                lineLength = 70;
            }

            for (int i = 0; i < base64Key.Length; i += lineLength)
            {
                builder.AppendLine(base64Key.Substring(i, Math.Min(lineLength, base64Key.Length - i)));
            }

            builder.AppendLine("-----END OPENSSH PRIVATE KEY-----");
            return builder.ToString();
        }
    }
}