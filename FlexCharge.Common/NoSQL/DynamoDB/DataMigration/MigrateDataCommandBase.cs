using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.Model;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Telemetry;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Common.NoSQL.DynamoDB.DataMigration;

public abstract class MigrateDataCommandBase<TEntity, TDbContext> : BackgroundWorkerCommand
    where TEntity : class
    where TDbContext : DbContext
{
    private readonly string _targetTable;
    private readonly Func<TEntity, string> _partitionKeySelector;
    private readonly Func<TEntity, string>? _sortKeySelector;
    private readonly Func<TEntity, Dictionary<string, AttributeValue>> _itemSelector;

    public MigrateDataCommandBase(string targetTable,
        Func<TEntity, string> partitionKeySelector, Func<TEntity, string>? sortKeySelector,
        Func<TEntity, Dictionary<string, AttributeValue>> itemSelector)
    {
        _targetTable = targetTable;
        _partitionKeySelector = partitionKeySelector;
        _sortKeySelector = sortKeySelector;
        _itemSelector = itemSelector;
    }

    protected override async Task ExecuteAsync(IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        using var serviceScope = serviceProvider.CreateScope();
        using var dbContext = serviceScope.ServiceProvider.GetRequiredService(typeof(TDbContext)) as TDbContext;

        var entitiesQuery = GetEntitiesToMigrate(dbContext).AsNoTracking();

        var dynamoDB = serviceScope.ServiceProvider.GetRequiredService<IAmazonDynamoDB>();
        await WriteToDynamoDbAsync(dynamoDB, entitiesQuery, _targetTable, _partitionKeySelector, _sortKeySelector,
            _itemSelector);
    }

    protected abstract IQueryable<TEntity> GetEntitiesToMigrate(TDbContext dbContext);


    private async Task WriteToDynamoDbAsync<T>(IAmazonDynamoDB dynamoDb, IQueryable<T> rowsToTransfer,
        string targetTable,
        Func<T, string> partitionKeySelector, Func<T, string>? sortKeySelector,
        Func<T, Dictionary<string, AttributeValue>> itemSelector)
    {
        using var workspan = Workspan.Start<MigrateDataCommandBase<TEntity, TDbContext>>()
            .Baggage("TargetTable", targetTable)
            .LogEnterAndExit();

        try
        {
            DynamoDbClient dynamoDbClient = new(dynamoDb);
            
            int totalCount = await rowsToTransfer.CountAsync();

            workspan.Log
                .Information("Items to transfer count: {TotalCount}", totalCount);

            int transferred = 0;
            DateTime startedOn = DateTime.UtcNow;

            const int
                MAX_DYNAMO_DB_WRITE_BATCH_SIZE =
                    25; // Amazon DynamoDB allows us to write up to 25 items in a single BatchWriteItem request

            List<T> buffer = new(MAX_DYNAMO_DB_WRITE_BATCH_SIZE);
            List<T> duplicateItemsBuffer = new();
            HashSet<string> keysAlreadyInBuffer = new();

            int activitiesLeftToProcess = totalCount;
            int previousTransferredWrittenToLog = 0;
            await foreach (var row in rowsToTransfer.AsAsyncEnumerable())
            {
                var uniqueItemKey = partitionKeySelector(row);

                if (sortKeySelector != null)
                {
                    uniqueItemKey += "_" + sortKeySelector(row);
                }


                if (!keysAlreadyInBuffer.Contains(uniqueItemKey))
                {
                    buffer.Add(row);
                    keysAlreadyInBuffer.Add(uniqueItemKey);
                }
                else
                {
                    //we need to write duplicates one-by-one - they cannot be added to DynamoDB in BatchWriteItemRequest
                    duplicateItemsBuffer.Add(row);
                }


                activitiesLeftToProcess--;

                if (buffer.Count < MAX_DYNAMO_DB_WRITE_BATCH_SIZE &&
                    duplicateItemsBuffer.Count < MAX_DYNAMO_DB_WRITE_BATCH_SIZE &&
                    activitiesLeftToProcess > 0)
                    continue;

                if (buffer.Count != 0)
                {
                    await dynamoDbClient.WriteBatchAsync(dynamoDb, targetTable, buffer, itemSelector);
                    transferred += buffer.Count();
                }

                if (duplicateItemsBuffer.Count != 0)
                {
                    foreach (var duplicateItem in duplicateItemsBuffer)
                    {
                        await dynamoDbClient.WriteItemToDynamoDbAsync(dynamoDb, targetTable, duplicateItem, itemSelector);

                        transferred++;
                    }
                }

                if (transferred / 1000 != previousTransferredWrittenToLog / 1000)
                {
                    previousTransferredWrittenToLog = transferred;

                    var transferredRounded = transferred / 1000 * 1000;

                    var timeAlreadySpent = DateTime.UtcNow - startedOn;
                    var etc = DateTime.UtcNow.AddSeconds(timeAlreadySpent.TotalSeconds
                        / transferredRounded * (totalCount - transferredRounded));

                    workspan.Log
                        .Information(
                            "Transferred {Transferred} of {TotalCount} ({PercentCompleted}%). Processing time: {ProcessingTime}. ETC: {EstimatedTimeOfCompletion} UTC. Estimated time left: {EstimatedTimeLeft}",
                            transferredRounded, totalCount,
                            (transferredRounded / (float) totalCount * 100).ToString("F2"),
                            timeAlreadySpent.ToString("hh\\:mm\\:ss"), etc.ToString("yyyy-MM-dd HH:mm:ss"),
                            (etc - DateTime.UtcNow).ToString("hh\\:mm\\:ss"));
                }

                buffer.Clear();
                duplicateItemsBuffer.Clear();
                keysAlreadyInBuffer.Clear();
            }

            workspan.Log
                .Information("Transferred total {Transferred} to DynamoDB", transferred);
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    
}