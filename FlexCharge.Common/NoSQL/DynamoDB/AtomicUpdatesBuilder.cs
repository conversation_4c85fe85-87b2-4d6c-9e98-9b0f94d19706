using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Amazon.DynamoDBv2;
using FlexCharge.Utils;

namespace FlexCharge.Common.NoSQL.DynamoDB;

public class AtomicUpdatesBuilder
{
    public List<DynamoDbClient.ItemAtomicOperations> Updates { get; } = new();

    List<ItemUpdate> _updates = new();

    ItemUpdate CurrentUpdate { get; set; }

    public async Task UpdateAsync(IAmazonDynamoDB dynamoDb, bool useRetries, int? maxDegreeOfParallelism = null)
    {
        using var dynamoDbClient = new DynamoDbClient(dynamoDb);

        var updateTasks = _updates.Select(update =>
            dynamoDbClient.AtomicUpdateItemsAsync(
                update.Operations,
                DynamoDbClient.AtomicOperationReturnValue.None,
                update.TableName,
                update.PartitionKeyName, update.PartitionKey,
                useRetries,
                update.SortKeyName, update.SortKey)
        );

        await ThreadingHelpers.RunTasksAsync(updateTasks, maxDegreeOfParallelism);
    }


    /// <summary>
    /// 
    /// </summary>
    /// <param name="dynamoDb"></param>
    /// <param name="maxDegreeOfParallelism"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns>Current values of all updated items</returns>
    public async Task<List<T>> UpdateAndGetCurrentOrDefaultAsync<T>(IAmazonDynamoDB dynamoDb,
        bool useRetries,
        int? maxDegreeOfParallelism = null)
    {
        using var dynamoDbClient = new DynamoDbClient(dynamoDb);

        var updateTasks = _updates.Select(update =>
            dynamoDbClient.AtomicUpdateItemsAsync<T>(
                update.Operations,
                DynamoDbClient.AtomicOperationReturnValue.AllNew,
                update.TableName,
                update.PartitionKeyName, update.PartitionKey,
                useRetries,
                update.SortKeyName, update.SortKey)
        );


        var results = await ThreadingHelpers.RunTasksAsync(updateTasks, maxDegreeOfParallelism);

        return results.ToList();
    }

    public IDisposable StartItemUpdate(
        string tableName,
        string partitionKeyName, string partitionKey,
        string? sortKeyName = null,
        string? sortKey = null)
    {
        if (!string.IsNullOrWhiteSpace(sortKey) && string.IsNullOrWhiteSpace(sortKeyName))
            throw new ArgumentException("Cannot specify a sort key value without a sort key name.");

        if (CurrentUpdate != null)
            throw new InvalidOperationException("Cannot start a new update while another one is in progress.");

        CurrentUpdate = new ItemUpdate(this,
            tableName,
            partitionKeyName, partitionKey,
            sortKeyName, sortKey);

        _updates.Add(CurrentUpdate);

        return CurrentUpdate;
    }

    public IDisposable StartItemUpdate(
        string tableName,
        string partitionKeyName, string partitionKey,
        string sortKeyName,
        DateTime sortKey)
    {
        if (CurrentUpdate != null)
            throw new InvalidOperationException("Cannot start a new update while another one is in progress.");

        CurrentUpdate = new ItemUpdate(this,
            tableName,
            partitionKeyName, partitionKey,
            sortKeyName, sortKey);

        _updates.Add(CurrentUpdate);

        return CurrentUpdate;
    }

    public IDisposable StartItemUpdate<TItem>(string partitionKey, string? sortKey = null)
    {
        if (CurrentUpdate != null)
            throw new InvalidOperationException("Cannot start a new update while another one is in progress.");

        var tableMapping = DynamoDbClient.GetTableMapping<TItem>();

        return StartItemUpdate(tableMapping.TableName,
            tableMapping.PartitionKeyName, partitionKey,
            tableMapping.SortKeyName, sortKey);
    }

    public IDisposable StartItemUpdate<TItem>(Guid partitionKey, string? sortKey = null)
    {
        return StartItemUpdate<TItem>(partitionKey.ToString(), sortKey);
    }

    public void EndItemUpdate()
    {
        if (CurrentUpdate == null)
            throw new InvalidOperationException("Cannot end an update while none is in progress.");

        CurrentUpdate = null;
    }

    #region class ItemUpdate

    class ItemUpdate : IDisposable
    {
        public string TableName { get; }
        private readonly AtomicUpdatesBuilder _builder;
        public string PartitionKeyName { get; }
        public string PartitionKey { get; }
        public string SortKeyName { get; }
        public string SortKey { get; }

        public List<DynamoDbClient.AtomicOperation> Operations { get; } = new();

        public ItemUpdate(
            AtomicUpdatesBuilder builder,
            string tableName,
            string partitionKeyName, string partitionKey,
            string sortKeyName, string sortKey)
        {
            TableName = tableName;
            _builder = builder;
            PartitionKeyName = partitionKeyName;
            PartitionKey = partitionKey;
            SortKeyName = sortKeyName;
            SortKey = sortKey;
        }

        public ItemUpdate(
            AtomicUpdatesBuilder builder,
            string tableName,
            string partitionKeyName, string partitionKey,
            string sortKeyName, DateTime sortKey)
        {
            TableName = tableName;
            _builder = builder;
            PartitionKeyName = partitionKeyName;
            PartitionKey = partitionKey;
            SortKeyName = sortKeyName;
            SortKey = sortKey.ToString("o");
        }

        public void Dispose()
        {
            _builder.EndItemUpdate();
        }

        #region Operations

        public void AddValue(string name, long valueToAdd)
        {
            Operations.Add(new DynamoDbClient.AtomicOperation(
                DynamoDbClient.AtomicOperationType.Add,
                DynamoDbClient.AtomicOperandType.Number,
                name, valueToAdd));
        }

        public void AddValue(string name, int valueToAdd)
        {
            Operations.Add(new DynamoDbClient.AtomicOperation(
                DynamoDbClient.AtomicOperationType.Add,
                DynamoDbClient.AtomicOperandType.Number,
                name, valueToAdd));
        }

        public void SubtractValue(string name, long valueToAdd)
        {
            Operations.Add(new DynamoDbClient.AtomicOperation(
                DynamoDbClient.AtomicOperationType.Subtract,
                DynamoDbClient.AtomicOperandType.Number,
                name, valueToAdd));
        }

        public void SubtractValue(string name, int valueToAdd)
        {
            Operations.Add(new DynamoDbClient.AtomicOperation(
                DynamoDbClient.AtomicOperationType.Subtract,
                DynamoDbClient.AtomicOperandType.Number,
                name, valueToAdd));
        }

        public void SetValue(string name, long value)
        {
            Operations.Add(new DynamoDbClient.AtomicOperation(
                DynamoDbClient.AtomicOperationType.Set,
                DynamoDbClient.AtomicOperandType.String,
                name, value));
        }

        public void SetValue(string name, int value)
        {
            Operations.Add(new DynamoDbClient.AtomicOperation(
                DynamoDbClient.AtomicOperationType.Set,
                DynamoDbClient.AtomicOperandType.String,
                name, value));
        }

        public void SetValue(string name, string value)
        {
            Operations.Add(new DynamoDbClient.AtomicOperation(
                DynamoDbClient.AtomicOperationType.Set,
                DynamoDbClient.AtomicOperandType.String,
                name, value));
        }

        public void SetValue(string name, bool value)
        {
            Operations.Add(new DynamoDbClient.AtomicOperation(
                DynamoDbClient.AtomicOperationType.Set,
                DynamoDbClient.AtomicOperandType.Boolean,
                name, value));
        }

        public void SetValue(string name, DateTime value)
        {
            Operations.Add(new DynamoDbClient.AtomicOperation(
                DynamoDbClient.AtomicOperationType.Set,
                DynamoDbClient.AtomicOperandType.String,
                name, value.ToString("o")));
        }

        public void InitializeValue(string name, long value)
        {
            Operations.Add(new DynamoDbClient.AtomicOperation(
                DynamoDbClient.AtomicOperationType.Initialize,
                DynamoDbClient.AtomicOperandType.String,
                name, value));
        }

        public void InitializeValue(string name, int value)
        {
            Operations.Add(new DynamoDbClient.AtomicOperation(
                DynamoDbClient.AtomicOperationType.Initialize,
                DynamoDbClient.AtomicOperandType.String,
                name, value));
        }

        public void InitializeValue(string name, string value)
        {
            Operations.Add(new DynamoDbClient.AtomicOperation(
                DynamoDbClient.AtomicOperationType.Initialize,
                DynamoDbClient.AtomicOperandType.String,
                name, value));
        }

        public void InitializeValue(string name, bool value)
        {
            Operations.Add(new DynamoDbClient.AtomicOperation(
                DynamoDbClient.AtomicOperationType.Initialize,
                DynamoDbClient.AtomicOperandType.Boolean,
                name, value));
        }

        public void InitializeValue(string name, DateTime value)
        {
            Operations.Add(new DynamoDbClient.AtomicOperation(
                DynamoDbClient.AtomicOperationType.Initialize,
                DynamoDbClient.AtomicOperandType.String,
                name, value.ToString("o")));
        }

        #endregion
    }

    #endregion

    #region Long

    public void Initialize(string attributeName, long value)
    {
        if (CurrentUpdate == null)
            throw new InvalidOperationException("Cannot add an update while none is in progress.");

        CurrentUpdate.InitializeValue(attributeName, value);
    }


    public void Set(string attributeName, long value)
    {
        if (CurrentUpdate == null)
            throw new InvalidOperationException("Cannot add an update while none is in progress.");

        CurrentUpdate.SetValue(attributeName, value);
    }

    public void Add(string attributeName, long value)
    {
        if (CurrentUpdate == null)
            throw new InvalidOperationException("Cannot add an update while none is in progress.");

        CurrentUpdate.AddValue(attributeName, value);
    }

    public void Subtract(string attributeName, long value)
    {
        if (CurrentUpdate == null)
            throw new InvalidOperationException("Cannot add an update while none is in progress.");

        CurrentUpdate.SubtractValue(attributeName, value);
    }

    #endregion

    #region Integer

    public void Initialize(string attributeName, int value)
    {
        if (CurrentUpdate == null)
            throw new InvalidOperationException("Cannot add an update while none is in progress.");

        CurrentUpdate.InitializeValue(attributeName, value);
    }


    public void Set(string attributeName, int value)
    {
        if (CurrentUpdate == null)
            throw new InvalidOperationException("Cannot add an update while none is in progress.");

        CurrentUpdate.SetValue(attributeName, value);
    }

    public void Add(string attributeName, int value)
    {
        if (CurrentUpdate == null)
            throw new InvalidOperationException("Cannot add an update while none is in progress.");

        CurrentUpdate.AddValue(attributeName, value);
    }

    public void Subtract(string attributeName, int value)
    {
        if (CurrentUpdate == null)
            throw new InvalidOperationException("Cannot add an update while none is in progress.");

        CurrentUpdate.SubtractValue(attributeName, value);
    }

    #endregion

    #region Boolean

    public void Initialize(string attributeName, bool value)
    {
        if (CurrentUpdate == null)
            throw new InvalidOperationException("Cannot add an update while none is in progress.");

        CurrentUpdate.InitializeValue(attributeName, value);
    }


    public void Set(string attributeName, bool value)
    {
        if (CurrentUpdate == null)
            throw new InvalidOperationException("Cannot add an update while none is in progress.");

        CurrentUpdate.SetValue(attributeName, value);
    }

    #endregion

    #region String

    public void Initialize(string attributeName, string value)
    {
        if (CurrentUpdate == null)
            throw new InvalidOperationException("Cannot add an update while none is in progress.");

        CurrentUpdate.InitializeValue(attributeName, value);
    }


    public void Set(string attributeName, string value)
    {
        if (CurrentUpdate == null)
            throw new InvalidOperationException("Cannot add an update while none is in progress.");

        CurrentUpdate.SetValue(attributeName, value);
    }

    #endregion

    #region DateTime

    public void Initialize(string attributeName, DateTime value)
    {
        if (CurrentUpdate == null)
            throw new InvalidOperationException("Cannot add an update while none is in progress.");

        CurrentUpdate.InitializeValue(attributeName, value);
    }

    public void Set(string attributeName, DateTime value)
    {
        if (CurrentUpdate == null)
            throw new InvalidOperationException("Cannot add an update while none is in progress.");

        CurrentUpdate.SetValue(attributeName, value);
    }

    #endregion
}