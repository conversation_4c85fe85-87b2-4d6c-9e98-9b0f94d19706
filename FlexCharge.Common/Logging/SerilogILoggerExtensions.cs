using System;
using System.Collections.Generic;
using System.Text.Json;
using FlexCharge.Common.SensitiveData.Obfuscation;
using Newtonsoft.Json;
using Serilog;

namespace FlexCharge.Common.Logging;

public static class SerilogILoggerExtensions
{
    //see: https://benfoster.io/blog/serilog-best-practices/

    static readonly JsonSerializerSettings JsonSerializerSettings = new JsonSerializerSettings()
    {
        TypeNameHandling = TypeNameHandling.All,
        ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
        ContractResolver = ObfuscatorContractResolver.Default,
        MaxDepth = 5 // To avoid out of memory exception
    };

    public static ILogger Enrich(this ILogger logger, string parameterName, object parameterValue)
    {
        return logger.ForContext(parameterName,
            parameterValue != null ? JsonConvert.SerializeObject(parameterValue, JsonSerializerSettings) : "null");
    }

    public static ILogger Enrich(this ILogger logger, string callerFilePath, string callerMemberName,
        int callerLineNumber)
    {
        return logger
            .ForContext(new PropertyBagEnricher()
                .Add("File", callerFilePath)
                .Add("Member", callerMemberName)
                .Add("Line", callerLineNumber));
    }

    public static void LogRedacted<T>(
        this ILogger logger,
        T target,
        Func<string, (string template, object[] logparams)> getMessageTemplate,
        JsonSerializerOptions jsonOptions,
        Action<T> redactAction = null
    )
    {
        if (getMessageTemplate is null) return;

        if (!EqualityComparer<T>.Default.Equals(target, default(T)))
        {
            try
            {
                var newTarget = redactAction is null
                    ? target
                    : System.Text.Json.JsonSerializer.Deserialize(
                        System.Text.Json.JsonSerializer.Serialize(target, jsonOptions), target.GetType(),
                        jsonOptions);

                if (redactAction is not null)
                {
                    redactAction((T) newTarget);
                }

                var json = System.Text.Json.JsonSerializer.Serialize(newTarget, jsonOptions);
                var (message, logparams) = getMessageTemplate(json);
                logger.Information(message, logparams);
            }
            catch (Exception e)
            {
                logger.Error(e, "JSON Log exception for {Type}", typeof(T).Name);
            }
        }
    }

    public static void LogRedacted<T>(
        this ILogger logger,
        T target,
        Func<string, string> getMessageTemplate,
        JsonSerializerSettings jsonSerializeSettings,
        Action<T> redactAction = null,
        params object[] logparams)

    {
        if (getMessageTemplate is null) return;

        if (!EqualityComparer<T>.Default.Equals(target, default(T)))
        {
            try
            {
                var newTarget = redactAction is null
                    ? target
                    : JsonConvert.DeserializeObject(
                        JsonConvert.SerializeObject(target, jsonSerializeSettings), target.GetType(),
                        jsonSerializeSettings);

                if (redactAction is not null)
                {
                    redactAction((T) newTarget);
                }

                var json = JsonConvert.SerializeObject(
                    newTarget,
                    jsonSerializeSettings);

                var message = getMessageTemplate(json);
                logger.Information(message, logparams);
            }
            catch (Exception e)
            {
                logger.Error(e, "JSON Log exception for {Type}", typeof(T).Name);
            }
        }
    }
}