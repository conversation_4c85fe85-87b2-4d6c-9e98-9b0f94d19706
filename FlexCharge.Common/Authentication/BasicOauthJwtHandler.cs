using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using Microsoft.Extensions.Options;

namespace FlexCharge.Common.Authentication
{
    public class BasicOauthJwtHandler : IJwtHandler
    {
        private static readonly ISet<string> DefaultClaims = new HashSet<string>
        {
            JwtRegisteredClaimNames.Sub,
            JwtRegisteredClaimNames.UniqueName,
            JwtRegisteredClaimNames.Jti,
            JwtRegisteredClaimNames.Iat,
            ClaimTypes.Role,
        };

        private readonly JwtSecurityTokenHandler _jwtSecurityTokenHandler = new JwtSecurityTokenHandler();
        private readonly IOptions<BasicOauthOptions> _options;
        private readonly SigningCredentials _signingCredentials;
        private readonly TokenValidationParameters _tokenValidationParameters;

        // public RsaSecurityKey SigningKey(string Key, string Expo)
        // {
        //     return new RsaSecurityKey(new RSAParameters()
        //     {
        //         Modulus = Base64UrlEncoder.DecodeBytes(Key),
        //         Exponent = Base64UrlEncoder.DecodeBytes(Expo)
        //     });
        // }

        public BasicOauthJwtHandler(IOptions<BasicOauthOptions> options)
        {
            _options = options;

            var signingKeySecret = Environment.GetEnvironmentVariable("API_CLIENT_JWT_SIGNING_KEY");

            if (string.IsNullOrEmpty(signingKeySecret))
                throw new Exception("API_CLIENT_JWT_SIGNING_KEY environment variable is not set.");

            var issuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(signingKeySecret));
            _signingCredentials = new SigningCredentials(issuerSigningKey, SecurityAlgorithms.HmacSha256);

            _tokenValidationParameters = new TokenValidationParameters
            {
                IssuerSigningKey = issuerSigningKey,
                ValidIssuer = _options.Value.Issuer,
                ValidAudience = _options.Value.ValidAudience,
                ValidAudiences = _options.Value.ValidAudiences,
                ValidateAudience = _options.Value.ValidateAudience,
                ValidateLifetime = _options.Value.ValidateLifetime
            };
        }

        public JsonWebToken CreateToken(string userId, string[] aud, string[] roles = null, List<Claim> claims = null,
            DateTime? exp = null)
        {
            if (string.IsNullOrWhiteSpace(userId))
            {
                throw new ArgumentException("User id claim can not be empty.", nameof(userId));
            }

            var now = DateTime.Now;
            var jwtClaims = new List<Claim>
            {
                new Claim(JwtRegisteredClaimNames.Sub, userId),
                new Claim(JwtRegisteredClaimNames.UniqueName, userId),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(JwtRegisteredClaimNames.Iat, now.ToTimestamp().ToString()),
            };

            if (aud != null)
                foreach (var item in aud)
                {
                    jwtClaims.Add(new Claim(JwtRegisteredClaimNames.Aud, item));
                }

            if (!string.IsNullOrEmpty(_options.Value.ValidAudience))
            {
                jwtClaims.Add(new Claim(JwtRegisteredClaimNames.Aud, _options.Value.ValidAudience));
            }

            if (roles != null)
            {
                foreach (var role in roles)
                {
                    if (!string.IsNullOrWhiteSpace(role))
                    {
                        jwtClaims.Add(new Claim(ClaimTypes.Role, role));
                    }
                }
            }

            var customClaims = claims?.Select(claim => new Claim(claim.Type, claim.Value)).ToArray()
                               ?? Array.Empty<Claim>();
            jwtClaims.AddRange(customClaims);
            // var expires = now.AddMinutes(_options.Value.ExpiryMinutes);
            ;
            var expires = exp ?? now.AddMinutes(_options.Value.ExpiryMinutes);

            var jwt = new JwtSecurityToken(
                issuer: _options.Value.Issuer,
                claims: jwtClaims,
                notBefore: now,
                expires: expires,
                signingCredentials: _signingCredentials
            );

            var token = new JwtSecurityTokenHandler().WriteToken(jwt);

            return new JsonWebToken
            {
                AccessToken = token,
                RefreshToken = string.Empty,
                Expires = expires.ToTimestamp(),
                Id = userId,
                //Role = roles.Any() ? roles : default,
                //Claims = jwtClaims
            };
        }

        public JsonWebTokenPayload GetTokenPayload(string accessToken)
        {
            _jwtSecurityTokenHandler.ValidateToken(accessToken, _tokenValidationParameters,
                out var validatedSecurityToken);
            if (!(validatedSecurityToken is JwtSecurityToken jwt))
            {
                return null;
            }

            return new JsonWebTokenPayload
            {
                Subject = jwt.Subject,
                Role = jwt.Claims.SingleOrDefault(x => x.Type == ClaimTypes.Role)?.Value,
                Expires = jwt.ValidTo.ToTimestamp(),
                Claims = jwt.Claims
                    .Where(x => !DefaultClaims.Contains(x.Type))
                    .ToLookup(k => k.Type, v => v.Value)
                    .ToDictionary(g => g.Key, g => string.Join(",", g)),
            };
        }


        public Guid ValidateToken(string token)
        {
            var principal = GetPrincipal(token);
            if (principal == null)
            {
                return Guid.Empty;
            }

            ClaimsIdentity identity;
            try
            {
                identity = (ClaimsIdentity) principal.Identity;
            }
            catch (NullReferenceException)
            {
                return Guid.Empty;
            }

            //var userIdClaim = identity.FindFirst("userId");
            var userIdClaim = identity.FindFirst(ClaimTypes.NameIdentifier);
            var userId = new Guid(userIdClaim.Value);
            return userId;
        }

        public ClaimsPrincipal GetPrincipal(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var jwtToken = (JwtSecurityToken) tokenHandler.ReadToken(token);
                if (jwtToken == null)
                {
                    return null;
                }

                //IdentityModelEventSource.ShowPII = true;
                SecurityToken securityToken;
                ClaimsPrincipal principal = tokenHandler.ValidateToken(token,
                    _tokenValidationParameters, out securityToken);
                return principal;
            }
            catch (Exception)
            {
                return null;
            }
        }
    }
}