using System.Collections.Generic;

namespace FlexCharge.Common.Shared.UIBuilder;

public class ValidationsBuilder
{
    public ICollection<Validation> Validations { get; set; } = new List<Validation>();

    private ValidationsBuilder Add(ValidationType type, string errorMessage, object? value = null)
    {
        Validations.Add(new Validation(type, errorMessage, value));

        return this;
    }

    public ValidationsBuilder Required(string errorMessage)
    {
        Validations.Add(new Validation(ValidationType.Required, errorMessage));

        return this;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="regEx">JavaScript-compatible Regular Expression</param>
    /// <param name="errorMessage"></param>
    /// <returns></returns>
    /// <summary>Use https://regex101.com/ to create and check regular expressions</summary>
    public ValidationsBuilder Regex(string regEx, string errorMessage, bool stripWhitespaceBeforeValidation = false)
    {
        Validations.Add(new Validation(!stripWhitespaceBeforeValidation? ValidationType.RegEx : ValidationType.RegExStripWhitespace, errorMessage, regEx));

        return this;
    }
  

    public ValidationsBuilder MinValue(int minValue, string errorMessage)
    {
        Validations.Add(new Validation(ValidationType.Min, errorMessage, minValue));

        return this;
    }

    public ValidationsBuilder MaxValue(int maxValue, string errorMessage)
    {
        Validations.Add(new Validation(ValidationType.Max, errorMessage, maxValue));

        return this;
    }

    public ValidationsBuilder MinLength(int minLength, string errorMessage)
    {
        Validations.Add(new Validation(ValidationType.MinLength, errorMessage, minLength));

        return this;
    }

    public ValidationsBuilder MaxLength(int maxLength, string errorMessage)
    {
        Validations.Add(new Validation(ValidationType.MaxLength, errorMessage, maxLength));

        return this;
    }

    public ValidationsBuilder MustBeChecked(string errorMessage)
    {
        Validations.Add(new Validation(ValidationType.Checked, errorMessage));

        return this;
    }
}