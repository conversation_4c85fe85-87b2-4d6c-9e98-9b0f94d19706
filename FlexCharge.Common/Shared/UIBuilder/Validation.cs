namespace FlexCharge.Common.Shared.UIBuilder;

public enum ValidationType
{
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    RegExStripWhitespace,
    Checked
}
public class Validation
{
 

    public Validation(ValidationType type, string errorMessage, object? value = null)
    {
        string validationType = type.ToString();

        Type = validationType.Substring(0, 1).ToLower() + validationType.Substring(1);
        Value = value;
        ErrorMessage = errorMessage;
    }

    public string Type { get; set; }
    public object? Value { get; set; }
    public string ErrorMessage { get; set; }
}