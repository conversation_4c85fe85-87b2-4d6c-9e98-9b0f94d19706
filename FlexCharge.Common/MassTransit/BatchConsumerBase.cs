using MassTransit;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Common.MassTransit;

public abstract class BatchConsumerBase<TMessage> : ConsumerBase<Batch<TMessage>>
    where TMessage : class
{
    protected override bool LogEnterAndExit => true;

    public BatchConsumerBase(IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
    }

    protected ConsumeContext<Batch<TMessage>> Context { get; private set; }
}