using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Cache;
using FlexCharge.Common.Cache.RequestsIdempotency;
using FlexCharge.Common.MassTransit.Idempotency;
using FlexCharge.Contracts;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.DependencyInjection;
using StackExchange.Redis;

namespace FlexCharge.Common.MassTransit;

/// <summary>
/// This event consumer guarantees that event will be processed only once  
/// </summary>
/// <typeparam name="TEvent"></typeparam>
public abstract class IdempotentEventConsumer<TEvent> : ConsumerBase<TEvent>
    where TEvent : IdempotentEvent
{
    public IdempotentEventConsumer(IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
    }

    readonly byte[] IN_PROCESSING_CACHE_VALUE = new byte[] {0};

    protected override async Task ConsumeMessage(TEvent message, CancellationToken cancellationToken)
    {
        var idempotentMessage = (IIdempotentMessage) message;

        CacheKey idempotencyCacheKey = null;
        if (idempotentMessage.MessageIdempotencyKey != Guid.Empty)
        {
            idempotencyCacheKey =
                IdempotencyCacheKeyFactory.CreateEventIdempotencyKey(idempotentMessage.MessageIdempotencyKey);
        }
        else
        {
            Workspan
                .Tag("Type", this.GetType().Name)
                .Log.Fatal("Idempotency key cannot be empty");

            throw new Exception("Idempotency key cannot be empty");
        }

        try
        {
            Cache = GetRequiredService<IIdempotencyKeysDistributedCache>();


            var bytes = await Cache.GetAsync(idempotencyCacheKey.Key);

            if (bytes != null)
            {
                Workspan
                    .Tag("Type", this.GetType().Name)
                    .Log.Warning("Idempotency key already exists - Skipping duplicate request");
            }
            else
            {
                #region Storing IN_PROCESSING_CACHE_VALUE in distributed cache

                await Cache.SetAsync(idempotencyCacheKey.Key, IN_PROCESSING_CACHE_VALUE,
                    idempotencyCacheKey.CacheOptions);

                #endregion

                await ConsumeEvent(message, cancellationToken);
            }
        }
        catch (RedisTimeoutException ex)
        {
            Workspan
                .Tag("Type", this.GetType().Name)
                .Tag("Message", ex.Message)
                .Tag("CommandStatus", ex.Commandstatus)
                .Log.Fatal("RedisTimeoutException in idempotent consumer");

            //TODO: Add retries on masstransit here? (send MassTransitRetryException)

            throw;
        }
        catch (MassTransitRetryException ex)
        {
            Workspan
                .Tag("Type", this.GetType().Name)
                .Log.Information("Resetting idempotency cache key on retry");

            await Cache.RemoveAsync(idempotencyCacheKey.Key);

            throw;
        }
        finally
        {
            Cache = null;
        }
    }

    protected abstract Task ConsumeEvent(TEvent message, CancellationToken cancellationToken);

    private IDistributedCache Cache { get; set; }
}