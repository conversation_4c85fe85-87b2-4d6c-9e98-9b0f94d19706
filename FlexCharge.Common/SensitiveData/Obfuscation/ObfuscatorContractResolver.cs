using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace FlexCharge.Common.SensitiveData.Obfuscation;

public class ObfuscatorContractResolver : DefaultContractResolver
{
    private static readonly ConcurrentDictionary<Type, Dictionary<string, SensitiveDataAttribute>>
        ReflectionCache = new();

    protected override IList<JsonProperty> CreateProperties(Type type, MemberSerialization memberSerialization)
    {
        IList<JsonProperty> baseProperties = base.CreateProperties(type, memberSerialization);

        if (!ReflectionCache.TryGetValue(type, out var sensitiveData))
        {
            sensitiveData = new Dictionary<string, SensitiveDataAttribute>();

            foreach (PropertyInfo property in type.GetProperties())
            {
                var sensitiveAttribute = property.GetCustomAttribute<SensitiveDataAttribute>();

                if (sensitiveAttribute is null)
                    continue;

                var propertyName = property.Name;
                sensitiveData.Add(propertyName, sensitiveAttribute);
            }

            ReflectionCache.TryAdd(type, sensitiveData);
        }

        if (!sensitiveData.Any())
            return baseProperties;

        var processedProperties = new List<JsonProperty>();

        foreach (JsonProperty baseProperty in baseProperties)
        {
            // we want to use the property name as it appears in C# class
            var basePropertyName = baseProperty.UnderlyingName ?? baseProperty.PropertyName;
            if (sensitiveData.TryGetValue(basePropertyName, out SensitiveDataAttribute sensitiveAttribute))
            {
                baseProperty.PropertyType = typeof(string);
                baseProperty.ValueProvider =
                    new ObfuscatorValueProvider(baseProperty.ValueProvider, sensitiveAttribute);
            }

            processedProperties.Add(baseProperty);
        }

        return processedProperties;
    }

    public static readonly IContractResolver Default = new ObfuscatorContractResolver();
}