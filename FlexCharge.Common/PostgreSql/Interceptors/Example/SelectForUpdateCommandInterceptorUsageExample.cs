// #if DEBUG
// using System;
// using System.Linq;
// using System.Threading;
// using System.Threading.Tasks;
// using FlexCharge.Common.PostgreSql.Interceptors;
// using FlexCharge.Common.RuntimeEnvironment;
// using FlexCharge.Common.Telemetry;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.Extensions.DependencyInjection;
//
// public class SelectForUpdateCommandInterceptorUsageExample
// {
//     public static async Task TestQueueStyleLock(IServiceScopeFactory serviceScopeFactory)
//     {
//         using var workspan = Workspan.Start<SelectForUpdateCommandInterceptorUsageExample>();
//
//
//         if (!EnvironmentHelper.IsInStagingOrDevelopment) throw new NotSupportedException();
//
//         using var serviceScope1 = serviceScopeFactory.CreateScope();
//         using var dbContext1 = serviceScope1.ServiceProvider.GetRequiredService<PostgreSQLDbContext>();
//
//         using var serviceScope2 = serviceScopeFactory.CreateScope();
//         using var dbContext2 = serviceScope2.ServiceProvider.GetRequiredService<PostgreSQLDbContext>();
//
//         await using var lockingTransaction = await dbContext1.Database.BeginTransactionAsync();
//
//         var act1 = await dbContext1.AggregatedActivities
//             .TagWith(SelectForUpdateCommandInterceptor.SelectForUpdateSkipLockedTag)
//             .Where(x => x.Id == new Guid("07c23af2-f800-4865-a2b7-14164b42a753"))
//             .ToListAsync();
//
//         if (act1.Count != 1)
//             workspan.Log.Fatal("act1.Count != 1");
//
//         var act2 = await dbContext2.AggregatedActivities
//             .TagWith(SelectForUpdateCommandInterceptor.SelectForUpdateSkipLockedTag)
//             .Where(x => x.Id == new Guid("07c23af2-f800-4865-a2b7-14164b42a753"))
//             .ToListAsync();
//
//         if (act2.Count != 0)
//             workspan.Log.Fatal("act2.Count != 0");
//
//
//         await lockingTransaction.CommitAsync();
//
//         var act3 = await dbContext2.AggregatedActivities
//             .TagWith(SelectForUpdateCommandInterceptor.SelectForUpdateSkipLockedTag)
//             .Where(x => x.Id == new Guid("07c23af2-f800-4865-a2b7-14164b42a753"))
//             .ToListAsync();
//
//         if (act3.Count != 1)
//             workspan.Log.Fatal("act3.Count != 1");
//     }
//
//     public static async Task TestResourceLockStyleBlock(IServiceScopeFactory serviceScopeFactory)
//     {
//         using var workspan = Workspan.Start<SelectForUpdateCommandInterceptorUsageExample>();
//
//         if (!EnvironmentHelper.IsInStagingOrDevelopment) throw new NotSupportedException();
//
//         using var serviceScope1 = serviceScopeFactory.CreateScope();
//         using var dbContext1 = serviceScope1.ServiceProvider.GetRequiredService<PostgreSQLDbContext>();
//
//
//         using var serviceScope2 = serviceScopeFactory.CreateScope();
//         using var dbContext2 = serviceScope2.ServiceProvider.GetRequiredService<PostgreSQLDbContext>();
//
//         using CancellationTokenSource waitingTimeoutCts = new CancellationTokenSource();
//         waitingTimeoutCts.CancelAfter(TimeSpan.FromSeconds(5));
//
//         var blockingQuery = async () =>
//         {
//             using var workspan = Workspan.Start<TestController>();
//
//             await using var blockingTransaction = await dbContext1.Database.BeginTransactionAsync();
//
//             var act1 = await dbContext1.AggregatedActivities
//                 .TagWith(SelectForUpdateCommandInterceptor.SelectForUpdateBlockingTag)
//                 .Where(x => x.Id == new Guid("07c23af2-f800-4865-a2b7-14164b42a753"))
//                 .ToListAsync();
//
//             if (act1.Count != 1)
//                 workspan.Log.Fatal("act1.Count != 1");
//
//             await Task.Delay(5000);
//
//             await blockingTransaction.CommitAsync();
//         };
//
//         var blockedQuery = async () =>
//         {
//             using var workspan = Workspan.Start<TestController>();
//
//             var utcStart = DateTime.UtcNow;
//
//             // Note: no need to start transaction here as this is not a blocking query
//             var act2 = await dbContext2.AggregatedActivities
//                 .TagWith(SelectForUpdateCommandInterceptor.SelectForUpdateBlockingTag)
//                 .Where(x => x.Id == new Guid("07c23af2-f800-4865-a2b7-14164b42a753"))
//                 .ToListAsync();
//
//             var utcEnd = DateTime.UtcNow;
//
//             workspan.Log.Information("Blocked for {Seconds}s", (utcEnd - utcStart).TotalSeconds);
//
//             if (act2.Count != 1)
//                 workspan.Log.Fatal("act2.Count != 1");
//         };
//
//         await Task.WhenAll(blockingQuery(), blockedQuery());
//
//         var act3 = await dbContext2.AggregatedActivities
//             .TagWith(SelectForUpdateCommandInterceptor.SelectForUpdateBlockingTag)
//             .Where(x => x.Id == new Guid("07c23af2-f800-4865-a2b7-14164b42a753"))
//             .ToListAsync();
//
//         if (act3.Count != 1)
//             workspan.Log.Fatal("act3.Count != 1");
//     }
// }
// #endif

