// Not working in new Npgsql versions
// using System;
// using System.Text.Json;
// using System.Text.Json.Serialization;
// using FlexCharge.Common.Telemetry;
//
// internal class StringSanitizingJsonConverter : JsonConverter<string>
// {
//     public override string Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
//     {
//         return reader.GetString();
//     }
//
//     public override void Write(Utf8JsonWriter writer, string value, JsonSerializerOptions options)
//     {
//         // Replace \u0000 with empty string before writing
//         string sanitizedValue = ReplaceEscapeCharactersUnsupportedByPostgreSQL(value);
//         writer.WriteStringValue(sanitizedValue);
//     }
//             
//     private static string ReplaceEscapeCharactersUnsupportedByPostgreSQL(string jsonText)
//     {
//         try
//         {
//             // Replace \u0000 from jsonText and all preceding \ characters
//             // This is a workaround for PostgreSQL not supporting \u0000 in JSONB fields
//
//             int indexOfEscapeCharacter = 0;
//             bool firstIncorrectSymbol = true;
//
//             if (jsonText.Contains("\\u0000"))
//             {
//                 return string.Create(jsonText.Length, jsonText, (span, originalText) =>
//                 {
//                     originalText.AsSpan().CopyTo(span);
//
//                     int iteration = 0;
//                     int maxIterations = jsonText.Length;
//                     while (true)
//                     {
//                         iteration++;
//
//                         var index = span.Slice(indexOfEscapeCharacter).IndexOf("\\u0000");
//
//                         if (index >= 0)
//                         {
//                             indexOfEscapeCharacter = indexOfEscapeCharacter + index;
//
//                             if (firstIncorrectSymbol)
//                             {
//                                 firstIncorrectSymbol = false;
//
//                                 using var workspan = Workspan.Start<StringSanitizingJsonConverter>();
//
//                                 workspan.Log
//                                     .Fatal(
//                                         "Unsupported Unicode escape character fixed in Json data for PostgreSQL. Data: {K}",
//                                         span.ToString());
//                             }
//
//                             int start = indexOfEscapeCharacter;
//                             while (start > 0 && span[start - 1] == '\\') start--;
//
//                             for (int i = start; i < indexOfEscapeCharacter + 6; i++)
//                             {
//                                 span[i] = ' '; // replace with space
//                             }
//                         }
//                         else
//                         {
//                             break;
//                         }
//
//                         // Safety check
//                         if (iteration > maxIterations)
//                         {
//                             throw new Exception("Too many iterations. Possible infinite loop.");
//                         }
//                     }
//                 });
//             }
//             else return jsonText;
//         }
//         catch (Exception e)
//         {
//             using var workspan = Workspan.Start<StringSanitizingJsonConverter>();
//
//             workspan.Log
//                 .Fatal(e,
//                     "Cannot process unsupported Unicode escape character found in Json data for PostgreSQL. Data: {K}",
//                     jsonText);
//
//             return jsonText;
//         }
//     }
// }

