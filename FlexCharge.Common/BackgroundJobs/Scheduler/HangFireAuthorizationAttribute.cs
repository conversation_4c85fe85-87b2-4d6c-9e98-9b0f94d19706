using Hangfire.Annotations;
using Hangfire.Dashboard;

namespace FlexCharge.Common.BackgroundJobs
{
    public class HangFireAuthorizationAttribute : IDashboardAuthorizationFilter
    {
        private string role { get; set; }
        public HangFireAuthorizationAttribute(string role)
        {
            this.role = role;
        }

        public bool Authorize([NotNull] DashboardContext context)
        {
            return context.GetHttpContext().User.IsInRole(role);
        }
    }
}
