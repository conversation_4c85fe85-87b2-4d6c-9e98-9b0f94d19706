using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using static FlexCharge.Common.Partitions.PartitionScriptHelper;

namespace FlexCharge.Common.Partitions;

/// <summary>
///  Algorithm is suitable for now  only  for date/timestamp columns as partition columns
///  First, it reads existing partitions from DB and based on this takes some actions.
///  Algorithm is resilient for failed jobs , because if can create partitions from default partition.
///  
/// </summary>
/// <typeparam name="C"></typeparam>
public abstract class TablePartitionServiceBaseV17<C> : ITablePartitionService where C : DbContext
{
    C _context;
    private readonly TablePartitionOptions _partitionOptions;
    private readonly int _numberOfDailyPartitions = 5; //default value
    protected abstract string PartitionedTableName { get; }
    protected abstract string PartitionedColumnName { get; }

    string _defaultTableName = null;
    string _archiveTableName = null;

    private StringBuilder _sb = new();


    protected abstract List<Guid> GetMerchantIds();


    protected TablePartitionServiceBaseV17(C context, IOptions<TablePartitionOptions> partitionOptions)
    {
        _context = context;
        _partitionOptions = partitionOptions.Value;
        var tableSettings = _partitionOptions.TableSettings
            .FirstOrDefault(t => t.PartitionTableName.Equals(PartitionedTableName, StringComparison.OrdinalIgnoreCase));
        if (tableSettings?.NumberOfDailyPartitions > 0)
            _numberOfDailyPartitions = tableSettings.NumberOfDailyPartitions;
    }

    static TablePartitionInfo GetPartitionInfo(TablePartitionDTO dto)
    {
        var pattern = @"'(\d{4}-\d{2}-\d{2})\s";
        var matches = Regex.Matches(dto.PartitionValues, pattern);
        var group1 = matches[0].Groups[1];
        var group2 = matches[1].Groups[1];
        var from = DateOnly.Parse(group1.Value);
        var to = DateOnly.Parse(group2.Value);
        return new()
        {
            From = from,
            To = to,
            TableName = dto.TableName
        };
    }

    //get all partitions for PartitionedTableName from DB
    protected async Task<List<TablePartitionInfo>> GetPartitionsAsync(string schema = "public")
    {
        var script = GetPartitionsScripts(PartitionedTableName);

        var records = await _context.Database.SqlQueryRaw<string>(script).ToListAsync();

        if (records?.Count > 0)
        {
            var partitions = System.Text.Json.JsonSerializer.Deserialize<List<TablePartitionDTO>>(records[0],
                new JsonSerializerOptions() { PropertyNameCaseInsensitive = true });


            var result = partitions
                .Where(p => p.PartitionValues != "DEFAULT")
                .Select(p => GetPartitionInfo(p))
                .OrderByDescending(p => p.From).ToList();

            _defaultTableName = partitions.FirstOrDefault(p => p.PartitionValues == "DEFAULT")?.TableName;
            _archiveTableName = result.FirstOrDefault(p => p.PartitionType == PartitionType.Archive)?.TableName;
            return result;
        }
        else
        {
            return null;
        }
    }

    /// <summary>
    ///  get min and max dates of existing partitions
    /// </summary>
    /// <param name="partitions"></param>
    /// <returns></returns>
    async Task<PartitionsDateRanges>
        GetInclusiveDateRangeOfExistingPartitions(List<TablePartitionInfo> partitions)
    {
        List<DateOnly> dates = new();


        PartitionsDateRanges result = new();

        foreach (var partition in partitions)
        {
            if (partition.PartitionType == PartitionType.Month)
            {
                dates.Add(partition.From);
                dates.Add(partition.To.AddDays(-1));
            }
            else if (partition.PartitionType == PartitionType.Day)
            {
                dates.Add(partition.From);
            }
        }


        result.MinOverallDate = dates?.Count > 0 ? dates?.Min() : null;
        result.MaxOverallDate = dates?.Count > 0 ? dates?.Max() : null;
        result.MinDefaultTableDate = await GetTableMinDate(_defaultTableName);
        result.MaxDefaultTableDate = await GetTableMaxDate(_defaultTableName);


        if (result.MaxDefaultTableDate is not null &&
            (result.MaxOverallDate is null || result.MaxDefaultTableDate.Value > result.MaxOverallDate))
        {
            result.MaxOverallDate = result.MaxDefaultTableDate.Value;
        }

        if (result.MinDefaultTableDate is not null &&
            (result.MinOverallDate is null || result.MinDefaultTableDate.Value < result.MinOverallDate))
        {
            result.MinOverallDate = result.MinDefaultTableDate.Value;
        }


        if (_archiveTableName is not null)
        {
            result.MinArchiveTabletDate = await GetTableMinDate(_archiveTableName);
            result.MaxArchiveTableDate = await GetTableMaxDate(_archiveTableName);
        }

        return result;
    }


    //calculate candidates  year->month  tuples for monthly tables for the given partitions date range
    List<MonthYear> GetMonthlyPartitionsToCreate(DateOnly? startDate, DateOnly? enddate,
        List<TablePartitionInfo> existingPartitions)
    {
        List<MonthYear> result = new();

        if (startDate.HasValue && enddate.HasValue && enddate.Value >= startDate.Value)
        {
            var monthlyPartitions = existingPartitions
                .Where(p => p.PartitionType == PartitionType.Month)
                .ToDictionary(p => new MonthYear(p.From.Month, p.From.Year), p => p);

            result = DateHelper.GetAllMonthsBetweenDates(startDate.Value, enddate.Value)
                .Where(m => !monthlyPartitions.ContainsKey(m)).ToList();
        }

        return result;
    }

    //calculate dates for daily partition to create for the given partitions date range
    List<DateOnly> GetDailyPartitionsToCreate(
        DateOnly startDate,
        DateOnly endDate,
        List<TablePartitionInfo> existingPartitions)
    {
        List<DateOnly> result = new();

        var dailyPartitions = existingPartitions
            .Where(p => p.PartitionType == PartitionType.Day)
            .ToDictionary(p => p.From, p => p);

        result = DateHelper.GetDatesBetweenDates(startDate, endDate)
            .Where(m => !dailyPartitions.ContainsKey(m)).ToList();

        return result;
    }


    /// <summary>
    /// get table min date from database
    /// </summary>
    /// <returns></returns>
    async Task<DateOnly?> GetTableMinDate(string tableName)
    {
        var script = GetMinDateScript(tableName, PartitionedColumnName);
        var minDates = await _context.Database.SqlQueryRaw<DateOnly?>(script).ToListAsync();
        if (minDates is null || minDates.Count == 0) return null;

        return minDates[0];
    }

    /// <summary>
    /// get table max date from database
    /// </summary>
    /// <returns></returns>
    async Task<DateOnly?> GetTableMaxDate(string tableName)
    {
        var script = GetMaxDateScript(tableName, PartitionedColumnName);
        var maxDates = await _context.Database.SqlQueryRaw<DateOnly?>(script).ToListAsync();
        if (maxDates?.Count == 0) return null;
        return maxDates[0];
    }


    /// <summary>
    /// main method to maintain table partitions
    /// </summary>
    /// <param name="token"></param>
    public async Task MaintainTablePartitionsAsync(CancellationToken token)
    {
        using var workspan = Workspan.Start<TablePartitionServiceBase<C>>().LogEnterAndExit();

        try
        {
            var script = await GenerateAndMergePartitionsScript();
            if (!string.IsNullOrWhiteSpace(script))
            {
                await _context.Database.ExecuteSqlRawAsync(script, token);
                workspan.Log.Information("Table Partitioning script executed successfully for {Table}",
                    PartitionedTableName);
            }
            else
            {
                workspan.Log.Information("Table Partitioning script not created  for {Table}", PartitionedTableName);
            }
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }

    private void CopyIndices(string tableName)
    {
        var script = CopyIndicesScript(_defaultTableName, tableName);
        _sb.AppendLine(script);
    }

    private void GenerateScriptHeader()
    {
        _sb.Append("ROLLBACK;");
        _sb.AppendLine(StartTransactionScript()); // start ttrans
        SetLocalStatementTimeout(_partitionOptions.Timeout);
        _sb.AppendLine(DoScript());
        //   _sb.AppendLine(DeclareVariableScript("count_before", "int"));
        //   _sb.AppendLine(DeclareVariableScript("count_after", "int"));
        _sb.AppendLine(StartBlockScript());
        // _sb.AppendLine($"count_before := (select count(*) from \"{PartitionedTableName}\");");
    }

    /// <summary>
    ///  add check for count of rows before and after partitioning
    /// </summary>
    private void GenerateScriptFooter()
    {
        // _sb.AppendLine(AttachDefaultPartitionTableScript(PartitionedTableName, _defaultTableName));
        //  _sb.AppendLine($"count_after := (select count(*) from \"{PartitionedTableName}\");");
        // _sb.AppendLine(Assert("count_after", "count_before", "Table partitioning failed"));
        _sb.AppendLine(EndBlockScript());
        _sb.AppendLine(CommitTransactionScript());
    }


    private async Task<string> GenerateAndMergePartitionsScript()
    {
        using var workspan = Workspan.Start<ITablePartitionService>();

        var toMerge = false;

        GenerateScriptHeader();

        var existingPartitions = await GetPartitionsAsync();

        LockTable(_defaultTableName);
        var partitionRange = await GetInclusiveDateRangeOfExistingPartitions(existingPartitions);

        var now = DateOnly.FromDateTime(DateTime.UtcNow);
        var firstDailyDate = now.AddDays(-_numberOfDailyPartitions);
        if (partitionRange.MinOverallDate.HasValue)
        {
            if (partitionRange.MinOverallDate.Value > firstDailyDate)
                firstDailyDate = partitionRange.MinOverallDate.Value;
        }
        else
        {
            firstDailyDate = now.AddDays(1);
        }


        workspan.Log.Information("{FirstDailyDate}", firstDailyDate);


        Dictionary<DateOnly, string> dateToTableName = existingPartitions
            .Where(p => p.PartitionType == PartitionType.Day)
            .ToDictionary(p => p.From, p => p.TableName);

        //REMOVE OLD DAILY PARTITIONS
        List<DateOnly> tablesToDrop = new();
        foreach (var kv in dateToTableName)
        {
            if (kv.Key < firstDailyDate)
            {
                if (toMerge)
                {
                    DetachTable(kv.Value);
                    MergeDailyTableIntoDefault(kv.Value);
                }

                DropTable(kv.Value);
                tablesToDrop.Add(kv.Key);
                partitionRange.UpdateTableRange(kv.Key);
            }
        }

        foreach (var date in tablesToDrop)
        {
            dateToTableName.Remove(date);
        }

        //get all dates in default table - todo-optimization: get it in sql instead  of caclulation
        List<DateOnly> defaultDates = partitionRange.MinDefaultTableDate is null
            ? new()
            : DateHelper.GetDatesBetweenDates(partitionRange.MinDefaultTableDate.Value,
                partitionRange.MaxDefaultTableDate.Value);


        // List<DateOnly> archiveDates = partitionRange.archiveMinDate is null
        //     ? new()
        //     : DateHelper.GetDatesBetweenDates(partitionRange.archiveMinDate.Value, partitionRange.archiveMaxDate.Value);


        //all monthly partitions without existing
        var monthlyPartitionsToCreate =
            GetMonthlyPartitionsToCreate(partitionRange.MinOverallDate, firstDailyDate.AddDays(-1), existingPartitions);


        Dictionary<MonthYear, string> monthToTableName = existingPartitions
            .Where(p => p.PartitionType == PartitionType.Month)
            .ToDictionary(p => new MonthYear(p.From.Month, p.From.Year), p => p.TableName);


        //merge default table to exising monthly partitions
        if (toMerge) // disable merge for now
        {
            foreach (var kv in monthToTableName)
            {
                if (defaultDates.Any(x => x.Year == kv.Key.Year && x.Month == kv.Key.Month))
                {
                    //optimize -omit detach and attach
                    DetachTable(kv.Value);
                    MergeDefaultTableIntoMonthly(kv.Value, kv.Key, firstDailyDate.AddDays(-1),
                        null);

                    AttachMonthlyTable(kv.Value, kv.Key, firstDailyDate, partitionRange.MinOverallDate);
                }

                // if (archiveDates.Any(x => x.Year == kv.Key.Year && x.Month == kv.Key.Month))
                // {
                //     MergeArchiveTableIntoMonthly(kv.Value, kv.Key, firstDailyDate.AddDays(-1));
                //   
                // }
            }


            //merge default table to new monthly partitions
            foreach (var monthYear in monthlyPartitionsToCreate)
            {
                var tableName = CreateDetachedMonthlyTable(monthYear);
                monthToTableName.Add(monthYear, tableName);
                if (defaultDates.Any(x => x.Year == monthYear.Year && x.Month == monthYear.Month))
                {
                    MergeDefaultTableIntoMonthly(tableName, monthYear, firstDailyDate.AddDays(-1),
                        null);
                }

                // if (archiveDates.Any(x => x.Year == monthYear.Year && x.Month == monthYear.Month))
                // {
                //     MergeArchiveTableIntoMonthly(tableName, monthYear, firstDailyDate.AddDays(-1));
                // }
                AttachMonthlyTable(tableName, monthYear, firstDailyDate, partitionRange.MinOverallDate);
                //CopyIndices(tableName);
            }
        }


        var dailyPartitionsToCreate =
            GetDailyPartitionsToCreate(firstDailyDate, now.AddDays(5), existingPartitions);

        //merge default table to new daily partitions
        foreach (var day in dailyPartitionsToCreate)
        {
            if (defaultDates.Any(x => x == day))
            {
                if (toMerge)
                {
                    var tableName = CreateDetachedDailyTable(day);

                    MergeDefaultTableIntoDaily(tableName, day);
                    AttachDailyTable(tableName, day);
                }
            }
            else
            {
                var tableName = CreateDetachedDailyTable(day);
                AttachDailyTable(tableName, day);
            }
        }

        // var dailyPartitionsToMerge = existingPartitions
        //     .Where(p => p.PartitionType == PartitionType.Day && p.From < borderDate)
        //     .ToList();
        //
        // foreach (var day in dailyPartitionsToMerge)
        // {
        //     var monthPartition = monthToTableName
        //         .Where(kv => day.From.Year == kv.Key.Year && day.From.Month == kv.Key.Month)
        //         .FirstOrDefault();
        //     
        //     if (monthPartition.Value != null)
        //     {
        //         //DetachTable(day.TableName);
        //         MergeDailyTableIntoMonthly(day.TableName, monthPartition.Value);
        //         DropTable(day.TableName);
        //     }
        // }

        //await AttachDefaultTable();
        // foreach (var kv in dateToTableName)
        // {
        //     if (kv.Key >= borderDate)
        //         AttachDailyTable(kv.Value, kv.Key);
        // }

        // foreach (var kv in monthToTableName)
        // {
        //     AttachMonthlyTable(kv.Value, kv.Key, borderDate);
        // }

        GenerateScriptFooter();
        var result = _sb.ToString();
        return result;
    }

    protected string CreateDetachedMonthlyTable(MonthYear monthYear)
    {
        var script = CreateMonthlyTableForPartitionScript(monthYear, PartitionedTableName, out var newTableName, 30);
        _sb.AppendLine(script);
        var detach = DetachTableScript(PartitionedTableName, newTableName);
        _sb.AppendLine(detach);
        return newTableName;
    }

    protected string CreateDetachedDailyTable(DateOnly date)
    {
        var dummyDate = new DateOnly(date.Year, date.Month, date.Day);
        var script =
            CreateTableForPartitionScript(date, date.AddDays(1), PartitionedTableName,
                out var newTableName,
                50); //      CreateDailyDetachedTableScript(date, PartitionedTableName, out var newTableName);
        _sb.AppendLine(script);
        var detach = DetachTableScript(PartitionedTableName, newTableName);
        _sb.AppendLine(detach);

        return newTableName;
    }


    protected void MergeDefaultTableIntoMonthly(string monthlyTableName, MonthYear monthYear, DateOnly enddate,
        DateOnly? startdate)
    {
        var script = MergeTableIntoMonthlyScript(monthlyTableName, monthYear, enddate, startdate, _defaultTableName,
            PartitionedColumnName);
        if (script is not null)
        {
            RaiseNotice($"Before merging default table into {monthlyTableName} partition");
            _sb.AppendLine(script);
            RaiseNotice($"Merged default table into {monthlyTableName} partition");
        }
    }


    protected void MergeDefaultTableIntoDaily(string dailyTableName, DateOnly date)
    {
        var script = MergeDefaultTableIntoDailyScript(dailyTableName, _defaultTableName, date, PartitionedColumnName);
        RaiseNotice($"Before merging default table into {dailyTableName} partition");
        _sb.AppendLine(script);
        RaiseNotice($"Merged default table into {dailyTableName} partition");
    }

    protected void MergeDailyTableIntoMonthly(string dailyTableName, string monthlyTableName)
    {
        var script = MergeTableScript(monthlyTableName, dailyTableName, PartitionedColumnName);
        _sb.AppendLine(script);
    }

    protected void MergeDailyTableIntoDefault(string dailyTableName)
    {
        var script = MergeTableScript(_defaultTableName, dailyTableName, PartitionedColumnName);
        _sb.AppendLine(script);
        RaiseNotice($"Merged {dailyTableName} partition into default");
    }


    protected void DetachTable(string tableName)
    {
        var script = DetachTableScript(PartitionedTableName, tableName);
        _sb.AppendLine(script);
    }

    protected void LockTable(string tableName)
    {
        var script = LockTableScript(tableName);
        _sb.AppendLine(script);
    }

    protected void SetLocalStatementTimeout(int timeout)
    {
        var script = SetLocalStatementTimeoutScript(timeout);
        _sb.AppendLine(script);
    }


    protected void AttachMonthlyTable(string tableName, MonthYear monthYear, DateOnly enddate, DateOnly? startDate)
    {
        //  borderDate = borderDate.AddDays(-1);
        if (startDate.HasValue && (startDate.Value.Month != monthYear.Month || startDate.Value.Year != monthYear.Year))
            startDate = null;
        // if (tartDate.Value.Month!=monthYear.Month ||startDate.Value.Year!=monthYear.Year )
        // startDate=null;
        var minDate = new DateOnly(monthYear.Year, monthYear.Month, 1);
        var maxDate = minDate.AddMonths(1);
        if (startDate is not null && startDate > minDate)
            minDate = startDate.Value;

        if (enddate < maxDate)
            maxDate = enddate;
        if (minDate > maxDate) return;
        var script = AttachMonthlyTableScript(tableName, minDate, maxDate, PartitionedTableName);
        _sb.AppendLine(script);
        RaiseNotice($"Attached monthly table  {tableName} ");
        //await _context.Database.ExecuteSqlRawAsync(script);
    }

    protected void DropTable(string tableName)
    {
        var script = DropTableScript(tableName);
        _sb.AppendLine(script);
        //await _context.Database.ExecuteSqlRawAsync(script);
    }

    protected void TruncateTable(string tableName)
    {
        var script = TruncateTableScript(tableName);
        _sb.AppendLine(script);
        //await _context.Database.ExecuteSqlRawAsync(script);
    }

    protected void AttachDefaultTable()
    {
        var script = AttachDefaultPartitionTableScript(PartitionedTableName, _defaultTableName);
        _sb.AppendLine(script);
        // await _context.Database.ExecuteSqlRawAsync(script);
    }

    protected void AttachDailyTable(string tableName, DateOnly date)
    {
        var script = AttachDailyTableScript(tableName, date, PartitionedTableName);
        _sb.AppendLine(script);

        // await _context.Database.ExecuteSqlRawAsync(script);
    }
}