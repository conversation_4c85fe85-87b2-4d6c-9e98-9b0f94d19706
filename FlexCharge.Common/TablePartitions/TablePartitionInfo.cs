using System;
using System.Text.RegularExpressions;

namespace FlexCharge.Common.Partitions;

public class TablePartitionInfo
{
    public string TableName { get; set; }
    public DateOnly From { get; set; }
    public DateOnly To { get; set; }
    public int PeriodDays => To.DayNumber - From.DayNumber;

    private static Regex DayRegex = new Regex(@".*\d{4}_\d{2}_\d{2}$");
    private static Regex MonthRegex = new Regex(@".*\d{4}_\d{2}$");
    private static Regex DefaultRegex = new Regex(@".*default$");

    internal PartitionType PartitionType
    {
        get
        {
            var result = DayRegex.IsMatch(TableName) ? PartitionType.Day :
                MonthRegex.IsMatch(TableName) ? PartitionType.Month :
                DefaultRegex.IsMatch(TableName) ? PartitionType.Default :
                PartitionType.Archive;

            return result;
        }
    }

    /// <summary>
    /// like 202410
    /// </summary>
    public int? Month
    {
        get
        {
            int? result = PartitionType switch
            {
                PartitionType.Day => int.Parse(TableName.Substring(TableName[^10], 7).Replace("_", "")),
                PartitionType.Month => int.Parse(TableName.Substring(TableName[^7], 7).Replace("_", "")),
                _ => null
            };
            return result;
        }
    }
}

internal enum PartitionType
{
    Archive,
    Day,
    Month,
    Default
}