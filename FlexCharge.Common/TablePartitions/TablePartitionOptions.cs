using System;

namespace FlexCharge.Common.Partitions;

public class TablePartitionOptions
{
    public TableSettings[] TableSettings { get; set; } = Array.Empty<TableSettings>();
    public int Timeout { get; set; }
    public static string SectionName = "tablepart";
}

public class TableSettings
{
    public string PartitionTableName { get; set; }
    public int NumberOfDailyPartitions { get; set; }

    public int NumberOfDailyPartitionsToCreateAhead { get; set; } = 5;
    public bool ToMerge { get; set; }
}