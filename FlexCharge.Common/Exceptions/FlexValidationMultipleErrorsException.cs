using System.Collections.Generic;
using System.Text;

namespace FlexCharge.Common.Exceptions;

public class FlexValidationMultipleErrorsException : FlexValidationException
{
    List<FlexValidationException> _errors { get; } = new();
    public IReadOnlyList<FlexValidationException> Errors => _errors;

    public FlexValidationMultipleErrorsException(string message) : base(message)
    {
    }

    public void AddError(FlexValidationException error)
    {
        _errors.Add(error);
    }

    public void AddError(string property, string message)
    {
        _errors.Add(new FlexValidationException(property, message));
    }

    public override string Message
    {
        get
        {
            StringBuilder message = new();

            message.Append(base.Message);
            message.Append(" [");


            for (var index = 0; index < Errors.Count; index++)
            {
                if (index > 0)
                    message.Append(", ");

                var error = Errors[index];
                message.Append($"{error.Property}: {error.Message}");
            }

            message.Append("]");

            return message.ToString();
        }
    }
}