using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Cloud.Storage;
using FlexCharge.Common.SftpServices.RenciSshAsyncExtensions;
using FlexCharge.Common.Telemetry;
using MassTransit;
using Renci.SshNet;
using Renci.SshNet.Common;

namespace FlexCharge.Common.SftpServices
{
    public class SftpClientService : ISftpClientService
    {
        private readonly ICloudStorage _cloudStorage;
        private readonly IPublishEndpoint _publisher;

        public SftpClientService(
            ICloudStorage cloudStorage,
            IPublishEndpoint publisher)
        {
            _cloudStorage = cloudStorage;
            _publisher = publisher;
        }

        public async Task DownloadFileAsync(string remoteFilePath, string localFilePath, string host, int port,
            string username, string password, CancellationToken cancellationToken = default)
        {
            try
            {
                using var client = new SftpClient(host, port, username, password);

                await client.ConnectAsync(cancellationToken);

                if (client.IsConnected)
                {
                    Console.WriteLine("SFTP connection successful.");

                    // download file from SFTP server
                    using var stream = new MemoryStream();

                    await client.DownloadAsync(remoteFilePath, stream);
                    stream.Seek(0, SeekOrigin.Begin);

                    // write downloaded file to local disk
                    using (var fileStream = new FileStream(localFilePath, FileMode.Create, FileAccess.Write))
                    {
                        await stream.CopyToAsync(fileStream);
                    }

                    Console.WriteLine($"File downloaded successfully from {remoteFilePath} to {localFilePath}");
                }
                else
                {
                    Console.WriteLine("SFTP connection failed.");
                }

                client.Disconnect();
            }
            catch (SshAuthenticationException ex)
            {
                Console.WriteLine($"Authentication failed: {ex.Message}");
                throw;
            }
            catch (SshException ex)
            {
                Console.WriteLine($"An SSH error occurred: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
                throw;
            }
        }

        public async Task<Stream> DownloadStreamAsync(string remoteFilePath, string host, int port, string username,
            string password, CancellationToken cancellationToken = default)
        {
            try
            {
                using var client = new SftpClient(host, port, username, password);
                await client.ConnectAsync(cancellationToken);

                if (client.IsConnected)
                {
                    Console.WriteLine("SFTP connection successful.");

                    // download file from SFTP server
                    using var stream = new MemoryStream();
                    await client.DownloadAsync(remoteFilePath, stream);
                    stream.Seek(0, SeekOrigin.Begin);

                    if (stream.Length == 0)
                        throw new Exception("File is empty");

                    Console.WriteLine($"File downloaded successfully from {remoteFilePath}");

                    return stream;
                }
                else
                {
                    Console.WriteLine("SFTP connection failed.");
                }

                client.Disconnect();
            }
            catch (SshAuthenticationException ex)
            {
                Console.WriteLine($"Authentication failed: {ex.Message}");
                throw;
            }
            catch (SshException ex)
            {
                Console.WriteLine($"An SSH error occurred: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
                throw;
            }

            return null;
        }


        public async Task UploadFileAsync(string localFilePath, string remoteFilePath, string host, int port,
            string username, string password, CancellationToken cancellationToken = default)
        {
            try
            {
                using var client = new SftpClient(host, port, username, password);

                await client.ConnectAsync(cancellationToken);

                if (client.IsConnected)
                {
                    Console.WriteLine("SFTP connection successful.");

                    // upload file to SFTP server
                    await using var fileStream = new FileStream(localFilePath, FileMode.Open);

                    await client.UploadAsync(fileStream, remoteFilePath);
                    Console.WriteLine($"File uploaded successfully from {localFilePath} to {remoteFilePath}");
                }
                else
                {
                    Console.WriteLine("SFTP connection failed.");
                }

                client.Disconnect();
            }
            catch (SshAuthenticationException ex)
            {
                Console.WriteLine($"Authentication failed: {ex.Message}");
                throw;
            }
            catch (SshException ex)
            {
                Console.WriteLine($"An SSH error occurred: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
                throw;
            }
        }

        public async Task UploadFileAsync(Stream stream, string remoteFilePath, string host, int port, string username,
            string password, CancellationToken cancellationToken = default)
        {
            using var workspan = Workspan.Start<SftpClientService>();
            workspan.Log.Information(
                "Uploading file to SFTP host: {host}, port: {port}, username: {username}, remoteFilePath: {remoteFilePath}  Stream Length: {stream}",
                host, port, username, remoteFilePath, stream.Length);

            try
            {
                using var client = new SftpClient(host, port, username, password);
                await client.ConnectAsync(cancellationToken);

                if (client.IsConnected)
                {
                    workspan.Log.Information("SFTP connection successful");
                    await client.UploadAsync(stream, remoteFilePath);
                    workspan.Log.Information("File uploaded successfully to {RemoteFilePath}", remoteFilePath);
                }
                else
                {
                    workspan.Log.Information("SFTP connection failed");
                }

                client.Disconnect();
            }
            catch (SshAuthenticationException ex)
            {
                workspan.Log.Information("Authentication failed: {ExMessage}", ex.Message);
                throw;
            }
            catch (SshException ex)
            {
                workspan.Log.Information("An SSH error occurred: {ExMessage}", ex.Message);
                throw;
            }
            catch (Exception ex)
            {
                workspan.Log.Information("An error occurred: {ExMessage}", ex.Message);
                throw;
            }
        }

        public async Task<List<string>> PollSftpAndUploadToS3Async(
            string host,
            int port,
            string username,
            string password,
            string pollingPath,
            string destinationBucket,
            string destinationPath,
            CancellationToken cancellationToken = default)
        {
            var fileNames = new List<string>();

            try
            {
                //log payload

                using var client = new SftpClient(host, port, username, password);

                await client.ConnectAsync(cancellationToken);

                if (client.IsConnected)
                {
                    var files = await client.ListDirectoryAsync(pollingPath);

                    foreach (var file in files)
                    {
                        try
                        {
                            if (file.IsDirectory) continue;

                            using var stream = new MemoryStream();
                            await client.DownloadAsync(file.FullName, stream);
                            stream.Position = 0;

                            await _cloudStorage.UploadFileAsync(stream, destinationBucket, file.Name, destinationPath);

                            fileNames.Add(file.Name);

                            await client.DeleteFileAsync(file.FullName, cancellationToken);
                        }
                        catch (Exception e)
                        {
                            Console.WriteLine(e);
                            throw;
                        }
                    }
                }
                else
                {
                    Console.WriteLine("client.IsConnected > client connection failed.");
                }

                client.Disconnect();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                Console.WriteLine("SFTP connection failed.");
                throw;
            }

            return fileNames;
        }

        public async Task<List<string>> PollSftpAndFilterAndUploadAsync(
            string host,
            int port,
            string username,
            string pollingPath,
            string password,
            string privateKey,
            long? maxFileSize,
            string? partialFileName,
            Func<string, Stream, Task> processStreamCallback,
            Func<string, Task<bool>> filterByFileName = null,
            CancellationToken cancellationToken = default)
        {
            using var workspan = Workspan.Start<SftpClientService>();
            var fileNames = new List<string>();

            try
            {
                using var client = CreateSftpClientWithKeys(host, port, username, privateKey, password);
                await client.ConnectAsync(cancellationToken);

                if (client.IsConnected)
                {
                    var files = await client.ListDirectoryAsync(pollingPath);

                    foreach (var file in files)
                    {
                        try
                        {
                            if (file.IsDirectory || file.IsSymbolicLink) continue;

                            if (file.Attributes.Size > maxFileSize)
                            {
                                workspan.Log.Fatal(
                                    $"PollSftpAndDownloadAsync > Skipping file {file.Name} as it exceeds 20 MB.");
                                continue;
                            }

                            if (partialFileName != null &&
                                !file.FullName.Contains(partialFileName))
                            {
                                continue;
                            }

                            if (filterByFileName != null)
                            {
                                var isFileProcessed = await filterByFileName(file.Name);
                                if (isFileProcessed)
                                {
                                    continue;
                                }
                            }

                            using var stream = new MemoryStream();
                            await client.DownloadAsync(file.FullName, stream);
                            stream.Position = 0;

                            await processStreamCallback(file.Name, stream);

                            fileNames.Add(file.Name);
                            await stream.DisposeAsync();
                        }
                        catch (Exception e)
                        {
                            workspan.RecordFatalException(e);
                        }
                    }
                }
                else
                {
                    Console.WriteLine("SFTP connection failed.");
                }

                client.Disconnect();
            }
            catch (Exception e)
            {
                Console.WriteLine($"An error occurred: {e.Message}");
                throw;
            }

            return fileNames;
        }

        private SftpClient CreateSftpClientWithKeys(string host, int port, string username, string privateKey,
            string passphrase = null)
        {
            var privateKeyStream = new MemoryStream(Encoding.UTF8.GetBytes(privateKey));
            privateKeyStream.Position = 0;
            var keyFile = new PrivateKeyFile(privateKeyStream, passphrase);
            var keyFiles = new[] {keyFile};

            var connectionInfo = new ConnectionInfo(host, port, username,
                new PrivateKeyAuthenticationMethod(username, keyFiles));

            return new SftpClient(connectionInfo);
        }
    }
}