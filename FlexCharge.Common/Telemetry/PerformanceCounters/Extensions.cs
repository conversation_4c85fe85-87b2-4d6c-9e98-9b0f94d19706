//#define ENABLE_PERORMANCE_METRICS // They are very costly at Amazon!!!! CHECK CODE FOR MEMORY LEAKS!!!

#define DEBUG_PERFORMANCE_COUNTERS
//see: https://im5tu.io/article/2020/12/publish-metrics-to-cloudwatch-in-.net-core/
//see: https://www.nocture.dk/2020/05/16/observing-.net-core-counters-in-cloudwatch/

using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Common.Telemetry.PerformanceCounters
{
    public static class PerformanceCountersTelemetryServiceCollectionExtensions
    {
        public static IServiceCollection AddCloudWatchPerformanceCountersTelemetry<TStartup>(
            this IServiceCollection services,
            bool enableInDebugEnvironment = false,
            string? overrideServiceName = null)
        {
#if ENABLE_PERORMANCE_METRICS
            bool performanceCountersEnabled = true;

#if DEBUG && !DEBUG_PERFORMANCE_COUNTERS
            performanceCountersEnabled = enableInDebugEnvironment;
#endif


            if (performanceCountersEnabled)
            {
                // Unbounded channel will handle all events, but can lead to out of memory exceptions
                ////var channel = Channel.CreateUnbounded<MetricUpdate>();


                //see: https://www.stevejgordon.co.uk/an-introduction-to-system-threading-channels
                var channel = Channel.CreateBounded<MetricUpdate>(
                    new BoundedChannelOptions(100)
                    {
                        SingleWriter = false,
                        FullMode = BoundedChannelFullMode.DropOldest
                    });

                services.AddSingleton<ChannelReader<MetricUpdate>>(channel);
                services.AddSingleton<ChannelWriter<MetricUpdate>>(channel);

                services.AddSingleton<IMetricsObservable, MetricsObservable>();

                services.AddTransient<ICloudWatchMetricsPublisher, CloudWatchMetricsPublisher>();

                services.AddHostedService<MetricsCollectionService>();
                services.AddHostedService<Publishers.CloudwatchPublishingService>();


                CloudwatchPublishingService.ServiceName = string.IsNullOrWhiteSpace(overrideServiceName)
                    ? GetServiceName<TStartup>()
                    : overrideServiceName;

                // Add observer implementations to connect to specific telemetry services 
                services.AddTransient<IObserver<MetricUpdate>, CloudwatchMetricObserver>();
            }

#endif


            return services;
        }

        private static string GetServiceName<TStartup>()
        {
            string startupNamespace = typeof(TStartup).Assembly.GetName().Name;
            int indexOfLastDot = startupNamespace.LastIndexOf('.');

            return startupNamespace.Substring(indexOfLastDot + 1);
        }
    }
}