using System;
using System.Collections.Generic;

namespace FlexCharge.Common.NotificationCenter;

/// <summary>
/// Represents a high-level intent to notify a set of audiences about a specific event.
/// </summary>
public interface INotificationMessage
{
    /// <summary>
    /// Unique ID for the notification event (used for deduplication/tracking).
    /// </summary>
    string EventId { get; set; }

    /// <summary>
    /// The type of event (e.g. "order.shipped", "risk.alert.detected").
    /// Used to resolve templates and routing logic.
    /// </summary>
    string EventType { get; set; }

    /// <summary>
    /// Optional: A short, human-readable name for the notification.
    /// </summary>
    string Name { get; set; }

    /// <summary>
    /// Optional: A human-readable description for observability or audit trails.
    /// </summary>
    string Description { get; set; }

    /// <summary>
    /// Target audience identifiers in the format: "type:referenceId"
    /// e.g. "user:123", "device:abc", "team:support"
    /// </summary>
    IEnumerable<string> Audiences { get; set; }

    /// <summary>
    /// Fields to be merged into templates for rendering.
    /// Values can be strings, numbers, or objects.
    /// </summary>
    Dictionary<string, string> MergeFields { get; set; }
}