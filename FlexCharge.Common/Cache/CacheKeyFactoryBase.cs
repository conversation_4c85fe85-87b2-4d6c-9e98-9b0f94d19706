using System;
using FlexCharge.Common.RuntimeEnvironment;
using Microsoft.Extensions.Caching.Distributed;

namespace FlexCharge.Common.Cache;

/// <summary>
/// Cache key factory base. It provides methods to create cache keys.
/// Should be inherited by the concrete cache key factory.
/// </summary>
public abstract class CacheKeyFactoryBase
{
    private static string EnvironmentName { get; }
    private static string EnvironmentScopePrefix { get; }

    static CacheKeyFactoryBase()
    {
        var environment = EnvironmentHelper.GetCurrentEnvironment();
        EnvironmentName = environment.ToString();

        EnvironmentScopePrefix = EnvironmentName + "-";

#if DEBUG
        EnvironmentScopePrefix = $"local-{Environment.UserName}-" + EnvironmentScopePrefix;
#endif
    }

    /// <summary>
    /// The default expiration options for cache entries.
    /// Used when no specific expiration options are provided.
    /// </summary>
    protected static DistributedCacheEntryOptions DefaultSlidingExpirationEntryOptions { get; } =
        new() {SlidingExpiration = TimeSpan.FromMinutes(5)};

    /// <summary>
    /// Add environment scope to the key
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public static CacheKey CreateScopedKey(string key)
    {
        return CreateKey(EnvironmentScopePrefix + key);
    }

    /// <summary>
    /// Add environment scope to the key
    /// </summary>
    /// <param name="key"></param>
    /// <param name="cacheEntryOptions"></param>
    /// <returns></returns>
    public static CacheKey CreateScopedKey(string key, DistributedCacheEntryOptions cacheEntryOptions)
    {
        return CreateKey(EnvironmentScopePrefix + key, cacheEntryOptions);
    }

    /// <summary>
    /// Create cache key
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public static CacheKey CreateKey(string key)
    {
        return new CacheKey(key, DefaultSlidingExpirationEntryOptions);
    }

    /// <summary>
    /// Create cache key
    /// </summary>
    /// <param name="key"></param>
    /// <param name="cacheEntryOptions"></param>
    /// <returns></returns>
    public static CacheKey CreateKey(string key, DistributedCacheEntryOptions cacheEntryOptions = default)
    {
        return new CacheKey(key, cacheEntryOptions);
    }

    protected string MicroserviceName { get; private set; }
}