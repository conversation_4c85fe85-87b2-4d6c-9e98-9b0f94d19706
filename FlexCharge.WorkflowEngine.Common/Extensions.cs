using FlexCharge.Utils;
using FlexCharge.WorkflowEngine.Common.Services.WorkflowService;
using FlexCharge.WorkflowEngine.Common.Workflows.Providers;
using FlexCharge.WorkflowEngine.Common.Workflows.Providers.ProviderFactory;
using FlexCharge.WorkflowEngine.Common.Workflows.Providers.ProviderServices;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.WorkflowEngine.Common;

public static class Extensions
{
    public static IServiceCollection AddWorkflows(this IServiceCollection services)
    {
        services.AddTransient<IWorkflowService, WorkflowService>();

        return services;
    }
}

public static class ServiceCollectionExtensionsForProviders
{
    public delegate IProvider? ProviderResolver(Guid providerId);

    public delegate IProviderService? ProviderServicesResolver(Guid providerId, Type providerServiceType);

    public static IServiceCollection AddProviders<TFromAssembly>(this IServiceCollection services)
    {
        AddProvidersFromAssembly<TFromAssembly>(services);

        services.AddTransient<ProviderResolver>(serviceProvider =>
            (providerId) => { return GetProvider(serviceProvider, providerId); });

        AddProviderServicesFromAssembly<TFromAssembly>(services);

        services.AddTransient<ProviderServicesResolver>(serviceProvider =>
            (providerId, serviceType) => { return GetProviderService(serviceProvider, providerId, serviceType); });

        services.AddTransient<IProviderFactory, ProviderFactory>();

        return services;
    }


    private static IProvider? GetProvider(IServiceProvider serviceProvider, Guid providerId)
    {
        var availableProviders = serviceProvider.GetServices<IProvider>();

        //TODO: Optimize by adding cache: (providerId) -> typeof(AConcreteProvider) 

        foreach (var provider in availableProviders)
        {
            if (provider.Id == providerId)
            {
                return provider; //!!!
            }
        }

        return null;
    }

    private static IProviderService? GetProviderService(IServiceProvider serviceProvider, Guid providerId,
        Type serviceType)
    {
        var availableServices = serviceProvider.GetServices(serviceType);

        //TODO: Optimize by adding cache: (providerName, serviceType) -> typeof(AConcreteProviderService) 

        foreach (var service in availableServices)
        {
            if (service is IProviderService providerService &&
                providerService.ProviderId == providerId)
            {
                return providerService; //!!!
            }
        }

        return null;
    }

    private static void AddProvidersFromAssembly<TFromAssembly>(IServiceCollection services)
    {
        var providers = ReflectionHelpers
            .GetInstantiableClassesThatImplementInterface<TFromAssembly, IProvider>();

        foreach (var provider in providers)
        {
            services.AddTransient(typeof(IProvider), provider);
        }
    }

    private static void AddProviderServicesFromAssembly<TFromAssembly>(IServiceCollection services)
    {
        var providerServices = ReflectionHelpers
            .GetInstantiableClassesThatImplementInterface<TFromAssembly, IProviderService>();


        foreach (var providerService in providerServices)
        {
            var implementedProviderServices = providerService
                .GetInterfaces()
                .Where(x => x.IsAssignableTo(typeof(IProviderService)))
                .Where(x => x != typeof(IProviderService));

            foreach (var providerServiceInterface in implementedProviderServices)
            {
                services.AddTransient(providerServiceInterface, providerService);
            }
        }
    }
}