using System.Collections.Generic;
using FlexCharge.Common.Shared.UIBuilder.DTO;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl;

namespace FlexCharge.WorkflowEngine.Workflows;

public interface IBlockLink
{
    public string Name { get; }
    public string Text { get; }
    public IBlock Source { get; }
    public IBlock Target { get; }
    public int ConnectorIndex { get; }
}

public class BlockLink : IBlockLink
{
    public string Name { get; set; }
    public string Text { get; set; }
    public IBlock Source { get; }
    public IBlock Target { get; set; }
    public int ConnectorIndex { get; set; }

    public BlockLink(IBlock source)
    {
        Source = source;
    }
}

public interface IBlock : IExternalControllable
{
    BlockType Type { get; }

    string Name { get; }
    string FullName { get; }

    /// <summary>
    /// User-friendly name of the block instance to be shown in the UI
    /// E.g. "Dynamic amount authorization (A/B Test Block)" 
    /// </summary>
    string InstanceName { get; }

    bool IsTerminalBlock { get; }

    public IEnumerable<IBlockLink> GetNextLinks();
    public Dictionary<string, string> GetParameters();

    void SetInstanceName(string instanceName);
}

public interface IBlockWithParametersEditor
{
    public IEnumerable<ChallengeItemDTO> GetParametersEditor();
}

public interface IGroupBlock : IBlock
{
    public IEnumerable<IBlock> GetChildBlocks();
}

public interface IParallelBlock : IGroupBlock
{
}

public interface ISequenceBlock
    : IGroupBlock
{
}