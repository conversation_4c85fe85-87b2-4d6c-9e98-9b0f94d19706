using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Amazon.S3.Model;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl;
using Newtonsoft.Json;

namespace FlexCharge.WorkflowEngine.Workflows;

public partial class WorkflowDefinition
{
    public List<BlockDefinition> Blocks { get; set; }

    /// <summary>
    ///  Workflow External Controls. Blocks can also have their own external controls.
    /// </summary>
    public List<ExternalControlDefinition> WorkflowExternalControls { get; set; }

    [JsonIgnore] public BlockDefinition StartBlock => Blocks?.FirstOrDefault();

    public static WorkflowDefinition CreateWorkflowDefinition(IWorkflow workflow)
    {
        WorkflowDefinition workflowDefinition = new();
        workflowDefinition.CreateFromWorkflow(workflow);

        return workflowDefinition;
    }

    #region Factory Methods

    private void CreateFromWorkflow(IWorkflow workflow)
    {
        Blocks = new();

        // Adding all blocks to Blocks list and _blockToBlockDefinition map

        int blockLinkReferenceId = 0;
        Dictionary<IBlock, BlockDefinition> blockToBlockDefinition = new();
        TraverseBlocksGraph(workflow.GetStartBlock(),
            (block, parentBlock) =>
            {
                var blockDefinition = BlockDefinitionFactory.CreateBlockDefinition(block);
                blockDefinition.RefId = blockLinkReferenceId++;

                // Only add root blocks to Blocks list. Child blocks will be added to their parent's ChildBlocks list
                if (parentBlock == null)
                {
                    Blocks.Add(blockDefinition);
                }
                else
                {
                    var parentBlockDefinition = blockToBlockDefinition[parentBlock];

                    if (parentBlockDefinition.ChildBlocks == null)
                        parentBlockDefinition.ChildBlocks = new();

                    parentBlockDefinition.ChildBlocks.Add(blockDefinition);
                }

                blockToBlockDefinition[block] = blockDefinition;

                return true;
            },
            null);

        //Creating links between blocks
        TraverseBlocksGraph(workflow.GetStartBlock(),
            null,
            (block, link) =>
            {
                var blockDefinition = blockToBlockDefinition[block];
                var nextLinkedBlockDefinition = blockToBlockDefinition[link.Target];

                var linkDefinition = LinkDefinition.Create(link.Name, link.Text,
                    blockDefinition,
                    link.ConnectorIndex,
                    nextLinkedBlockDefinition,
                    0);

                blockDefinition.AddLink(linkDefinition);
            });

        blockToBlockDefinition.Clear();
        blockToBlockDefinition = null;

        #region Collecting workflow's own external controls

        WorkflowExternalControls = workflow.GetExternalControls()
            .Select(x => ExternalControlDefinition.Create(x)).ToList();

        #endregion
    }

    #endregion


    #region Graph Traverse

    /// <summary>
    /// 
    /// </summary>
    /// <returns>True if traversing should continue, false if it should stop</returns>
    delegate bool ProcessBlockDelegate(IBlock block, IBlock? parentBlock);

    delegate void ProcessLinkDelegate(IBlock block, IBlockLink link);

    private void TraverseBlocksGraph(IBlock block,
        ProcessBlockDelegate processBlock, ProcessLinkDelegate processLink,
        HashSet<IBlock> alreadyProcessedBlocks = null,
        HashSet<IBlockLink> alreadyProcessedLinks = null,
        Stack<IBlock> parentBlocks = null)
    {
        bool startingBlock = alreadyProcessedBlocks == null;

        if (startingBlock)
        {
            alreadyProcessedBlocks = new();
            alreadyProcessedLinks = new();
            parentBlocks = new();
        }


        if (block == null || alreadyProcessedBlocks.Contains(block))
            return;

        alreadyProcessedBlocks.Add(block);

        parentBlocks.TryPeek(out var parentBlock);

        if (processBlock != null)
        {
            // Is stop traversing requested?
            if (processBlock(block, parentBlock) == false)
                return;
        }

        var groupBlock = block as IGroupBlock;
        if (groupBlock != null)
        {
            var childBlocks = groupBlock.GetChildBlocks();
            if (childBlocks != null)
            {
                parentBlocks.Push(block);

                foreach (var childBlock in childBlocks)
                {
                    TraverseBlocksGraph(childBlock, processBlock, processLink,
                        alreadyProcessedBlocks, alreadyProcessedLinks, parentBlocks);
                }

                parentBlocks.Pop();
            }
        }

        var nextLinks = block.GetNextLinks();

        if (nextLinks != null)
        {
            foreach (var link in nextLinks)
            {
                if (alreadyProcessedLinks.Contains(link))
                    continue;

                alreadyProcessedLinks.Add(link);

                if (link?.Target == null)
                    continue;

                if (processLink != null)
                    processLink(block, link);

                TraverseBlocksGraph(link.Target, processBlock, processLink,
                    alreadyProcessedBlocks, alreadyProcessedLinks, parentBlocks);
            }
        }

        if (startingBlock)
        {
            alreadyProcessedBlocks.Clear();
            alreadyProcessedBlocks = null;

            alreadyProcessedLinks.Clear();
            alreadyProcessedLinks = null;

            parentBlocks.Clear();
            parentBlocks = null;
        }
    }


    /// <summary>
    /// 
    /// </summary>
    /// <returns>True if traversing should continue, false if it should stop</returns>
    delegate bool ProcessBlockDefinitionDelegate(BlockDefinition block, BlockDefinition? parentBlock);

    delegate void ProcessLinkDefinitionDelegate(BlockDefinition block, LinkDefinition link);


    private static void TraverseBlockDefinitionsGraph(BlockDefinition blockDefinition,
        ProcessBlockDefinitionDelegate? processBlock,
        ProcessLinkDefinitionDelegate? processLink,
        HashSet<BlockDefinition> alreadyProcessedBlocks = null,
        HashSet<LinkDefinition> alreadyProcessedLinks = null,
        Stack<BlockDefinition> parentBlocks = null)
    {
        bool startingBlock = alreadyProcessedBlocks == null;

        if (startingBlock)
        {
            alreadyProcessedBlocks = new();
            alreadyProcessedLinks = new();
            parentBlocks = new();
        }

        if (blockDefinition == null || alreadyProcessedBlocks.Contains(blockDefinition))
            return;

        alreadyProcessedBlocks.Add(blockDefinition);

        parentBlocks.TryPeek(out var parentBlock);

        if (processBlock != null)
        {
            // Is stop traversing requested?
            if (processBlock(blockDefinition, parentBlock) == false)
                return;
        }

        if (blockDefinition.ChildBlocks != null)
        {
            parentBlocks.Push(blockDefinition);

            foreach (var childBlock in blockDefinition.ChildBlocks)
            {
                TraverseBlockDefinitionsGraph(childBlock, processBlock, processLink,
                    alreadyProcessedBlocks, alreadyProcessedLinks, parentBlocks);
            }

            parentBlocks.Pop();
        }

        var nextLinks = blockDefinition.Links;

        if (nextLinks != null)
        {
            foreach (var link in nextLinks)
            {
                if (alreadyProcessedLinks.Contains(link))
                    continue;

                alreadyProcessedLinks.Add(link);

                if (processLink != null)
                    processLink(blockDefinition, link);

                TraverseBlockDefinitionsGraph(link.TargetBlock, processBlock, processLink,
                    alreadyProcessedBlocks, alreadyProcessedLinks, parentBlocks);
            }
        }

        if (startingBlock)
        {
            alreadyProcessedBlocks.Clear();
            alreadyProcessedBlocks = null;

            alreadyProcessedLinks.Clear();
            alreadyProcessedLinks = null;

            parentBlocks.Clear();
            parentBlocks = null;
        }
    }

    #endregion

    public static WorkflowDefinition Deserialize(string storedDefinition)
    {
        var workflowDefinition = JsonConvert.DeserializeObject<WorkflowDefinition>(storedDefinition);

        Dictionary<int, BlockDefinition> blockDefinitionsMap = workflowDefinition.PopulateBlockDefinitions();

        InitializeBlockLinks(workflowDefinition.Blocks);

        #region InitializeBlockLinks function

        void InitializeBlockLinks(IEnumerable<BlockDefinition> blocks,
            HashSet<BlockDefinition>? alreadyProcessedBlocks = null)
        {
            if (blocks == null)
                return;

            bool topLevelCall = alreadyProcessedBlocks == null;

            if (alreadyProcessedBlocks == null)
                alreadyProcessedBlocks = new();

            foreach (var block in blocks)
            {
                alreadyProcessedBlocks.Add(block);

                if (block.Links != null)
                {
                    foreach (var link in block.Links)
                    {
                        if (link.SourceBlock == null)
                        {
                            link.SourceBlock = block;
                            link.TargetBlock = blockDefinitionsMap[link.TargetRefId];
                        }
                    }

                    // Process links of connected blocks
                    InitializeBlockLinks(block.Links.Select(x => x.TargetBlock),
                        alreadyProcessedBlocks);
                }

                // Process links of child blocks
                InitializeBlockLinks(block.ChildBlocks, alreadyProcessedBlocks);
            }

            if (topLevelCall)
            {
                alreadyProcessedBlocks.Clear();
                alreadyProcessedBlocks = null;
            }
        }

        #endregion

        return workflowDefinition;
    }

    private Dictionary<int, BlockDefinition> PopulateBlockDefinitions()
    {
        Dictionary<int, BlockDefinition> blockDefinitionsMap = new();

        // We cannot traverse as links are not initialized yet -> so we go block-by-block and populate the map including child blocks
        foreach (var block in Blocks)
        {
            PopulateBlockDefinitions(block, blockDefinitionsMap);
        }

        return blockDefinitionsMap;
    }

    private void PopulateBlockDefinitions(BlockDefinition block, Dictionary<int, BlockDefinition> blockDefinitionsMap)
    {
        blockDefinitionsMap[block.RefId] = block;

        if (block.ChildBlocks != null)
        {
            foreach (var childBlock in block.ChildBlocks)
            {
                PopulateBlockDefinitions(childBlock, blockDefinitionsMap);
            }
        }
    }


    public BlockDefinition? GetBlockDefinitionByReferenceId(int blockReferenceId)
    {
        BlockDefinition? blockDefinition = null;

        TraverseBlockDefinitionsGraph(
            StartBlock,
            (block, parentBlock) =>
            {
                if (block.RefId == blockReferenceId)
                {
                    blockDefinition = block;
                    return false;
                }

                return true;
            },
            null);

        return blockDefinition;
    }


    private IEnumerable<BlockDefinition> GetOrphanedChainsOfBlocks()
    {
        // Block is orphaned if it is not reachable from start block
        HashSet<BlockDefinition> reachableBlocks = new();

        // Finding all blocks reachable from start block
        TraverseBlockDefinitionsGraph(StartBlock,
            (block, parentBlock) => reachableBlocks.Add(block),
            (block, link) => { });

        // Finding all blocks that are not reachable from any other block block
        foreach (var startBlock in Blocks)
        {
            if (reachableBlocks.Contains(startBlock))
                continue;

            TraverseBlockDefinitionsGraph(startBlock,
                (block, parentBlock) =>
                {
                    // Do not add start block to reachable blocks - as we don't know if it's reachable from another block
                    if (block != startBlock)
                    {
                        return
                            reachableBlocks
                                .Add(block); // Stop traversing if block is already reachable from another block
                    }
                    else return true;
                },
                (block, link) => { });
        }

        foreach (var block in Blocks)
        {
            if (reachableBlocks.Contains(block))
                continue;

            yield return block;
        }
    }

    #region External Controls

    /// <summary>
    /// Returns all external controls defined in the workflow.
    /// </summary>
    /// <returns></returns>
    /// <exception cref="InvalidOperationException"></exception>
    public IEnumerable<ExternalControlDefinition> GetAllExternalControls()
    {
        List<ExternalControlDefinition> externalControls = new();

        // Adding external controls from workflow itself
        externalControls.AddRange(WorkflowExternalControls);

        // Collecting external controls from all blocks
        TraverseBlockDefinitionsGraph(StartBlock,
            (block, parentBlock) =>
            {
                externalControls.AddRange(block.ExternalControls);

                return true;
            },
            null);

        Dictionary<Guid, ExternalControlDefinition> alreadyDefinedControls = new();
        foreach (var control in externalControls)
        {
            if (alreadyDefinedControls.TryGetValue(control.Id, out var existingControl))
            {
                if (!control.Equals(existingControl)) // ExternalControlDefinition is a record, Equals compares values
                {
                    throw new InvalidOperationException(
                        $"External control with id {control.Id} is already defined with different parameters");
                }
            }
            else
            {
                alreadyDefinedControls.Add(control.Id, control);
            }
        }

        return externalControls;
    }

    #endregion
}