using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Reporting;
using FlexCharge.Payments.Reporting;

namespace FlexCharge.Payments.Reports.Lookups;

public class Reports : ILookupService
{
    private IEnumerable<IReport> _reports;

    public Reports(IEnumerable<IReport> reports)
    {
        _reports = reports;
    }
    public Task<Lookup> GetLookup()
    {
        var retval = new Lookup()
        {
            Title = "availableReports",
            IdToValue = _reports.Where(r => r != null).ToDictionary(r => r.ReportId.ToString(), r => r.ReportName)
        };
        return Task.FromResult(retval);
    }
}