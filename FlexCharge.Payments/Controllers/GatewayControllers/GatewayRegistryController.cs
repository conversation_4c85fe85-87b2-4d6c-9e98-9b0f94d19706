using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Authentication;
using FlexCharge.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Payments.Controllers.GatewayControllers;

[Route("gateways/[controller]/[action]")]
[ApiController]
[JwtAuth]
public class GatewayRegistryController
{
    private readonly PostgreSQLDbContext _dbContext;

    public GatewayRegistryController(PostgreSQLDbContext dbContext)
    {
        _dbContext = dbContext;
    }


    [HttpGet]
    [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
    [ProducesResponseType(typeof(Dictionary<string, List<string>>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async ValueTask<IActionResult> Get()
    {
        var result = await _dbContext.ProvidersMeta.Select(g => new NameLogo(g.Name.FirstCharToUpper(), g.LogoIcon))
            .ToListAsync();


        return new OkObjectResult(result);
    }
}

public record NameLogo(string Name, string? LogoIcon = null);