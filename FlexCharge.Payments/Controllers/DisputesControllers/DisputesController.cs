using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CsvHelper;
using CsvHelper.Configuration;
using FlexCharge.Common.Activities;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Telemetry;
using FlexCharge.Payments.Activities;
using FlexCharge.Payments.DTO.reports;
using FlexCharge.Payments.DTO.Reports;
using FlexCharge.Payments.Enums;
using FlexCharge.Payments.Services.DisputeServices;
using FlexCharge.Payments.Services.DisputeServices.Models;
using FlexCharge.Utils;
using FlexCharge.Payments.Services.DisputeServices.Kount;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Payments.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [JwtAuth]
    public class DisputesController : BaseController
    {
        private readonly PostgreSQLDbContext _dbcontext;
        private readonly AppOptions _globalData;
        private readonly IDisputeServices _disputeServices;
        private readonly IDisputeImportServices _disputeImportServices;
        private readonly IActivityService _activityService;
        private readonly IBackgroundWorkerCommandQueue _backgroundWorkerCommandQueue;
        private readonly IKountAlertService _kountAlertService;
        private readonly IMyRcvrAlertService _myRcvrAlertService;
        private readonly DisputeQueueService _disputeQueueService;
        private readonly IServiceProvider _serviceProvider;

        public DisputesController(
            IOptions<AppOptions> globalData,
            PostgreSQLDbContext context,
            IDisputeServices disputeServices,
            IDisputeImportServices disputeImportServices,
            IActivityService activityService,
            IKountAlertService kountAlertService,
            IMyRcvrAlertService myRcvrAlertService,
            IServiceProvider serviceProvider,
            IBackgroundWorkerCommandQueue backgroundWorkerCommandQueue, DisputeQueueService disputeQueueService)
        {
            _dbcontext = context;
            _disputeServices = disputeServices;
            _disputeImportServices = disputeImportServices;
            _globalData = globalData.Value;
            _activityService = activityService;
            _backgroundWorkerCommandQueue = backgroundWorkerCommandQueue;
            _disputeQueueService = disputeQueueService;
            _kountAlertService = kountAlertService;
            _myRcvrAlertService = myRcvrAlertService;
            _serviceProvider = serviceProvider;
        }

        [HttpPost]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(CreateDisputeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Post(CreateDisputeRequest request, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, request, _globalData);

            var response = new CreateDisputeResponse();
            try
            {
                if (!ModelState.IsValid)
                {
                    await _activityService.CreateActivityAsync(ApiErrorActivities.ModelStateError, ModelState,
                        set => set
                            .CorrelationId(request.OrderId)
                            .Data(request));
                    return ValidationProblem();
                }

                response = await _disputeServices.CreateDisputeAsync(request, token: token);

                return
                    response.Success ? Ok(response) : BadResponse(response);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .CorrelationId(request.OrderId)
                        .Data(request));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed creating dispute");
            }
        }

        [HttpPost("pre")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(CreateDisputeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> PreDispute(PreDisputeRequest request, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, request, _globalData)
                .Baggage(nameof(request.OrderId), request.OrderId);

            try
            {
                if (!ModelState.IsValid)
                {
                    await _activityService.CreateActivityAsync(ApiErrorActivities.ModelStateError, ModelState,
                        set => set
                            .CorrelationId(request.OrderId));
                    return ValidationProblem();
                }

                request.IsManualInserted = true;
                // request.Stage = DisputeStage.PRE_DISPUTE;
                var response = await _disputeServices.AddPreDisputeAsync(request, token: token);

                if (response.Success)
                    return Ok(response);

                ModelState.AddModelError("General", response?.Message ?? "");

                return BadResponse(ModelState);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .CorrelationId(request.OrderId)
                        .Data(request));
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed Add PreDispute");
            }
        }

        [HttpGet]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(DisputeResponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Get(Guid? mid, string query, DateTime? from, DateTime? to,
            DateTime? requestFrom, DateTime? requestTo, string? timezone, DateTime? createdFrom, DateTime? createdTo,
            string sort, string? sortField, [FromQuery] List<string>? providerName, [FromQuery] List<string>? cardType,
            [FromQuery] List<string>? stage, bool? isWebhook, bool? isMatch, bool? isArchived, bool? isFileImported,
            bool? isManualInserted, int pageSize = 10, int pageNumber = 1)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, null, _globalData);

            try
            {
                if (!ModelState.IsValid)
                {
                    await _activityService.CreateActivityAsync(ApiErrorActivities.ModelStateError, ModelState,
                        set => set
                            .TenantId(mid));
                    return ValidationProblem();
                }

                DisputeResponseDTO disputes;

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    disputes =
                        await _disputeServices.AdminGetDisputes(pageSize, pageNumber, mid, query, from, to, requestFrom,
                            requestTo, createdFrom, createdTo, timezone, sort, sortField, providerName, cardType,
                            stage, isWebhook, isMatch, isArchived, isFileImported, isManualInserted);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) ||
                         HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    var pid = GetPID();

                    if (pid == null || pid == Guid.Empty)
                    {
                        return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                    }

                    disputes =
                        await _disputeServices.PartnerGetDisputes(pageSize, pageNumber, pid, mid, query, from, to,
                            requestFrom,
                            requestTo, createdFrom, createdTo, timezone, sort, sortField, providerName, cardType,
                            stage, isWebhook, isMatch, isArchived, isFileImported, isManualInserted);
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }


                return Ok(disputes);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(mid));
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed to return disputes");
            }
        }

        [HttpGet()] // GET BY ID
        [Route("{id:guid}")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(DisputeResponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Get([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, null, _globalData);

            try
            {
                return Ok(await _disputeServices.GetDisputeByIdAsync(id));
            }
            catch (Exception e)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed to return item");
            }
        }

        [HttpGet("export")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(DisputeResponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Export(Guid? mid, string query, DateTime? from, DateTime? to,
            DateTime? requestFrom, DateTime? requestTo, string? timezone, DateTime? createdFrom, DateTime? createdTo,
            string sort, string? sortField, [FromQuery] List<string>? providerName, [FromQuery] List<string>? cardType,
            [FromQuery] List<string>? stage, bool? isWebhook, bool? isMatch, bool? isArchived, bool? isFileImported,
            bool? isManualInserted, int pageSize = 10, int pageNumber = 1)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, null, _globalData);

            try
            {
                if (!ModelState.IsValid)
                {
                    await _activityService.CreateActivityAsync(ApiErrorActivities.ModelStateError, ModelState,
                        set => set
                            .TenantId(mid));
                    return ValidationProblem();
                }

                string csv;

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    csv =
                        await _disputeServices.GenerateAsync(null, pageSize, pageNumber, mid, query, from, to,
                            requestFrom,
                            requestTo, createdFrom, createdTo, timezone, sort, sortField, providerName, cardType,
                            stage, isWebhook, isMatch, isArchived, isFileImported, isManualInserted);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) ||
                         HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    var pid = GetPID();

                    if (pid == null || pid == Guid.Empty)
                    {
                        return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                    }

                    csv =
                        await _disputeServices.GenerateAsync(pid, pageSize, pageNumber, mid, query, from, to,
                            requestFrom,
                            requestTo, createdFrom, createdTo, timezone, sort, sortField, providerName, cardType,
                            stage, isWebhook, isMatch, isArchived, isFileImported, isManualInserted);
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                return Ok(csv);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(mid));
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed to return disputes");
            }
        }

        [HttpGet("dispute-types")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(CreateDisputeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetDisputeTypes(CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, null, _globalData);

            try
            {
                if (!ModelState.IsValid)
                {
                    await _activityService.CreateActivityAsync(ApiErrorActivities.ModelStateError, ModelState,
                        set => set
                            .TenantId(GetMID()));
                    return ValidationProblem();
                }

                var values = Enum.GetValues(typeof(CreditCardDisputeType));

                var disputes = new List<CreditCardDisputeDTO>();

                foreach (var value in values)
                {
                    disputes.Add(new CreditCardDisputeDTO
                    {
                        Code = (int) value,
                        Name = value.ToString()
                    });
                }

                return Ok(disputes);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .CorrelationId(GetMID()));
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed to return dispute types");
            }
        }

        [HttpGet("dispute-management-system")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(CreateDisputeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetManagementSystem(CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, null, _globalData);

            try
            {
                var manageSystem = new List<string>();

                foreach (var field in typeof(AlertProviders).GetFields(System.Reflection.BindingFlags.Public |
                                                                       System.Reflection.BindingFlags.Static))
                {
                    manageSystem.Add(field.GetValue(null).ToString());
                }

                return Ok(manageSystem);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(GetMID()));
                return StatusCode(StatusCodes.Status500InternalServerError,
                    "Failed to return dispute management system");
            }
        }

        [HttpGet("dispute-card-brands")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(CreateDisputeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetCardTypes(CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, null, _globalData);

            try
            {
                var cardBrands = new List<string>();

                foreach (CardBrands foo in Enum.GetValues(typeof(CardBrands)))
                    cardBrands.Add(foo.ToString().Replace("_", " "));

                return Ok(cardBrands);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(GetMID()));
                return StatusCode(StatusCodes.Status500InternalServerError,
                    "Failed to return dispute card brands");
            }
        }

        [HttpGet("kount-status-codes")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(CreateDisputeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetKountStatusCodes(CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, null, _globalData);

            try
            {
                var ethocaFraud = new List<string>();
                var ethocaDispute = new List<string>();
                var chargeblastStatusCodes = new List<string>();

                foreach (KountEthocaFraudStatus foo in Enum.GetValues(typeof(KountEthocaFraudStatus)))
                {
                    ethocaFraud.Add(foo.ToString().Replace("_", " "));
                }

                foreach (KountEthocaDisputeStatus foo in Enum.GetValues(typeof(KountEthocaDisputeStatus)))
                {
                    ethocaDispute.Add(foo.ToString().Replace("_", " "));
                }

                foreach (ChargeblastStatusCodes foo in Enum.GetValues(typeof(ChargeblastStatusCodes)))
                {
                    chargeblastStatusCodes.Add(foo.ToString());
                }

                var verifyStatusCodes = KountVerifiStatusCodes.GetKountVerifiStatusCodes();

                var response = new
                {
                    ethocaFraud,
                    ethocaDispute,
                    verifyStatusCodes,
                    chargeblastStatusCodes
                };
                return Ok(response);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(GetMID()));
                return StatusCode(StatusCodes.Status500InternalServerError,
                    "Failed to return kount status codes");
            }
        }

        [HttpGet("dispute-activities")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(CreateDisputeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetDisptActivities(Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, null, _globalData);

            try
            {
                if (!ModelState.IsValid)
                {
                    await _activityService.CreateActivityAsync(ApiErrorActivities.ModelStateError, ModelState,
                        set => set
                            .TenantId(GetMID()));
                    return ValidationProblem();
                }

                var activities = _disputeServices.GetActivities(id);

                return Ok(activities);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(GetMID())
                        .Meta(meta => meta.SetValue("disputeId", id)));
                return StatusCode(StatusCodes.Status500InternalServerError,
                    "Failed to return dispute activities");
            }
        }

        [HttpPost("match")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(PreDisputeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Match(DisputeMatchRequestDTO payload, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, payload, _globalData);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                await _activityService.CreateActivityAsync(DisputeReportsActivities.DisputeReports_Match_Requested,
                    set => set
                        .TenantId(GetMID())
                        .CorrelationId(payload.TransactionId)
                        .Data(payload)
                        .Meta(meta => meta
                            .SetValue("DisputeQueueItemId", payload.DisputeQueueItemId)
                            .SetValue("IssueRefund", payload.IssueRefund)
                            .TransactionReference(payload.TransactionId)));

                var response = await _disputeServices.MatchDisputeAsync(payload, token);

                return Ok(response);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "Failed to reconcile disputes");

                await _activityService.CreateActivityAsync(DisputeReportsErrorsActivities.DisputeReports_Match_Error, e,
                    set => set
                        .TenantId(GetMID())
                        .CorrelationId(payload.TransactionId)
                        .Data(payload)
                        .Meta(meta => meta
                            .SetValue("DisputeQueueItemId", payload.DisputeQueueItemId)
                            .TransactionReference(payload.TransactionId)));

                return StatusCode(StatusCodes.Status500InternalServerError,
                    "Failed to match dispute");
            }
        }

        [HttpPost("reconcile")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(PreDisputeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Reconcile(DisputeReconcileRequestDTO payload, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, payload, _globalData);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                await _activityService.CreateActivityAsync(DisputeReportsActivities.DisputeReports_Reconcile_Requested,
                    set => set
                        .TenantId(GetMID())
                        .Data(payload));

                var response = await _disputeServices.ReconcileDisputeAsync(payload, token);

                return Ok(response);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);

                await _activityService.CreateActivityAsync(
                    DisputeReportsErrorsActivities.DisputeReports_Reconcile_Error, e,
                    set => set
                        .TenantId(GetMID())
                        .Data(payload)
                        .Meta(meta => meta
                            .Error(e.Message)));

                return StatusCode(StatusCodes.Status500InternalServerError,
                    "Failed to reconcile dispute");
            }
        }

        [HttpPost("alert-kount")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> AlertKount(AlertKountDTO payload, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, payload, _globalData);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                await _activityService.CreateActivityAsync(
                    PaymentsIncomingWebhooksActivities.Payments_IncomingWebhooks_AlertProvider_Requested,
                    set => set
                        .TenantId(GetMID())
                        .CorrelationId(payload.TransactionId)
                        .Data(payload)
                        .Meta(meta => meta
                            .ServiceProvider(AlertProviders.Kount)
                            .SetValue("DisputeQueueItemId", payload.DisputeQueueItemId)
                            .TransactionReference(payload.TransactionId)
                            .SetValue("KountStatusCode", payload.KountStatusCode)));

                var disputeQueueItems =
                    await _dbcontext.DisputeQueueItems.FirstOrDefaultAsync(x => x.Id == payload.DisputeQueueItemId,
                        token);

                if (disputeQueueItems == null)
                    return NotFound();

                var transaction =
                    await _dbcontext.Transactions.FirstOrDefaultAsync(x => x.Id == payload.TransactionId, token);
                await _kountAlertService.AlertKountAsync(disputeQueueItems, transaction,
                    payload.KountStatusCode, false, payload.Comments);

                await _disputeQueueService.UpdateDisputeQueueItemAsync(disputeQueueItems, new DisputeQueueItemUpdateDTO
                {
                    RepliedToEthoca = DateTime.UtcNow,
                }, token);

                ;
                await _disputeQueueService.RemoveDisputeQueueItemAsync(disputeQueueItems, token);

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);

                await _activityService.CreateActivityAsync(
                    PaymentsIncomingWebhooksErrorActivities.Payments_IncomingWebhooks_AlertProvider_Error, e,
                    set => set
                        .TenantId(GetMID())
                        .Meta(meta => meta
                            .ServiceProvider(AlertProviders.Kount)
                            .SetValue("KountStatusCode", payload.KountStatusCode)
                            .SetValue("DisputeQueueItemId", payload.DisputeQueueItemId)
                            .TransactionReference(payload.TransactionId)));

                return StatusCode(StatusCodes.Status500InternalServerError,
                    "Failed alert kount");
            }
        }

        [HttpPost("alert-myrcvr")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> AlertMyRcvr(AlertMyrcvrDTO payload, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, payload, _globalData);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                await _activityService.CreateActivityAsync(
                    PaymentsIncomingWebhooksActivities.Payments_IncomingWebhooks_AlertProvider_Requested,
                    set => set
                        .TenantId(GetMID())
                        .CorrelationId(payload.TransactionId)
                        .Data(payload)
                        .Meta(meta => meta
                            .ServiceProvider(AlertProviders.MyRCVR)
                            .SetValue("DisputeQueueItemId", payload.DisputeQueueItemId)
                            .TransactionReference(payload.TransactionId)
                            .SetValue("StatusCode", payload.StatusCode)));

                var disputeQueueItems =
                    await _dbcontext.DisputeQueueItems.FirstOrDefaultAsync(x => x.Id == payload.DisputeQueueItemId,
                        token);

                if (disputeQueueItems == null)
                    return NotFound();

                var transaction =
                    await _dbcontext.Transactions.FirstOrDefaultAsync(x => x.Id == payload.TransactionId, token);

                payload.Comments = "Transaction ID: " + payload.TransactionId + " - " + payload.Comments;

                await _myRcvrAlertService.AlertMyrcvrAsync(disputeQueueItems, transaction,
                    payload.StatusCode, false, payload.Comments);

                await _disputeQueueService.UpdateDisputeQueueItemAsync(disputeQueueItems, new DisputeQueueItemUpdateDTO
                {
                    RepliedToEthoca = DateTime.UtcNow,
                }, token);


                await _disputeQueueService.RemoveDisputeQueueItemAsync(disputeQueueItems, token);

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);

                await _activityService.CreateActivityAsync(
                    PaymentsIncomingWebhooksErrorActivities.Payments_IncomingWebhooks_AlertProvider_Error, e,
                    set => set
                        .TenantId(GetMID())
                        .Meta(meta => meta
                            .ServiceProvider(AlertProviders.MyRCVR)
                            .SetValue("KountStatusCode", payload.StatusCode)
                            .SetValue("DisputeQueueItemId", payload.DisputeQueueItemId)
                            .TransactionReference(payload.TransactionId)));

                return StatusCode(StatusCodes.Status500InternalServerError,
                    e.Message);
            }
        }

        [HttpPost("alert-chargeblast")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> AlertChargeblast(AlertDTO payload, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, payload, _globalData);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                await _activityService.CreateActivityAsync(
                    PaymentsIncomingWebhooksActivities.Payments_IncomingWebhooks_AlertProvider_Requested,
                    set => set
                        .TenantId(GetMID())
                        .CorrelationId(payload.TransactionId)
                        .Data(payload)
                        .Meta(meta => meta
                            .ServiceProvider(AlertProviders.Chargeblast)
                            .SetValue("DisputeQueueItemId", payload.DisputeQueueItemId)
                            .TransactionReference(payload.TransactionId)
                            .SetValue("StatusCode", payload.StatusCode)));

                var alertProvider = InitiateDisputeAlertsProviderService(AlertProviders.Chargeblast);

                await alertProvider.ProcessAlertAsync(payload);
                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);

                await _activityService.CreateActivityAsync(
                    PaymentsIncomingWebhooksErrorActivities.Payments_IncomingWebhooks_AlertProvider_Error, e,
                    set => set
                        .TenantId(GetMID())
                        .Meta(meta => meta
                            .ServiceProvider(AlertProviders.Chargeblast)
                            .SetValue("DisputeQueueItemId", payload.DisputeQueueItemId)
                            .TransactionReference(payload.TransactionId)));

                return StatusCode(StatusCodes.Status500InternalServerError,
                    "Failed alert chargeblast");
            }
        }


        [HttpPost("archive")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(CreateDisputeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Archive(Guid disputeId, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, disputeId, _globalData);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                var dispute = await _dbcontext.DisputeQueueItems.FirstOrDefaultAsync(x => x.Id == disputeId, token);

                if (dispute == null)
                    return NotFound();

                dispute.IsArchived = DateTime.UtcNow;

                _dbcontext.DisputeQueueItems.Update(dispute);
                await _dbcontext.SaveChangesAsync(token);

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    "Failed archive dispute");
            }
        }

        [HttpPost("unarchive")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(CreateDisputeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UnArchive(Guid disputeId, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, disputeId, _globalData);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                var dispute = await _dbcontext.DisputeQueueItems.FirstOrDefaultAsync(x => x.Id == disputeId, token);

                if (dispute == null)
                    return NotFound();

                dispute.IsArchived = null;

                _dbcontext.DisputeQueueItems.Update(dispute);
                await _dbcontext.SaveChangesAsync(token);

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    "Failed unarchive dispute");
            }
        }

        [HttpPost("reverse")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(CreateDisputeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Reverse(Guid? disputeId, Guid? orderId, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, disputeId, _globalData);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                await _disputeServices.ReverseAsync(disputeId, orderId);

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    "Failed reverse dispute");
            }
        }

        [HttpPost("load-nuvei-disputes")]
        //[AllowAnonymous]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(CreateDisputeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> LoadNuveiFile(IFormFile file, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, null, _globalData);

            try
            {
                if (file == null || file.Length == 0)
                    return BadRequest("No file selected.");

                var config = new CsvConfiguration(CultureInfo.InvariantCulture)
                {
                    PrepareHeaderForMatch = args =>
                    {
                        Console.WriteLine(args.Header);
                        return args.Header.Trim();
                    }
                };
                using (var reader = new StreamReader(file.OpenReadStream()))
                using (var csv = new CsvReader(reader, config))
                {
                    var records = csv.GetRecords<NuveiReportItemDTO>();

                    foreach (var record in records)
                    {
                        var trx = await _dbcontext.Transactions
                            .FirstOrDefaultAsync(x => x.ProviderTransactionToken == record.TransactionId);

                        var paymentInstrument = trx != null
                            ? await _dbcontext.PaymentInstruments.SingleOrDefaultAsync(x => x.Id == trx.PaymentMethodId)
                            : null;

                        workspan.Tag("Arn", record.Arn);

                        if (trx == null || paymentInstrument == null)
                        {
                            workspan.Log
                                .Information("Transaction not found for {RecordTransactionId}", record.TransactionId);

                            continue;
                        }

                        workspan.Log
                            .Information("Transaction found for {RecordTransactionId}", record.TransactionId);

                        var preDisputeRequestPayload = new PreDisputeRequest
                        {
                            CreateDateTime = record.UpdateDate.ToUtcDate(),
                            IsWebhook = false,
                            IssueRefund = false,
                            EarlyFraudWarning = false,
                            RequestDate = record.UpdateDate.ToUtcDate(),
                            Bin = paymentInstrument?.Bin,
                            //get last 4 from pan ,
                            Last4 = paymentInstrument?.Last4,
                            CardBrand = record?.PaymentMethod.ToUpper(),
                            DisputeType = record?.ChargebackReasonCode,
                            DisputeManagementSystem = string.Empty,
                            ProviderName = AlertProviders.Nuvei,
                            Stage = DisputeStage.RDR,
                            Meta = JsonConvert.SerializeObject(record),
                            Arn = record?.Arn,
                            Currency = record?.Currency,
                            TransactionId = trx.Id,
                            OrderId = trx.OrderId,
                            Descriptor = trx.DynamicDescriptor,
                            AuthorizationId = trx.AuthorizationId,
                            DisputeAmount = Utils.Formatters.DecimalToInt(decimal.Parse(record.Amount)),
                            Note = "Imported from Nuvei report",
                            IsFileImported = true,
                            IsManualInserted = false,
                        };

                        await _disputeServices.AddPreDisputeAsync(preDisputeRequestPayload);
                    }

                    return Ok();
                }
            }
            catch (Exception e)
            {
                workspan.RecordException(e);

                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(GetMID()));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed creating dispute");
            }
        }

        [HttpPost("queue")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(CreateDisputeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> CreateQueue(IFormFile file, [FromQuery] CreateQueueRequestDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, payload, _globalData);

            try
            {
                if (file == null || file.Length == 0)
                    return BadRequest("No file selected.");

                if (payload.Name == null)
                    return BadRequest("You must specify name.");

                await _activityService.CreateActivityAsync(DisputeReportsActivities.DisputeReports_Import_Requested,
                    set => set
                        .TenantId(GetMID())
                        .Data(payload)
                        .Meta(meta => meta
                            .ServiceProvider(payload.Source)
                            .SetValue("AlertProvider", payload.Source)
                            .SetValue("DisputeManagementSystem", payload.Source)
                            .SetValue("AutoMatch", payload.AutoMatch)
                            .SetValue("Type", payload.Type)));

                var queueItem = new DisputeQueue
                {
                    Name = payload.Name,
                    Source = payload.Source,
                    Type = payload.Type,
                    PartnerId = payload.PartnerId,
                };

                var config = new CsvConfiguration(CultureInfo.InvariantCulture)
                {
                    PrepareHeaderForMatch = args =>
                    {
                        Console.WriteLine(args.Header);
                        return args.Header.Trim();
                    },
                    TrimOptions = TrimOptions.Trim,
                };

                using (var reader = new StreamReader(file.OpenReadStream()))
                using (var csv = new CsvReader(reader, config))
                {
                    csv.Context.RegisterClassMap<NuveiReportItemDTOMap>();
                    var records = new List<object>();

                    if (payload.Type == DisputeReportType.FlexDefaultRdr ||
                        payload.Type == DisputeReportType.FlexDefaultChargeback)
                    {
                        records = csv.GetRecords<DefaultReportItemDTO>().Cast<object>().ToList();
                    }
                    else if (payload.Type == DisputeReportType.MerlinkRdr ||
                             payload.Type == DisputeReportType.MerlinkChargeback)
                    {
                        try
                        {
                            records = csv.GetRecords<MerlinkReportItemDTO>().Cast<object>().ToList();
                        }
                        catch (Exception e)
                        {
                            ModelState.AddModelError("General",
                                $"{payload.Source} report doesn't contain all required fields.");

                            await _activityService.CreateActivityAsync(
                                DisputeReportsErrorsActivities.DisputeReports_Validation_Error,
                                set => set
                                    .TenantId(GetMID())
                                    .Data(e)
                                    .Meta(meta => meta
                                        .Error(
                                            $"{payload.Source} report doesn't contain all required fields.")
                                        .SetValue("Name", payload.Name)
                                        .ServiceProvider(payload.Source)
                                        .SetValue("Type", payload.Type)
                                        .SetValue("Source", payload.Source)));

                            return ValidationProblem();
                        }
                    }
                    else
                    {
                        if (ReportSourceTypeToDtoTypeMap.sourceTypeToDtoTypeMap.TryGetValue(
                                (payload.Source, payload.Type), out var dtoType))
                        {
                            try
                            {
                                records = csv.GetRecords(dtoType).Cast<object>().ToList();
                            }
                            catch (Exception e)
                            {
                                ModelState.AddModelError("General",
                                    $"{payload.Source} report doesn't contain all required fields.");

                                await _activityService.CreateActivityAsync(
                                    DisputeReportsErrorsActivities.DisputeReports_Validation_Error,
                                    set => set
                                        .TenantId(GetMID())
                                        .Data(e)
                                        .Meta(meta => meta
                                            .Error(
                                                $"{payload.Source} report doesn't contain all required fields.")
                                            .SetValue("Name", payload.Name)
                                            .ServiceProvider(payload.Source)
                                            .SetValue("Type", payload.Type)
                                            .SetValue("Source", payload.Source)));

                                return ValidationProblem();
                            }

                            if (payload.Source == AlertProviders.Fiserv)
                            {
                                records = records.GroupBy(x => ((FservReportItemDTO) x).CaseNumber)
                                    .Select(x => x.First())
                                    .Cast<object>()
                                    .ToList();
                            }
                        }
                        else
                        {
                            throw new Exception($"Unknown source - {payload.Source}; type - {payload.Type}");
                        }
                    }

                    queueItem.TotalCount = records.Count;

                    if (payload.OpenItemsAlert.HasValue)
                    {
                        queueItem.OpenItemsAlert = payload.OpenItemsAlert.Value;
                    }

                    if (payload.NewItemsAlert.HasValue)
                    {
                        queueItem.NewItemsAlert = payload.NewItemsAlert.Value;
                    }

                    if (payload.EmailRecipient != null)
                    {
                        queueItem.EmailRecipient = payload.EmailRecipient;
                    }

                    var queue = _dbcontext.DisputeQueues.Add(queueItem);
                    await _dbcontext.SaveChangesAsync(token);

                    _backgroundWorkerCommandQueue.Enqueue(new ImportDisputesCommand(payload.Source, payload.Type,
                        records,
                        queue.Entity.Id,
                        payload.AutoMatch));

                    return Ok();
                }
            }
            catch (FlexValidationException e)
            {
                workspan.Log.Information("Dispute reports validation error {ErrorMessage}", e.Message);

                await _activityService.CreateActivityAsync(
                    DisputeReportsErrorsActivities.DisputeReports_Validation_Error, e,
                    set => set
                        .TenantId(GetMID())
                        .Data(payload)
                        .Meta(meta => meta
                            .SetValue("Name", payload.Name)
                            .SetValue("Type", payload.Type)
                            .SetValue("Provider", payload.Source)
                            .SetValue("Source", payload.Source)));

                ModelState.AddModelError("General", e.Message);
                return ValidationProblem();
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);

                await _activityService.CreateActivityAsync(DisputeReportsErrorsActivities.DisputeReports_Import_Error,
                    e,
                    set => set
                        .TenantId(GetMID())
                        .Data(payload)
                        .Meta(meta => meta.SetValue("Name", payload.Name))
                        .Meta(meta => meta.SetValue("Source", payload.Source)));

                return StatusCode(StatusCodes.Status400BadRequest, "Failed create Queue");
            }
        }

        [HttpPut()]
        [Route("{id:guid}/queue")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(CreateDisputeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateQueue([FromRoute] Guid id, [FromBody] UpdateQueueRequestDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, payload, _globalData);

            try
            {
                if (payload.Name == null)
                    return BadRequest("Name cannot be null.");

                var queue = await _dbcontext.DisputeQueues.FirstOrDefaultAsync(x => x.Id == id);

                if (queue == null)
                    return BadRequest("Queue not found.");
                if (payload.Name != null)
                {
                    queue.Name = payload.Name;
                }

                if (payload.IsPined != null)
                {
                    queue.IsPined = payload.IsPined.Value;
                }

                if (payload.OpenItemsAlert != null)
                {
                    queue.OpenItemsAlert = payload.OpenItemsAlert.Value;
                }

                if (payload.NewItemsAlert != null)
                {
                    queue.NewItemsAlert = payload.NewItemsAlert.Value;
                }

                if (payload.EmailRecipient != null)
                {
                    queue.EmailRecipient = payload.EmailRecipient;
                }

                _dbcontext.DisputeQueues.Update(queue);
                await _dbcontext.SaveChangesAsync(token);

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);

                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(GetMID())
                        .Data(payload)
                        .Meta(meta => meta
                            .SetValue("Name", payload.Name)
                            .SetValue("QueueId", id)));

                return StatusCode(StatusCodes.Status400BadRequest, "Failed create Queue");
            }
        }

        [HttpPut()]
        [Route("{id:guid}/retry")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(CreateDisputeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> RetryFailedRecords([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, null, _globalData);

            try
            {
                await _activityService.CreateActivityAsync(
                    DisputeReportsActivities.DisputeReports_RetryFailedRecords_Requested,
                    set => set
                        .TenantId(GetMID())
                        .Meta(meta => meta
                            .SetValue("QueueId", id)));

                var queue = await _dbcontext.DisputeQueues.FirstOrDefaultAsync(x => x.Id == id);

                if (queue == null)
                    return BadRequest("Queue not found.");

                if (queue.FailedRecords != null)
                {
                    // var records = JsonConvert.DeserializeObject<List<object>>(queue.FailedRecords);
                    var records = new List<object>();

                    if (queue.Type == DisputeReportType.FlexDefaultRdr ||
                        queue.Type == DisputeReportType.FlexDefaultChargeback)
                    {
                        if (JsonConvert.DeserializeObject(queue.FailedRecords, typeof(List<DefaultReportItemDTO>)) is
                            IEnumerable<object> failedRecords)
                        {
                            records.AddRange(failedRecords);
                        }
                    }
                    else if (queue.Type == DisputeReportType.MerlinkRdr ||
                             queue.Type == DisputeReportType.MerlinkChargeback)
                    {
                        if (JsonConvert.DeserializeObject(queue.FailedRecords, typeof(List<MerlinkReportItemDTO>)) is
                            IEnumerable<object> failedRecords)
                        {
                            records.AddRange(failedRecords);
                        }
                    }
                    else
                    {
                        if (ReportSourceTypeToDtoTypeMap.sourceTypeToDtoTypeMap.TryGetValue((queue.Source, queue.Type),
                                out var dtoType))
                        {
                            var failedRecords =
                                JsonConvert.DeserializeObject(queue.FailedRecords, dtoType) as IEnumerable<object>;
                            if (failedRecords != null)
                            {
                                records.AddRange(failedRecords);
                            }
                        }
                        else
                        {
                            await _activityService.CreateActivityAsync(
                                DisputeReportsErrorsActivities.DisputeReports_RetryDisputeReport_Error,
                                set => set
                                    .TenantId(GetMID())
                                    .Meta(meta => meta
                                        .Error(
                                            $"Retry queue > Unknown source - {queue.Source}; type - {queue.Type}")
                                        .SetValue("QueueId", id)));

                            throw new Exception($"Retry queue > Unknown source - {queue.Source}; type - {queue.Type}");
                        }
                    }

                    var source = queue.Source;

                    if (source == null)
                    {
                        source = _dbcontext.DisputeQueueItems.FirstOrDefault(x => x.DisputeQueueId == id)?.ProviderName;
                    }

                    if (source == null)
                    {
                        await _activityService.CreateActivityAsync(
                            DisputeReportsErrorsActivities.DisputeReports_RetryDisputeReport_Error,
                            set => set
                                .TenantId(GetMID())
                                .Meta(meta => meta
                                    .Error("Failed retry records")
                                    .SetValue("QueueId", id)));

                        return BadRequest("Failed retry records");
                    }

                    _backgroundWorkerCommandQueue.Enqueue(new ImportDisputesCommand(source, queue.Type ?? "default",
                        records, id, false));
                }

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);

                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(GetMID())
                        .Meta(meta => meta.SetValue("QueueId", id)));

                return StatusCode(StatusCodes.Status400BadRequest, "Failed retry records");
            }
        }

        [HttpGet("queue")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(CreateDisputeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetQueues(string query, DateTime? from, DateTime? to, string? timezone,
            DateTime? createdFrom, DateTime? createdTo, string sort, string sortField, bool? isArchived,
            bool? isFinished, Guid? partnerId,
            int pageSize = 10, int pageNumber = 1)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, null, _globalData);

            try
            {
                var queues = await _disputeQueueService.GetDisputesQueuesAsync(query, from, to, timezone,
                    sort, sortField, isArchived, isFinished, partnerId, pageSize, pageNumber);

                return Ok(queues);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);

                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(GetMID()));

                return StatusCode(StatusCodes.Status400BadRequest, "Failed to get Queue");
            }
        }

        [HttpDelete()]
        [Route("{id:guid}/queue")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(CreateDisputeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> DeleteQueue([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, null, _globalData);

            try
            {
                var queue = await _dbcontext.DisputeQueues.FirstOrDefaultAsync(x => x.Id == id);

                if (queue == null)
                    return NotFound();

                _dbcontext.DisputeQueues.Remove(queue);
                var res = await _dbcontext.SaveChangesAsync(token);

                if (res == 0)
                {
                    return BadRequest("Failed delete Queue");
                }

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);

                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(GetMID())
                        .Meta(meta => meta.SetValue("QueueId", id)));

                return StatusCode(StatusCodes.Status400BadRequest, "Failed delete Queue");
            }
        }

        [HttpGet()]
        [Route("{id:guid}/queue-items")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(CreateDisputeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetQueueItems([FromRoute] Guid id, bool isArchived = false, int pageSize = 10,
            int pageNumber = 1)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, null, _globalData);

            try
            {
                var response =
                    await _disputeImportServices.GetDisputeQueueItemsByIdAsync(id, isArchived, pageSize, pageNumber);

                return Ok(response);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);

                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(GetMID())
                        .Meta(meta => meta.SetValue("QueueId", id)));

                return StatusCode(StatusCodes.Status400BadRequest, "Failed get Queue items");
            }
        }

        [HttpGet()]
        [Route("archived-queue-items")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(CreateDisputeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetArchivedQueueItems(int pageSize = 10, int pageNumber = 1)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, null, _globalData);

            try
            {
                var response = await _disputeImportServices.GetArchivedDisputeQueueItemsByIdAsync(pageSize, pageNumber);

                return Ok(response);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);

                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(GetMID()));

                return StatusCode(StatusCodes.Status400BadRequest, "Failed get Queue items");
            }
        }

        [HttpGet()]
        [Route("{id:guid}/queue-item")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(CreateDisputeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetQueueItem([FromRoute] Guid id)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, null, _globalData);

            try
            {
                return Ok(await _disputeImportServices.GetQueueItemByIdAsync(id));
            }
            catch (FlexNotFoundException e)
            {
                workspan.RecordException(e);

                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(GetMID())
                        .Meta(meta => meta.SetValue("QueueItemId", id)));

                return NotFound(e.Message);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);

                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(GetMID())
                        .Meta(meta => meta.SetValue("QueueItemId", id)));

                return StatusCode(StatusCodes.Status400BadRequest, "Failed get Queue item");
            }
        }

        [HttpGet("report-providers-list")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(List<AlertProviderReportDTO>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetProvidersList()
        {
            var providersList = new List<AlertProviderReportDTO>();

            providersList.Add(new AlertProviderReportDTO
            {
                Value = AlertProviders.Fiserv,
                Label = AlertProviders.Fiserv,
                Types = new List<AlertProviderReportTypeDTO>
                {
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FiservDefault, Label = AlertProviders.Fiserv},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultRdr, Label = "RDR Report (default)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultChargeback, Label = "Chargeback Report (default)"},
                }
            });

            providersList.Add(new AlertProviderReportDTO
            {
                Value = AlertProviders.Nuvei,
                Label = AlertProviders.Nuvei,
                Types = new List<AlertProviderReportTypeDTO>
                {
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.NuveiDefault, Label = AlertProviders.Nuvei},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultRdr, Label = "RDR Report (default)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultChargeback, Label = "Chargeback Report (default)"},
                }
            });
            providersList.Add(new AlertProviderReportDTO
            {
                Value = AlertProviders.Stripe,
                Label = AlertProviders.Stripe,
                Types = new List<AlertProviderReportTypeDTO>
                {
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.StripeDefault, Label = AlertProviders.Stripe},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultRdr, Label = "RDR Report (default)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultChargeback, Label = "Chargeback Report (default)"},
                }
            });
            providersList.Add(new AlertProviderReportDTO
            {
                Value = AlertProviders.Checkout,
                Label = "Checkout.com",
                Types = new List<AlertProviderReportTypeDTO>
                {
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.CheckoutFraud, Label = "Reported Fraudulent Transactions"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultRdr, Label = "RDR Report (default)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultChargeback, Label = "Chargeback Report (default)"},
                }
            });
            providersList.Add(new AlertProviderReportDTO
            {
                Value = AlertProviders.Paysafe,
                Label = AlertProviders.Paysafe,
                Types = new List<AlertProviderReportTypeDTO>
                {
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.PaysafeDefault, Label = "Paysafe AMR Daily CB and Reversals"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.PaysafeOld, Label = "Paysafe Chergebacks With Disputes"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.PaysafeAMRFraud, Label = "AMR_Fraud_Report"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultChargeback, Label = "Chargeback Report (default)"},
                }
            });
            providersList.Add(new AlertProviderReportDTO
            {
                Value = AlertProviders.Disputifer,
                Label = AlertProviders.Disputifer,
                Types = new List<AlertProviderReportTypeDTO>
                {
                    new AlertProviderReportTypeDTO {Value = DisputeReportType.DisputiferRdr, Label = "Disputifer RDR"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.DisputiferEthoca, Label = "Disputifer Ethoca"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultRdr, Label = "RDR Report (default)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultChargeback, Label = "Chargeback Report (default)"},
                }
            });
            providersList.Add(new AlertProviderReportDTO
            {
                Value = AlertProviders.MyRCVR,
                Label = AlertProviders.MyRCVR,
                Types = new List<AlertProviderReportTypeDTO>
                {
                    new AlertProviderReportTypeDTO {Value = DisputeReportType.MyRCVRRdr, Label = "Myrcvr RDR"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.MyRCVRChargeback, Label = "Myrcvr Chergebacks"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultRdr, Label = "RDR Report (default)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultChargeback, Label = "Chargeback Report (default)"},
                }
            });
            providersList.Add(new AlertProviderReportDTO
            {
                Value = AlertProviders.Payarc,
                Label = AlertProviders.Payarc,
                Types = new List<AlertProviderReportTypeDTO>
                {
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.PayarcDefault, Label = AlertProviders.Payarc},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.MerlinkChargeback, Label = "Chargeback Report (Merlink)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.MerlinkRdr, Label = "RDR Report (Merlink)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultRdr, Label = "RDR Report (default)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultChargeback, Label = "Chargeback Report (default)"},
                }
            });
            providersList.Add(new AlertProviderReportDTO
            {
                Value = AlertProviders.PayScout,
                Label = AlertProviders.PayScout,
                Types = new List<AlertProviderReportTypeDTO>
                {
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.MerlinkChargeback, Label = "Chargeback Report (Merlink)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.MerlinkRdr, Label = "RDR Report (Merlink)"},
                }
            });
            providersList.Add(new AlertProviderReportDTO
            {
                Value = AlertProviders.Payshield,
                Label = AlertProviders.Payshield,
                Types = new List<AlertProviderReportTypeDTO>
                {
                    new() {Value = DisputeReportType.FlexDefaultRdr, Label = "RDR Report (default)"},
                    new() {Value = DisputeReportType.FlexDefaultChargeback, Label = "Chargeback Report (default)"},
                }
            });
            providersList.Add(new AlertProviderReportDTO
            {
                Value = AlertProviders.Kount,
                Label = AlertProviders.Kount,
                Types = new List<AlertProviderReportTypeDTO>
                {
                    new AlertProviderReportTypeDTO {Value = DisputeReportType.KountRdr, Label = "RDR"},
                    new AlertProviderReportTypeDTO {Value = DisputeReportType.KountEthoca, Label = "Ethoca"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultRdr, Label = "RDR Report (default)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultChargeback, Label = "Chargeback Report (default)"},
                }
            });
            providersList.Add(new AlertProviderReportDTO
            {
                Value = AlertProviders.Eurekapayments,
                Label = AlertProviders.Eurekapayments,
                Types = new List<AlertProviderReportTypeDTO>
                {
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.EurekapaymentsChargeback, Label = "Eurekapayments Chargebacks"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultRdr, Label = "RDR Report (default)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultChargeback, Label = "Chargeback Report (default)"},
                }
            });
            providersList.Add(new AlertProviderReportDTO
            {
                Value = AlertProviders.Luqra,
                Label = AlertProviders.Luqra,
                Types = new List<AlertProviderReportTypeDTO>
                {
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.LuqraChargeback, Label = "Luqra Chargebacks"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultRdr, Label = "RDR Report (default)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultChargeback, Label = "Chargeback Report (default)"},
                }
            });
            providersList.Add(new AlertProviderReportDTO
            {
                Value = AlertProviders.Maverick,
                Label = AlertProviders.Maverick,
                Types = new List<AlertProviderReportTypeDTO>
                {
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.MaverickChargeback, Label = "Maverick Chargebacks"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultRdr, Label = "RDR Report (default)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultChargeback, Label = "Chargeback Report (default)"},
                }
            });
            providersList.Add(new AlertProviderReportDTO
            {
                Value = AlertProviders.Paycosmo,
                Label = AlertProviders.Paycosmo,
                Types = new List<AlertProviderReportTypeDTO>
                {
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.PaycosmoChargeback, Label = "Paycosmo Chargebacks"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultRdr, Label = "RDR Report (default)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultChargeback, Label = "Chargeback Report (default)"},
                }
            });
            providersList.Add(new AlertProviderReportDTO
            {
                Value = AlertProviders.Quantum,
                Label = AlertProviders.Quantum,
                Types = new List<AlertProviderReportTypeDTO>
                {
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.QuantumChargeback, Label = "Quantum Chargebacks"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.MerlinkChargeback, Label = "Chargeback Report (Merlink)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.MerlinkRdr, Label = "RDR Report (Merlink)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultRdr, Label = "RDR Report (default)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultChargeback, Label = "Chargeback Report (default)"},
                }
            });
            providersList.Add(new AlertProviderReportDTO
            {
                Value = AlertProviders.Stax,
                Label = AlertProviders.Stax,
                Types = new List<AlertProviderReportTypeDTO>
                {
                    new AlertProviderReportTypeDTO {Value = DisputeReportType.StaxDefault, Label = "Stax Report"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultRdr, Label = "RDR Report (default)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultChargeback, Label = "Chargeback Report (default)"},
                }
            });
            providersList.Add(new AlertProviderReportDTO()
            {
                Value = AlertProviders.EMS,
                Label = AlertProviders.EMS,
                Types = new List<AlertProviderReportTypeDTO>
                {
                    new AlertProviderReportTypeDTO {Value = DisputeReportType.EMSDefault, Label = "EMS Report"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.MerlinkChargeback, Label = "Chargeback Report (Merlink)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.MerlinkRdr, Label = "RDR Report (Merlink)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultRdr, Label = "RDR Report (default)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultChargeback, Label = "Chargeback Report (default)"},
                }
            });
            providersList.Add(new AlertProviderReportDTO
            {
                Value = AlertProviders.RMS,
                Label = AlertProviders.RMS,
                Types = new List<AlertProviderReportTypeDTO>
                {
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.MerlinkChargeback, Label = "Chargeback Report (Merlink)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.MerlinkRdr, Label = "RDR Report (Merlink)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultRdr, Label = "RDR Report (default)"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultChargeback, Label = "Chargeback Report (default)"},
                }
            });
            providersList.Add(new AlertProviderReportDTO
            {
                Value = AlertProviders.Verifi,
                Label = AlertProviders.Verifi,
                Types = new List<AlertProviderReportTypeDTO>
                {
                    new AlertProviderReportTypeDTO {Value = DisputeReportType.VerifiRdr, Label = "Verifi RDR Report"},
                    new AlertProviderReportTypeDTO
                        {Value = DisputeReportType.FlexDefaultRdr, Label = "RDR Report (default)"},
                }
            });

            return Ok(providersList);
        }

        // endpoint to get fields for standard report in csv
        [HttpGet("report-template")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(List<DefaultReportService>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetStandardReportFields(string provider, string type)
        {
            using var workspan = Workspan.StartEndpoint<DisputesController>(this, null, _globalData);

            try
            {
                var csv = await _disputeImportServices.GenerateCsvReportHeaders(provider, type);

                return Ok(csv);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);

                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(GetMID()));

                return StatusCode(StatusCodes.Status400BadRequest, e.Message);
            }
        }

        protected T GetRequiredService<T>()
        {
            return _serviceProvider.GetRequiredService<T>();
        }

        private IDisputeAlertsService InitiateDisputeAlertsProviderService(string providerKey)
        {
            var disputeAlertProviderResolver =
                GetRequiredService<ServiceCollectionExtensions.DisputeAlertProviderResolver>();
            return disputeAlertProviderResolver(providerKey);
        }
    }
}