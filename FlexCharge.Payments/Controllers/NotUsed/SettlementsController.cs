// using System;
// using System.Collections.Generic;
// using System.Linq;
// using System.Threading;
// using System.Threading.Tasks;
// using AutoMapper;
// using FlexCharge.Common;
// using FlexCharge.Common.Authentication;
// using FlexCharge.Common.AutoMapper;
// using FlexCharge.Common.Telemetry;
// using FlexCharge.Payments.DTO;
// using FlexCharge.Payments.Services.BatchServices;
// using FlexCharge.Payments.Services.PayoutServices;
// using Microsoft.AspNetCore.Authorization;
// using Microsoft.AspNetCore.Http;
// using Microsoft.AspNetCore.Mvc;
// using Microsoft.Extensions.Logging;
// using Microsoft.Extensions.Options;
//
// namespace FlexCharge.Payments.Controllers
// {
//     [Route("[controller]")]
//     [ApiController]
//     [JwtAuth]
//     public class SettlementsController : BaseController
//     {
//         private readonly AppOptions _globalData;
//         private readonly IMapper _mapper;
//         private readonly IBatchService _batchService;
//         private readonly IPayoutService _payoutService;
//
//         public SettlementsController(PostgreSQLDbContext context,
//             IMapper mapper,
//             IOptions<AppOptions> globalData, 
//             IBatchService batchService, IPayoutService payoutService)
//         {
//             _mapper = mapper;
//             _batchService = batchService;
//             _payoutService = payoutService;
//             _globalData = globalData.Value;
//         }
//
//
//         [HttpGet] // GET ALL
//         [Authorize(MyPolicies.ADMINS_AND_MERACHANTS_ADMINS)]
//         [ProducesResponseType(typeof(PagedDTO<SettlementDTO>), StatusCodes.Status200OK)]
//         [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
//         [ProducesResponseType(StatusCodes.Status404NotFound)]
//         public async Task<IActionResult> Get([FromQuery] SettlementQueryDTO payload, 
//             Guid? mid,
//             CancellationToken token)
//         {
//             using var workspan = Workspan.StartEndpoint<SettlementsController>(this, payload, _globalData);
//             
//             try
//             {
//                 List<SettlementDTO> settlements;
//                 if (HttpContext.User.Claims.Any(x =>
//                         x.Type == MyClaimTypes.COGNITO_GROUP && x.Value == SuperAdminGroups.SUPER_ADMIN))
//                 {
//                     settlements = await _batchService.AdminGetSettlementsAsync(mid);
//                 }
//                 else
//                 {
//                     settlements = await _batchService.GetSettlementsAsync(GetMID());
//                 }
//                 
//                 //var mockSettlements = new List<SettlementDTO>();
//                 //
//                 // for (int i = 0; i < 36; i++)
//                 // {
//                 //     mockSettlements.Add(new SettlementDTO
//                 //     {
//                 //         PaymentRef = Guid.NewGuid(),
//                 //         PayoutDate = DateTime.UtcNow.AddYears(-3).AddMonths(i),
//                 //         CapturePeriodFrom = DateTime.UtcNow.AddYears(-3).AddMonths(i),
//                 //         CapturePeriodTo = DateTime.UtcNow.AddYears(-3).AddMonths(i + 1),
//                 //         Mid = Guid.NewGuid(),
//                 //         TotalValue = 1000.12M,
//                 //         FlexChargeFees = 150M,
//                 //         Returns = 10M,
//                 //         Chargebacks = 100.23M,
//                 //         PayoutAmount = 600.12M,
//                 //         CurrencyCode = "USD",
//                 //         CurrencySymbol = "$"
//                 //     });
//                 // }
//
//                 return Ok(_mapper.Map<IPagedList<SettlementDTO>, PagedDTO<SettlementDTO>>(settlements
//                     .OrderByDescending(x => x.PayoutDate).ToPagedList(payload.PageNumber, payload.PageSize)));
//             }
//             catch (Exception e)
//             {
//                 workspan.RecordException(e);
//                 return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
//             }
//         }
//     }
// }