using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Telemetry;
using FlexCharge.Payments.Domain.Payments.IntegrityManager;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.PaymentServices;
using PaymentMethodType = FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels.PaymentMethodType;

namespace FlexCharge.Payments.Domain.Payments.Validation;

public class RefundValidator
{
    private readonly SupportedGateway _provider;
    private readonly Transaction _transactionToRefund;
    private readonly IReadOnlyList<Transaction> _relatedTransactions;
    private readonly IReadOnlyList<Transaction> _orderTransactions;

    private readonly bool _isPartialRefundPossible;

    public RefundValidator(
        SupportedGateway provider,
        Entities.Transaction transactionToRefund,
        IReadOnlyList<Entities.Transaction> relatedTransactions,
        IReadOnlyList<Entities.Transaction> orderTransactions,
        bool isPartialRefundPossible)
    {
        _provider = provider;
        _transactionToRefund = transactionToRefund;
        _relatedTransactions = relatedTransactions;
        _orderTransactions = orderTransactions;
        _isPartialRefundPossible = isPartialRefundPossible;
    }

    /// <summary>
    /// Validate and correct refund amount.
    /// This method will throw an exception if the refund amount is invalid.
    /// </summary>
    /// <param name="desiredRefundAmount"></param>
    /// <exception cref="FlexValidationException"></exception>
    public void ValidateAndThrow(ref int desiredRefundAmount)
    {
        using var workspan = Workspan.Start<RefundValidator>()
            .Baggage("TransactionId", _transactionToRefund.Id)
            .Baggage("DesiredRefundAmount", desiredRefundAmount);

        var refundTransactionTypes = new[]
        {
            TransactionType.Refund, TransactionType.RefundByConsumer, TransactionType.RefundByEarlyWarning,
            TransactionType.RefundPreDispute,
            TransactionType.Credit, TransactionType.Chargeback, TransactionType.PreDispute,
        };

        var refundTransactionTypesSet = new HashSet<string>(refundTransactionTypes.Select(x => x.ToString()));

        //get and sum refunds
        var alreadyRefunded = _relatedTransactions.Where(x => refundTransactionTypesSet.Contains(x.Type)
                                                              && x.Status is TransactionStatus.Completed
                                                                  or TransactionStatus.Initialized or
                                                                  TransactionStatus.InProcess).Sum(x => x.Amount);

        workspan
            .Baggage("AlreadyRefunded", alreadyRefunded);

        int refundAttempts = _relatedTransactions.Count(x =>
            refundTransactionTypesSet.Contains(x.Type) && x.Status is TransactionStatus.Failed);

        workspan.Baggage("RefundAttempts", refundAttempts);

        if (refundAttempts >= _provider.Capabilities.MaxRefundAttemptThreshold)
            throw new FlexValidationException(ValidationCodes.MaximumRefundAttemptsExceeded,
                ValidationCodes.GetMessage(ValidationCodes.MaximumRefundAttemptsExceeded));

        var authorizationAmount = _transactionToRefund.AuthorizationAmount ?? _transactionToRefund.Amount;

        if (desiredRefundAmount <= 0)
            throw new FlexValidationException(ValidationCodes.InvalidRefundAmount,
                ValidationCodes.GetMessage(ValidationCodes.InvalidRefundAmount));

        if (desiredRefundAmount < authorizationAmount && !_isPartialRefundPossible)
            throw new FlexValidationException(ValidationCodes.ProviderNotSupportPartialRefunds,
                ValidationCodes.GetMessage(ValidationCodes.ProviderNotSupportPartialRefunds));

        if (desiredRefundAmount > authorizationAmount + _transactionToRefund.DiscountAmount - alreadyRefunded)
            throw new FlexValidationException(ValidationCodes.InvalidRefundAmount,
                ValidationCodes.GetMessage(ValidationCodes.InvalidRefundAmount));

        // If the amount is equal to the original transaction amount + discount amount, then we should refund the original amount
        // In case refund amount is greater than auth amount but less than auth amount + discount amount, we should refund the original amount
        if ((desiredRefundAmount == authorizationAmount + _transactionToRefund.DiscountAmount) ||
            desiredRefundAmount > authorizationAmount)
        {
            desiredRefundAmount = authorizationAmount;
        }

        // if (payload.Amount > (authorizationAmount - refunds))
        //     throw new FlexChargeValidationException(ValidationCodes.InvalidRefundAmount,
        //         ValidationCodes.GetMessage(ValidationCodes.InvalidRefundAmount));

        bool isRefundableAmountCheckPassed = false;
        if (TryGetRefundableAmount(_orderTransactions, out var refundableAmount))
        {
            workspan
                .Baggage("RefundableAmount", refundableAmount);

            if (desiredRefundAmount > refundableAmount)
            {
                workspan.Log.Fatal("Refund amount is greater than refundable amount");
            }
            else
            {
                isRefundableAmountCheckPassed = true;
            }
        }
        else
        {
            workspan.Log.Fatal("Cannot get refundable amount");
        }

        if (!isRefundableAmountCheckPassed)
        {
            // For now,this check is mandatory only for ACH transactions
            // When it will be fully tested, we should use it for all transactions
            if (_transactionToRefund.PaymentType == nameof(PaymentMethodType.Ach))
            {
                throw new FlexValidationException(ValidationCodes.InvalidRefundAmount,
                    ValidationCodes.GetMessage(ValidationCodes.InvalidRefundAmount));
            }
        }
    }

    private bool TryGetRefundableAmount(IReadOnlyList<Transaction> orderTransactions, out int refundableAmount)
    {
        refundableAmount = 0;

        using var workspan = Workspan.Start<RefundValidator>()
            .Baggage("TransactionId", _transactionToRefund.Id);

        try
        {
            var orderPayments = new OrderPayments(orderTransactions);
            refundableAmount = orderPayments.TotalRefundableAmount();

            return true;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "Failed to get refunded amount");

            return false;
        }
    }
}