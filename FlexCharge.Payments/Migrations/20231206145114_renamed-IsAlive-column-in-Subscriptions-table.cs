using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    /// <inheritdoc />
    public partial class renamedIsAlivecolumninSubscriptionstable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Subscriptions_OrderId",
                table: "Subscriptions");

            migrationBuilder.DropIndex(
                name: "IX_Subscriptions_ProviderSubscriptionId_Provider",
                table: "Subscriptions");

            migrationBuilder.RenameColumn(
                name: "Success",
                table: "Subscriptions",
                newName: "LastPaymentSuccess");

            migrationBuilder.RenameColumn(
                name: "IsActive",
                table: "Subscriptions",
                newName: "IsAlive");

            migrationBuilder.RenameColumn(
                name: "EndDate",
                table: "Subscriptions",
                newName: "CancelAt");

            migrationBuilder.CreateIndex(
                name: "IX_Subscriptions_OrderId",
                table: "Subscriptions",
                column: "OrderId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Subscriptions_OrderId",
                table: "Subscriptions");

            migrationBuilder.RenameColumn(
                name: "LastPaymentSuccess",
                table: "Subscriptions",
                newName: "Success");

            migrationBuilder.RenameColumn(
                name: "IsAlive",
                table: "Subscriptions",
                newName: "IsActive");

            migrationBuilder.RenameColumn(
                name: "CancelAt",
                table: "Subscriptions",
                newName: "EndDate");

            migrationBuilder.CreateIndex(
                name: "IX_Subscriptions_OrderId",
                table: "Subscriptions",
                column: "OrderId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Subscriptions_ProviderSubscriptionId_Provider",
                table: "Subscriptions",
                columns: new[] { "ProviderSubscriptionId", "Provider" },
                unique: true);
        }
    }
}
