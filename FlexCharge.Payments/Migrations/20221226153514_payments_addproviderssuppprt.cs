using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    public partial class payments_addproviderssuppprt : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Capabilities",
                table: "SupportedGateways",
                type: "jsonb",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Configuration",
                table: "SupportedGateways",
                type: "jsonb",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Methods",
                table: "SupportedGateways",
                type: "jsonb",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Capabilities",
                table: "SupportedGateways");

            migrationBuilder.DropColumn(
                name: "Configuration",
                table: "SupportedGateways");

            migrationBuilder.DropColumn(
                name: "Methods",
                table: "SupportedGateways");
        }
    }
}
