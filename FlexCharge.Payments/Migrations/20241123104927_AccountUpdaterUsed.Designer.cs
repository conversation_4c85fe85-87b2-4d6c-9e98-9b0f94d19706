// <auto-generated />
using System;
using System.Collections.Generic;
using System.Text.Json;
using FlexCharge.Payments;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Entities.JsonbModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    [DbContext(typeof(PostgreSQLDbContext))]
    [Migration("20241123104927_AccountUpdaterUsed")]
    partial class AccountUpdaterUsed
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "payment_method_type", new[] { "un_known", "credit", "debit", "e_check", "check", "money_order", "cash", "google_pay", "apple_pay", "samsung_pay", "bnpl", "other" });
            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "transaction_status", new[] { "unknown", "initialized", "in_process", "completed", "failed", "canceled", "held" });
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("FlexCharge.Payments.Entities.AlertProvider", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ApiUrl")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("PrivateKey")
                        .HasColumnType("text");

                    b.Property<string>("PrivateName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AlertProviders");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.AuditLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AffectedColumns")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Entity")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Microservice")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("NewValues")
                        .HasColumnType("text");

                    b.Property<string>("OldValues")
                        .HasColumnType("text");

                    b.Property<string>("Operation")
                        .HasColumnType("text");

                    b.Property<string>("PrimaryKey")
                        .HasColumnType("text");

                    b.Property<string>("TableName")
                        .HasColumnType("text");

                    b.Property<string>("UserEmail")
                        .HasColumnType("text");

                    b.Property<string>("UserFirstName")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("UserIp")
                        .HasColumnType("text");

                    b.Property<string>("UserLastName")
                        .HasColumnType("text");

                    b.Property<string>("UserRole")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AuditLogs");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.Batch", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("BatchType")
                        .HasColumnType("text");

                    b.Property<Guid?>("BeneficiaryId")
                        .HasColumnType("uuid");

                    b.Property<int>("Chargebacks")
                        .HasColumnType("integer");

                    b.Property<string>("ConcurrencyUniqueBatchId")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("FlexChargeFees")
                        .HasColumnType("integer");

                    b.Property<DateTime>("From")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsOffline")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsPosted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("PayoutStatus")
                        .HasColumnType("integer");

                    b.Property<Guid?>("RelatedTransaction")
                        .HasColumnType("uuid");

                    b.Property<int>("Returns")
                        .HasColumnType("integer");

                    b.Property<string>("StatusDescription")
                        .HasColumnType("text");

                    b.Property<string>("StatusText")
                        .HasColumnType("text");

                    b.Property<DateTime>("To")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("TotalAmount")
                        .HasColumnType("integer");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("BeneficiaryId");

                    b.HasIndex("ConcurrencyUniqueBatchId")
                        .IsUnique();

                    b.ToTable("Batches");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.CycleMetrics", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CycleEndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CycleStartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<int>("MasterCardCurrentTransactionsCount")
                        .HasColumnType("integer");

                    b.Property<int>("MasterCardCurrentTransactionsValue")
                        .HasColumnType("integer");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("SupportGatewayId")
                        .HasColumnType("uuid");

                    b.Property<int>("TotalTransactionAmount")
                        .HasColumnType("integer");

                    b.Property<int>("TotalTransactionCount")
                        .HasColumnType("integer");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.Property<int>("VisaCurrentTransactionsCount")
                        .HasColumnType("integer");

                    b.Property<int>("VisaCurrentTransactionsValue")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("SupportGatewayId")
                        .IsUnique()
                        .HasFilter("\"IsDeleted\"=false");

                    b.ToTable("Metrics");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.Dispute", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<string>("Arn")
                        .HasColumnType("text");

                    b.Property<string>("AuthorizationId")
                        .HasColumnType("text");

                    b.Property<string>("Bin")
                        .HasColumnType("text");

                    b.Property<string>("CardBrand")
                        .HasColumnType("text");

                    b.Property<string>("CardType")
                        .HasColumnType("text");

                    b.Property<string>("CaseNumber")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<string>("Descriptor")
                        .HasColumnType("text");

                    b.Property<string>("DisputeManagementSystem")
                        .HasColumnType("text");

                    b.Property<string>("DisputeType")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("EarlyFraudWarning")
                        .HasColumnType("boolean");

                    b.Property<bool?>("HasMatch")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("IsArchived")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsFileImported")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsManualInserted")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsReviewRequired")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSftp")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsWebhook")
                        .HasColumnType("boolean");

                    b.Property<string>("Issuer")
                        .HasColumnType("text");

                    b.Property<string>("Last4")
                        .HasColumnType("text");

                    b.Property<string>("Mcc")
                        .HasColumnType("text");

                    b.Property<string>("Message")
                        .HasColumnType("text");

                    b.Property<string>("Meta")
                        .HasColumnType("jsonb");

                    b.Property<Guid>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProcessorName")
                        .HasColumnType("text");

                    b.Property<string>("ProviderName")
                        .HasColumnType("text");

                    b.Property<Guid>("RefundTransactionId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("RepliedToEthoca")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("RequestDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("RequestId")
                        .HasColumnType("uuid");

                    b.Property<string>("Stage")
                        .HasColumnType("text");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("TransactionId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("Disputes");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.DisputeActivity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<string>("Arn")
                        .HasColumnType("text");

                    b.Property<string>("AuthorizationId")
                        .HasColumnType("text");

                    b.Property<string>("Bin")
                        .HasColumnType("text");

                    b.Property<string>("CardBrand")
                        .HasColumnType("text");

                    b.Property<string>("CardType")
                        .HasColumnType("text");

                    b.Property<string>("CaseNumber")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<string>("Descriptor")
                        .HasColumnType("text");

                    b.Property<Guid>("DisputeId")
                        .HasColumnType("uuid");

                    b.Property<string>("DisputeManagementSystem")
                        .HasColumnType("text");

                    b.Property<Guid?>("DisputeQueueItemId")
                        .HasColumnType("uuid");

                    b.Property<string>("DisputeType")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("EarlyFraudWarning")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsFileImported")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsManualInserted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSftp")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsWebhook")
                        .HasColumnType("boolean");

                    b.Property<string>("Last4")
                        .HasColumnType("text");

                    b.Property<string>("Mcc")
                        .HasColumnType("text");

                    b.Property<string>("Message")
                        .HasColumnType("text");

                    b.Property<string>("Meta")
                        .HasColumnType("jsonb");

                    b.Property<Guid>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProcessorName")
                        .HasColumnType("text");

                    b.Property<string>("ProviderName")
                        .HasColumnType("text");

                    b.Property<Guid>("RefundTransactionId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("RequestDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Stage")
                        .HasColumnType("text");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("TransactionId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("DisputeId");

                    b.HasIndex("DisputeQueueItemId");

                    b.ToTable("DisputeActivities");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.DisputeQueue", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EmailRecipient")
                        .HasColumnType("text");

                    b.Property<int>("ErrorCount")
                        .HasColumnType("integer");

                    b.Property<string>("Errors")
                        .HasColumnType("text");

                    b.Property<string>("FailedRecords")
                        .HasColumnType("jsonb");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsPined")
                        .HasColumnType("boolean");

                    b.Property<int>("LoadedRecordsCount")
                        .HasColumnType("integer");

                    b.Property<string>("MerchantNumber")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<bool>("NewItemsAlert")
                        .HasColumnType("boolean");

                    b.Property<bool>("OpenItemsAlert")
                        .HasColumnType("boolean");

                    b.Property<int>("ProcessedRecordsCount")
                        .HasColumnType("integer");

                    b.Property<string>("Source")
                        .HasColumnType("text");

                    b.Property<int>("TotalCount")
                        .HasColumnType("integer");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("DisputeQueues");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.DisputeQueueItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<string>("Arn")
                        .HasColumnType("text");

                    b.Property<DateTime?>("AuthorizationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AuthorizationId")
                        .HasColumnType("text");

                    b.Property<string>("Bin")
                        .HasColumnType("text");

                    b.Property<string>("CardBrand")
                        .HasColumnType("text");

                    b.Property<string>("CardType")
                        .HasColumnType("text");

                    b.Property<string>("CaseNumber")
                        .HasColumnType("text");

                    b.Property<string>("ChargebackWinLoss")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<string>("Descriptor")
                        .HasColumnType("text");

                    b.Property<string>("DisputeManagementSystem")
                        .HasColumnType("text");

                    b.Property<Guid>("DisputeQueueId")
                        .HasColumnType("uuid");

                    b.Property<string>("DisputeType")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("EarlyFraudWarning")
                        .HasColumnType("boolean");

                    b.Property<string>("EventType")
                        .HasColumnType("text");

                    b.Property<string>("ExternalReferenceID")
                        .HasColumnType("text");

                    b.Property<bool?>("HasMatch")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("IsArchived")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsFileImported")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsManualInserted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSftp")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsWebhook")
                        .HasColumnType("boolean");

                    b.Property<string>("Issuer")
                        .HasColumnType("text");

                    b.Property<string>("KountAuthorizationCode")
                        .HasColumnType("text");

                    b.Property<string>("KountEventType")
                        .HasColumnType("text");

                    b.Property<string>("Last4")
                        .HasColumnType("text");

                    b.Property<int?>("MatchedTransactionCount")
                        .HasColumnType("integer");

                    b.Property<string>("Mcc")
                        .HasColumnType("text");

                    b.Property<string>("Message")
                        .HasColumnType("text");

                    b.Property<string>("Meta")
                        .HasColumnType("jsonb");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<string>("OrderId")
                        .HasColumnType("text");

                    b.Property<Guid?>("PartnerId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProcessorName")
                        .HasColumnType("text");

                    b.Property<string>("ProviderName")
                        .HasColumnType("text");

                    b.Property<string>("Reason")
                        .HasColumnType("text");

                    b.Property<Guid>("RefundTransactionId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("RepliedToEthoca")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("RequestDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("RequestId")
                        .HasColumnType("uuid");

                    b.Property<string>("Stage")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("DisputeQueueId");

                    b.ToTable("DisputeQueueItems");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.FinancialAccount", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AccountDescription")
                        .HasColumnType("text");

                    b.Property<string>("AccountName")
                        .HasColumnType("text");

                    b.Property<string>("AccountNumber")
                        .HasColumnType("text");

                    b.Property<string>("AccountType")
                        .HasColumnType("text");

                    b.Property<string>("BankName")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsPrimary")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("RelatedEntityId")
                        .HasColumnType("uuid");

                    b.Property<string>("RelatedEntityType")
                        .HasColumnType("text");

                    b.Property<string>("RoutingNumber")
                        .HasColumnType("text");

                    b.Property<bool?>("SupportACH")
                        .HasColumnType("boolean");

                    b.Property<bool?>("SupportWire")
                        .HasColumnType("boolean");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.Property<string>("VerificationState")
                        .HasColumnType("text");

                    b.Property<Guid>("VerificationTransactionId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("FinancialAccounts");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.Gateway", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<CapabilitiesModel>("Capabilities")
                        .HasColumnType("jsonb");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("Default")
                        .HasColumnType("boolean");

                    b.Property<string>("Identifier")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSandbox")
                        .HasColumnType("boolean");

                    b.Property<string>("MCC")
                        .HasColumnType("text");

                    b.Property<Guid>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("NameIdentifier")
                        .HasColumnType("text");

                    b.Property<int?>("Order")
                        .HasColumnType("integer");

                    b.Property<string>("ProcessorId")
                        .HasColumnType("text");

                    b.Property<Guid>("SupportedGatewayId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("MerchantId");

                    b.HasIndex("SupportedGatewayId");

                    b.ToTable("Gateways");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.Merchant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AchReceivingAccountNumber")
                        .HasColumnType("text");

                    b.Property<string>("AchReceivingAccountType")
                        .HasColumnType("text");

                    b.Property<string>("AchReceivingRoutingNumber")
                        .HasColumnType("text");

                    b.Property<bool>("AllowBinCheckOnTokenization")
                        .HasColumnType("boolean");

                    b.Property<int>("ChargebackFee")
                        .HasColumnType("integer");

                    b.Property<string>("ChargebackFeeType")
                        .HasColumnType("text");

                    b.Property<string>("CompanyName")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Dba")
                        .HasColumnType("text");

                    b.Property<string>("Descriptor")
                        .HasColumnType("text");

                    b.Property<string>("Descriptor_Address")
                        .HasColumnType("text");

                    b.Property<string>("Descriptor_City")
                        .HasColumnType("text");

                    b.Property<string>("Descriptor_Country")
                        .HasColumnType("text");

                    b.Property<string>("Descriptor_Mcc")
                        .HasColumnType("text");

                    b.Property<string>("Descriptor_Merchant_Id")
                        .HasColumnType("text");

                    b.Property<string>("Descriptor_Phone")
                        .HasColumnType("text");

                    b.Property<string>("Descriptor_Postal")
                        .HasColumnType("text");

                    b.Property<string>("Descriptor_State")
                        .HasColumnType("text");

                    b.Property<string>("Descriptor_Url")
                        .HasColumnType("text");

                    b.Property<bool>("EnableGlobalNetworkTokenization")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("IntegrationPartnerId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("LegalEntityName")
                        .HasColumnType("text");

                    b.Property<bool>("Locked")
                        .HasColumnType("boolean");

                    b.Property<Guid>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PayoutFrequency")
                        .HasColumnType("text");

                    b.Property<bool>("PayoutsEnabled")
                        .HasColumnType("boolean");

                    b.Property<Guid>("Pid")
                        .HasColumnType("uuid");

                    b.Property<int>("RefundFee")
                        .HasColumnType("integer");

                    b.Property<string>("RefundFeeType")
                        .HasColumnType("text");

                    b.Property<string>("Spreedly3dsMerchantProfileKey")
                        .HasColumnType("text");

                    b.Property<string>("Spreedly3dsScaProviderKey")
                        .HasColumnType("text");

                    b.Property<string>("SpreedlyEnvironmentKey")
                        .HasColumnType("text");

                    b.Property<string>("SpreedlySecretKey")
                        .HasColumnType("text");

                    b.Property<int>("TransactionBaseFee")
                        .HasColumnType("integer");

                    b.Property<string>("TransactionBaseFeeType")
                        .HasColumnType("text");

                    b.Property<int>("TransactionFee")
                        .HasColumnType("integer");

                    b.Property<string>("TransactionFeeType")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Merchants");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.MonitoredPartnerTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateOnly?>("CreatedOnDate")
                        .HasColumnType("date");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MonitoringType")
                        .HasColumnType("text");

                    b.Property<string>("PaymentType")
                        .HasColumnType("text");

                    b.Property<Guid>("ProviderId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("TransactionCreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("TransactionId")
                        .HasColumnType("uuid");

                    b.Property<string>("TransactionType")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CreatedOnDate");

                    b.HasIndex("TransactionId");

                    b.ToTable("PartnerTransactionMonitoring");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.MonitoredTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateOnly?>("CreatedOnDate")
                        .HasColumnType("date");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MonitoringType")
                        .HasColumnType("text");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<string>("PaymentType")
                        .HasColumnType("text");

                    b.Property<Guid>("ProviderId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("TransactionCreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("TransactionId")
                        .HasColumnType("uuid");

                    b.Property<string>("TransactionType")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CreatedOnDate");

                    b.HasIndex("TransactionId");

                    b.ToTable("TransactionMonitoring");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.OpenBankingAccessToken", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AccessToken")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("Expiration")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ItemId")
                        .HasColumnType("text");

                    b.Property<Guid?>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("OwnerId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("MerchantId");

                    b.ToTable("OpenBankingAccessTokens");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.OpenBankingData", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("AccessTokenId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Data")
                        .HasColumnType("text");

                    b.Property<string>("DataType")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("OwnerId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("AccessTokenId");

                    b.HasIndex("MerchantId");

                    b.ToTable("OpenBankingData");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.PartnerTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<string>("AuthorizationId")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateOnly?>("CreatedOnDate")
                        .HasColumnType("date");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<string>("ExtraData")
                        .HasColumnType("text");

                    b.Property<string>("InternalResponseCode")
                        .HasColumnType("text");

                    b.Property<string>("InternalResponseGroup")
                        .HasColumnType("text");

                    b.Property<string>("InternalResponseMessage")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<JsonDocument>("Meta")
                        .HasColumnType("jsonb");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<Guid>("ParentId")
                        .HasColumnType("uuid");

                    b.Property<string>("PaymentType")
                        .HasColumnType("text");

                    b.Property<Guid>("Pid")
                        .HasColumnType("uuid");

                    b.Property<string>("ProcessorId")
                        .HasColumnType("text");

                    b.Property<string>("ProcessorName")
                        .HasColumnType("text");

                    b.Property<Guid>("ProviderId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProviderName")
                        .HasColumnType("text");

                    b.Property<string>("ProviderTransactionToken")
                        .HasColumnType("text");

                    b.Property<string>("ResponseCode")
                        .HasColumnType("text");

                    b.Property<string>("ResponseMessage")
                        .HasColumnType("text");

                    b.Property<TransactionStatus>("Status")
                        .HasColumnType("transaction_status");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CreatedOn");

                    b.HasIndex("CreatedOnDate");

                    b.HasIndex("ParentId");

                    b.HasIndex("ProviderTransactionToken");

                    b.ToTable("PartnerTransactions");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.PaymentInstrument", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AccountHolderFirstName")
                        .HasColumnType("text");

                    b.Property<string>("AccountHolderLastName")
                        .HasColumnType("text");

                    b.Property<string>("AccountHolderName")
                        .HasColumnType("text");

                    b.Property<string>("AccountHolderType")
                        .HasColumnType("text");

                    b.Property<DateTime?>("AccountLastUpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AccountNumber")
                        .HasColumnType("text");

                    b.Property<string>("AccountType")
                        .HasColumnType("text");

                    b.Property<string>("BankName")
                        .HasColumnType("text");

                    b.Property<string>("Bin")
                        .HasColumnType("text");

                    b.Property<string>("CardBrand")
                        .HasColumnType("text");

                    b.Property<string>("CardCountry")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderFirstName")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderLastName")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderName")
                        .HasColumnType("text");

                    b.Property<string>("CardNumberMasked")
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ExpirationMonth")
                        .HasColumnType("text");

                    b.Property<string>("ExpirationYear")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsVerified")
                        .HasColumnType("boolean");

                    b.Property<string>("Issuer")
                        .HasColumnType("text");

                    b.Property<string>("Last4")
                        .HasColumnType("text");

                    b.Property<Guid?>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Meta")
                        .HasColumnType("jsonb");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("NetworkTokenEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<string>("Number")
                        .HasColumnType("text");

                    b.Property<Guid>("OwnerId")
                        .HasColumnType("uuid");

                    b.Property<string>("RoutingNumber")
                        .HasColumnType("text");

                    b.Property<string>("Token")
                        .HasColumnType("text");

                    b.Property<string>("TokenizationProvider")
                        .HasColumnType("text");

                    b.Property<PaymentMethodType>("Type")
                        .HasColumnType("payment_method_type");

                    b.Property<bool>("ValidLuhn")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("MerchantId");

                    b.HasIndex("Token");

                    b.ToTable("PaymentInstruments");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.ProviderMeta", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("LogoIcon")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ProvidersMeta");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.ProviderService", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Configuration")
                        .HasColumnType("jsonb");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSandbox")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<int>("NameIdentifier")
                        .HasColumnType("integer");

                    b.Property<int>("ProviderType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("ProviderServices");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.ReportingConfiguration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AutoMatch")
                        .HasColumnType("boolean");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ProviderName")
                        .HasColumnType("text");

                    b.Property<string>("SftpFileNamePart")
                        .HasColumnType("text");

                    b.Property<string>("SftpHost")
                        .HasColumnType("text");

                    b.Property<string>("SftpPassword")
                        .HasColumnType("text");

                    b.Property<string>("SftpPollingPath")
                        .HasColumnType("text");

                    b.Property<int?>("SftpPort")
                        .HasColumnType("integer");

                    b.Property<string>("SftpPrivateKey")
                        .HasColumnType("text");

                    b.Property<string>("SftpPrivateName")
                        .HasColumnType("text");

                    b.Property<string>("SftpUsername")
                        .HasColumnType("text");

                    b.Property<string>("Source")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ReportingConfigurations");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.ReportsHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FileName")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("ProcessedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("ProviderId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProviderName")
                        .HasColumnType("text");

                    b.Property<string>("Source")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ReportsHistory");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.Subscription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<string>("BinNumber")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CancelAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CancelReason")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CancelledAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<string>("InitialStatus")
                        .HasColumnType("text");

                    b.Property<bool?>("IsAlive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsExternal")
                        .HasColumnType("boolean");

                    b.Property<bool?>("LastPaymentSuccess")
                        .HasColumnType("boolean");

                    b.Property<JsonDocument>("Meta")
                        .HasColumnType("jsonb");

                    b.Property<Guid>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("PaymentInstrumentId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ProviderId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProviderName")
                        .HasColumnType("text");

                    b.Property<string>("ProviderSubscriptionId")
                        .HasColumnType("text");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.ToTable("Subscriptions");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.SupportedGateway", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ApiFormat")
                        .HasColumnType("text");

                    b.Property<int>("AuthFee")
                        .HasColumnType("integer");

                    b.Property<string>("AuthenticationType")
                        .HasColumnType("text");

                    b.Property<string>("BIN")
                        .HasColumnType("text");

                    b.Property<string>("CAID")
                        .HasColumnType("text");

                    b.Property<CapabilitiesModel>("Capabilities")
                        .HasColumnType("jsonb");

                    b.Property<string>("Category")
                        .HasColumnType("text");

                    b.Property<string>("ChargebacksManagementProvider")
                        .HasColumnType("text");

                    b.Property<string>("Configuration")
                        .HasColumnType("jsonb");

                    b.Property<string>("ContactEmail")
                        .HasColumnType("text");

                    b.Property<string>("ContactName")
                        .HasColumnType("text");

                    b.Property<string>("ContactPhone")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Domain")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DynamicDescriptorValidationDateTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EthocaProvider")
                        .HasColumnType("text");

                    b.Property<string>("FixedDescriptorPrefix")
                        .HasColumnType("text");

                    b.Property<string>("Identifier")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsOrchestratorGateway")
                        .HasColumnType("boolean");

                    b.Property<string>("MCC")
                        .HasColumnType("text");

                    b.Property<string>("MasterCardDescriptorPrefix")
                        .HasColumnType("text");

                    b.Property<Guid?>("MetaId")
                        .HasColumnType("uuid");

                    b.Property<Dictionary<string, bool>>("Methods")
                        .HasColumnType("jsonb");

                    b.Property<Guid?>("MetricsId")
                        .HasColumnType("uuid");

                    b.Property<string>("MidNumber")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("NameIdentifier")
                        .HasColumnType("text");

                    b.Property<Guid?>("PartnerId")
                        .HasColumnType("uuid");

                    b.Property<string>("Password")
                        .HasColumnType("text");

                    b.Property<string>("PricingModel")
                        .HasColumnType("text");

                    b.Property<string>("ProcessorId")
                        .HasColumnType("text");

                    b.Property<string>("ProcessorPlatform")
                        .HasColumnType("text");

                    b.Property<string>("RdrProvider")
                        .HasColumnType("text");

                    b.Property<Guid?>("ReportingConfigurationsId")
                        .HasColumnType("uuid");

                    b.Property<bool>("Sandbox")
                        .HasColumnType("boolean");

                    b.Property<string>("SecretKey")
                        .HasColumnType("text");

                    b.Property<string>("SponsorBank")
                        .HasColumnType("text");

                    b.Property<string>("User")
                        .HasColumnType("text");

                    b.Property<string>("Version")
                        .HasColumnType("text");

                    b.Property<string>("VisaDescriptorPrefix")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("MetaId");

                    b.HasIndex("MetricsId");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasFilter("\"IsDeleted\"=false");

                    b.HasIndex("ReportingConfigurationsId");

                    b.ToTable("SupportedGateways");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.Transaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool?>("AccountUpdaterUsed")
                        .HasColumnType("boolean");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<string>("Arn")
                        .HasColumnType("text");

                    b.Property<int?>("AuthorizationAmount")
                        .HasColumnType("integer");

                    b.Property<string>("AuthorizationId")
                        .HasColumnType("text");

                    b.Property<string>("AvsResultCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("BatchId")
                        .HasColumnType("uuid");

                    b.Property<string>("CavvResultCode")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateOnly?>("CreatedOnDate")
                        .HasColumnType("date");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<string>("CvvResultCode")
                        .HasColumnType("text");

                    b.Property<int>("DiscountAmount")
                        .HasColumnType("integer");

                    b.Property<string>("DynamicDescriptor")
                        .HasColumnType("text");

                    b.Property<string>("DynamicDescriptorPhone")
                        .HasColumnType("text");

                    b.Property<string>("ExtraData")
                        .HasColumnType("text");

                    b.Property<int?>("FeeAmount")
                        .HasColumnType("integer");

                    b.Property<string>("InternalResponseCode")
                        .HasColumnType("text");

                    b.Property<string>("InternalResponseGroup")
                        .HasColumnType("text");

                    b.Property<string>("InternalResponseMessage")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsExternal")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRecurring")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<JsonDocument>("Meta")
                        .HasColumnType("jsonb");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("NetworkTokenUsed")
                        .HasColumnType("boolean");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ParentId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("PayerId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("PaymentMethodId")
                        .HasColumnType("uuid");

                    b.Property<string>("PaymentType")
                        .HasColumnType("text");

                    b.Property<string>("ProcessorId")
                        .HasColumnType("text");

                    b.Property<string>("ProcessorName")
                        .HasColumnType("text");

                    b.Property<Guid>("ProviderId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProviderName")
                        .HasColumnType("text");

                    b.Property<string>("ProviderTransactionToken")
                        .HasColumnType("text");

                    b.Property<string>("ResponseCode")
                        .HasColumnType("text");

                    b.Property<string>("ResponseMessage")
                        .HasColumnType("text");

                    b.Property<string>("RiskReasonCode")
                        .HasColumnType("text");

                    b.Property<string>("RiskReasonMessage")
                        .HasColumnType("text");

                    b.Property<string>("SchemeTransactionId")
                        .HasColumnType("text");

                    b.Property<bool>("SchemeTransactionIdUsed")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("SiteId")
                        .HasColumnType("uuid");

                    b.Property<TransactionStatus>("Status")
                        .HasColumnType("transaction_status");

                    b.Property<Guid?>("SubscriptionId")
                        .HasColumnType("uuid");

                    b.Property<int?>("TaxAmount")
                        .HasColumnType("integer");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("BatchId");

                    b.HasIndex("CreatedOn");

                    b.HasIndex("CreatedOnDate");

                    b.HasIndex("MerchantId");

                    b.HasIndex("OrderId");

                    b.HasIndex("ParentId");

                    b.HasIndex("PaymentMethodId");

                    b.HasIndex("ProviderTransactionToken");

                    b.HasIndex("SubscriptionId");

                    b.ToTable("Transactions");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.Batch", b =>
                {
                    b.HasOne("FlexCharge.Payments.Entities.Merchant", "Beneficiary")
                        .WithMany()
                        .HasForeignKey("BeneficiaryId");

                    b.Navigation("Beneficiary");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.DisputeActivity", b =>
                {
                    b.HasOne("FlexCharge.Payments.Entities.Dispute", null)
                        .WithMany("DisputeActivities")
                        .HasForeignKey("DisputeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("FlexCharge.Payments.Entities.DisputeQueueItem", null)
                        .WithMany("DisputeActivities")
                        .HasForeignKey("DisputeQueueItemId");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.DisputeQueueItem", b =>
                {
                    b.HasOne("FlexCharge.Payments.Entities.DisputeQueue", null)
                        .WithMany("DisputeQueueItems")
                        .HasForeignKey("DisputeQueueId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.Gateway", b =>
                {
                    b.HasOne("FlexCharge.Payments.Entities.Merchant", "Merchant")
                        .WithMany("RelatedGateways")
                        .HasForeignKey("MerchantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("FlexCharge.Payments.Entities.SupportedGateway", "SupportedGateway")
                        .WithMany()
                        .HasForeignKey("SupportedGatewayId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Merchant");

                    b.Navigation("SupportedGateway");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.MonitoredPartnerTransaction", b =>
                {
                    b.HasOne("FlexCharge.Payments.Entities.PartnerTransaction", "Transaction")
                        .WithMany()
                        .HasForeignKey("TransactionId");

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.MonitoredTransaction", b =>
                {
                    b.HasOne("FlexCharge.Payments.Entities.Transaction", "Transaction")
                        .WithMany()
                        .HasForeignKey("TransactionId");

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.OpenBankingAccessToken", b =>
                {
                    b.HasOne("FlexCharge.Payments.Entities.Merchant", "Merchant")
                        .WithMany()
                        .HasForeignKey("MerchantId");

                    b.Navigation("Merchant");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.OpenBankingData", b =>
                {
                    b.HasOne("FlexCharge.Payments.Entities.OpenBankingAccessToken", "AccessToken")
                        .WithMany()
                        .HasForeignKey("AccessTokenId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("FlexCharge.Payments.Entities.Merchant", "Merchant")
                        .WithMany()
                        .HasForeignKey("MerchantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AccessToken");

                    b.Navigation("Merchant");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.PaymentInstrument", b =>
                {
                    b.HasOne("FlexCharge.Payments.Entities.Merchant", "Merchant")
                        .WithMany()
                        .HasForeignKey("MerchantId");

                    b.Navigation("Merchant");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.SupportedGateway", b =>
                {
                    b.HasOne("FlexCharge.Payments.Entities.ProviderMeta", "Meta")
                        .WithMany()
                        .HasForeignKey("MetaId");

                    b.HasOne("FlexCharge.Payments.Entities.CycleMetrics", "Metrics")
                        .WithMany()
                        .HasForeignKey("MetricsId");

                    b.HasOne("FlexCharge.Payments.Entities.ReportingConfiguration", "ReportingConfigurations")
                        .WithMany()
                        .HasForeignKey("ReportingConfigurationsId");

                    b.Navigation("Meta");

                    b.Navigation("Metrics");

                    b.Navigation("ReportingConfigurations");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.Transaction", b =>
                {
                    b.HasOne("FlexCharge.Payments.Entities.Batch", "Batch")
                        .WithMany("Transactions")
                        .HasForeignKey("BatchId");

                    b.HasOne("FlexCharge.Payments.Entities.Merchant", "Merchant")
                        .WithMany()
                        .HasForeignKey("MerchantId");

                    b.HasOne("FlexCharge.Payments.Entities.PaymentInstrument", "PaymentMethod")
                        .WithMany()
                        .HasForeignKey("PaymentMethodId");

                    b.HasOne("FlexCharge.Payments.Entities.Subscription", "Subscription")
                        .WithMany("Transactions")
                        .HasForeignKey("SubscriptionId");

                    b.Navigation("Batch");

                    b.Navigation("Merchant");

                    b.Navigation("PaymentMethod");

                    b.Navigation("Subscription");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.Batch", b =>
                {
                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.Dispute", b =>
                {
                    b.Navigation("DisputeActivities");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.DisputeQueue", b =>
                {
                    b.Navigation("DisputeQueueItems");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.DisputeQueueItem", b =>
                {
                    b.Navigation("DisputeActivities");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.Merchant", b =>
                {
                    b.Navigation("RelatedGateways");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.Subscription", b =>
                {
                    b.Navigation("Transactions");
                });
#pragma warning restore 612, 618
        }
    }
}
