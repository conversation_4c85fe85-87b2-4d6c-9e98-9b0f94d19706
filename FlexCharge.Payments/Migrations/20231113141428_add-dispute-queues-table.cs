using System;
using FlexCharge.Payments.Entities;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    /// <inheritdoc />
    public partial class adddisputequeuestable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TransactionMatches");

            migrationBuilder.AddColumn<Guid>(
                name: "DisputeQueueItemId",
                table: "DisputeActivities",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "DisputeQueues",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    RefundTransactionId = table.Column<Guid>(type: "uuid", nullable: false),
                    Descriptor = table.Column<string>(type: "text", nullable: true),
                    CaseNumber = table.Column<string>(type: "text", nullable: true),
                    Stage = table.Column<string>(type: "text", nullable: true),
                    Amount = table.Column<int>(type: "integer", nullable: false),
                    AuthorizationId = table.Column<string>(type: "text", nullable: true),
                    Currency = table.Column<string>(type: "text", nullable: true),
                    TransactionDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    RequestDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    DueDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Mcc = table.Column<string>(type: "text", nullable: true),
                    Arn = table.Column<string>(type: "text", nullable: true),
                    ProviderName = table.Column<string>(type: "text", nullable: true),
                    ProcessorName = table.Column<string>(type: "text", nullable: true),
                    DisputeManagementSystem = table.Column<string>(type: "text", nullable: true),
                    DisputeType = table.Column<string>(type: "text", nullable: true),
                    CardType = table.Column<string>(type: "text", nullable: true),
                    Bin = table.Column<string>(type: "text", nullable: true),
                    Last4 = table.Column<string>(type: "text", nullable: true),
                    Message = table.Column<string>(type: "text", nullable: true),
                    EarlyFraudWarning = table.Column<bool>(type: "boolean", nullable: false),
                    IsWebhook = table.Column<bool>(type: "boolean", nullable: false),
                    Note = table.Column<string>(type: "text", nullable: true),
                    Meta = table.Column<string>(type: "jsonb", nullable: true),
                    HasMatch = table.Column<bool>(type: "boolean", nullable: true),
                    IsArchived = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    MatchedTransactionCount = table.Column<int>(type: "integer", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DisputeQueues", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_DisputeActivities_DisputeQueueItemId",
                table: "DisputeActivities",
                column: "DisputeQueueItemId");

            migrationBuilder.AddForeignKey(
                name: "FK_DisputeActivities_DisputeQueues_DisputeQueueItemId",
                table: "DisputeActivities",
                column: "DisputeQueueItemId",
                principalTable: "DisputeQueues",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DisputeActivities_DisputeQueues_DisputeQueueItemId",
                table: "DisputeActivities");

            migrationBuilder.DropTable(
                name: "DisputeQueues");

            migrationBuilder.DropIndex(
                name: "IX_DisputeActivities_DisputeQueueItemId",
                table: "DisputeActivities");

            migrationBuilder.DropColumn(
                name: "DisputeQueueItemId",
                table: "DisputeActivities");

            migrationBuilder.CreateTable(
                name: "TransactionMatches",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    MerchantId = table.Column<Guid>(type: "uuid", nullable: true),
                    Amount = table.Column<int>(type: "integer", nullable: false),
                    Arn = table.Column<string>(type: "text", nullable: true),
                    AuthorizationId = table.Column<string>(type: "text", nullable: true),
                    Bin = table.Column<string>(type: "text", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Currency = table.Column<string>(type: "text", nullable: true),
                    DisputeId = table.Column<Guid>(type: "uuid", nullable: false),
                    DynamicDescriptor = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    Last4 = table.Column<string>(type: "text", nullable: true),
                    Meta = table.Column<string>(type: "jsonb", nullable: true),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true),
                    ModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Note = table.Column<string>(type: "text", nullable: true),
                    OrderId = table.Column<Guid>(type: "uuid", nullable: false),
                    ProviderName = table.Column<string>(type: "text", nullable: true),
                    ProviderTransactionToken = table.Column<string>(type: "text", nullable: true),
                    ResponseCode = table.Column<string>(type: "text", nullable: true),
                    SiteId = table.Column<Guid>(type: "uuid", nullable: true),
                    Status = table.Column<TransactionStatus>(type: "transaction_status", nullable: false),
                    TransactionId = table.Column<Guid>(type: "uuid", nullable: false),
                    Type = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TransactionMatches", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TransactionMatches_Disputes_DisputeId",
                        column: x => x.DisputeId,
                        principalTable: "Disputes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TransactionMatches_Merchants_MerchantId",
                        column: x => x.MerchantId,
                        principalTable: "Merchants",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_TransactionMatches_DisputeId",
                table: "TransactionMatches",
                column: "DisputeId");

            migrationBuilder.CreateIndex(
                name: "IX_TransactionMatches_MerchantId",
                table: "TransactionMatches",
                column: "MerchantId");
        }
    }
}
