using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    /// <inheritdoc />
    public partial class alterfinancialaccountsaddverificationfields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsVerified",
                table: "FinancialAccounts");

            migrationBuilder.AddColumn<string>(
                name: "VerificationState",
                table: "FinancialAccounts",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "VerificationTransactionId",
                table: "FinancialAccounts",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("********-0000-0000-0000-************"));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "VerificationState",
                table: "FinancialAccounts");

            migrationBuilder.DropColumn(
                name: "VerificationTransactionId",
                table: "FinancialAccounts");

            migrationBuilder.AddColumn<bool>(
                name: "IsVerified",
                table: "FinancialAccounts",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }
    }
}
