using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace FlexCharge.Payments.Entities;

public class TransactionMatch : AuditableEntity
{
    public Guid DisputeId { get; set; }
    public Guid TransactionId { get; set; }
    public string ProviderTransactionToken { get; set; }
    public string DynamicDescriptor { get; set; }
    public int Amount { get; set; }
    public string Currency { get; set; }
    public string ResponseCode { get; set; }
    public string AuthorizationId { get; set; }
    public TransactionStatus Status { get; set; }
    public Guid? SiteId { get; set; }
    public Merchant Merchant { get; set; }
    public Guid OrderId { get; set; }
    public string Arn { get; set; }
    public string Note { get; set; }
    [Column(TypeName = "jsonb")] public string Meta { get; set; }
    public string Bin { get; set; }
    public string Last4 { get; set; }
    public string ProviderName { get; set; }
    public string Type { get; set; }
}