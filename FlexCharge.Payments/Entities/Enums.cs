namespace FlexCharge.Payments.Entities
{
    public enum TransactionType
    {
        Debit, // Sale, ACH Debit
        Authorization, // authorize a payment instrument
        Capture, // capture auth
        Credit, //ACH Credit, Standalone refund (Push to card)
        Charge, // Charge a payment instrument on external payment system
        Void,
        VoidedByConsumer,
        Verify, // Verify zero amount transaction
        Chargeback,
        Refund,
        RefundPreDispute,
        PreDispute,
        RefundByConsumer,
        RefundByEarlyWarning,
        Reverse, // Chargeback reversed
        CancelRefund,
        CancelDebit, // ACH
        CancelCredit, // ACH, Standalone refund (Push to card)
        DebitReturn, // ACH debit return
        CreditReturn // ACH debit return
    }

    public enum TransactionStatus
    {
        Unknown,
        Initialized,
        InProcess,
        Completed,
        Failed,
        Canceled,
        Held, // Paysafe in-processing state, ACH hold state
        //ValidationError //todo-check if is ok
        // Expired
    }

    public enum PaymentMethodType
    {
        UnKnown = 0,
        Credit = 1,
        Debit = 2,
        ECheck = 3,
        Check = 4,
        MoneyOrder = 5,
        Cash = 6,
        GooglePay = 7,
        ApplePay = 8,
        SamsungPay = 9,
        Bnpl = 10,
        Other = 999
    }

    public enum AuthenticationType
    {
        UnKnown = 0,
        NoAuthentication,
        Basic,
        Oauth2,
        UserPassword,
        ApiKey
    }
}