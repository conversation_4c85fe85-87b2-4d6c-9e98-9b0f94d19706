using System;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Payments.Entities;

[Index(nameof(AccessToken))]
public class OpenBankingAccessToken : AuditableEntity
{
    public Guid? MerchantId { get; set; }
    public Merchant Merchant { get; set; }
    public Guid? OwnerId { get; set; }
    public Guid OrderId { get; set; }
    public string AccessToken { get; set; }
    public string ItemId { get; set; }
    public DateTime? Expiration { get; set; }
    public string InstitutionName { get; set; }
}