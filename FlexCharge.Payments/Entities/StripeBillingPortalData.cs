using System;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Payments.Entities
{
    // Each merchant can have only one billing portal per configuration
    [Index(nameof(Mid), nameof(StripeAccountId), nameof(ConfigurationName), IsUnique = true)]
    public class StripeBillingPortalData : AuditableEntity
    {
        public Guid Mid { get; set; }
        public string StripeAccountId { get; set; }
        public string ConfigurationName { get; set; }
        public string Data { get; set; }
    }
}