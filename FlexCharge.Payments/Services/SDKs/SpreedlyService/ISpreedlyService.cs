using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Services.SpreedlyService.CreateMerchantProfileRequest;

namespace FlexCharge.Payments.Services.SpreedlyService;

public interface ISpreedlyService
{
    Task<AuthorizeResponse> AuthorizeAsync(Guid merchantId, string environment, string secret, string gatewayId,
        AuthorizeRequest payload, CancellationToken token);

    Task<CaptureResponse> CaptureAsync(Guid merchantId, string environment, string secret, string transactionToken,
        CaptureRequest payload, CancellationToken token);

    Task<CreditResponse> CreditAsync(Guid merchantId, string environment, string secret, string transactionToken,
        CreditRequest payload, CancellationToken token);

    Task<General_CreditResponse> General_CreditAsync(Guid merchantId, string environment, string secret,
        string gatewayId,
        General_CreditRequest payload, CancellationToken token);

    Task<VoidResponse> VoidAsync(Guid merchantId, string environment, string secret,
        VoidRequest payload,
        CancellationToken token);


    Task<VerifyResponse> VerifyAsync(string environment, string secret, Guid merchantId, string gatewayId,
        VerifyRequest payload, CancellationToken token);

    Task<EnvironmentResponse> AddEnvironmentAsync(NewEnvironmentRequest payload,
        Guid merchantId,
        CancellationToken token);

    Task<EnvironmentSecretResponse> AddEnvironmentSecretAsync(string environment,
        EnvironmentSecretRequest payload, Guid merchantId,
        CancellationToken token);
    //Task<PurchaseResponse> PurchaseAsync(PurchaseRequest Payload, CancellationToken Token);

    Task<AddGatewayResponse> AddGatewayAsync(string environment, string secret, Guid merchantId,
        AddGatewayRequest payload, CancellationToken token);

    Task<PurchaseResponse> PurchaseAsync(string environment, string secret, string gatewayId, Guid merchantId,
        PurchaseRequest payload, CancellationToken token);

    Task<PaymentInstrumentsDTO> GetPaymentInstrumentsAsync(string environment, string secret, Guid merchantId,
        CancellationToken token);

    Task<QueryPaymentInstrumentDTO> GetPaymentInstrumentAsync(string environment, string secret, Guid merchantId,
        string paymentInstrumentToken,
        CancellationToken token);

    Task<CreatePaymentInstrumentResponse> CreatePaymentInstrumentAsync(string environment, string secret,
        Guid merchantId,
        CreatePaymentInstrumentRequest payload, CancellationToken token);

    Task<CreatePaymentInstrumentResponse> CreatePaymentInstrumentAsync(string environment,
        Guid merchantId, CreatePaymentInstrumentRequest payload, CancellationToken token);

    Task<RetainResponse> RetainPaymentInstrumentAsync(Guid merchantId, string environment, string secret,
        string paymentInstrumentToken,
        CancellationToken token);
    
    Task<RecachePaymentMethodResponse> ReCachePaymentInstrumentAsync(Guid merchantId, string environment,
        string paymentInstrumentToken, RecachePaymentMethodRequest payload,
        CancellationToken token);

    Task<Create3DSMerchantProfileResponse> Create3dsMerchantProfileAsync(string environment, string secret,
        Guid merchantId, Create3DSMerchantProfileRequest payload, CancellationToken token);

    Task<CreateSCAProviderRequest.CreateSCAProviderResponse> Create3dsSCAProviderAsync(string environment,
        string secret,
        Guid merchantId, CreateSCAProviderRequest.CreateSCAProviderRequest payload, CancellationToken token);

    Task<AuthenticateResponse> AuthenticateAsync(Guid merchantId, string environment, string secret,
        string scaProviderKey,
        AuthenticateRequest payload, CancellationToken token);

    Task<AuthenticateResponse> GetAuthenticateTransactionAsync(
        Guid merchantId, string environment, string secret,
        string transactionToken, CancellationToken token);

    Task<bool> Add3DSSupportIfMissingAsync(Entities.Merchant merchant,
        CancellationToken cancellationToken);
}