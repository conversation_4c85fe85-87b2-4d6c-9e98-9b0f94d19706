using System;
using System.Text.Json.Serialization;
using Newtonsoft.Json;

namespace FlexCharge.Payments.Services.SpreedlyService;

public class CreditRequest : SpreedlyRequest
{
    [JsonProperty("transaction")] public CreditTransaction Transaction { get; set; }
}

public class CreditTransaction
{
    //The token of the payment method to use
    [JsonProperty("payment_method_token")] public string PaymentMethodToken { get; set; }

    //The amount to request, as an integer. E.g., 1000 for $10.00.
    [JsonProperty("amount")] public int Amount { get; set; }

    //The Currency of the funds, as ISO 4217 alpha Currency codes, e.g., USD for US dollars.
    [JsonProperty("currency_code")] public string CurrencyCode { get; set; }

    //The merchant specified order id. If not provided, the Spreedly transaction token will be used.
    [JsonProperty("order_id")] public string OrderId { get; set; }

    //A human readable description of the transaction which will be passed to the gateway if it’s supported
    [JsonProperty("description")] public string Description { get; set; }

    //A boolean option to keep the cvv cached for a few minutes. Otherwise cvv is deleted immediately.
    [JsonProperty("continue_caching")] public bool ContinueCaching { get; set; }
}

public class General_CreditRequest : SpreedlyRequest
{
    [JsonProperty("transaction")] public AuthorizeTransaction Transaction { get; set; }
}

public class General_CreditTransaction
{
    [JsonProperty("payment_method_token")] public int PaymentMethodToken { get; set; }

    [JsonProperty("amount")] public int Amount { get; set; }

    [JsonProperty("currency_code")] public string CurrencyCode { get; set; }
}