using System;
using System.Collections.Generic;
using FlexCharge.Common.Response;
using Newtonsoft.Json;

namespace FlexCharge.Payments.Services.SpreedlyService.CreateMerchantProfileRequest;

public class Create3DSMerchantProfileRequest
{
    [JsonProperty("merchant_profile")] public MerchantProfileRequest MerchantProfile { get; set; }
}

public class MerchantProfileRequest
{
    [JsonProperty("description")] public string Description { get; set; }

    [JsonProperty("visa")] public Visa Visa { get; set; }

    [JsonProperty("mastercard")] public Mastercard Mastercard { get; set; }

    [JsonProperty("amex")] public Amex Amex { get; set; }
}

public class Amex
{
    [JsonProperty("acquirer_merchant_id")] public string AcquirerMerchantId { get; set; }

    [JsonProperty("merchant_name")] public string MerchantName { get; set; }

    //see: ISO 3166-1
    [Json<PERSON>roperty("country_code")] public string CountryCode { get; set; }

    [JsonProperty("mcc")] public string Mcc { get; set; }
}

public class Mastercard
{
    [JsonProperty("acquirer_merchant_id")] public string AcquirerMerchantId { get; set; }

    [JsonProperty("merchant_name")] public string MerchantName { get; set; }

    [JsonProperty("country_code")] public string CountryCode { get; set; }

    [JsonProperty("mcc")] public string Mcc { get; set; }
}

public class Visa
{
    [JsonProperty("acquirer_merchant_id")] public string AcquirerMerchantId { get; set; }

    [JsonProperty("merchant_name")] public string MerchantName { get; set; }

    [JsonProperty("country_code")] public string CountryCode { get; set; }

    [JsonProperty("mcc")] public string Mcc { get; set; }
}