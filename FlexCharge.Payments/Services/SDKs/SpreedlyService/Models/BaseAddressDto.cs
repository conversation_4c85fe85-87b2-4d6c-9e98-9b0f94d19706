using Newtonsoft.Json;

namespace FlexCharge.Payments.Services.SpreedlyService;

public class BaseAddressDto
{
    [JsonProperty("name")] public string Name { get; set; }
    [JsonProperty("address1")] public string Address1 { get; set; }
    [JsonProperty("address2")] public string Address2 { get; set; }
    [JsonProperty("city")] public string City { get; set; }
    [JsonProperty("state")] public string State { get; set; }
    [JsonProperty("zip")] public string Zip { get; set; }
    [JsonProperty("country")] public string Country { get; set; }
    [JsonProperty("phone_number")] public string PhoneNumber { get; set; }
}

public class BillingAddressDto : BaseAddressDto
{
}

public class ShippingAddressDto : BaseAddressDto
{
}