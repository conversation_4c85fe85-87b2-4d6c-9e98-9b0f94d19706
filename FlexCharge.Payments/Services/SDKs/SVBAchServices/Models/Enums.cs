using System.Runtime.Serialization;

namespace FlexCharge.Payments.Services.SVBAchServices.Models;

public enum DirectionEnum
{
    /// <summary>
    /// Enum CREDIT for value: CREDIT
    /// </summary>
    [EnumMember(Value = "CREDIT")] CREDIT = 1,

    /// <summary>
    /// Enum DEBIT for value: DEBIT
    /// </summary>
    [EnumMember(Value = "DEBIT")] DEBIT = 2
}

public enum SettlementPriorityEnum
{
    /// <summary>
    /// Enum STANDARD for value: STANDARD
    /// </summary>
    [EnumMember(Value = "STANDARD")] STANDARD = 1,

    /// <summary>
    /// Enum SAMEDAY for value: SAME_DAY
    /// </summary>
    [EnumMember(Value = "SAME_DAY")] SAMEDAY = 2
}

public enum SecCodeEnum
{
    /// <summary>
    /// Enum CCD for value: CCD
    /// </summary>
    [EnumMember(Value = "CCD")] CCD = 1,

    /// <summary>
    /// Enum PPD for value: PPD
    /// </summary>
    [EnumMember(Value = "PPD")] PPD = 2,

    /// <summary>
    /// Enum WEB for value: WEB
    /// </summary>
    [EnumMember(Value = "WEB")] WEB = 3
}

public enum AccountTypeEnum
{
    /// <summary>
    /// Enum CHECKING for value: CHECKING
    /// </summary>
    [EnumMember(Value = "CHECKING")] CHECKING = 1,

    /// <summary>
    /// Enum SAVINGS for value: SAVINGS
    /// </summary>
    [EnumMember(Value = "SAVINGS")] SAVINGS = 2
}