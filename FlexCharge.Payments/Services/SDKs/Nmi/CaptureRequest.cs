using System.Collections.Generic;
using System.Globalization;
using FlexCharge.Utils;

namespace FlexCharge.Payments.Services.SDKs.Nmi;

public class CaptureRequest : BaseNMIRequest
{
    public CaptureRequest(int amount)
    {
        Amount = Formatters.IntToDecimal(amount);
    }
    
    //type
    public string Type { get; } = NmiTransactionTypes.Capture;
    public string TransactionId { get; set; }
    //amount
    public decimal Amount { get; private set; }

    public string OrderId { get; set; }

    public Dictionary<string, string> ToDictionary()
    {
        return new Dictionary<string, string>
        {
            { "type", Type },
            { "security_key", SecurityKey },
            { "transactionid", TransactionId },
            { "amount", Amount.ToString(CultureInfo.InvariantCulture) },
            { "orderid", OrderId }};
    }
}