//see: https://docs.adyen.com/online-payments/classic-integrations/api-integration-ecommerce/3d-secure
//see: https://docs.adyen.com/online-payments/classic-integrations/api-integration-ecommerce/3d-secure/native-3ds2/browser-based-integration
//see: https://docs.tokenex.com/docs/
//see: https://docs.3dsecure.io/3dsv2/guides.html

using System;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Telemetry;
using FlexCharge.Payments.Activities;
using FlexCharge.Payments.Services.SDKs.TokenExService.Models;
using FlexCharge.Payments.Services.SDKs.TokenExService.Models.ThreeDSecure.Authentication;
using FlexCharge.Payments.Services.SDKs.TokenExService.Models.ThreeDSecure.ChallengeResults;
using FlexCharge.Payments.Services.SDKs.TokenExService.Models.ThreeDSecure.SupportedVersions;
using FlexCharge.Payments.Services.TokenExService.Models.ThreeDSecure;
using MassTransit;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace FlexCharge.Payments.Services.TokenExService;

class TokenExService : BaseService, ITokenExService
{
    private readonly TokenExOptions _tokenExOptions;

    public TokenExService(HttpClient httpClient,
        IOptions<TokenExOptions> tokenExOptions) : base(httpClient)
    {
        _tokenExOptions = tokenExOptions.Value;
    }

    private string ApiBaseUrl => $"{_tokenExOptions.BaseUrl}";
    private string ThreeDSecureBaseUrl => $"{ApiBaseUrl}/ThreeDSecure";


    #region 3DSecure

    #region SupportedVersions

    public async Task<PreAuthenticateResponse> PreAuthenticateAsync(Guid merchantId, string cardPanOrToken,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<TokenExService>();

        var payload = new SupportedVersionsRequest(
            data: cardPanOrToken); //PAN or Token, depending on tx-tokenize header value

        var postResponse = await Post<SupportedVersionsRequest>(
            $"{ThreeDSecureBaseUrl}/SupportedVersions",
            null, null,
            payload, token,
            AddTokenExHeaders);

        var responseContent = await postResponse.Content.ReadAsStringAsync(token);
        workspan.Log.Information("Response: {ResponseContent}", responseContent);

        var responseBase = JsonConvert.DeserializeObject<SupportedVersionsResponseBase>(responseContent);

        if (responseBase.success == false || !string.IsNullOrWhiteSpace(responseBase.error))
        {
            workspan.Log.Error("TokenEx 3DSecure SupportedVersions request failed: {ResponseMessage}",
                responseBase.error ?? responseBase.message);

            var errorResponse = JsonConvert.DeserializeObject<UnsuccessfulSupportedVersionsResponse>(responseContent);

            PreAuthenticateResponse preAuthenticateResponse = new(
                token: errorResponse.token,
                recommendedDirectoryServiceIdentifier: null,
                recommended3dsVersion: null,
                threeDSecureResponse: errorResponse.threeDSecureResponse,
                referenceNumber: errorResponse.referenceNumber,
                success: errorResponse.success,
                error: errorResponse.error,
                message: errorResponse.message,
                thirdPartyStatusCode: errorResponse.thirdPartyStatusCode);

            return preAuthenticateResponse;
        }
        else
        {
            var response = JsonConvert.DeserializeObject<SuccessfulSupportedVersionsResponse>(responseContent);

            workspan.Log.Information("TokenEx 3DSecure SupportedVersions succeeded: {Response}", response);

            string recommendedDirectoryServiceIdentifier = null;
            string recommended3dsVersion = null;

            //Get recommended 3DS Directory Service and version from TokenEx response
            var recommended3dsVersionInfo = response.recommended3dsVersion.Properties().FirstOrDefault();
            if (recommended3dsVersionInfo != null)
            {
                recommendedDirectoryServiceIdentifier = recommended3dsVersionInfo.Name;
                recommended3dsVersion = (string) recommended3dsVersionInfo.Value;
            }

            //Determines which 3DS Directory Service response to use based on the recommended3dsVersionInfo
            var threeDSecureResponse = recommendedDirectoryServiceIdentifier != null
                ? response.threeDSecureResponse.FirstOrDefault(x =>
                    x.dsIdentifier == recommendedDirectoryServiceIdentifier)
                : null;


            PreAuthenticateResponse preAuthenticateResponse = new(
                token: response.token,
                recommendedDirectoryServiceIdentifier: recommendedDirectoryServiceIdentifier,
                recommended3dsVersion: recommended3dsVersion,
                threeDSecureResponse: threeDSecureResponse,
                referenceNumber: response.referenceNumber,
                success: response.success,
                error: response.error,
                message: response.message,
                thirdPartyStatusCode: response.thirdPartyStatusCode);

            return preAuthenticateResponse;
        }
    }

    #endregion

    #region Authenticate

    public async Task<AuthenticationResponse> Authenticate3dsAsync(
        Guid merchantId,
        MethodCompletionIndicator methodCompletionIndicator,
        string directoryServerIdentifier,
        string messageVersion, DeviceChannel deviceChannel,
        MessageCategory messageCategory, RequestorAuthenticationIndicator? authenticationIndicator,
        TransactionType transactionType,
        CardDetails cardDetails, CardholderDetails cardholderDetails,
        PurchaseDetails purchaseDetails,
        BrowserInfo browserInfo,
        string challengeResultNotificationWebhookUrl,
        ChallengeWindowSize challengeWindowSize,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<TokenExService>()
            .Baggage("Mid", merchantId);

        var cardSchema = _tokenExOptions.CardSchemas.FirstOrDefault(x => x.Name == directoryServerIdentifier);

        if (cardSchema == null)
        {
            workspan.Log.Warning("Unknown card schema {DirectoryServerIdentifier}", directoryServerIdentifier);
            return new AuthenticationResponse(
                token: null,
                threeDSecureResponse: null,
                referenceNumber: null,
                success: false,
                error: "Unknown card schema: " + directoryServerIdentifier,
                message: null,
                thirdPartyStatusCode: null);
        }

        //see: https://docs.tokenex.com/docs/authentications
        var payload = new AuthenticationRequest(
            MethodCompletionIndicator: (int) methodCompletionIndicator,
            MessageVersion: messageVersion,
            BrowserInfo: browserInfo,
            CardholderDetails: cardholderDetails,
            CardDetails: cardDetails,
            ChallengeWindowSize: (int) challengeWindowSize,
            DeviceChannel: (int) deviceChannel,
            DirectoryServerIdentifier: directoryServerIdentifier,
            // Indicator as to whether or not the 3DS Server should generate and return a Base64 encoded CReq message
            // to be used to initiate the challenge with the ACS. Default is true
            GenerateChallengeRequest: true,
            NotificationUrl: challengeResultNotificationWebhookUrl,
            // Payment Authentication (PA) or Non-Payment Authentication (NPA
            MessageCategory: (int) messageCategory,
            // Requestor Authentication Indicator : a one-time payment transaction, a recurring transaction, installment transaction, adding a card to a mobile wallet, maintaining card information on file, or cardholder verification.
            AuthenticationIndicator: authenticationIndicator != null ? (int) authenticationIndicator : null,
            PurchaseDetails: purchaseDetails,
            TransactionType: (int) transactionType,
            AcquirerBin: cardSchema.AcquirerBin,
            MerchantDetails: new MerchantDetails(
                AcquirerMerchantId: cardSchema.AcquirerMerchantId,
                CategoryCode: cardSchema.MCC,
                CountryCode: cardSchema.MerchantCountry,
                Name: cardSchema.MerchantName
            ));

        var response = await Post<AuthenticationResponse, AuthenticationRequest>(
            $"{ThreeDSecureBaseUrl}/Authentications",
            null, null,
            payload, token,
            AddTokenExHeaders);

        return response.Response;
    }

    #endregion

    #region GetChallengeResultsAsync

    public async Task<ChallengeResultsResponse> GetChallengeResultsAsync(Guid merchantId,
        string serverTransactionId, CancellationToken token)
    {
        using var workspan = Workspan.Start<TokenExService>()
            .Tag(nameof(merchantId), merchantId);

        var payload = new ChallengeResultsRequest
        (
            serverTransactionId
        );

        var response = await Post<ChallengeResultsResponse, ChallengeResultsRequest>(
            $"{ThreeDSecureBaseUrl}/ChallengeResults",
            null, null,
            payload, token,
            AddTokenExHeaders);

        return response.Response;
    }

    #endregion

    #endregion


    #region Authorization

    private void AddTokenExHeaders(HttpContentHeaders headers)
    {
        AddAuthorizationHeaders(headers);
        AddParametersHeaders(headers);
    }

    private void AddAuthorizationHeaders(HttpContentHeaders headers)
    {
        headers.Add("tx-TokenEx-Id", _tokenExOptions.TokenExId);
        headers.Add("tx-APIKey", _tokenExOptions.APIKey);
    }

    #endregion

    private void AddParametersHeaders(HttpContentHeaders headers)
    {
        headers.Add("tx-token-scheme", _tokenExOptions.TokenScheme);
        headers.Add("tx-tokenize", _tokenExOptions.Tokenize.ToString().ToLower());
    }
}