using System.Collections.Generic;

namespace FlexCharge.Payments.Services.OpenBanking;

public class AccountsBalance
{
    public List<AccountBalance> Accounts { get; init; }
}

public class AccountBalance
{
    public string AccountId { get; init; }

    public AccountType Type { get; init; }

    public long? Available { get; init; }
    public long? Current { get; init; }
    public long? Limit { get; init; }
    public string CurrencyCode { get; init; }

    public AccountSubType SubType { get; init; }
}

public enum AccountType
{
    Undefined,
    Depository,
    Credit,
    Loan,
    Investment,
    Other,
}

public enum AccountSubType
{
    Undefined,
    Checking,
    Savings,
    Other,
}