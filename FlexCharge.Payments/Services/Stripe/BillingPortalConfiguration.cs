namespace FlexCharge.Payments.Services.Stripe;

sealed class BillingPortalConfiguration
{
    private BillingPortalConfiguration(string ConfigurationName, string FlowType, string AfterCompletionType)
    {
        this.ConfigurationName = ConfigurationName;
        this.FlowType = FlowType;
        this.AfterCompletionType = AfterCompletionType;
    }

    public string ConfigurationName { get; init; }
    public string FlowType { get; init; }
    public string AfterCompletionType { get; init; }

    public bool? SubscriptionUpdateEnabled { get; set; }

    public bool? InvoiceHistoryEnabled { get; set; }

    public bool? CustomerUpdateEnabled { get; set; }
    public bool? PaymentMethodUpdateEnabled { get; set; }
    public bool? SubscriptionCancelEnabled { get; set; }

    public static readonly BillingPortalConfiguration SubscriptionsListWithUpdatePaymentMethodOption =
        new(nameof(SubscriptionsListWithUpdatePaymentMethodOption),
            null, //"payment_method_update",//"subscription_update", 
            "hosted_confirmation")
        {
            SubscriptionUpdateEnabled = false, InvoiceHistoryEnabled = false, CustomerUpdateEnabled = false,
            PaymentMethodUpdateEnabled = true
        };
}