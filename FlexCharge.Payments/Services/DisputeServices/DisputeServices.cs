using System;
using System.Collections.Generic;
using System.Threading;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common;
using FlexCharge.Common.Activities;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Exports;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Payments.Activities;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Enums;
using FlexCharge.Payments.Services.DisputeServices.Kount;
using FlexCharge.Payments.Services.DisputeServices.Models;
using FlexCharge.Payments.Services.KountDisputeService;
using FlexCharge.Payments.Services.PaymentServices;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using PaymentMethodType = FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels.PaymentMethodType;
using Transaction = FlexCharge.Payments.Entities.Transaction;

namespace FlexCharge.Payments.Services.DisputeServices;

public class DisputeServices : IDisputeServices
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IPublishEndpoint _publisher;
    private readonly IPaymentOrchestrator _paymentOrchestrator;
    private readonly IActivityService _activityService;
    private readonly IKountAlertService _kountAlertService;
    private readonly IMyRcvrAlertService _myRcvrAlertService;
    private readonly DisputeQueueService _disputeQueueService;
    private readonly IDisputeAlertsService _disputeAlertsService;
    private readonly IServiceProvider _serviceProvider;
    private IMapper _mapper;

    public DisputeServices(PostgreSQLDbContext dbContext, IPublishEndpoint publisher,
        IPaymentOrchestrator paymentOrchestrator, IActivityService activityService, IMapper mapper,
        IKountAlertService kountAlertService, IMyRcvrAlertService myRcvrAlertService,
        DisputeQueueService disputeQueueService, IServiceProvider serviceProvider)
    {
        _dbContext = dbContext;
        _publisher = publisher;
        _paymentOrchestrator = paymentOrchestrator;
        _activityService = activityService;
        _mapper = mapper;
        _kountAlertService = kountAlertService;
        _myRcvrAlertService = myRcvrAlertService;
        _disputeQueueService = disputeQueueService;
        _serviceProvider = serviceProvider;
    }

    public async Task<PreDisputeResponse> AddPreDisputeAsync(PreDisputeRequest payload,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<DisputeServices>()
            .Baggage("OrderId", payload.OrderId)
            .Baggage("TransactionId", payload.TransactionId)
            .Baggage("Arn", payload.Arn)
            .Baggage("DisputeType", payload.DisputeType)
            .Request(payload)
            .LogEnterAndExit();

        await _activityService.CreateActivityAsync(DisputeActivities.DisputeProcessing_PreDisputeCreate_Started,
            set => set
                .CorrelationId(payload.OrderId)
                .Data(payload)
                .Meta(meta => meta
                    .SetValue("Arn", payload.Arn)
                    .TransactionReference(payload.TransactionId)));

        var response = new PreDisputeResponse();
        try
        {
            var dispute =
                await CheckForExistingDispute(payload.TransactionId, payload.OrderId, payload.Arn, token, workspan);

            if (dispute != null)
            {
                if (dispute?.Stage == DisputeStage.REVERSED || payload.Stage == DisputeStage.REVERSED)
                {
                    dispute.Stage = DisputeStage.REVERSED;
                    dispute.DisputeType = payload.DisputeType;
                }
                else if (dispute?.Stage == DisputeStage.CHARGEBACK || payload.Stage == DisputeStage.CHARGEBACK)
                {
                    dispute.Stage = DisputeStage.CHARGEBACK;
                    dispute.DisputeType = payload.DisputeType;
                }
                else if (dispute?.Stage == DisputeStage.RDR || payload.Stage == DisputeStage.RDR)
                {
                    dispute.Stage = DisputeStage.RDR;
                    dispute.DisputeType = payload.Stage == DisputeStage.RDR ? payload.DisputeType : dispute.DisputeType;
                }
                else if (dispute?.Stage == DisputeStage.PRE_DISPUTE || payload.Stage == DisputeStage.PRE_DISPUTE)
                {
                    dispute.Stage = DisputeStage.PRE_DISPUTE;
                    dispute.DisputeType = payload.Stage == DisputeStage.PRE_DISPUTE
                        ? payload.DisputeType
                        : dispute.DisputeType;
                }
                else
                {
                    dispute.Stage = payload.Stage;
                    dispute.DisputeType = payload.DisputeType;
                }

                dispute.IsWebhook = payload.IsWebhook ? payload.IsWebhook : dispute.IsWebhook;
                dispute.IsFileImported = payload.IsFileImported ?? dispute.IsFileImported;
                dispute.IsManualInserted = payload.IsManualInserted ?? dispute.IsManualInserted;

                dispute.Meta = payload.Meta;

                _dbContext.Disputes.Update(dispute);
                await _dbContext.SaveChangesAsync(token);

                await _activityService.CreateActivityAsync(
                    DisputeActivities.DisputeProcessing_PreDisputeUpdate_Succeeded,
                    set => set
                        .TenantId(dispute.Mid)
                        .CorrelationId(dispute.OrderId)
                        .Data(dispute)
                        .Meta(meta => meta
                            .SetValue("DisputeId", dispute?.Id)
                            .SetValue("Arn", dispute?.Arn)
                            .TransactionReference(payload.TransactionId)));

                response.Success = true;
                response.DisputeId = dispute.Id;
                response.Dispute = dispute;

                await CreatePreDisputeActivity(payload, dispute.Id, workspan, token);

                if (dispute.Stage == DisputeStage.REVERSED)
                {
                    var merchant = await _dbContext.Merchants
                        .Where(x => x.Mid == dispute.Mid)
                        .SingleOrDefaultAsync(token);

                    await _publisher.Publish(new OrderReversedEvent
                    {
                        Mid = dispute.Mid,
                        Pid = merchant.Pid,
                        OrderId = dispute.OrderId,
                        Amount = dispute.Amount,
                        DisputeDateTime = dispute.RequestDate,
                        TransactionId = dispute.TransactionId,
                        Currency = dispute.Currency
                    }, token);
                }
            }
            else
            {
                var trx = await CheckForExistingTransaction(payload.TransactionId, token, workspan);

                //perform a refund if necessary
                ICreditPaymentResult creditResult = new CreditPaymentResult();

                if (payload.IssueRefund)
                {
                    workspan.Log.Information(
                        "Issuing REFUND pre-dispute for order {RequestOrderId} and type {RequestDisputeType}",
                        payload.OrderId, payload.DisputeType);

                    await _activityService.CreateActivityAsync(
                        DisputeActivities.DisputeProcessing_IssuedRefund_PreDispute_Started,
                        set => set
                            .TenantId(trx.Merchant.Mid)
                            .CorrelationId(trx.OrderId)
                            .Data(payload)
                            .Meta(meta => meta
                                .SetValue("TransactionId", trx.Id)));

                    creditResult = await _paymentOrchestrator.CreditAsync(new CreditPaymentRequest()
                    {
                        Mid = trx.Merchant.Mid,
                        OrderId = trx.OrderId,
                        Amount = payload.DisputeAmount > 0 ? payload.DisputeAmount : trx.Amount,
                        TransactionId = trx.Id,
                        PayerId = trx.PayerId,
                        SiteId = trx.SiteId,
                        Reason = payload.DisputeType,
                        IsPredispute = !payload.EarlyFraudWarning,
                        IsEarlyWarningRequestedRefund = payload.EarlyFraudWarning
                    }, token: token);

                    if (creditResult.Success)
                    {
                        workspan.Log.Information(
                            "Issued refund for pre-dispute transaction {TrxId} RESULT: {CreditResult}",
                            trx.Id, JsonConvert.SerializeObject(creditResult));

                        await _activityService.CreateActivityAsync(
                            DisputeActivities.DisputeProcessing_IssuedRefund_PreDispute_Succeeded,
                            set => set
                                .TenantId(trx.Merchant.Mid)
                                .CorrelationId(trx.OrderId)
                                .Data(payload)
                                .Meta(meta => meta
                                    .SetValue("TransactionId", trx.Id)));
                    }
                    else
                    {
                        workspan.Log.Information(
                            "Unable to issue refund for pre-dispute transaction {TrxId} RESULT: {CreditResult}", trx.Id,
                            JsonConvert.SerializeObject(creditResult));

                        throw new FlexChargeException("RefundError",
                            "Unable to issue refund for pre-dispute transaction");
                    }
                }
                else
                {
                    workspan.Log.Information(
                        "Issuing OFFLINE pre-dispute for order {RequestOrderId} and type {RequestDisputeType}",
                        payload.OrderId, payload.DisputeType);

                    if (payload.DisputeAmount > trx.Amount)
                    {
                        throw new FlexChargeException("DisputeAmount",
                            "Dispute amount cannot be greater than transaction amount");
                    }

                    var disputeTransaction = new Transaction
                    {
                        ParentId = trx.Id,
                        PaymentMethodId = trx.PaymentMethodId,
                        Amount = payload.DisputeAmount = payload.DisputeAmount > 0 ? payload.DisputeAmount : trx.Amount,
                        AuthorizationAmount = trx.AuthorizationAmount,
                        Currency = trx.Currency,
                        ResponseCode = payload.DisputeType,
                        ResponseMessage = payload.DisputeType?.ToString(),
                        DynamicDescriptor = trx.DynamicDescriptor,
                        Type = nameof(TransactionType.PreDispute),
                        PaymentType = nameof(PaymentMethodType.CreditCard),
                        Status = TransactionStatus.Completed,
                        //ProviderId = trx.ProviderId,
                        ProviderName = payload.DisputeManagementSystem,
                        ProviderTransactionToken = payload.CaseNumber,
                        ProcessorName = trx.ProcessorName,
                        Arn = payload.Arn,
                        PayerId = trx.PayerId,
                        SiteId = trx.SiteId,
                        Merchant = trx.Merchant,
                        OrderId = trx.OrderId,
                        //IsRecurring = false,
                        Meta = System.Text.Json.JsonSerializer.SerializeToDocument(payload),
                        SchemeTransactionId = trx.SchemeTransactionId,
                    };

                    _dbContext.Transactions.Add(disputeTransaction);
                }

                var dis = new Dispute
                {
                    Note = payload.Note,
                    Mid = trx.Merchant.Mid,
                    OrderId = trx.OrderId,
                    TransactionId = trx.Id,
                    Descriptor = payload.Descriptor,
                    DisputeManagementSystem = payload.DisputeManagementSystem,
                    RefundTransactionId = creditResult.Success ? creditResult.InternalTransactionId : Guid.Empty,
                    CaseNumber = payload.CaseNumber,
                    Stage = payload.Stage ?? DisputeStage.PRE_DISPUTE,
                    Amount = payload.Amount = payload.Amount > 0 ? payload.Amount : trx.Amount,
                    DisputeAmount = payload.DisputeAmount =
                        payload.DisputeAmount > 0 ? payload.DisputeAmount : trx.Amount,
                    AuthorizationId = trx.AuthorizationId,
                    Currency = trx.Currency,
                    // we decided to use transaction date from dispute report (dispute queue item)
                    TransactionDate = payload.TransactionDate ?? trx.CreatedOn,
                    RequestDate = payload.RequestDate,
                    DueDate = payload.DueDate,
                    //ExpirationDate = null,
                    Mcc = null,
                    Arn = payload.Arn,
                    CardAcceptorId = payload.CardAcceptorId,
                    ProcessorName = trx.ProcessorName,
                    DisputeType = payload.DisputeType?.ToString(),
                    CardBrand = payload.CardBrand?.ToUpper(),
                    CardType = payload?.CardType?.ToUpper(),
                    Bin = payload.Bin,
                    Last4 = payload.Last4,
                    ProviderName = payload.ProviderName,
                    EarlyFraudWarning = payload.EarlyFraudWarning,
                    IsWebhook = payload.IsWebhook,
                    IsFileImported = payload.IsFileImported,
                    IsManualInserted = payload.IsManualInserted,
                    Meta = payload.Meta,
                    HasMatch = payload.HasMatch,
                    IsReviewRequired = payload.IsReviewRequired,
                    Issuer = payload.Issuer,
                };

                var disputeRecord = _dbContext.Disputes.Add(dis);

                var res = await _dbContext.SaveChangesAsync(token);
                if (res > 0)
                {
                    if (DisputeStage.IsEarlyWarningStage(payload.Stage))
                    {
                        await _publisher.Publish(new PaymentDisputeAlertCreatedEvent()
                        {
                            Mid = disputeRecord.Entity.Mid,
                            TransactionId = disputeRecord.Entity.TransactionId,
                            OrderId = disputeRecord.Entity.OrderId,
                            DisputeId = disputeRecord.Entity.Id,
                            Description = disputeRecord.Entity.DisputeType,
                            Amount = disputeRecord.Entity.Amount,
                            DiscountAmount = trx.DiscountAmount,
                            Date = disputeRecord.Entity.CreatedOn,
                            Type = DisputeStage.EARLY_WARNING.ToLower(),
                            Bin = trx.PaymentMethod?.Bin,
                            Last4 = trx.PaymentMethod?.Last4,
                        }, token);

                        workspan
                            .Baggage("dispute", JsonConvert.SerializeObject(disputeRecord.Entity))
                            .Log.Information("Dispute Early warning created: disputeId - {DisputeId}",
                                disputeRecord.Entity.Id);


                        await _activityService.CreateActivityAsync(
                            DisputeActivities.DisputeProcessing_DisputeAlertCreated,
                            set => set
                                .TenantId(disputeRecord.Entity.Mid)
                                .CorrelationId(disputeRecord.Entity.OrderId)
                                .Data(disputeRecord.Entity)
                                .Meta(meta => meta
                                    .SetValue("DisputeId", disputeRecord.Entity.Id)
                                    .SetValue("Arn", disputeRecord.Entity.Arn)
                                    .TransactionReference(payload.TransactionId)
                                    .SetValue("Amount", disputeRecord.Entity.Amount)));
                    }
                    else
                    {
                        await _publisher.Publish(new PaymentDisputeCreatedEvent()
                        {
                            Mid = disputeRecord.Entity.Mid,
                            TransactionId = disputeRecord.Entity.TransactionId,
                            OrderId = disputeRecord.Entity.OrderId,
                            DisputeId = disputeRecord.Entity.Id,
                            Description = disputeRecord.Entity.DisputeType,
                            Amount = disputeRecord.Entity.Amount,
                            DiscountAmount = trx.DiscountAmount,
                            Date = disputeRecord.Entity.RequestDate,
                            Type = TransactionType.Chargeback.ToString().ToLower(),
                            Bin = trx.PaymentMethod?.Bin,
                            Last4 = trx.PaymentMethod?.Last4,
                        }, token);

                        workspan
                            .Baggage("dispute", JsonConvert.SerializeObject(disputeRecord.Entity))
                            .Log.Information("Dispute created: disputeId - {DisputeId}",
                                disputeRecord.Entity.Id);

                        await _activityService.CreateActivityAsync(
                            DisputeActivities.DisputeProcessing_DisputeCreate_Succeeded,
                            set => set
                                .TenantId(disputeRecord.Entity.Mid)
                                .CorrelationId(disputeRecord.Entity.OrderId)
                                .Data(disputeRecord.Entity)
                                .Meta(meta => meta
                                    .SetValue("DisputeId", disputeRecord.Entity.Id)
                                    .SetValue("Arn", disputeRecord.Entity.Arn)
                                    .TransactionReference(payload.TransactionId)
                                    .SetValue("Amount", disputeRecord.Entity.Amount)));
                    }

                    //Will be invoked only in case refund was not requested,
                    // in case of refund, the event will be published from CreditPaymentService on _paymentOrchestrator.CreditAsync
                    // if (!payload.IssueRefund && !payload.EarlyFraudWarning)
                    // {
                    //     await _publisher.Publish(new PaymentPreDisputeCreditedEvent()
                    //     {
                    //         Mid = disputeRecord.Entity.Mid,
                    //         TransactionId = disputeRecord.Entity.TransactionId,
                    //         OrderId = disputeRecord.Entity.OrderId,
                    //         DisputeId = disputeRecord.Entity.Id,
                    //         Description = disputeRecord.Entity.DisputeType,
                    //         Amount = disputeRecord.Entity.Amount,
                    //         DiscountAmount = trx.DiscountAmount,
                    //         RequestDate = disputeRecord.Entity.RequestDate,
                    //         PaymentDate = disputeRecord.Entity.CreatedOn,
                    //         Type = TransactionType.Chargeback.ToString().ToLower(),
                    //         EarlyFraudWarning = payload.EarlyFraudWarning,
                    //         CreatedDateTime = payload.CreateDateTime,
                    //         Reason = payload.Note,
                    //         Currency = trx.Currency,
                    //     }, token);
                    //
                    //     await _activityService.CreateActivityAsync(DisputesActivities.Payments_PreDisputeCreated,
                    //         set => set
                    //             .TenantId(disputeRecord.Entity.Mid)
                    //             .CorrelationId(disputeRecord.Entity.OrderId)
                    //             .Data(disputeRecord.Entity)
                    //             .Meta(meta => meta
                    //                 .SetValue("Amount", disputeRecord.Entity.Amount)));
                    // }

                    response.Success = true;
                    response.DisputeId = disputeRecord.Entity.Id;
                    response.Dispute = disputeRecord.Entity;

                    await CreatePreDisputeActivity(payload, disputeRecord.Entity.Id, workspan, token);
                }
                else
                {
                    throw new FlexChargeException(
                        "CreateRecordError",
                        $"Unable to add dispute for order {payload.OrderId} and type {payload.DisputeType}");
                }
            }
        }
        catch (FlexChargeException e)
        {
            workspan.RecordException(e);
            response.Success = false;
            response.Message = e.Message;
            response.Code = e.Code;

            await _activityService.CreateActivityAsync(
                DisputeErrorActivities.DisputeProcessing_PreDisputeCreate_Error,
                set => set
                    .CorrelationId(payload.OrderId)
                    .Data(e)
                    .Meta(meta => meta
                        .Error(e.Message)
                        .SetValue("OrderId", payload.OrderId)
                        .TransactionReference(payload.TransactionId)));
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            response.Success = false;
            response.Message = e.Message;

            await _activityService.CreateActivityAsync(
                DisputeErrorActivities.DisputeProcessing_PreDisputeCreate_Error,
                set => set
                    .CorrelationId(payload.OrderId)
                    .Data(e)
                    .Meta(meta => meta
                        .Error(e.Message)
                        .TransactionReference(payload.TransactionId)));
        }

        return response;
    }

    private async Task CreatePreDisputeActivity(PreDisputeRequest payload, Guid disputeId,
        Workspan workspan, CancellationToken token)
    {
        try
        {
            var trx = await CheckForExistingTransaction(payload.TransactionId, token, workspan);

            var disputeActivity = new DisputeActivity
            {
                DisputeId = disputeId,
                Mid = trx.Merchant.Mid,
                OrderId = trx.OrderId,
                TransactionId = trx.Id,
                Descriptor = payload?.Descriptor,
                DisputeManagementSystem = payload.DisputeManagementSystem,
                RefundTransactionId = Guid.Empty,
                CaseNumber = payload.CaseNumber,
                Stage = payload.Stage ?? DisputeStage.PRE_DISPUTE,
                DisputeAmount = payload.DisputeAmount = payload.DisputeAmount > 0 ? payload.DisputeAmount : trx.Amount,
                Amount = payload.Amount = payload.Amount > 0 ? payload.Amount : trx.Amount,
                AuthorizationId = trx.AuthorizationId,
                Currency = trx.Currency,
                // we decided to use transaction date from dispute report (dispute queue item)
                TransactionDate = payload.TransactionDate ?? trx.CreatedOn,
                RequestDate = payload.RequestDate,
                DueDate = payload.DueDate,
                Mcc = null,
                Arn = payload.Arn,
                ProcessorName = trx.ProcessorName,
                DisputeType = payload.DisputeType,
                CardBrand = payload.CardBrand,
                CardType = payload.CardType,
                Bin = payload.Bin,
                Last4 = payload.Last4,
                ProviderName = payload.ProviderName,
                EarlyFraudWarning = payload.EarlyFraudWarning,
                IsWebhook = payload.IsWebhook,
                Meta = payload.Meta,
                IsFileImported = payload.IsFileImported.Value,
                IsManualInserted = payload.IsManualInserted.Value
            };

            _dbContext.DisputeActivities.Add(disputeActivity);

            await _dbContext.SaveChangesAsync(token);
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
        }
    }

    public async Task UpdateDisputeAsync(Dispute dispute, DisputeUpdateRequestDTO payload,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<DisputeServices>()
            .Baggage("DisputeId", dispute.Id)
            .Baggage("OrderId", dispute.OrderId)
            .Baggage("TransactionId", dispute.TransactionId);

        try
        {
            if (payload.RepliedToEthoca != null)
            {
                dispute.RepliedToEthoca = payload.RepliedToEthoca;
            }

            if (String.IsNullOrEmpty(dispute.RequestId))
            {
                dispute.RequestId = payload.RequestId;
            }

            if (payload.DisputeQueueItemId != null && payload.DisputeQueueItemId != Guid.Empty)
            {
                dispute.DisputeQueueItemId = payload.DisputeQueueItemId;
            }

            _dbContext.Disputes.Update(dispute);
            await _dbContext.SaveChangesAsync(token);
        }
        catch (Exception e)
        {
            workspan.Log.Fatal(e, "Error updating dispute {DisputeId}", dispute.Id);
            throw;
        }
    }


    private async Task<Dispute> CheckForExistingDispute(Guid transactionId, Guid orderId, string Arn,
        CancellationToken token,
        Workspan workspan)
    {
        var dispute = await _dbContext.Disputes
            .Where(x =>
                transactionId == Guid.Empty && orderId == Guid.Empty
                    ? x.Arn == Arn
                    : x.TransactionId == transactionId || x.OrderId == orderId)
            .SingleOrDefaultAsync(cancellationToken: token);

        if (dispute != null)
        {
            workspan.Log.Information(
                "Dispute already exists for transaction {RequestTransactionId} and orderId {RequestOrderId}",
                transactionId, orderId);

            await _activityService.CreateActivityAsync(DisputeErrorActivities.DisputeProcessing_DisputeExists_Error,
                set => set
                    .CorrelationId(orderId)
                    .Meta(meta => meta
                        .SetValue("TransactionId", transactionId)));

            // throw new FlexChargeException(nameof(transactionId),
            //     "This type of dispute already exists for this transaction");
            return dispute;
        }

        return null;
    }

    private async Task<Transaction> CheckForExistingTransaction(Guid? transactionId, CancellationToken token,
        Workspan workspan)
    {
        var trx = await _dbContext
            .Transactions
            .Include(x => x.Merchant)
            .Where(x => x.Id == transactionId)
            .Include(x => x.Merchant)
            .SingleOrDefaultAsync(token);

        if (trx is null)
        {
            workspan.Log.Error("Transaction {RequestTransactionId} not found", transactionId);
            throw new FlexChargeException(nameof(transactionId), "Transaction not found");
        }

        if (trx.Merchant is null)
        {
            workspan.Log.Error("Merchant not found in the include function for transaction {RequestTransactionId}",
                transactionId);
            throw new FlexChargeException("Merchant",
                $"Merchant not found in the include function for transaction {transactionId}");
        }

        //log the transaction
        workspan.Log.Information("Found transaction {TransactionId} for dispute", trx.Id);

        return trx;
    }

    public async Task<CreateDisputeResponse> CreateDisputeAsync(CreateDisputeRequest payload,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<DisputeServices>();

        var response = new CreateDisputeResponse();

        var mid = Guid.Empty;
        try
        {
            var dispute =
                await CheckForExistingDispute(payload.TransactionId, payload.OrderId, payload.Arn, token, workspan);

            if (dispute != null)
            {
                //look for transaction with authorization id
                var trx = await CheckForExistingTransaction(payload.TransactionId, token, workspan);

                workspan.Log.Information("Issuing new dispute for order {RequestOrderId} and type {RequestDisputeType}",
                    payload.OrderId, payload.DisputeType?.ToString());

                // in case we need this in the catch block
                mid = trx.Merchant.Mid;

                var disputeTransaction = new Transaction
                {
                    ParentId = trx.Id,
                    PaymentMethodId = trx.PaymentMethodId,
                    Amount = payload.DisputeAmount = payload.DisputeAmount > 0 ? payload.DisputeAmount : trx.Amount,
                    AuthorizationAmount = trx.AuthorizationAmount,
                    Currency = trx.Currency,
                    ResponseCode = payload.DisputeType,
                    ResponseMessage = payload.DisputeType?.ToString(),
                    DynamicDescriptor = trx.DynamicDescriptor,
                    Type = nameof(TransactionType.PreDispute),
                    PaymentType = nameof(PaymentMethodType.CreditCard),
                    Status = TransactionStatus.Completed,
                    ProviderName = payload.DisputeManagementSystem,
                    ProviderTransactionToken = payload.CaseNumber,
                    ProcessorName = trx.ProcessorName,
                    Arn = payload.Arn,
                    PayerId = trx.PayerId,
                    SiteId = trx.SiteId,
                    Merchant = trx.Merchant,
                    OrderId = trx.OrderId,
                    //IsRecurring = false,
                    Meta = System.Text.Json.JsonSerializer.SerializeToDocument(payload),
                    SchemeTransactionId = trx.SchemeTransactionId
                };
                _dbContext.Transactions.Add(disputeTransaction);

                var disputeRecord = _dbContext.Disputes.Add(new Dispute
                {
                    Mid = trx.Merchant.Mid,
                    OrderId = trx.OrderId,
                    TransactionId = trx.Id,
                    CaseNumber = payload.CaseNumber,
                    Descriptor = payload.Descriptor,
                    DisputeManagementSystem = payload.DisputeManagementSystem,
                    Stage = DisputeStage.RDR,
                    Amount = payload.DisputeAmount = payload.DisputeAmount > 0 ? payload.DisputeAmount : trx.Amount,
                    AuthorizationId = trx.AuthorizationId,
                    Currency = trx.Currency,
                    TransactionDate = trx.CreatedOn,
                    RequestDate = payload.RequestDate.ToUniversalTime(),
                    DueDate = null,
                    //ExpirationDate = null,
                    Mcc = null,
                    Arn = payload.Arn,
                    CardAcceptorId = payload.CardAcceptorId,
                    ProviderName = trx.ProviderName,
                    ProcessorName = trx.ProcessorName,
                    CardBrand = payload.CardBrand?.ToUpper(),
                    CardType = payload.CardType?.ToUpper(),
                    Bin = payload.Bin,
                    Last4 = payload.Last4,
                    IsWebhook = payload.IsWebhook,
                    Meta = payload.Meta,
                });

                await CreateDisputeActivity(payload, disputeRecord.Entity.Id, workspan, token);

                var dbInsertResult = await _dbContext.SaveChangesAsync(token);

                if (dbInsertResult > 0)
                {
                    await _publisher.Publish(new PaymentDisputeCreatedEvent
                    {
                        Mid = disputeRecord.Entity.Mid,
                        TransactionId = disputeRecord.Entity.TransactionId,
                        OrderId = disputeRecord.Entity.OrderId,
                        DisputeId = disputeRecord.Entity.Id,
                        Description = disputeRecord.Entity.DisputeType,
                        Amount = disputeRecord.Entity.Amount,
                        DiscountAmount = trx.DiscountAmount,
                        Date = disputeRecord.Entity.RequestDate,
                        Type = TransactionType.Chargeback.ToString().ToLower(),
                        Bin = trx.PaymentMethod?.Bin,
                        Last4 = trx.PaymentMethod?.Last4,
                    }, token);

                    await _activityService.CreateActivityAsync(
                        DisputeActivities.DisputeProcessing_DisputeCreate_Succeeded,
                        set => set
                            .TenantId(disputeRecord.Entity.Mid)
                            .CorrelationId(disputeRecord.Entity.OrderId)
                            .Data(disputeRecord.Entity)
                            .Meta(meta => meta
                                .SetValue("DisputeId", disputeRecord.Entity.Id)
                                .SetValue("Arn", disputeRecord.Entity.Arn)
                                .TransactionReference(payload.TransactionId)
                                .SetValue("Amount", disputeRecord.Entity.Amount)));

                    response.Success = true;
                }
                else
                {
                    workspan.Log.Fatal(
                        "Unable to create dispute for order {RequestOrderId} and type {RequestDisputeType}",
                        payload.OrderId, payload.DisputeType);

                    throw new FlexChargeException("CreateRecordError", "Unable to create dispute.");
                }
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e);

            await _publisher.Publish(new PaymentDisputeCreateFailedEvent
            {
                Mid = mid,
                OrderId = payload.OrderId,
                Error = e.Message
            }, token);

            response.Success = false;
            response.Message = e.Message;

            await _activityService.CreateActivityAsync(DisputeErrorActivities.DisputeProcessing_DisputeCreate_Error,
                set => set
                    .CorrelationId(payload.OrderId)
                    .Data(e)
                    .Meta(meta => meta
                        .Error(e.Message)
                        .SetValue("OrderId", payload.OrderId)
                        .TransactionReference(payload.TransactionId)));
        }

        return response;
    }

    private async Task CreateDisputeActivity(CreateDisputeRequest payload, Guid disputeId,
        Workspan workspan, CancellationToken token)
    {
        try
        {
            var trx = await CheckForExistingTransaction(payload.TransactionId, token, workspan);
            _dbContext.DisputeActivities.Add(new DisputeActivity
            {
                DisputeId = disputeId,
                Mid = trx.Merchant.Mid,
                OrderId = trx.OrderId,
                TransactionId = trx.Id,
                CaseNumber = payload.CaseNumber,
                Descriptor = payload.Descriptor,
                DisputeManagementSystem = payload.DisputeManagementSystem,
                Stage = "DISPUTE",
                Amount = payload.DisputeAmount = payload.DisputeAmount > 0 ? payload.DisputeAmount : trx.Amount,
                AuthorizationId = trx.AuthorizationId,
                Currency = trx.Currency,
                TransactionDate = trx.CreatedOn,
                RequestDate = payload.RequestDate.ToUniversalTime(),
                DueDate = null,
                //ExpirationDate = null,
                Mcc = null,
                Arn = payload.Arn,
                ProviderName = trx.ProviderName,
                ProcessorName = trx.ProcessorName,
                DisputeType = payload.DisputeType?.ToString(),
                CardBrand = payload.CardBrand?.ToUpper(),
                CardType = payload.CardType?.ToUpper(),
                Bin = payload.Bin,
                Last4 = payload.Last4,
                IsWebhook = payload.IsWebhook,
                Meta = payload.Meta,
                IsFileImported = payload.IsFileImported.Value,
            });

            await _dbContext.SaveChangesAsync(token);
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
        }
    }

    public async Task<DisputeResponseDTO> AdminGetDisputes(int pageSize,
        int pageNumber,
        Guid? mid,
        string? query,
        DateTime? from,
        DateTime? to,
        DateTime? requestFrom,
        DateTime? requestTo,
        DateTime? createdFrom,
        DateTime? createdTo,
        string? timezone,
        string? sort,
        string? sortField,
        List<string>? providerName,
        List<string>? cardType,
        List<string>? stage,
        bool? isWebhook,
        bool? isMatch,
        bool? isArchived = false,
        bool? isFileImported = false,
        bool? isManualInserted = false)
    {
        return await GetDisputes(pageSize, pageNumber, null, mid, query, from, to, requestFrom, requestTo, createdFrom,
            createdTo, timezone, sort, sortField, providerName, cardType, stage, isWebhook, isMatch, isArchived,
            isFileImported, isManualInserted);
    }

    public async Task<DisputeResponseDTO> PartnerGetDisputes(int pageSize,
        int pageNumber,
        Guid? pid,
        Guid? mid,
        string? query,
        DateTime? from,
        DateTime? to,
        DateTime? requestFrom,
        DateTime? requestTo,
        DateTime? createdFrom,
        DateTime? createdTo,
        string? timezone,
        string? sort,
        string? sortField,
        List<string>? providerName,
        List<string>? cardType,
        List<string>? stage,
        bool? isWebhook,
        bool? isMatch,
        bool? isArchived = false,
        bool? isFileImported = false,
        bool? isManualInserted = false)
    {
        return await GetDisputes(pageSize, pageNumber, pid, mid, query, from, to, requestFrom, requestTo, createdFrom,
            createdTo, timezone, sort, sortField, providerName, cardType, stage, isWebhook, isMatch, isArchived,
            isFileImported, isManualInserted);
    }

    private async Task<DisputeResponseDTO> GetDisputes(int pageSize,
        int pageNumber,
        Guid? pid,
        Guid? mid,
        string query,
        DateTime? from,
        DateTime? to,
        DateTime? requestFrom,
        DateTime? requestTo,
        DateTime? createdFrom,
        DateTime? createdTo,
        string? timezone,
        string sort,
        string sortField,
        List<string>? providerName,
        List<string>? cardType,
        List<string> stage,
        bool? isWebhook,
        bool? isMatch,
        bool? isArchived = false,
        bool? isFileImported = false,
        bool? isManualInserted = false)
    {
        using var workspan = Workspan.Start<DisputeServices>()
            .LogEnterAndExit();

        try
        {
            var response = new DisputeResponseDTO();
            var disputes = _dbContext.Disputes
                .Where(x => !x.IsDeleted)
                .Include(x => x.DisputeActivities.OrderByDescending(x => x.CreatedOn))
                .IgnoreQueryFilters()
                .AsQueryable();

            disputes = SortAndFilter(disputes, from, to, requestFrom, requestTo, createdFrom, createdTo, timezone, sort,
                sortField,
                providerName, cardType, stage, isWebhook, isMatch, isArchived, isFileImported, isManualInserted);

            if (!string.IsNullOrEmpty(query))
            {
                disputes = disputes.Where(x =>
                    x.Mid.ToString().Contains(query) ||
                    x.OrderId.ToString().Contains(query) ||
                    x.TransactionId.ToString().Contains(query) ||
                    x.Arn.Contains(query) ||
                    x.Descriptor != null && x.Descriptor.ToLower().Contains(query.ToLower()));
            }

            if (mid.HasValue)
            {
                disputes = disputes.Where(x => x.Mid == mid);
            }

            if (pid.HasValue)
            {
                disputes = disputes
                    .Join(_dbContext.Merchants,
                        dispute => dispute.Mid,
                        merchant => merchant.Mid,
                        (dispute, merchant) => new
                        {
                            Dispute = dispute,
                            Pid = merchant.Pid
                        })
                    .Where(joined => joined.Pid == pid)
                    .Select(joined => joined.Dispute);
            }

            response.Disputes =
                _mapper.Map<IPagedList<Dispute>, PagedDTO<DisputeResponseDTO.DisputeItemQueryDTO>>(
                    await disputes.ToPagedListAsync(pageNumber, pageSize));
            return response;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);

            await _activityService.CreateActivityAsync(DisputeErrorActivities.DisputeProcessing_DisputeGet_Error,
                set => set
                    .Data(e)
                    .Meta(meta => meta
                        .Error(e.Message)));
            throw;
        }
    }

    public async Task<DisputeResponseDTO.DisputeItemQueryDTO> GetDisputeByIdAsync(Guid id)
    {
        using var workspan = Workspan.Start<DisputeServices>()
            .LogEnterAndExit();

        try
        {
            var dispute = await _dbContext.Disputes
                .FirstOrDefaultAsync(x => x.Id == id);

            var response = _mapper.Map<Dispute, DisputeResponseDTO.DisputeItemQueryDTO>(dispute);
            return response;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);

            await _activityService.CreateActivityAsync(DisputeErrorActivities.DisputeProcessing_DisputeGet_Error,
                set => set
                    .Data(e)
                    .Meta(meta => meta
                        .Error(e.Message)));
            throw;
        }
    }

    // Export disputes to CSV
    public async Task<string> GenerateAsync(
        Guid? pid,
        int pageSize,
        int pageNumber,
        Guid? mid,
        string query,
        DateTime? from,
        DateTime? to,
        DateTime? requestFrom,
        DateTime? requestTo,
        DateTime? createdFrom,
        DateTime? createdTo,
        string? timezone,
        string sort,
        string sortField,
        List<string>? providerName,
        List<string>? cardBrand,
        List<string>? stage,
        bool? isWebhook,
        bool? isMatch,
        bool? isArchived = false,
        bool? isFileImported = false,
        bool? isManualInserted = false)
    {
        using var workspan = Workspan.Start<DisputeServices>()
            .LogEnterAndExit();

        try
        {
            var disputes = _dbContext.Disputes
                .Where(x => !x.IsDeleted && (!mid.HasValue ||
                                             mid.HasValue && x.Mid == mid) &&
                            (!isMatch.HasValue ||
                             isMatch.HasValue && isMatch.Value == false && x.OrderId == Guid.Empty ||
                             isMatch.HasValue && isMatch.Value == true && x.OrderId != Guid.Empty))
                .Join(_dbContext.Merchants, d => d.Mid, m => m.Mid, (d, m) =>
                    new DisputeExportDTO
                    {
                        AuthorizationDate = d.TransactionDate,
                        RequestDate = d.RequestDate,
                        OrderId = d.OrderId,
                        MerchantName = m.LegalEntityName,
                        DisputeType = d.DisputeType,
                        ProviderName = d.ProviderName,
                        IsRefunded = d.RefundTransactionId != Guid.Empty,
                        Descriptor = d.Descriptor,
                        Currency = d.Currency,
                        Amount = d.Amount,
                        CardBrand = d.CardBrand,
                        Bin = d.Bin,
                        Last4 = d.Last4,
                        Stage = d.Stage,
                        Arn = d.Arn,
                        CardAcceptorId = d.CardAcceptorId,
                        IsWebhook = d.IsWebhook,
                        CreatedOn = d.CreatedOn,
                        DisputeManagementSystem = d.DisputeManagementSystem,
                        IsArchived = d.IsArchived,
                        IsFileImported = d.IsFileImported,
                        IsManualInserted = d.IsManualInserted,
                        TransactionDate = d.TransactionDate,
                        Mid = d.Mid,
                    });

            if (pid.HasValue)
            {
                disputes = disputes
                    .Join(_dbContext.Merchants,
                        dispute => dispute.Mid,
                        merchant => merchant.Mid,
                        (dispute, merchant) => new
                        {
                            Dispute = dispute,
                            Pid = merchant.Pid
                        })
                    .Where(joined => joined.Pid == pid)
                    .Select(joined => joined.Dispute);
            }

            #region Filter by

            if (from.HasValue && to.HasValue && from.Value > DateTime.MinValue && to.Value > DateTime.MinValue)
            {
                var fromUTC = from.Value.Date.ToUniversalTime();
                var toUTC = to.Value.Date.ToUniversalTime();

                if (timezone != null)
                {
                    var isTimezoneValid = TimeZoneInfo.TryConvertIanaIdToWindowsId(timezone, out var timezoneName);

                    if (!isTimezoneValid)
                        throw new Exception("Invalid timezone");

                    fromUTC = TimeZoneInfo.ConvertTimeToUtc(from.Value,
                        TimeZoneInfo.FindSystemTimeZoneById(timezoneName));
                    toUTC = TimeZoneInfo.ConvertTimeToUtc(to.Value, TimeZoneInfo.FindSystemTimeZoneById(timezoneName));
                }

                disputes = disputes.Where(x =>
                    x.TransactionDate.Date >= fromUTC.Date &&
                    x.TransactionDate.Date <= toUTC.Date);
            }

            if (isArchived.HasValue && isArchived == true)
            {
                disputes = disputes.Where(x => x.IsArchived != null);
            }

            if (!isArchived.HasValue || isArchived.HasValue && isArchived == false)
            {
                disputes = disputes.Where(x => x.IsArchived == null);
            }

            if (requestFrom.HasValue && requestTo.HasValue && requestFrom.Value > DateTime.MinValue &&
                requestTo.Value > DateTime.MinValue)
            {
                var requestFromUTC = requestFrom.Value.Date.ToUniversalTime();
                var requestToUTC = requestTo.Value.Date.ToUniversalTime();
                if (timezone != null)
                {
                    var isTimezoneValid = TimeZoneInfo.TryConvertIanaIdToWindowsId(timezone, out var timezoneName);

                    if (!isTimezoneValid)
                        throw new Exception("Invalid timezone");

                    requestFromUTC = TimeZoneInfo.ConvertTimeToUtc(requestFrom.Value,
                        TimeZoneInfo.FindSystemTimeZoneById(timezoneName));
                    requestToUTC = TimeZoneInfo.ConvertTimeToUtc(requestTo.Value,
                        TimeZoneInfo.FindSystemTimeZoneById(timezoneName));
                }

                disputes = disputes.Where(x =>
                    x.RequestDate.Date >= requestFromUTC.Date &&
                    x.RequestDate.Date <= requestToUTC.Date);
            }

            if (createdFrom.HasValue && createdTo.HasValue && createdFrom.Value > DateTime.MinValue &&
                createdTo.Value > DateTime.MinValue)
            {
                var createdFromUTC = createdFrom.Value.Date.ToUniversalTime();
                var createdToUTC = createdTo.Value.Date.ToUniversalTime();
                if (timezone != null)
                {
                    var isTimezoneValid = TimeZoneInfo.TryConvertIanaIdToWindowsId(timezone, out var timezoneName);

                    if (!isTimezoneValid)
                        throw new Exception("Invalid timezone");

                    createdFromUTC = TimeZoneInfo.ConvertTimeToUtc(createdFrom.Value,
                        TimeZoneInfo.FindSystemTimeZoneById(timezoneName));
                    createdToUTC = TimeZoneInfo.ConvertTimeToUtc(createdTo.Value,
                        TimeZoneInfo.FindSystemTimeZoneById(timezoneName));
                }

                disputes = disputes.Where(x =>
                    x.CreatedOn.Date >= createdFromUTC.Date &&
                    x.CreatedOn.Date <= createdToUTC.Date);
            }

            if (stage.Count > 0)
            {
                disputes = disputes.Where(x => stage.Contains(x.Stage));
            }

            if (isWebhook.HasValue)
            {
                disputes = disputes.Where(x => x.IsWebhook == isWebhook);
            }

            if (isMatch.HasValue)
            {
                disputes = disputes.Where(x =>
                    isMatch.Value == true && x.OrderId != Guid.Empty ||
                    isMatch.Value == false && x.OrderId == Guid.Empty);
            }

            if (isFileImported.HasValue)
            {
                disputes = disputes.Where(x => x.IsFileImported == true);
            }

            if (isManualInserted.HasValue)
            {
                disputes = disputes.Where(x => x.IsManualInserted == true);
            }

            #endregion

            #region Sort by column name

            if (sortField == null || sortField == "transactionDate")
            {
                disputes = sort == "asc"
                    ? disputes.OrderBy(x => x.TransactionDate)
                    : disputes.OrderByDescending(x => x.TransactionDate);
            }

            if (sortField == "requestDate")
            {
                disputes = sort == "asc"
                    ? disputes.OrderBy(x => x.RequestDate)
                    : disputes.OrderByDescending(x => x.RequestDate);
            }

            #endregion

            if (providerName.Count > 0)
            {
                disputes = disputes.Where(x => providerName.Contains(x.ProviderName));
            }

            if (cardBrand.Count > 0)
            {
                disputes = disputes.Where(x => cardBrand.Contains(x.CardBrand));
            }

            var csv = CSVExport.GenerateCSVFromRows(await disputes.ToListAsync());

            return csv;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task<List<DisputeActivityResponseDTO>> GetActivities(Guid Id)
    {
        using var workspan = Workspan.Start<DisputeServices>()
            .LogEnterAndExit();

        try
        {
            var activities = await _dbContext.DisputeActivities
                .Where(x => x.TransactionId == Id)
                .OrderByDescending(x => x.CreatedOn)
                .Select(x => new DisputeActivityResponseDTO
                {
                    Id = x.Id,
                    TransactionId = x.TransactionId.ToString(),
                    Mid = x.Mid,
                    OrderId = x.OrderId.ToString(),
                    Stage = x.Stage,
                    CreatedOn = x.CreatedOn,
                    Descriptor = x.Descriptor,
                    Amount = x.Amount,
                    Currency = x.Currency,
                    CardBrand = x.CardBrand,
                    Bin = x.Bin,
                    Last4 = x.Last4,
                    Arn = x.Arn,
                    IsWebhook = x.IsWebhook,
                    ProviderName = x.ProviderName,
                    DisputeType = x.DisputeType,
                    RequestDate = x.RequestDate,
                    TransactionDate = x.TransactionDate,
                    Message = x.Message,
                    IsRefunded = x.RefundTransactionId != Guid.Empty,
                    IsFileImported = x.IsFileImported,
                })
                .ToListAsync();

            return activities;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task<PreDisputeResponse> MatchDisputeAsync(DisputeMatchRequestDTO payload,
        CancellationToken token = default, bool shouldAlert = true)
    {
        using var workspan = Workspan.Start<DisputeServices>()
            .Baggage("DisputeQueueItemId", payload.DisputeQueueItemId)
            .Baggage("TransactionId", payload.TransactionId)
            .LogEnterAndExit();

        await _activityService.CreateActivityAsync(DisputeActivities.DisputeProcessing_DisputeMatch_Started,
            set => set
                .CorrelationId(payload.TransactionId)
                .Data(payload)
                .Meta(meta => meta
                    .SetValue("DisputeQueueItemId", payload.DisputeQueueItemId)
                    .TransactionReference(payload.TransactionId)));

        //start the DB transaction
        await using var dbTransaction = await _dbContext.Database.BeginTransactionAsync(token);

        var response = new PreDisputeResponse();
        Transaction transaction = null;
        try
        {
            var queueItem = await _disputeQueueService.GetItemByIdAsync(payload.DisputeQueueItemId, token);
            var queue = await _disputeQueueService.GetByIdAsync(queueItem.DisputeQueueId, token);
            transaction = await CheckForExistingTransaction(payload.TransactionId, token, workspan);

            workspan
                .Baggage("Mid", transaction?.Merchant?.Mid)
                .Baggage("OrderId", transaction?.OrderId);


            await _activityService.CreateActivityAsync(
                DisputeActivities.DisputeProcessing_DisputeMatch_TransactionFound,
                set => set
                    .CorrelationId(transaction?.OrderId)
                    .TenantId(transaction?.Merchant?.Mid)
                    .Data(payload)
                    .Meta(meta => meta
                        .SetValue("DisputeQueueItemId", payload.DisputeQueueItemId)
                        .SetValue("Provider", queueItem.ProviderName)
                        .TransactionReference(payload.TransactionId)));

            var preDisputeRequest = new PreDisputeRequest
            {
                CreateDateTime = queueItem.RequestDate,
                OrderId = transaction.OrderId,
                TransactionId = transaction.Id,
                IsReviewRequired = false,
                CaseNumber = queueItem.CaseNumber,
                Descriptor = transaction.DynamicDescriptor,
                DisputeManagementSystem = queueItem.DisputeManagementSystem,
                Stage = queueItem.Stage,
                DisputeAmount = payload?.AmountToRefund != null && payload?.AmountToRefund > 0
                    ? Utils.Formatters.DecimalToInt(payload.AmountToRefund)
                    : queueItem.Amount,
                Amount = queueItem.Amount,
                AuthorizationId = queueItem.AuthorizationId,
                Currency = queueItem.Currency,
                RequestDate = queueItem.RequestDate,
                Mcc = queueItem.Mcc,
                Arn = queueItem.Arn,
                CardAcceptorId = queueItem.CardAcceptorId,
                ProviderName = queueItem.ProviderName,
                DisputeType = queueItem.DisputeType ?? queueItem.Reason,
                CardBrand = queueItem.CardBrand,
                CardType = queueItem.CardType,
                Bin = queueItem.Bin,
                Last4 = queueItem.Last4,
                IsWebhook = queueItem.IsWebhook,
                IsManualInserted = false,
                Meta = queueItem.Meta,
                EarlyFraudWarning = queueItem.EarlyFraudWarning,
                IsFileImported = queueItem.IsFileImported,
                IssueRefund = payload.IssueRefund,
            };

            // TODO for now a lot of queue items has infinity transaction date, when we fix it we should use transaction date
            if (queueItem.AuthorizationDate.HasValue)
            {
                preDisputeRequest.TransactionDate = queueItem.AuthorizationDate.Value;
            }

            var result = await AddPreDisputeAsync(preDisputeRequest, token);

            workspan
                .Response(result);

            if (result.Success)
            {
                await UpdateMatchedDisputeReferences(result.Dispute, queueItem);

                if (queue != null)
                {
                    queue.ProcessedRecordsCount++;
                    _dbContext.DisputeQueues.Update(queue);
                }

                await NotifyAlertProviders(payload, queue, queueItem, result.DisputeId, transaction, workspan, token);

                _dbContext.DisputeQueueItems.Remove(queueItem);
                await _dbContext.SaveChangesAsync();

                workspan.Log
                    .Information("Dispute match processing succeeded");

                await _activityService.CreateActivityAsync(DisputeActivities.DisputeProcessing_DisputeMatch_Succeeded,
                    set => set
                        .CorrelationId(transaction?.OrderId)
                        .TenantId(transaction?.Merchant?.Mid)
                        .Data(payload)
                        .Meta(meta => meta
                            .SetValue("DisputeQueueItemId", payload.DisputeQueueItemId)
                            .SetValue("DisputeId", result.DisputeId)
                            .SetValue("Provider", queueItem.ProviderName)
                            .TransactionReference(payload.TransactionId)
                            .SetValue("DisputeId", result.DisputeId)));
            }
            else
            {
                workspan.Log.Error("Dispute match error");

                await _activityService.CreateActivityAsync(DisputeErrorActivities.DisputeProcessing_DisputeMatch_Error,
                    set => set
                        .CorrelationId(transaction?.OrderId)
                        .TenantId(transaction?.Merchant?.Mid)
                        .Data(result)
                        .Meta(meta => meta
                            .Error(result.Message)
                            .SetValue("DisputeQueueItemId", payload.DisputeQueueItemId)
                            .TransactionReference(payload.TransactionId)));
            }

            response.Success = result.Success;
            response.Message = result.Message;
            response.Code = result.Code;
            response.DisputeId = result.DisputeId;

            await _dbContext.Database.CommitTransactionAsync(token);
        }
        catch (FlexChargeException e)
        {
            workspan
                .Tag("DisputeQueueItemId", payload.DisputeQueueItemId)
                .Tag("TransactionId", payload.TransactionId)
                .Log.Fatal(e, "Error matching dispute");

            response.Success = false;
            response.Message = e.Message;
            response.Code = e.Code;

            await _activityService.CreateActivityAsync(DisputeErrorActivities.DisputeProcessing_DisputeMatch_Error,
                set => set
                    .CorrelationId(transaction?.OrderId)
                    .TenantId(transaction?.Merchant?.Mid)
                    .Data(e)
                    .Meta(meta => meta
                        .Error(e.Message)
                        .SetValue("DisputeQueueItemId", payload.DisputeQueueItemId)
                        .TransactionReference(payload.TransactionId)));
            throw;
        }
        catch (Exception e)
        {
            workspan
                .Tag("DisputeQueueItemId", payload.DisputeQueueItemId)
                .Tag("TransactionId", payload.TransactionId)
                .Log.Fatal(e, "Error matching dispute for dispute");

            response.Success = false;
            response.Message = e.Message;

            await _activityService.CreateActivityAsync(DisputeErrorActivities.DisputeProcessing_DisputeMatch_Error,
                set => set
                    .CorrelationId(transaction?.OrderId)
                    .TenantId(transaction?.Merchant?.Mid)
                    .Data(e)
                    .Meta(meta => meta
                        .Error(e.Message)
                        .SetValue("DisputeQueueItemId", payload.DisputeQueueItemId)
                        .TransactionReference(payload.TransactionId)));
            throw;
        }

        return response;
    }

    private async Task NotifyAlertProviders(
        DisputeMatchRequestDTO payload,
        DisputeQueue queue,
        DisputeQueueItem disputeQueueItem,
        Guid disputeId,
        Transaction transaction,
        Workspan workspan,
        CancellationToken token)
    {
        try
        {
            var properEventType = disputeQueueItem.KountEventType == KountEventType.ETHOCA_FRAUD ||
                                  disputeQueueItem.EventType == KountEventType.ETHOCA_FRAUD ||
                                  disputeQueueItem.KountEventType == KountEventType.ETHOCA_DISPUTE ||
                                  disputeQueueItem.EventType == KountEventType.ETHOCA_DISPUTE;

            if ((queue?.Source == AlertProviders.Kount && properEventType) ||
                (queue?.Source == AlertProviders.MyRCVR && properEventType) ||
                queue?.Source == AlertProviders.Chargeblast)
            {
                if (queue?.Source == AlertProviders.Kount)
                {
                    await _kountAlertService.AlertKountAsync(disputeQueueItem, transaction,
                        payload.KountStatusCode, payload.IssueRefund, payload.Comments);
                }

                if (queue?.Source == AlertProviders.MyRCVR)
                {
                    await _myRcvrAlertService.AlertMyrcvrAsync(disputeQueueItem, transaction,
                        payload.KountStatusCode, payload.IssueRefund, payload.Comments);
                }

                if (queue?.Source == AlertProviders.Chargeblast)
                {
                    var alertProvider = InitiateDisputeAlertsProviderService(AlertProviders.Chargeblast);

                    await alertProvider.ProcessAlertAsync(new AlertDTO
                    {
                        TransactionId = transaction.Id,
                        StatusCode = payload.KountStatusCode,
                        DisputeQueueItemId = disputeQueueItem.Id,
                    });
                }

                var repliedToEthoca = DateTime.UtcNow;

                var disputeQueueItemUpdateRequest = new DisputeQueueItemUpdateDTO
                {
                    RepliedToEthoca = repliedToEthoca,
                };

                var dispute = await _dbContext.Disputes.FirstOrDefaultAsync(x => x.Id == disputeId);
                if (dispute != null)
                {
                    disputeQueueItemUpdateRequest.DisputeId = dispute.Id;
                    await UpdateDisputeAsync(dispute, new DisputeUpdateRequestDTO
                    {
                        RepliedToEthoca = repliedToEthoca,
                        RequestId = disputeQueueItem.RequestId,
                        DisputeQueueItemId = disputeQueueItem.Id,
                    }, token);
                }

                await _disputeQueueService.UpdateDisputeQueueItemAsync(disputeQueueItem, new DisputeQueueItemUpdateDTO
                {
                    RepliedToEthoca = repliedToEthoca,
                    DisputeId = dispute.Id
                }, token);
            }
        }
        catch (Exception e)
        {
            workspan
                .Tag("Provider", queue?.Source)
                .Tag("DisputeQueueItemId", disputeQueueItem.Id)
                .Tag("TransactionId", transaction.Id)
                .RecordFatalException(e, "Error notifying alert provider");
        }
    }

    public async Task<DisputeReconcileResponseDTO> ReconcileDisputeAsync(DisputeReconcileRequestDTO payload,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<DisputeServices>()
            .LogEnterAndExit();


        await _activityService.CreateActivityAsync(DisputeActivities.DisputeProcessing_DisputeReconcile_Started,
            set => set
                .Data(payload));

        // needs for the activity
        var successCount = 0;
        var failedCount = 0;
        var errors = new List<DisputeReconcileErrorsDTO>();

        try
        {
            var queueItemIds = payload.Items.Select(x => x.DisputeQueueItemId).ToList();
            var disputeQueueItems = await _dbContext.DisputeQueueItems
                .Include(x => x.PotentialMatches)
                .Where(x => queueItemIds.Contains(x.Id))
                .ToListAsync();


            foreach (var item in disputeQueueItems)
            {
                workspan
                    .Baggage("DisputeQueueItemId", item.Id);

                try
                {
                    if (item.Stage is DisputeStage.PRE_DISPUTE or DisputeStage.UNMAPPED)
                    {
                        failedCount++;
                        errors.Add(new DisputeReconcileErrorsDTO
                        {
                            DisputeQueueItemId = item.Id,
                            Error = $"Reconciliation for items in stage {item.Stage} is not supported"
                        });
                        continue;
                    }

                    if (item.PotentialMatches.Count != 1)
                    {
                        failedCount++;
                        errors.Add(new DisputeReconcileErrorsDTO
                        {
                            DisputeQueueItemId = item.Id,
                            Error = "Potential matches count is not equal to 1"
                        });
                        continue;
                    }

                    var disputeMatchRequestDTO = new DisputeMatchRequestDTO
                    {
                        DisputeQueueItemId = item.Id,
                        TransactionId = item.PotentialMatches.First().TransactionId,
                        IssueRefund = false, // TODO could be set from payload
                    };

                    var response = await MatchDisputeAsync(disputeMatchRequestDTO, token, false);

                    if (!response.Success)
                    {
                        failedCount++;
                        errors.Add(new DisputeReconcileErrorsDTO
                        {
                            DisputeQueueItemId = item.Id,
                            Error = response.Message
                        });
                    }
                    else
                    {
                        successCount++;
                    }
                }
                catch (Exception e)
                {
                    workspan
                        .Log.Fatal(e, "Error while reconciling dispute for dispute queue item");

                    failedCount++;
                    errors.Add(new DisputeReconcileErrorsDTO
                    {
                        DisputeQueueItemId = item.Id,
                        Error = e.Message
                    });
                }
            }

            await _activityService.CreateActivityAsync(DisputeActivities.DisputeProcessing_DisputeReconcile_Succeeded,
                set => set
                    .Data(payload)
                    .Meta(meta => meta
                        .SetValue("TotalCount", payload.Items.Count)
                        .SetValue("SuccessCount", successCount)
                        .SetValue("FailedCount", failedCount)));

            if (errors.Count > 0)
            {
                await UpdateReconcileErrors(errors);
            }

            return new DisputeReconcileResponseDTO
            {
                Success = true,
                Message = successCount == payload.Items.Count
                    ? "All dispute items reconciled successfully"
                    : "Some dispute items failed to reconcile. Please try manually",
                SuccessCount = successCount,
                FailedCount = failedCount,
                TotalCount = payload.Items.Count,
                Errors = errors
            };
        }
        catch (Exception e)
        {
            throw;
        }
    }


    public async Task ReverseAsync(Guid? disputeId, Guid? orderId)
    {
        using var workspan = Workspan.Start<DisputeServices>()
            .LogEnterAndExit();

        try
        {
            var dispute = await _dbContext.Disputes.FirstOrDefaultAsync(x => x.Id == disputeId || x.OrderId == orderId);

            dispute.Stage = DisputeStage.REVERSED;
            dispute.IsFileImported = true;

            var payload = new PreDisputeRequest
            {
                OrderId = dispute.OrderId,
                TransactionId = dispute.TransactionId,
                IsReviewRequired = false,
                CaseNumber = dispute.CaseNumber,
                Descriptor = dispute.Descriptor,
                DisputeManagementSystem = dispute.DisputeManagementSystem,
                Stage = dispute.Stage,
                Amount = dispute.Amount,
                AuthorizationId = dispute.AuthorizationId,
                Currency = dispute.Currency,
                RequestDate = dispute.RequestDate,
                Mcc = dispute.Mcc,
                Arn = dispute.Arn,
                CardAcceptorId = dispute.CardAcceptorId,
                ProviderName = dispute.ProviderName,
                DisputeType = dispute.DisputeType,
                CardBrand = dispute.CardBrand,
                CardType = dispute.CardType,
                Bin = dispute.Bin,
                Last4 = dispute.Last4,
                IsWebhook = dispute.IsWebhook,
                IsManualInserted = true,
                Meta = dispute.Meta,
                EarlyFraudWarning = dispute.EarlyFraudWarning,
                IsFileImported = false,
                TransactionDate = dispute.TransactionDate,
                DueDate = dispute.DueDate,
            };

            await CreatePreDisputeActivity(payload, dispute.Id, workspan, default);

            _dbContext.Disputes.Update(dispute);

            var trx = await CheckForExistingTransaction(payload.TransactionId, default, workspan);

            var disputeTransaction = new Transaction
            {
                ParentId = trx.Id,
                PaymentMethodId = trx.PaymentMethodId,
                Amount = trx.Amount,
                AuthorizationAmount = trx.AuthorizationAmount,
                Currency = trx.Currency,
                ResponseCode = payload.DisputeType,
                ResponseMessage = payload.DisputeType?.ToString(),
                DynamicDescriptor = trx.DynamicDescriptor,
                Type = nameof(TransactionType.Reverse),
                PaymentType = nameof(PaymentMethodType.CreditCard),
                Status = TransactionStatus.Completed,
                ProviderName = payload.DisputeManagementSystem,
                ProviderTransactionToken = payload.CaseNumber,
                ProcessorName = trx.ProcessorName,
                Arn = payload.Arn,
                PayerId = trx.PayerId,
                SiteId = trx.SiteId,
                Merchant = trx.Merchant,
                OrderId = trx.OrderId,
                //IsRecurring = false,
                Meta = System.Text.Json.JsonSerializer.SerializeToDocument(payload),
                SchemeTransactionId = trx.SchemeTransactionId
            };
            _dbContext.Transactions.Add(disputeTransaction);

            await _dbContext.SaveChangesAsync();

            await _publisher.Publish(new OrderReversedEvent
            {
                OrderId = trx.OrderId,
                Mid = trx.Merchant.Mid,
                Pid = trx.Merchant.Pid,
                Amount = trx.Amount,
                DisputeDateTime = trx.CreatedOn,
                TransactionId = trx.Id,
                Currency = trx.Currency
            });
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    protected IQueryable<Dispute> SortAndFilter(IQueryable<Dispute> dbset, DateTime? from, DateTime? to,
        DateTime? requestFrom, DateTime? requestTo, DateTime? createdFrom,
        DateTime? createdTo, string? timezone, string? sort, string? sortField,
        List<string>? providerName, List<string>? cardType, List<string>? stage, bool? isWebhook, bool? isMatch,
        bool? isArchived, bool? isFileImported, bool? isManualInserted)
    {
        try
        {
            #region Filter by

            if (from.HasValue && to.HasValue && from.Value > DateTime.MinValue && to.Value > DateTime.MinValue)
            {
                var fromUTC = from.Value.Date.ToUniversalTime();
                var toUTC = to.Value.Date.ToUniversalTime();

                if (timezone != null)
                {
                    var isTimezoneValid = TimeZoneInfo.TryConvertIanaIdToWindowsId(timezone, out var timezoneName);

                    if (!isTimezoneValid)
                        throw new Exception("Invalid timezone");

                    fromUTC = TimeZoneInfo.ConvertTimeToUtc(from.Value,
                        TimeZoneInfo.FindSystemTimeZoneById(timezoneName));
                    toUTC = TimeZoneInfo.ConvertTimeToUtc(to.Value, TimeZoneInfo.FindSystemTimeZoneById(timezoneName));
                }

                dbset = dbset.Where(x =>
                    x.TransactionDate.Date >= fromUTC.Date &&
                    x.TransactionDate.Date <= toUTC.Date);
            }

            if (isArchived.HasValue && isArchived == true)
            {
                dbset = dbset.Where(x => x.IsArchived != null);
            }

            if (!isArchived.HasValue || isArchived.HasValue && isArchived == false)
            {
                dbset = dbset.Where(x => x.IsArchived == null);
            }

            if (requestFrom.HasValue && requestTo.HasValue && requestFrom.Value > DateTime.MinValue &&
                requestTo.Value > DateTime.MinValue)
            {
                var requestFromUTC = requestFrom.Value.Date.ToUniversalTime();
                var requestToUTC = requestTo.Value.Date.ToUniversalTime();
                if (timezone != null)
                {
                    var isTimezoneValid = TimeZoneInfo.TryConvertIanaIdToWindowsId(timezone, out var timezoneName);

                    if (!isTimezoneValid)
                        throw new Exception("Invalid timezone");

                    requestFromUTC = TimeZoneInfo.ConvertTimeToUtc(requestFrom.Value,
                        TimeZoneInfo.FindSystemTimeZoneById(timezoneName));
                    requestToUTC = TimeZoneInfo.ConvertTimeToUtc(requestTo.Value,
                        TimeZoneInfo.FindSystemTimeZoneById(timezoneName));
                }

                dbset = dbset.Where(x =>
                    x.RequestDate.Date >= requestFromUTC.Date &&
                    x.RequestDate.Date <= requestToUTC.Date);
            }

            if (createdFrom.HasValue && createdTo.HasValue && createdFrom.Value > DateTime.MinValue &&
                createdTo.Value > DateTime.MinValue)
            {
                var createdFromUTC = createdFrom.Value.Date.ToUniversalTime();
                var createdToUTC = createdTo.Value.Date.ToUniversalTime();
                if (timezone != null)
                {
                    var isTimezoneValid = TimeZoneInfo.TryConvertIanaIdToWindowsId(timezone, out var timezoneName);

                    if (!isTimezoneValid)
                        throw new Exception("Invalid timezone");

                    createdFromUTC = TimeZoneInfo.ConvertTimeToUtc(createdFrom.Value,
                        TimeZoneInfo.FindSystemTimeZoneById(timezoneName));
                    createdToUTC = TimeZoneInfo.ConvertTimeToUtc(createdTo.Value,
                        TimeZoneInfo.FindSystemTimeZoneById(timezoneName));
                }

                dbset = dbset.Where(x =>
                    x.CreatedOn.Date >= createdFromUTC.Date &&
                    x.CreatedOn.Date <= createdToUTC.Date);
            }

            if (stage.Count > 0)
            {
                dbset = dbset.Where(x => stage.Contains(x.Stage));
            }

            if (isWebhook.HasValue)
            {
                dbset = dbset.Where(x => x.IsWebhook == isWebhook);
            }

            if (isMatch.HasValue)
            {
                dbset = dbset.Where(x =>
                    isMatch.Value == true && x.OrderId != Guid.Empty ||
                    isMatch.Value == false && x.OrderId == Guid.Empty);
            }

            if (isFileImported.HasValue)
            {
                dbset = dbset.Where(x => x.IsFileImported == true);
            }

            if (isManualInserted.HasValue)
            {
                dbset = dbset.Where(x => x.IsManualInserted == true);
            }

            #endregion

            #region Sort by column name

            if (sortField == null || sortField == "transactionDate")
            {
                dbset = sort == "asc"
                    ? dbset.OrderBy(x => x.TransactionDate)
                    : dbset.OrderByDescending(x => x.TransactionDate);
            }

            if (sortField == "requestDate")
            {
                dbset = sort == "asc"
                    ? dbset.OrderBy(x => x.RequestDate)
                    : dbset.OrderByDescending(x => x.RequestDate);
            }

            #endregion

            if (providerName.Count > 0)
            {
                dbset = dbset.Where(x => providerName.Contains(x.ProviderName));
            }

            if (cardType.Count > 0)
            {
                dbset = dbset.Where(x => cardType.Contains(x.CardBrand));
            }

            return dbset;
        }
        catch (Exception e)
        {
            throw;
        }
    }

    protected async Task UpdateMatchedDisputeReferences(Dispute dispute, DisputeQueueItem queueItem)
    {
        using var workspan = Workspan.Start<DisputeServices>();
        try
        {
            if (dispute == null || queueItem == null)
            {
                workspan.Log.Error("Dispute and queue item are null");
                throw new FlexChargeException("Dispute and queue item are null");
            }

            dispute.DisputeQueueItemId = queueItem.Id;
            dispute.HasMatch = true;

            queueItem.DisputeId = dispute.Id;
            queueItem.HasMatch = true;

            _dbContext.Disputes.Update(dispute);
            _dbContext.DisputeQueueItems.Update(queueItem);
            await _dbContext.SaveChangesAsync();
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "Error updating dispute references");
        }
    }

    protected async Task UpdateReconcileErrors(List<DisputeReconcileErrorsDTO> errors)
    {
        using var workspan = Workspan.Start<DisputeServices>();
        try
        {
            foreach (var error in errors)
            {
                var queueItem =
                    await _dbContext.DisputeQueueItems.FirstOrDefaultAsync(x => x.Id == error.DisputeQueueItemId);

                if (queueItem != null)
                {
                    queueItem.MatchError = error.Error;

                    _dbContext.DisputeQueueItems.Update(queueItem);
                }
            }

            await _dbContext.SaveChangesAsync();
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "Error updating dispute references");
        }
    }

    protected T GetRequiredService<T>()
    {
        return _serviceProvider.GetRequiredService<T>();
    }

    private IDisputeAlertsService InitiateDisputeAlertsProviderService(string providerKey)
    {
        var disputeAlertProviderResolver =
            _serviceProvider.GetRequiredService<ServiceCollectionExtensions.DisputeAlertProviderResolver>();
        return disputeAlertProviderResolver(providerKey);
    }
}