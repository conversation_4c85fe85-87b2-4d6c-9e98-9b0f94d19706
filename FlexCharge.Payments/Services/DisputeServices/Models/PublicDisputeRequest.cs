using System;
using System.ComponentModel.DataAnnotations;

namespace FlexCharge.Payments.Services.DisputeServices.Models;

public class PublicDisputeRequest
{
    [Required] public Guid OrderId { get; set; }
    [Required] public Guid TransactionId { get; set; }
    [Required] public string Descriptor { get; set; }
    public DateTime RequestDate { get; set; }
    public bool IssueRefund { get; set; }
    public bool EarlyFraudWarning { get; set; }
    // public string CardBrand { get; set; }
    [Required] public string DisputeType { get; set; }
}