using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Telemetry;
using FlexCharge.Payments.Activities;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Enums;
using FlexCharge.Utils;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

public interface IMyRcvrAlertService
{
    Task GetMyRcvrAlertsAsync(string apiUrl, string privateKey, CancellationToken token = default);

    Task AlertMyrcvrAsync(DisputeQueueItem disputeItem, Transaction transaction, string statusCode,
        bool issueRefund,
        string? comments);
}

namespace FlexCharge.Payments.Services.DisputeServices
{
    public class MyRcvrAlertService : IMyRcvrAlertService
    {
        private readonly PostgreSQLDbContext _dbContext;
        private IMapper _mapper;
        private HttpClient _httpClient;
        private readonly INotificationReportService _notificationReportService;
        private readonly IActivityService _activityService;

        public MyRcvrAlertService(PostgreSQLDbContext dbContext, IMapper mapper,
            HttpClient httpClient, INotificationReportService notificationReportService,
            IActivityService activityService)

        {
            _dbContext = dbContext;
            _mapper = mapper;
            _httpClient = httpClient;
            _notificationReportService = notificationReportService;
            _activityService = activityService;
        }

        public async Task AlertMyrcvrAsync(DisputeQueueItem disputeQueueItem, Transaction transaction,
            string StatusCode, bool issueRefund, string? comments)
        {
            using var workspan = Workspan.Start<MyRcvrAlertService>()
                .Baggage("TransactionId", transaction.Id)
                .Baggage("StatusCode", StatusCode)
                .LogEnterAndExit();

            try
            {
                if (transaction == null)
                {
                    workspan.Log.Fatal("AlertMyrcvrAsync > Transaction not found: transactionId: {transactionId}",
                        transaction.Id);
                    throw new Exception("Transaction not found.");
                }

                var alertProvider = await _dbContext.AlertProviders
                    .FirstOrDefaultAsync(x => x.Id == disputeQueueItem.AlertProviderId);

                if (alertProvider == null)
                {
                    workspan.Log.Fatal("AlertMyRCVRAsync > AlertProvider not found: transactionId: {transactionId}",
                        transaction.Id);
                    throw new Exception("AlertProvider not found.");
                }

                if (alertProvider.PrivateName == null || alertProvider.PrivateKey == null)
                {
                    workspan.Log.Fatal("AlertMyRCVRAsync > MyRCVR settings are missing: transactionId: {transactionId}",
                        transaction.Id);
                    throw new Exception("MyRCVR settings are missing.");
                }

                // requestID we get from the webhook payload
                var requestId = disputeQueueItem.RequestId;
                if (String.IsNullOrEmpty(requestId))
                {
                    var meta = JsonConvert.DeserializeObject<WebhookKountDTO>(disputeQueueItem.Meta);

                    var newRequestId = meta?.events[0]?.requestID;

                    if (string.IsNullOrEmpty(newRequestId))
                    {
                        requestId = newRequestId;
                    }
                    else
                    {
                        workspan.Log.Fatal(
                            "AlertMyrcvrAsync > Request ID is missing: disputeItemId: {disputeItemId}; transactionId: {transactionId}",
                            disputeQueueItem.Id, transaction.Id);
                        throw new Exception("Request ID is missing.");
                    }
                }

                var myRcvrBaseUrl = Environment.GetEnvironmentVariable("MYRCVR_API");
                string myRcvrApi = $"{myRcvrBaseUrl}/{alertProvider.PrivateName}";
                string myRcvrApiKey = alertProvider.PrivateKey;

                if (myRcvrApi == null || myRcvrApiKey == null)
                {
                    workspan.Log.Error("AlertMyRCVRAsync > MyRCVR settings are missing: transactionId: {transactionId}",
                        transaction.Id);
                    throw new Exception("MyRCVR settings are missing.");
                }

                using (var request = new HttpRequestMessage(new HttpMethod("POST"), $"{myRcvrApi}"))
                {
                    request.Headers.TryAddWithoutValidation("authorization", $"Bearer {myRcvrApiKey}");
                    request.Headers.TryAddWithoutValidation("Content-Type", "application/json");

                    var requestContent = new KountAlertDTO
                    {
                        actions = new List<KountActionDTO>()
                    };

                    var alertSystem = "Ethoca";

                    string parsedStatusCode =
                        KountVerifiStatusCodes.ParseKountStatus(StatusCode, disputeQueueItem.EventType);

                    var refundedState = GetRefundedState(issueRefund, transaction);

                    requestContent.actions.Add(new KountActionDTO
                    {
                        id = disputeQueueItem.ExternalReferenceID,
                        alertSystem = alertSystem,
                        action = GetMyRcvrDisputeAction(disputeQueueItem.Stage),
                        alertType = GetMyRcvrAlertType(disputeQueueItem.Stage),
                        refunded = refundedState,
                        statusCode = parsedStatusCode,
                        date = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                        amount = transaction.Amount.ToString(),
                        currency = transaction.Currency,
                        comments = string.IsNullOrEmpty(comments) ? "Transaction ID: " + transaction.Id : comments,
                    });

                    request.Content = new StringContent(JsonConvert.SerializeObject(requestContent), Encoding.UTF8,
                        "application/json");

                    var response = await _httpClient.SendAsync(request);
                    var responseContent = await response.Content.ReadAsStringAsync();

                    if (!response.IsSuccessStatusCode)
                    {
                        workspan.Log.Error(
                            "AlertMyRcvrAsync error: request: {request}; {StatusCode} - {ReasonPhrase}; response: {responseContent}",
                            JsonConvert.SerializeObject(requestContent), response.StatusCode, response.ReasonPhrase,
                            responseContent);

                        await _activityService.CreateActivityAsync(
                            PaymentsIncomingWebhooksErrorActivities.Payments_IncomingWebhooks_AlertProvider_Error,
                            data: responseContent,
                            set => set
                                .CorrelationId(transaction?.OrderId)
                                .TenantId(transaction?.Merchant?.Mid)
                                .Data(responseContent)
                                .Meta(meta => meta
                                    .ServiceProvider(AlertProviders.MyRCVR)
                                    .TransactionReference(transaction?.Id)
                                    .SetValue("Comments", comments)
                                    .SetValue("StatusCode", StatusCode)
                                    .SetValue("DisputeItemID", disputeQueueItem.Id)));

                        throw new Exception($"MyRcvr call error: {response.ReasonPhrase}.");
                    }
                    else
                    {
                        workspan.Log.Information("AlertKountAsync success: request: {request}; response: {response}",
                            JsonConvert.SerializeObject(requestContent), JsonConvert.SerializeObject(responseContent));

                        await _activityService.CreateActivityAsync(
                            PaymentsIncomingWebhooksActivities.Payments_IncomingWebhooks_AlertProvider_Succeeded,
                            set => set
                                .CorrelationId(transaction?.OrderId)
                                .TenantId(transaction?.Merchant?.Mid)
                                .Data(responseContent)
                                .Meta(meta => meta
                                    .ServiceProvider(AlertProviders.MyRCVR)
                                    .TransactionReference(transaction?.Id)
                                    .SetValue("Comments", comments)
                                    .SetValue("StatusCode", StatusCode)
                                    .SetValue("DisputeItemID", disputeQueueItem.Id)
                                    .SetValue("Provider", AlertProviders.MyRCVR)));
                    }
                }
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);

                await _activityService.CreateActivityAsync(
                    PaymentsIncomingWebhooksErrorActivities.Payments_IncomingWebhooks_AlertProvider_Error,
                    set => set
                        .Data(e)
                        .Meta(meta => meta
                            .ServiceProvider(AlertProviders.MyRCVR)
                            .TransactionReference(transaction.Id)
                            .SetValue("Comments", comments)
                            .SetValue("StatusCode", StatusCode)
                            .SetValue("DisputeItemID", disputeQueueItem.Id)
                            .SetValue("Provider", "Kount")));
                throw;
            }
        }

        public async Task GetMyRcvrAlertsAsync(string apiUrl, string privateKey, CancellationToken token = default)
        {
            using var workspan = Workspan.Start<MyRcvrAlertService>()
                .LogEnterAndExit();

            try
            {
                await _activityService.CreateActivityAsync(DisputeReportsActivities.DisputeReports_GetAlert_Started,
                    set => set
                        .Meta(meta => meta.SetValue("Provider", AlertProviders.MyRCVR)));

                using (var request = new HttpRequestMessage(new HttpMethod("GET"), apiUrl))
                {
                    request.Headers.TryAddWithoutValidation("authorization", $"Bearer {privateKey}");

                    var response = await _httpClient.SendAsync(request);

                    if (!response.IsSuccessStatusCode)
                    {
                        throw new Exception($"MyRCVR call error: {response.ReasonPhrase}.");
                    }
                    else
                    {
                        var content = await response.Content.ReadAsStringAsync();

                        var records = JsonConvert.DeserializeObject<MyRcvrAlertsResponseDTO>(content)?.data?.results;

                        if (records == null || records.Count == 0)
                        {
                            return;
                        }

                        var disputeQueue = await _dbContext.DisputeQueues
                            .Include(x => x.DisputeQueueItems)
                            .FirstOrDefaultAsync(x => x.Name == "MyRCVR");

                        if (disputeQueue == null)
                        {
                            disputeQueue = new DisputeQueue
                            {
                                Name = "MyRCVR",
                                CreatedOn = DateTime.UtcNow,
                                DisputeQueueItems = new List<DisputeQueueItem>(),
                                TotalCount = records.Count,
                                LoadedRecordsCount = 0,
                                Source = AlertProviders.MyRCVR,
                                ProcessedRecordsCount = 0,
                                ErrorCount = 0,
                                Errors = "",
                                FailedRecords = "",
                            };
                        }
                        else
                        {
                            records = FilterAlertsByTime(records);
                            disputeQueue.TotalCount += records.Count;
                        }

                        var queueItems = disputeQueue.DisputeQueueItems.ToList();

                        var errorCount = disputeQueue.ErrorCount;
                        var loadedRecordsCount = disputeQueue.LoadedRecordsCount;
                        var processedRecordsCount = disputeQueue.ProcessedRecordsCount;
                        var errors = disputeQueue?.Errors ?? "";
                        var failedRecords = disputeQueue?.FailedRecords == null
                            ? new List<MyRcvrAlertsResponseItemDTO>()
                            : JsonConvert.DeserializeObject<List<MyRcvrAlertsResponseItemDTO>>(disputeQueue
                                .FailedRecords);

                        foreach (var record in records)
                        {
                            try
                            {
                                var disputeQueueItem = new DisputeQueueItem
                                {
                                    IsWebhook = false,
                                    IsManualInserted = false,
                                    // ExternalReferenceID = record.CrmTransactionId, // TODO check if this correct
                                    ExternalReferenceID = record.crm_transaction_id, // TODO check if this correct
                                    RequestDate = DateTime.Parse(record?.transaction_date).ToUtcDate(),
                                    AuthorizationDate = DateTime.Parse(record?.transaction_date).ToUtcDate(),
                                    TransactionDate = DateTime.Parse(record?.transaction_date).ToUtcDate(),
                                    Bin = record?.card_first6,
                                    Last4 = record?.card_last4,
                                    DisputeManagementSystem = DisputeManagementSystem.MyRCVR,
                                    ProviderName = AlertProviders.MyRCVR,
                                    Stage = GetDisputeStage(record?.alert_type),
                                    EarlyFraudWarning = false,
                                    Meta = JsonConvert.SerializeObject(record),
                                    Currency = record?.currency,
                                    Amount = (int) (decimal.Parse(record?.transaction_amount) * 100),
                                    HasMatch = false,
                                    IsFileImported = false,
                                    IsSftp = false,
                                    CardBrand = record?.card_type?.ToUpper(),
                                };

                                var isExist = queueItems.Any(x => x.ExternalReferenceID == record.crm_transaction_id &&
                                                                  x.RequestDate == disputeQueueItem.RequestDate &&
                                                                  x.Amount == disputeQueueItem.Amount &&
                                                                  x.Bin == x.Bin &&
                                                                  x.Last4 == x.Last4);
                                if (isExist)
                                {
                                    continue;
                                }

                                queueItems.Add(disputeQueueItem);
                                loadedRecordsCount++;
                            }
                            catch (Exception e)
                            {
                                workspan.RecordException(
                                    e,
                                    "ERROR: GetMyRcvrAlertsAsync => Alert id - {Id} ",
                                    record.crm_transaction_id);
                                // record.CrmTransactionId);

                                errorCount++;
                                errors += $"Error: {e.Message} \n";
                                failedRecords.Add(record);
                            }
                        }

                        disputeQueue.Errors = errors;
                        disputeQueue.LoadedRecordsCount = loadedRecordsCount;
                        disputeQueue.ErrorCount = errorCount;
                        disputeQueue.FailedRecords = JsonConvert.SerializeObject(failedRecords);
                        disputeQueue.ProcessedRecordsCount = processedRecordsCount;

                        var mappedQueueItems = _mapper.Map<List<DisputeQueueItem>>(queueItems);
                        var existingItemIds = disputeQueue.DisputeQueueItems.Select(x => x.Id).ToList();

                        disputeQueue.DisputeQueueItems.AddRange(mappedQueueItems);

                        _dbContext.DisputeQueues.Update(disputeQueue);
                        await _dbContext.SaveChangesAsync();

                        await _activityService.CreateActivityAsync(
                            DisputeReportsActivities.DisputeReports_GetAlert_Succeeded,
                            set => set
                                .Data(new {failedRecords, errors})
                                .Meta(meta => meta
                                    .SetValue("LoadedRecordsCount", loadedRecordsCount)
                                    .SetValue("ErrorCount", errorCount)
                                    .SetValue("ProcessedRecordsCount", processedRecordsCount)
                                    .SetValue("DisputeQueueId", disputeQueue.Id)
                                    .ServiceProvider(AlertProviders.MyRCVR)
                                    .SetValue("DisputeManagementSystem", DisputeManagementSystem.MyRCVR)));

                        var newQueueItems = disputeQueue.DisputeQueueItems
                            .Where(x => !existingItemIds.Contains(x.Id))
                            .ToList();

                        if (disputeQueue.NewItemsAlert && disputeQueue.EmailRecipient != null)
                        {
                            foreach (var item in newQueueItems)
                            {
                                await _notificationReportService.NotifyNewQueueItem(item.Id);
                            }
                        }

                        workspan.Log.Information(
                            "EXIT: GetMyRcvrAlertsAsync => items count {Count} ",
                            disputeQueue.TotalCount);
                    }
                }
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);

                await _activityService.CreateActivityAsync(DisputeReportsErrorsActivities.DisputeReports_GetAlert_Error,
                    set => set
                        .Data(e)
                        .Meta(meta => meta.SetValue("Provider", AlertProviders.MyRCVR)));

                throw;
            }
        }

        protected List<MyRcvrAlertsResponseItemDTO> FilterAlertsByTime(List<MyRcvrAlertsResponseItemDTO> alerts)
        {
            DateTime oneHourAgo = DateTime.UtcNow.AddHours(-1);

            return alerts.Where(alert => DateTime.Parse(alert.alert_time) > oneHourAgo).ToList();
        }

        protected string GetDisputeStage(string eventType)
        {
            switch (eventType.ToUpper())
            {
                case "ETHOCA": return DisputeStage.PRE_DISPUTE;
                case "RDR": return DisputeStage.RDR;
                case "DISPUTE": return DisputeStage.RDR;
                case "CHARGEBACK": return DisputeStage.CHARGEBACK;
                default: return DisputeStage.UNMAPPED;
            }
        }

        protected string GetMyRcvrDisputeAction(string eventType)
        {
            switch (eventType)
            {
                case MyRcvrEventType.ETHOCA_FRAUD:
                case MyRcvrEventType.ETHOCA_DISPUTE: return MyRcvrDisputeAction.resolved;
                case "RDR":
                    return
                        MyRcvrDisputeAction
                            .resolved; // could be resolved, declined for Dispute Alerts, or cancelled, declined for Cancel Alerts 
                case "DISPUTE": return MyRcvrDisputeAction.resolved;
                default: return "UNMAPPED";
            }
        }

        protected string GetMyRcvrAlertType(string eventType)
        {
            switch (eventType)
            {
                case MyRcvrEventType.ETHOCA_FRAUD: return MyRcvrEventType.ETHOCA_FRAUD;
                case MyRcvrEventType.ETHOCA_DISPUTE: return MyRcvrEventType.ETHOCA_DISPUTE;
                case "RDR": return MyRcvrEventType.DISPUTE; // could be DISPUTE, CANCEL
                case "DISPUTE": return MyRcvrEventType.DISPUTE;
                default: return "UNMAPPED";
            }
        }

        protected string GetRefundedState(bool issueRefund, Transaction transaction)
        {
            if (issueRefund)
            {
                return KountRefundedState.Refunded;
            }
            else if (transaction.Status == TransactionStatus.Failed)
            {
                return KountRefundedState.NotSettled;
            }
            else
            {
                return KountRefundedState.NotRefunded;
            }
        }
    }
}