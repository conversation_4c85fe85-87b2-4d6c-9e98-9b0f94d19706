namespace FlexCharge.Payments.Services.DisputeServices;

public enum DisputeStatus
{
    Resolved,
    ResolvedPartial,
    ResolvedCancelled,
    AccountCancelled,
    CancellationProcessed,
    UnmatchedGeneral,
    UnmatchedCannotFindTransaction,
    Duplicate,
    MIDLost,
    AlreadyRefunded,
    AlreadyChargeback,
    Ineligible,
    TDS,
    CannotCancel,
    MatchedWillShip,
    EscalateChargeback,
    ContactedShipper,
    WIP,
    Error,
    None,
    NotMyDescriptor,
    Unactioned
}