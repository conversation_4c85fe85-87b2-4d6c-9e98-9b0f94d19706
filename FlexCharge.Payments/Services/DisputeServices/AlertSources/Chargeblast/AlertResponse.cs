using System;
using System.Text.Json.Serialization;
using FlexCharge.Utils.JsonConverters;

namespace FlexCharge.Payments.Services.DisputeServices;

public class AlertResponse
{
    [JsonPropertyName("provider")] public string Provider { get; set; }
    [JsonPropertyName("id")] public string Id { get; set; }
    [JsonPropertyName("card")] public string Card { get; set; }
    [JsonPropertyName("issuer")] public string Issuer { get; set; }
    [JsonPropertyName("invoicedAt")] public string InvoicedAt { get; set; }
    [JsonPropertyName("authCode")] public string AuthCode { get; set; }
    [JsonPropertyName("site")] public string Site { get; set; }
    [JsonPropertyName("customerEmail")] public string CustomerEmail { get; set; }
    [JsonPropertyName("creditStatus")] public string CreditStatus { get; set; }
    [Json<PERSON>ropertyName("alertType")] public string AlertType { get; set; }
    [JsonPropertyName("creditAppealed")] public bool CreditAppealed { get; set; }
    [JsonPropertyName("currency")] public string Currency { get; set; }
    [JsonPropertyName("reasonCode")] public string ReasonCode { get; set; }
    [JsonPropertyName("merchantId")] public string MerchantId { get; set; }
    [JsonPropertyName("acquirerAction")] public string AcquirerAction { get; set; }
    [JsonPropertyName("alertId")] public string AlertId { get; set; }
    [JsonPropertyName("amount")] public decimal Amount { get; set; }

    [JsonPropertyName("createdAt")]
    [System.Text.Json.Serialization.JsonConverter(typeof(DateTimeOffsetFormatConverter))]
    public DateTime CreatedAt { get; set; }

    [JsonPropertyName("resolvedDate")]
    [System.Text.Json.Serialization.JsonConverter(typeof(DateTimeOffsetFormatConverter))]
    public DateTime ResolvedDate { get; set; }

    [JsonPropertyName("transactionDate")]
    [System.Text.Json.Serialization.JsonConverter(typeof(DateTimeOffsetFormatConverter))]
    public DateTime TransactionDate { get; set; }

    [JsonPropertyName("resolvedByApi")] public bool ResolvedByApi { get; set; }
    [JsonPropertyName("externalOrder")] public string ExternalOrder { get; set; }
    [JsonPropertyName("externalUrl")] public string ExternalUrl { get; set; }
    [JsonPropertyName("creditNotes")] public string CreditNotes { get; set; }
    [JsonPropertyName("arn")] public string Arn { get; set; }
    [JsonPropertyName("customerId")] public string CustomerId { get; set; }
    [JsonPropertyName("descriptor")] public string Descriptor { get; set; }
    [JsonPropertyName("subprovider")] public string SubProvider { get; set; }
    [JsonPropertyName("cardBrand")] public string CardBrand { get; set; }
    [JsonPropertyName("responseAction")] public string ResponseAction { get; set; }
}

// Alert.Created
//{
//   "acquirerAction": "Resolved",
//   "alertId": "1234",
//   "alertType": "FRAUD",
//   "amount": 65.5,
//   "arn": "******************",
//   "binDetails": {
//     "brand": "Platinum",
//     "prepaid": false,
//     "type": "debit"
//   },
//   "card": "534444******1234",
//   "cardBrand": "Mastercard",
//   "createdAt": "2023-10-02 21:07:22.479000Z",
//   "creditNotes": "some notes",
//   "creditStatus": "None",
//   "currency": "USD",
//   "descriptor": "Black Nike Shoes",
//   "funding": "Credit",
//   "id": "1234",
//   "issuer": "Bank of America",
//   "merchantId": "cb_xxxxxxxxx",
//   "provider": "ethoca",
//   "reasonCode": "Resolved",
//   "responseAction": "Accepted",
//   "site": "Nike Stores Alerts",
//   "subprovider": "Ethoca",
//   "transactionDate": "2023-10-02 21:07:22.479000Z"
// }


// Alert.Refunded
//{
//   "acquirerAction": "Resolved",
//   "alertId": "1234",
//   "alertType": "FRAUD",
//   "amount": 65.5,
//   "arn": "******************",
//   "binDetails": {
//     "brand": "Platinum",
//     "prepaid": false,
//     "type": "debit"
//   },
//   "card": "534444******1234",
//   "cardBrand": "Mastercard",
//   "createdAt": "2023-10-02 21:07:22.479000Z",
//   "creditNotes": "some notes",
//   "creditStatus": "None",
//   "currency": "USD",
//   "descriptor": "Black Nike Shoes",
//   "externalOrder": "ch_xxxxxxxxxxxxx",
//   "funding": "Credit",
//   "id": "1234",
//   "issuer": "Bank of America",
//   "merchantId": "cb_xxxxxxxxxxx",
//   "provider": "ethoca",
//   "reasonCode": "Resolved",
//   "responseAction": "Accepted",
//   "site": "Nike Stores Alerts",
//   "subprovider": "Ethoca",
//   "transactionDate": "2023-10-02 21:07:22.479000Z"
// }

//{
//   "acquirerAction": "Resolved",
//   "alertId": "1234",
//   "alertType": "FRAUD",
//   "amount": 65.5,
//   "arn": "******************",
//   "binDetails": {
//     "brand": "Platinum",
//     "prepaid": false,
//     "type": "debit"
//   },
//   "card": "534444******1234",
//   "cardBrand": "Mastercard",
//   "createdAt": "2023-10-02 21:07:22.479000Z",
//   "creditNotes": "some notes",
//   "creditStatus": "None",
//   "currency": "USD",
//   "customerEmail": "<EMAIL>",
//   "customerId": "cus_xxxxxxxx",
//   "descriptor": "Black Nike Shoes",
//   "externalOrder": "ch_xxxxxxxxxxxxx",
//   "funding": "Credit",
//   "id": "1234",
//   "issuer": "Bank of America",
//   "merchantId": "cb_xxxxxxxxxxx",
//   "provider": "ethoca",
//   "reasonCode": "Resolved",
//   "responseAction": "Accepted",
//   "site": "Nike Stores Alerts",
//   "subprovider": "Ethoca",
//   "transactionDate": "2023-10-02 21:07:22.479000Z"
// }