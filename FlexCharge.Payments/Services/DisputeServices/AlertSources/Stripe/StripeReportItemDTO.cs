using CsvHelper.Configuration.Attributes;
using FlexCharge.Payments.Services.DisputeServices;

namespace FlexCharge.Payments.DTO;

public class StripeReportItemDTO : IDisputeReportRecord
{
    [Name("Description")] public string Description { get; set; }
    [Name("Dispute Created (UTC)")] public string AuthorizationDate { get; set; }
    [Name("Charge Created (UTC)")] public string RequestedDate { get; set; }
    [Name("Dispute Amount")] public string Amount { get; set; }
    [Name("Dispute Currency")] public string Currency { get; set; }
    [Name("Charge Amount")] public string ChargeAmount { get; set; }
    [Name("Charge ID")] public string ChargeID { get; set; }
    [Name("Card Fingerprint")] public string CardFingerprint { get; set; }
    [Name("Card Brand")] public string CardBrand { get; set; }
    [Name("Card Funding")] public string CardFunding { get; set; }
    [Name("Reason")] public string Reason { get; set; }
    [Name("Status")] public string Status { get; set; }
    [Name("Customer Email")] public string CustomerEmail { get; set; }
    [Name("Customer ID")] public string CustomerID { get; set; }
    [Name("Due By (UTC)")] public string DueByDate { get; set; }
    [Name("Past Due")] public string PastDue { get; set; }
    [Name("Has Evidence")] public string HasEvidence { get; set; }
    [Name("Submission Count")] public string SubmissionCount { get; set; }
    [Name("Is Charge Refundable")] public string IsChargeRefundable { get; set; }
    [Name("Amount Refunded")] public string AmountRefunded { get; set; }

    [Name("Is Visa Rapid Dispute Resolution")]
    public string IsVisaRapidDisputeResolution { get; set; }

    [Name("Payment Method Type")] public string PaymentMethodType { get; set; }
}