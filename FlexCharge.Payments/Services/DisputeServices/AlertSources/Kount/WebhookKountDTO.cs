using System;
using System.Collections.Generic;
using FlexCharge.Utils.JsonConverters;
using Newtonsoft.Json;

namespace FlexCharge.Payments.DTO;

public class WebhookKountDTO
{
    public string transactionCurrency { get; set; }

    [System.Text.Json.Serialization.JsonConverter(typeof(DateTimeOffsetFormatConverter))]
    //[JsonConverter(typeof(CustomDateTimeConverter), "yyyy-MM-ddTHH:mm:ssZ")]
    public DateTime transactionDateTime { get; set; }

    public decimal transactionAmount { get; set; }
    public string descriptor { get; set; }
    public string authorizationCode { get; set; }
    public string accountNumber { get; set; }
    public string transactionID { get; set; }
    public string arn { get; set; }
    public string merchantOrderID { get; set; }
    public List<KountEventDTO> events { get; set; }
    public string? Issuer { get; set; }

    public string cardAcceptorID { get; set; }
}

public class KountEventDTO
{
    [System.Text.Json.Serialization.JsonConverter(typeof(DateTimeOffsetFormatConverter))]
    //[System.Text.Json.Serialization.JsonConverter(typeof(DateTimeOffsetFormatConverter))]
    public DateTime eventDateTime { get; set; }

    public string eventType { get; set; }
    public string requestID { get; set; }
    public string disputeCode { get; set; }
}