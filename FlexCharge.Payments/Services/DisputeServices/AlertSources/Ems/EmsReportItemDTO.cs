using CsvHelper.Configuration.Attributes;
using FlexCharge.Payments.Services.DisputeServices;

namespace FlexCharge.Payments.DTO.reports;

public class EmsReportItemDTO : IDisputeReportRecord
{
    [Name("Card #")] public string CardNumber { get; set; }
    [Name("Reference #")] public string ReferenceNumber { get; set; }
    [Name("Reason")] public string Reason { get; set; }
    [Name("Auth #")] public string AuthNumber { get; set; }
    [Name("Date")] public string Date { get; set; }
    [Name("Trans Date")] public string TransDate { get; set; }
    [Name("Trans Amount")] public string Amount { get; set; }
    [Name("Amount")] public string DisputeAmount { get; set; }
    [Name("Represent")] public string Represent { get; set; }
    [Name("Last Action")] public string LastAction { get; set; }
    [Name("Days To Rebut")] public string DaysToRebut { get; set; }
}