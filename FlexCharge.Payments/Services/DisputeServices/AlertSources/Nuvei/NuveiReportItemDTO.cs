using System;
using CsvHelper.Configuration;
using CsvHelper.Configuration.Attributes;
using FlexCharge.Payments.Services.DisputeServices;

namespace FlexCharge.Payments.DTO;

public class NuveiReportItemDTO : IDisputeReportRecord
{
    [Name("Transaction ID")] public string TransactionId { get; set; }

    [Name("ARN")] public string Arn { get; set; }

    [Name("Transaction Date")] public DateTime TransactionDate { get; set; }

    [Name("Chargeback Date")] public DateTime? ChargebackDate { get; set; }

    [Name("Update Date")] public DateTime UpdateDate { get; set; }

    [Name("Dispute Due Date")] public DateTime DisputeDueDate { get; set; }

    [Name("Client Name")] public string ClientName { get; set; }

    [Name("Chargeback Type")] public string ChargebackType { get; set; }

    [Name("Status")] public string Status { get; set; }

    [Name("Chargeback Category")] public string ChargebackCategory { get; set; }

    [Name("Chargeback Reason Code")] public string ChargebackReasonCode { get; set; }

    [Name("Acquirer Bank")] public string AcquirerBank { get; set; }

    [Name("Custom Data")] public string CustomData { get; set; }

    [Name("External Token Provider")] public string ExternalTokenProvider { get; set; }

    [Name("Payment Method")] public string PaymentMethod { get; set; }

    [Name("PAN")] public string Pan { get; set; }

    [Name("Currency")] public string Currency { get; set; }

    [Name("Transaction Amount")] public string Amount { get; set; }

    [Name("Chargeback Currency")] public string ChargebackCurrency { get; set; }

    [Name("Chargeback Amount")] public string DisputeAmount { get; set; }

    [Name("Email Address")] public string EmailAddress { get; set; }

    [Name("User ID")] public string UserId { get; set; }

    [Name("Card Type")] public string CardType { get; set; }

    [Name("Billing Country")] public string BillingCountry { get; set; }

    [Name("IP Country")] public string IpCountry { get; set; }

    [Name("Is 3D")] public string Is3D { get; set; }

    [Name("Is SCA Mandated")] public string IsScAMandated { get; set; }

    [Name("PSD2 Scope")] public string Psd2Scope { get; set; }

    [Name("3D Version")] public string ThreeDVersion { get; set; }

    [Name("Authentication Flow")] public string AuthenticationFlow { get; set; }

    [Name("Is Liability Shift")] public string IsLiabilityShift { get; set; }

    [Name("External Fraud Score %")] public string ExternalFraudScore { get; set; }

    [Name("Is External MPI")] public string IsExternalMpi { get; set; }

    [Name("Lifecycle ID")] public string LifecycleId { get; set; }

    [Name("Acquirer Decision")] public string AcquirerDecision { get; set; }

    [Name("Acquirer Decision Reason")] public string AcquirerDecisionReason { get; set; }

    [Name("Bank Reference")] public string BankReference { get; set; }
    [Name("Transaction Date")] public string AuthorizationDate { get; set; }
    [Name("BIN")] public string Bin { get; set; }
    [Name("Issuer Bank")] public string Issuer { get; set; }
}

public sealed class NuveiReportItemDTOMap : ClassMap<NuveiReportItemDTO>
{
    public NuveiReportItemDTOMap()
    {
        Map(m => m.TransactionId).Name("Transaction ID");
        Map(m => m.Arn).Name("ARN");
        Map(m => m.TransactionDate).Name("Transaction Date");
        Map(m => m.ChargebackDate).Name("Chargeback Date");
        Map(m => m.UpdateDate).Name("Update Date");
        Map(m => m.DisputeDueDate).Name("Dispute Due Date");
        Map(m => m.ClientName).Name("Client Name");
        Map(m => m.ChargebackType).Name("Chargeback Type");
        Map(m => m.Status).Name("Status");
        Map(m => m.ChargebackCategory).Name("Chargeback Category");
        Map(m => m.ChargebackReasonCode).Name("Chargeback Reason Code");
        Map(m => m.AcquirerBank).Name("Acquirer Bank");
        Map(m => m.CustomData).Name("Custom Data");
        Map(m => m.ExternalTokenProvider).Name("External Token Provider");
        Map(m => m.PaymentMethod).Name("Payment Method");
        Map(m => m.Pan).Name("PAN");
        Map(m => m.Currency).Name("Currency");
        Map(m => m.Amount).Name("Transaction Amount");
        Map(m => m.ChargebackCurrency).Name("Chargeback Currency");
        Map(m => m.DisputeAmount).Name("Chargeback Amount");
        Map(m => m.EmailAddress).Name("Email Address");
        Map(m => m.UserId).Name("User ID");
        Map(m => m.CardType).Name("Card Type");
        Map(m => m.BillingCountry).Name("Billing Country");
        Map(m => m.IpCountry).Name("IP Country");
        Map(m => m.Is3D).Name("Is 3D");
        Map(m => m.IsScAMandated).Name("Is SCA Mandated");
        Map(m => m.Psd2Scope).Name("PSD2 Scope");
        Map(m => m.ThreeDVersion).Name("3D Version");
        Map(m => m.AuthenticationFlow).Name("Authentication Flow");
        Map(m => m.IsLiabilityShift).Name("Is Liability Shift");
        Map(m => m.ExternalFraudScore).Name("External Fraud Score %");
        Map(m => m.IsExternalMpi).Name("Is External MPI");
        Map(m => m.LifecycleId).Name("Lifecycle ID");
        Map(m => m.AcquirerDecision).Name("Acquirer Decision");
        Map(m => m.AcquirerDecisionReason).Name("Acquirer Decision Reason");
        Map(m => m.BankReference).Name("Bank Reference");
        Map(m => m.AuthorizationDate).Name("Transaction Date");
        Map(m => m.Bin).Name("BIN").Optional();
        Map(m => m.Issuer).Name("Issuer Bank").Optional();
    }
}