using System;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FlexCharge.Common.Cloud.Storage;
using FlexCharge.Common.Telemetry;
using FlexCharge.Utils;

namespace FlexCharge.Payments.Services.DisputeServices;

public class FileProcessor
{
    private readonly string _initialFolder;
    private readonly string _initialFileName;

    public string BucketName { get; }
    private string RootFolderToWatch { get; }

    private string ProcessingRootFolder { get; }


    public string Folder { get; private set; }
    public string FileName { get; private set; }


    public string FilePath => Folder == String.Empty ? FileName : $"{Folder}/{FileName}";

    public string InitialFilePath => $"{_initialFolder}{_initialFileName}";

    public FileProcessor(string storageName, string rootFolderToWatch, string processingRootFolder,
        string initialFolder, string initialFileName)
    {
        RootFolderToWatch = rootFolderToWatch;
        ProcessingRootFolder = processingRootFolder;

        _initialFolder = initialFolder;
        _initialFileName = initialFileName;

        BucketName = storageName;

        SetCurrentPath(initialFolder, initialFileName);
    }

    private void SetCurrentPath(string path)
    {
        Folder = Path.GetDirectoryName(path);
        FileName = Path.GetFileName(path);
    }

    private void SetCurrentPath(string initialFolder, string initialFileName)
    {
        Folder = initialFolder;
        FileName = initialFileName;
    }

    public string GetProviderProcessingRootFolder() =>
        $"{ProcessingRootFolder}";

    public string GetProcessingFolder() =>
        $"{GetProviderProcessingRootFolder()}/Processing";

    public string GetQuarantineFolder() =>
        $"{GetProviderProcessingRootFolder()}/Quarantine";

    public string GetProcessedFolder() =>
        $"{GetProviderProcessingRootFolder()}/Processed";

    public string GetInvalidFolder() =>
        $"{GetProviderProcessingRootFolder()}/Invalid";
    

    public async Task MoveToProcessingFolderAsync(ICloudStorage cloudStorage)
    {
        using var workspan = Workspan.Start<FileProcessor>();

        string sourceFolder = Folder;
        string sourceFileName = FileName;

        var filePathInProcessingFolder = await MoveFileAsync(cloudStorage, $"{Folder}{FileName}",
            GetProcessingFolder() + FileName, true);

        workspan.Log.Information(
            "File {FileName} in folder {Folder} moved to processing folder {FilePathInProcessingFolder}",
            sourceFileName, sourceFolder, filePathInProcessingFolder);
    }

    public async Task MoveToQuarantineFolderAsync(ICloudStorage cloudStorage)
    {
        using var workspan = Workspan.Start<FileProcessor>();

        string sourceFolder = Folder;
        string sourceFileName = FileName;

        var quarantinedFileName = GetFileNameWithTimeAndRandomTextPostfix(FileName);
        var filePathInQuarantineFolder = await MoveFileAsync(cloudStorage, FilePath,
            GetQuarantineFolder() + "/" + quarantinedFileName, true);

        workspan.Log.Information(
            "File {FileName} in folder {Folder} moved to quarantine folder {FilePathInQuarantineFolder}",
            sourceFileName, sourceFolder, filePathInQuarantineFolder);
    }

    public async Task MoveToProcessedFolderAsync(ICloudStorage cloudStorage)
    {
        using var workspan = Workspan.Start<FileProcessor>();

        string sourceFolder = Folder;
        string sourceFileName = FileName;

        var filePathInProcessedFolder =
            await MoveFileAsync(cloudStorage, FilePath, GetFilePathInProcessedFolder(sourceFileName), true);

        workspan.Log.Information(
            "File {FileName} in folder {Folder} moved to processed folder {FilePathInProcessedFolder}",
            sourceFileName, sourceFolder, filePathInProcessedFolder);
    }

    private string GetFilePathInProcessedFolder(string fileName)
    {
        var processedFileName = GetFileNameWithTimeAndRandomTextPostfix(fileName);
        var filePathInProcessedFolder = GetProcessedFolder() + "/" + processedFileName;
        return filePathInProcessedFolder;
    }

    public async Task MoveToInvalidFolderAsync(ICloudStorage cloudStorage)
    {
        using var workspan = Workspan.Start<FileProcessor>();

        string sourceFolder = Folder;
        string sourceFileName = FileName;

        var filePathInProcessedFolder =
            await MoveFileAsync(cloudStorage, FilePath, GetFilePathInInvalidFolder(sourceFileName), true);

        workspan.Log.Information(
            "File {FileName} in folder {Folder} moved to invalid folder {FilePathInProcessedFolder}",
            sourceFileName, sourceFolder, filePathInProcessedFolder);
    }

    private string GetFilePathInInvalidFolder(string fileName)
    {
        var processedFileName = GetFileNameWithTimeAndRandomTextPostfix(fileName);
        var filePathInProcessedFolder = GetProcessedFolder() + "/" + processedFileName;
        return filePathInProcessedFolder;
    }

    private async Task<string> MoveFileAsync(ICloudStorage cloudStorage, string sourceFilePath,
        string destinationFilePath,
        bool setAsCurrentFilePath,
        bool overwrite = true,
        bool createTargetFolderIfMissing = true)
    {
        if (createTargetFolderIfMissing)
        {
            var folderPath = Path.GetDirectoryName(destinationFilePath);
            await cloudStorage.CreateFolderIfMissingAsync(BucketName, folderPath);
        }

        await cloudStorage.MoveFileAsync(BucketName, sourceFilePath, destinationFilePath, overwrite);

        if (setAsCurrentFilePath)
        {
            SetCurrentPath(destinationFilePath);
        }

        return destinationFilePath;
    }

    public async Task<string> CreateFileCopyInProcessedFolderAsync(ICloudStorage cloudStorage, string sourceFilePath)
    {
        using var workspan = Workspan.Start<FileProcessor>();

        string sourceFolder = Path.GetDirectoryName(sourceFilePath);
        string sourceFileName = Path.GetFileName(sourceFilePath);

        var filePathInProcessedFolder = GetFilePathInProcessedFolder(sourceFileName);
        await CopyFileAsync(cloudStorage, sourceFilePath, filePathInProcessedFolder, false);

        workspan.Log.Information(
            "File {FileName} in folder {Folder} copied to processed folder {FilePathInProcessedFolder}",
            FileName, Folder, filePathInProcessedFolder);

        return filePathInProcessedFolder;
    }
    
    public async Task<string> CreateProcessedFolderAsync(ICloudStorage cloudStorage)
    {
        var processedFolder = GetProcessedFolder();
        await cloudStorage.CreateFolderIfMissingAsync(BucketName, processedFolder);
        
        return processedFolder;
    }
    
    public async Task<string> CreateQuarantineFolderAsync(ICloudStorage cloudStorage)
    {
        var quarantineFolder = GetQuarantineFolder();
        await cloudStorage.CreateFolderIfMissingAsync(BucketName, quarantineFolder);
        
        return quarantineFolder;
    }
    
    public async Task<string> CreateInvalidFolderAsync(ICloudStorage cloudStorage)
    {
        var invalidFolder = GetInvalidFolder();
        await cloudStorage.CreateFolderIfMissingAsync(BucketName, invalidFolder);
        
        return invalidFolder;
    }
    
    public async Task<string> CreateProcessingFolderAsync(ICloudStorage cloudStorage)
    {
        var processingFolder = GetProcessingFolder();
        await cloudStorage.CreateFolderIfMissingAsync(BucketName, processingFolder);

        return processingFolder;
    }
    
    public async Task<string> GetProcessingFilePathAsync(ICloudStorage cloudStorage)
    {
        var processingFolder = GetProcessingFolder();
        await cloudStorage.CreateFolderIfMissingAsync(BucketName, processingFolder);

        return $"{processingFolder}{FileName}";
    }

    private async Task CopyFileAsync(ICloudStorage cloudStorage, string sourceFilePath, string destinationFilePath,
        bool setAsCurrentFilePath,
        bool overwrite = true,
        bool createTargetFolderIfMissing = true)
    {
        if (createTargetFolderIfMissing)
        {
            var folderPath = Path.GetDirectoryName(destinationFilePath);
            await cloudStorage.CreateFolderIfMissingAsync(BucketName, folderPath);
        }

        await cloudStorage.CopyFileAsync(BucketName, sourceFilePath, destinationFilePath, overwrite);

        if (setAsCurrentFilePath)
        {
            SetCurrentPath(destinationFilePath);
        }
    }
    
    private string _filePostfixWithTimeAndRandomPart = null;

    private string GetFileNameWithTimeAndRandomTextPostfix(string fileName)
    {
        if (_filePostfixWithTimeAndRandomPart == null)
        {
            var randomFileNamePostfix = GenerateRandomFileNamePostfix(fileName);

            var utcNow = DateTime.UtcNow;

            _filePostfixWithTimeAndRandomPart = utcNow.ToString("_yyyy_MM_dd_HH_mm_ss_") + randomFileNamePostfix;
        }

        StringBuilder fileNameBuilder = new();
        fileNameBuilder.Append(Path.GetFileNameWithoutExtension(fileName));
        fileNameBuilder.Append(_filePostfixWithTimeAndRandomPart);
        fileNameBuilder.Append(Path.GetExtension(fileName));

        return fileNameBuilder.ToString();
    }

    private static string GenerateRandomFileNamePostfix(string fileName)
    {
        return UniqueIdsHelper.GeneratePseudoUniqueId(fileName);
    }

    private string AddPostfixToFileName(string filePath, string postfix)
    {
        StringBuilder fileNameBuilder = new();

        string directoryName = Path.GetDirectoryName(filePath);
        if (!string.IsNullOrWhiteSpace(directoryName))
        {
            fileNameBuilder.Append(directoryName);
            fileNameBuilder.Append("/");
        }

        fileNameBuilder.Append(Path.GetFileNameWithoutExtension(filePath));
        fileNameBuilder.Append(postfix);
        fileNameBuilder.Append(Path.GetExtension(filePath));

        return fileNameBuilder.ToString();
    }

    public async Task<long> GetFileSizeAsync(ICloudStorage cloudStorage)
    {
        return await cloudStorage.GetFileSizeAsync(BucketName, FilePath);
    }
}