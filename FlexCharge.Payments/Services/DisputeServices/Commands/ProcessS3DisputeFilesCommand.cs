using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.BackgroundJobs;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Payments.Services.DisputeServices
{
    public class ProcessS3DisputeFilesCommand : BackgroundWorkerCommand
    {
        protected override async Task ExecuteAsync(IServiceProvider serviceProvider,
            CancellationToken cancellationToken)
        {
            var reportsService = serviceProvider.GetRequiredService<IReportsService>();
            await reportsService.PollAndProcessS3DisputesFilesAsync();
        }
    }
}