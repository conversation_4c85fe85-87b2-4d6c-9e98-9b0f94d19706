using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common;
using FlexCharge.Common.Telemetry;
using FlexCharge.Payments.DTO;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Payments.Services.AlertProvidersService;

public class AlertProvidersService : IAlertProvidersService
{
    private readonly PostgreSQLDbContext _dbContext;

    public AlertProvidersService(PostgreSQLDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<AlertProvidersQueryResponseDTO> GetAlertProvidersAsync(AlertProvidersQueryRequestDTO payload,
        CancellationToken cancellationToken = default)
    {
        using var workspan = Workspan.Start<AlertProvidersService>()
            .LogEnterAndExit();

        try
        {
            var response = new AlertProvidersQueryResponseDTO();

            var query = _dbContext.AlertProviders
                .AsQueryable();
            
            if (payload.PartnerId.HasValue)
            {
                query = query.Where(x => x.PartnerId == payload.PartnerId.Value);
            }

            if (payload.IsActive.HasValue)
            {
                query = query.Where(x => x.IsActive == payload.IsActive.Value);
            }

            var alertProviders = await query
                // .OrderByDescending(x => x.CreatedOn)
                .ToPagedListAsync(payload.PageNumber, payload.PageSize);
            
            var supportedGateways = await _dbContext.SupportedGateways.ToListAsync(cancellationToken);

            response.AlertProviders = alertProviders.Select(x => new AlertProvidersQueryResponseDTO.AlertProviderDTO
            {
                Id = x.Id,
                CreatedOn = x.CreatedOn,
                Name = x.Name,
                ApiUrl = x.ApiUrl,
                PartnerId = x.PartnerId,
                IsRdrProvider = x.IsRdrProvider,
                IsEthocaProvider = x.IsEthocaProvider,
                IsChargebacksManagementProvider = x.IsChargebacksManagementProvider,
                IsSandbox = x.IsSandbox,
                Description = x.Description,
                ConnectedGateways = supportedGateways.Count(g => g.RdrProvider == x.Id || g.EthocaProvider == x.Id || g.ChargebacksManagementProvider == x.Id),
                IsActive = x.IsActive
            }).ToList();

            return response;
        }
        catch (Exception ex)
        {
            workspan.Log.Error(ex, "Failed fetching alert providers");

            throw;
        }
    }
}