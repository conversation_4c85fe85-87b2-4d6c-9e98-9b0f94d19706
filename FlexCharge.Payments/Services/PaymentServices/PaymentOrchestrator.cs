using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.DistributedLock;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.GeoServices;
using FlexCharge.Common.PostgreSql.Interceptors;
using FlexCharge.Common.Response;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Commands.Vault;
using FlexCharge.Contracts.Common;
using FlexCharge.Payments.Activities;
using FlexCharge.Payments.DistributedLock;
using FlexCharge.Payments.Domain.Payments.Validation;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.DTO.Webhooks;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.AccountUpdater;
using FlexCharge.Payments.Services.PaymentInstrumentsServices;
using FlexCharge.Payments.Services.PaymentInstrumentsServices.Models;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;
using FlexCharge.Payments.Services.PaymentServices.Models;
using FlexCharge.Payments.Services.PaymentServices.Models.ExternalTokenPayments;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using FlexCharge.Payments.Services.PaymentServices.ProcessingStrategies;
using FlexCharge.Payments.Services.PaymentServices.Providers.Checkout;
using FlexCharge.Payments.Services.TransactionMonitoring.MerchantTransactions;
using FlexCharge.PaymentsUtils;
using FlexCharge.Utils;
using MassTransit;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Merchant = FlexCharge.Payments.Entities.Merchant;
using PaymentMethodType = FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels.PaymentMethodType;
using Transaction = FlexCharge.Payments.Entities.Transaction;
using TransactionType = FlexCharge.Payments.Entities.TransactionType;

namespace FlexCharge.Payments.Services.PaymentServices;

public class PaymentOrchestrator : IPaymentOrchestrator, IDisposable
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IServiceProvider _serviceProvider;
    private readonly IDistributedLockService _distributedLockService;
    private IPaymentProvider _paymentProvider;
    private readonly IPaymentInstrumentsService _paymentInstrumentsService;
    private readonly IPublishEndpoint _publisher;
    private readonly IRequestClient<GetByVaultIdCommand> _getByVaultIdCommand;
    private readonly IBackgroundWorkerCommandQueue _backgroundWorkerCommandQueue;
    private IActivityService _activityService { get; set; }

    private IEnumerable<IPaymentProvider> _paymentProviders;
    private readonly ITransactionMonitoringService _transactionMonitoringService;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly IServiceScope _serviceScope;
    private readonly IRealTimeAccountUpdater _realTimeAccountUpdater;
    private readonly IGeoServices _geoServices;

    public PaymentOrchestrator(
        IDistributedLockService distributedLockService,
        IRequestClient<GetByVaultIdCommand> getByVaultIdCommand,
        IPublishEndpoint publisher, IBackgroundWorkerCommandQueue backgroundWorkerCommandQueue,
        IPaymentInstrumentsService paymentInstrumentsService, IActivityService activityService,
        IEnumerable<IPaymentProvider> paymentProviders,
        ITransactionMonitoringService transactionMonitoringService,
        IServiceScopeFactory serviceScopeFactory,
        IRealTimeAccountUpdater realTimeAccountUpdater, IGeoServices geoServices)
    {
        _distributedLockService = distributedLockService;
        _getByVaultIdCommand = getByVaultIdCommand;
        _publisher = publisher;
        _backgroundWorkerCommandQueue = backgroundWorkerCommandQueue;
        _paymentInstrumentsService = paymentInstrumentsService;
        _activityService = activityService;
        _paymentProviders = paymentProviders;
        _transactionMonitoringService = transactionMonitoringService;
        _serviceScopeFactory = serviceScopeFactory;
        _realTimeAccountUpdater = realTimeAccountUpdater;
        _geoServices = geoServices;

        // We want to have new scope for payment orchestrator to avoid side-effects of other services 
        // e.g. if a code that invokes payment orchestrator creates a transaction and than rolls it back
        // transactions will not be rolled back in payment orchestrator
        _serviceScope = _serviceScopeFactory.CreateScope();
        _serviceProvider = _serviceScope.ServiceProvider;
        _dbContext = _serviceProvider.GetRequiredService<PostgreSQLDbContext>();
    }

    public void Dispose()
    {
        _serviceScope?.Dispose();
    }

    #region Gateway functions

    public async Task<Gateway?> GetGatewayAsync(Guid mid, Guid? providerId,
        OrchestrationOptions options = null,
        Func<Gateway, bool> matchCondition = null,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", mid)
            .Baggage("Options", options)
            .LogEnterAndExit();

        Gateway? gateway = null;
        try
        {
            var relatedGateways = await GetRelatedGateways(mid);

            relatedGateways = GatewayFilterHelper.FilterGateways(relatedGateways, providerId, mid, options);

            gateway = relatedGateways.FirstOrDefault();

            if (gateway is null)
            {
                workspan.Log.Information("Gateway not found for mid {mid} for provider {ProviderId}",
                    mid, providerId);
            }
            else if (gateway.SupportedGateway?.Capabilities is null)
            {
                throw new FlexChargeException(
                    $"Supported Gateway {gateway.SupportedGateway?.Name} Capabilities is null");
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }


        return gateway;
    }

    public async Task<Gateway?> GetGatewayByIdAsync(Guid mid, Guid providerId,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", mid)
            .Baggage("ProviderId", providerId)
            .LogEnterAndExit();

        Gateway? gateway = null;

        try
        {
            gateway = await _dbContext.Gateways.SingleOrDefaultAsync(x => x.Id == providerId, cancellationToken: token);

            // if (result is null)
            // {
            //     throw new FlexChargeException($"Related Gateway is null");
            // }

            // if (result.SupportedGateway is null)
            // {
            //     throw new FlexChargeException($"SupportedGateway is null");
            // }

            // if (result.SupportedGateway.Capabilities is null)
            // {
            //     throw new FlexChargeException($"Supported Gateway {result.SupportedGateway.Name} Capabilities is null");
            // }

            return gateway;
        }
        catch (FlexChargeException e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }

    public async Task<Gateway?> GetGatewayAsync(Guid mid, OrchestrationOptions options,
        Func<Gateway, bool> matchCondition,
        CancellationToken token = default)
    {
        return await GetGatewayAsync(mid, null, options, matchCondition, token);
    }

    private CountryFormat GetCountryCodeFormat(SupportedGateway supportedGateway)
    {
        switch (supportedGateway.ProcessorPlatform)
        {
            case ProcessorPlatform.Adyen:
            case ProcessorPlatform.Stripe:
            case ProcessorPlatform.Braintree:
            case ProcessorPlatform.PayPal:
            case ProcessorPlatform.Square:
            case ProcessorPlatform.Worldpay:
            case ProcessorPlatform.CyberSource:
            case ProcessorPlatform.TSYS:
            case ProcessorPlatform.Rs2:
                return CountryFormat.Alpha3;
            case ProcessorPlatform.FiservNorth:
            case ProcessorPlatform.FiservOmaha:
                return CountryFormat.ISO;
            default:
                Workspan.Current.Log.Fatal("Country code format not found for ProcessorPlatform => {ProcessorPlatform}",
                    supportedGateway.ProcessorPlatform);
                return CountryFormat.Unknown;
        }
    }

    public async Task<Gateway?> AutoSelectGatewayAsync(Guid mid, OrchestrationOptions options = null,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", mid)
            .LogEnterAndExit();

        try
        {
            var relatedGateways = await GetRelatedGateways(mid);
            relatedGateways = GatewayFilterHelper.FilterGateways(relatedGateways, mid, options);

            // relatedGateways = FilterNotCompatibleProviders(relatedGateways);
            // relatedGateways = FilterBlockedProviders(options?.BlocklistedProviders, relatedGateways);
            //
            // if (options != null)
            // {
            //     var conditions = ApplyOptions(options);
            //     if (!conditions.IsEmpty())
            //         relatedGateways = relatedGateways.Where(x => conditions.Build()(x));
            // }

            var gateway = relatedGateways.FirstOrDefault(x => x.Default) ?? relatedGateways.MinBy(x => x.Order);

            if (gateway is null)
            {
                workspan.Log.Information("Gateway not found");
            }
            else if (gateway.SupportedGateway?.Capabilities is null)
            {
                throw new FlexChargeException(
                    $"Supported Gateway {gateway.SupportedGateway?.Name} Capabilities is null");
            }

            return gateway;
        }
        catch (FlexChargeException e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    //Orchestration feature for payment service to get next gateway based on previous gateway order and match condition
    public async Task<Gateway> GetNextGatewayAsync(Guid mid, OrchestrationOptions options,
        Func<Gateway, bool> matchCondition, CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", mid)
            .LogEnterAndExit();

        try
        {
            var allRelatedGateways = await GetRelatedGateways(mid);
            var filteredGateways = GatewayFilterHelper.FilterGateways(allRelatedGateways, mid, options, matchCondition)
                .ToList();

            foreach (var filteredGateway in filteredGateways)
            {
                workspan.Log.Information("Gateway {GatewayName} available GID: {Gid} Order: {Order}",
                    filteredGateway.SupportedGateway.Name, filteredGateway.Id, filteredGateway.Order);
            }

            Gateway? gateway = null;

            // Try to get next gateway based on previous gateway order
            if (options.PreviousOrder.HasValue)
            {
                workspan.Log.Information("Previous Order {PreviousOrder}", options.PreviousOrder);
                gateway = filteredGateways.FirstOrDefault(x => x.Order > options.PreviousOrder);
            }

            //if no previous gateway specified or no gateway found, use first gateway
            if (gateway == null)
            {
                gateway = filteredGateways.FirstOrDefault();
                workspan.Log.Information(
                    "No previous gateway specified or no gateway found, using first gateway {GatewayName} GID: {Gid} Order: {Order}",
                    gateway?.SupportedGateway?.Name, gateway?.Id, gateway?.Order);
            }

            if (gateway is not null && gateway?.SupportedGateway?.Capabilities is null)
            {
                throw new FlexChargeException(
                    $"Supported Gateway {gateway?.SupportedGateway?.Name} Capabilities is null");
            }

            workspan.Log.Information("Selected Gateway {GatewayName} GID: {Gid} Order: {Order}",
                gateway?.SupportedGateway?.Name, gateway?.Id, gateway?.Order);

            return gateway;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }


    public async Task<Gateway?> GetGatewayAsync(Guid mid, OrchestrationOptions options,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", mid)
            .Baggage("GatewayOrder", options?.Order);

        var gateway = options?.Order.HasValue == true
            ? await GetGatewayAsync(mid, null, options, null, token)
            : await AutoSelectGatewayAsync(mid, options, token);

        return gateway;
    }


    //Orchestration feature for payment service to get next gateway based on previous gateway order and match condition
    public async Task<Gateway> GetSupportedGateway(Guid mid, OrchestrationOptions options,
        Func<Gateway, bool> matchCondition, CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", mid)
            .LogEnterAndExit();

        try
        {
            var allRelatedGateways = await GetRelatedGateways(mid);
            var filteredGateways = GatewayFilterHelper.FilterGateways(allRelatedGateways, mid, options, matchCondition)
                .ToList();

            foreach (var filteredGateway in filteredGateways)
            {
                workspan.Log.Information("Gateway {GatewayName} available GID: {Gid} Order: {Order}",
                    filteredGateway.SupportedGateway.Name, filteredGateway.Id, filteredGateway.Order);
            }

            Gateway? gateway = null;

            // Try to get next gateway based on previous gateway order
            if (options.PreviousOrder.HasValue)
                gateway = filteredGateways.FirstOrDefault(x => x.Order > options.PreviousOrder);

            //if no previous gateway specified or no gateway found, use first gateway
            if (gateway == null)
                gateway = filteredGateways.FirstOrDefault();

            if (gateway is not null && gateway?.SupportedGateway?.Capabilities is null)
            {
                throw new FlexChargeException(
                    $"Supported Gateway {gateway.SupportedGateway.Name} Capabilities is null");
            }

            return gateway;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }

    #endregion

    public async Task DispatchWebhookEventAsync(string messageBody, IHeaderDictionary headers, IQueryCollection query,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("MessageBody", messageBody);

        foreach (var provider in _paymentProviders)
        {
            (bool CanHandle, Guid? SupportedGatewayId) response = (false, null);
            try
            {
                response = await provider.CanHandleWebhookEventAsync(messageBody, headers, token);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "Error while checking if provider can handle webhook event");
            }

            if (response.CanHandle)
            {
                var providerName = provider.CurrentPaymentProvider;
                var gatewayId = response.SupportedGatewayId!.Value;

                await _publisher.Publish(new PaymentWebhookEvent(messageBody, providerName, gatewayId));

                return;
            }
        }
    }

    public async Task<bool> HandleWebhookEventAsync(string body, string providerName, Guid gatewayId,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Body", body)
            .Baggage("ProviderName", providerName)
            .LogEnterAndExit();

        #region ProcessSubscriptionChargeResult

        async Task<bool> ProcessSubscriptionChargeUpdate(SubscriptionChargeUpdate chargeResult)
        {
            using var workspan = Workspan.Start<PaymentOrchestrator>();

            try
            {
                var subscription = await _dbContext.Subscriptions.SingleAsync(
                    x => x.ProviderSubscriptionId == chargeResult.ProviderSubscriptionId, token);

                subscription.LastPaymentSuccess = chargeResult.Success;

                var transaction = new Transaction
                {
                    ParentId = default,
                    PaymentMethodId = subscription.PaymentInstrumentId,
                    ProviderTransactionToken = chargeResult.ProviderTransactionToken,
                    Amount = chargeResult.Amount,
                    AuthorizationAmount = chargeResult.Amount,
                    Currency = chargeResult.Currency,
                    Type = nameof(TransactionType.Debit),
                    PaymentType = nameof(PaymentMethodType.CreditCard),
                    Status = chargeResult.Success
                        ? TransactionStatus.Completed
                        : TransactionStatus.Failed,

                    // ProviderName = gateway?.NameIdentifier,
                    // ProviderId = (Guid) gateway?.Id,
                    // ProcessorName = gateway?.NameIdentifier,
                    // ProcessorId = gateway?.ProcessorId,

                    OrderId = subscription.OrderId,
                    IsRecurring = true,
                    Meta = chargeResult.Meta
                };


                _dbContext.Transactions.Add(transaction);

                await _dbContext.SaveChangesAsync();

                if (chargeResult.Success)
                {
                    await _publisher.Publish(
                        new SubscriptionChargeSucceededExternalEvent(subscription.Id, chargeResult.Amount));
                }
                else
                {
                    await _publisher.Publish(
                        new SubscriptionChargeFailedExternalEvent(subscription.Id, chargeResult.Amount));
                }

                return true;
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);

                return false;
            }
        }

        #endregion

        #region ProcessSubscriptionStatusUpdateResult

        async Task<bool> ProcessSubscriptionStatusUpdate(SubscriptionStatusUpdate statusUpdate)
        {
            using var workspan = Workspan.Start<PaymentOrchestrator>();

            try
            {
                var subscription = await _dbContext.Subscriptions.SingleAsync(
                    x => x.ProviderSubscriptionId == statusUpdate.ProviderSubscriptionId, token);

                subscription.IsAlive = statusUpdate.IsAlive;

                if (!statusUpdate.IsAlive == false && subscription.CancelledAt == null)
                    subscription.CancelledAt = DateTime.UtcNow;

                // if (statusUpdate.Success.HasValue)
                //     subscription.LastPaymentSuccess = statusUpdate.Success;

                await _dbContext.SaveChangesAsync();

                if (statusUpdate.IsAlive.HasValue)
                {
                    if (statusUpdate.IsAlive == true)
                    {
                        await _publisher.Publish(
                            new SubscriptionStateUpdatedExternalEvent(statusUpdate.IsAlive.Value, subscription.Id));
                    }
                    else
                    {
                        await _publisher.Publish(new SubscriptionCancelledExternalEvent()
                        {
                            SubscriptionId = subscription.Id
                        });
                    }
                }


                return true;
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return false;
            }
        }

        #endregion

        bool processedSuccessfully = false;
        try
        {
            var provider = _paymentProviders.Single(x => x.CurrentPaymentProvider == providerName);
            var response = await provider.HandleWebhookEventAsync(body, gatewayId, token);

            var gateway =
                processedSuccessfully = response switch
                {
                    SubscriptionChargeUpdate x => await ProcessSubscriptionChargeUpdate(x),

                    SubscriptionStatusUpdate x => await ProcessSubscriptionStatusUpdate(x),
                    //SubscriptionInvoiceUpdate x => await ProcessSubscriptionInvoiceUpdate(x)
                    _ => false
                };
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            processedSuccessfully = false;
        }

        return processedSuccessfully;
    }

    public async Task RetainPaymentInstrumentAsync(Guid mid, string paymentInstrumentToken, CancellationToken token)
    {
        throw new NotImplementedException();
    }


    #region Private Methods

    private async Task<IEnumerable<Gateway>> GetRelatedGateways(Guid mid)
    {
        //Gets gateways and include supported gateways 
        var merchant = await GetMerchantAsync(mid);

        if (merchant.RelatedGateways == null || !merchant.RelatedGateways.Any())
            throw new FlexChargeException("No gateways found for merchant");

        var result = merchant.RelatedGateways
            .OrderBy(x => x.Order)
            .Where(x => x.IsActive
                        && x.SupportedGateway != null
                        && x.SupportedGateway.IsActive).ToArray();


        var notValidGateways
            = result
                .Where(x => x.SupportedGateway?.Capabilities is null).ToArray();

        if (notValidGateways.Any())
        {
            var s = string.Join(',', notValidGateways.Select(x => x.SupportedGateway.Name).ToArray());
            throw new NotValidGatewayException($"Found gateways {s} with no capabilities");
        }

        return result;
    }

    private async Task<List<SupportedGateway>> GetSupportedGateways(Guid partnerId)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("PartnerId", partnerId)
            .LogEnterAndExit();

        var result = await _dbContext.SupportedGateways
            .Where(x => x.IsActive && x.PartnerId == partnerId)
            .Include(x => x.Metrics)
            .ToListAsync();

        var notValidGateways = result
            .Where(x => x.Capabilities is null).ToArray();

        if (notValidGateways.Any())
        {
            var s = string.Join(',', notValidGateways.Select(x => x.Name).ToArray());
            throw new NotValidGatewayException($"Found gateways {s} with no capabilities");
        }

        workspan.Log.Information("Found {Count} supported gateways for partner {PartnerId}", result.Count, partnerId);

        return result;
    }

    private async Task<SupportedGateway> GetSupportedGatewayById(Guid supportedGatewayId, CancellationToken token)
    {
        var result = await _dbContext.SupportedGateways
            .Where(x => x.Id == supportedGatewayId)
            .Include(x => x.Metrics)
            .SingleOrDefaultAsync(cancellationToken: token);

        return result;
    }

    private async Task<Merchant> GetMerchantAsync(Guid mid)
    {
        var merchant = await _dbContext.Merchants
            .Include(x => x.RelatedGateways)
            .ThenInclude(x => x.SupportedGateway)
            .ThenInclude(x => x.Metrics)
            .SingleOrDefaultAsync(x => x.Mid == mid);

        ArgumentNullException.ThrowIfNull(merchant, $"Merchant id {mid} Not found");
        return merchant;
    }

    private void InitiatePaymentProvider(Gateway gateway)
    {
        if (_paymentProvider != null) return;

        var paymentProviderResolver = GetRequiredService<ServiceCollectionExtensions.PaymentProviderResolver>();

        _paymentProvider = paymentProviderResolver(gateway.SupportedGateway.NameIdentifier);
    }

    private void InitiatePaymentProvider(SupportedGateway gateway)
    {
        if (_paymentProvider != null) return;

        var paymentProviderResolver = GetRequiredService<ServiceCollectionExtensions.PaymentProviderResolver>();
        _paymentProvider = paymentProviderResolver(gateway.NameIdentifier);
    }

    private async Task<(string prefix, string suffix, string descriptor)>
        RunDescriptorBuilder(
            string bin, string descriptor, SupportedGateway gateway, CancellationToken token)
    {
        //check if visa
        var isVisa = bin.StartsWith("4");

        //check if bin is in the list of bin that need to be rewritten because
        //RDR is not supported for these bins so we want to catch them with Ethoca
        var binVisaDescriptorsPrefixRewrite = new List<string>()
        {
            "400022",
            "403163",
            "403766",
            "403784",
            "408039",
            "409451",
            "410793",
            "414720",
            "414775",
            "414776",
            "414778",
            "414779",
            "416046",
            "417903",
            "418646",
            "419002",
            "420231",
            "420767",
            "426684",
            "433077",
            "434258",
            "434769",
            "435545",
            "435546",
            "435577",
            "435983",
            "435996",
            "435997",
            "436618",
            "440393",
            "442630",
            "443264",
            "446597",
            "461608",
            "471830",
            "471927",
            "479851",
            "480209",
            "483313",
            "487042"
        };

        var supportedGateway =
            await _dbContext.SupportedGateways.SingleAsync(x => x.Id == gateway.Id,
                cancellationToken: token);

        var prefix = string.Empty;

        //if gateway mastercard descriptor set use it same for visa
        //if not use default descriptor
        if ((!isVisa && !string.IsNullOrEmpty(supportedGateway.MasterCardDescriptorPrefix)) ||
            binVisaDescriptorsPrefixRewrite.Contains(bin.GetFirst(6)))
            prefix = supportedGateway.MasterCardDescriptorPrefix;


        if (isVisa && !string.IsNullOrEmpty(supportedGateway.VisaDescriptorPrefix)
                   && !binVisaDescriptorsPrefixRewrite.Contains(bin.GetFirst(6)))
            prefix = supportedGateway.VisaDescriptorPrefix;

        var suffix = string.Empty;
        if (CapabilitiesHelper.UseDynamicDescriptorSuffix(
                null,
                gateway.Capabilities?.UseDynamicDescriptorSuffix))
        {
            suffix = RandomPasswordGenerator.GeneratePassword(
                useLowercase: false,
                useUppercase: true,
                useNumbers: true,
                useSpecial: false,
                passwordSize: 5);
        }

        var fullDescriptor = NormalizeDescriptor(descriptor, prefix, suffix);

        return (prefix, suffix, fullDescriptor);
    }

    private string NormalizeDescriptor(string descriptor, string prefix = null, string suffix = null)
    {
        if (!string.IsNullOrEmpty(descriptor))
        {
            if (descriptor.Length > 12)
                descriptor = descriptor.Substring(0, 12);
        }

        if (!string.IsNullOrEmpty(prefix))
            descriptor = $"{prefix} {descriptor}";

        if (!string.IsNullOrEmpty(suffix))
            descriptor += $" {suffix}";

        if (descriptor is {Length: > 22})
            descriptor = descriptor[..22];

        return descriptor;
    }


    private async Task StorePaymentInstrumentLocally(IAuthorizationRequest payload, CancellationToken token)
    {
        CreatePaymentInstrumentDTO paymentInstrument;
        if (string.IsNullOrEmpty(payload.Token))
        {
            paymentInstrument = await _paymentInstrumentsService.SavePaymentMethodAsync(payload.Mid,
                new CreateNewPaymentInstrumentDTO
                {
                    OwnerId = payload.PayerId ?? Guid.Empty,
                    PaymentInstrumentType = Entities.PaymentMethodType.Credit,
                    Provider = "FF",
                    First_Name = payload.CreditCard.FirstName,
                    Last_Name = payload.CreditCard.LastName,
                    Number = payload.Token,
                    Verification_Value = null,
                    Month = payload.CreditCard.Month,
                    Year = payload.CreditCard.Year,
                    Bin = payload.CreditCard.Bin,
                    Last4 = payload.CreditCard.Last4,
                    Masked = payload.CreditCard.CardNumberMasked,
                    Tokenized = true,
                    AccountLastUpdatedAt = payload.AccountLastUpdatedAt,
                }, true, false, token);
        }
        else
        {
            paymentInstrument = await _paymentInstrumentsService.SavePaymentMethodAsync(payload.Mid,
                new CreateNewPaymentInstrumentDTO
                {
                    OwnerId = payload.PayerId ?? Guid.Empty,
                    PaymentInstrumentType = Entities.PaymentMethodType.Credit,
                    Provider = "FF",
                    First_Name = payload.CreditCard.FirstName,
                    Last_Name = payload.CreditCard.LastName,
                    Number = payload.Token,
                    Month = payload.CreditCard.Month,
                    Year = payload.CreditCard.Year,
                    Bin = payload.CreditCard.Bin,
                    Last4 = payload.CreditCard.Last4,
                    Masked = payload.CreditCard.CardNumberMasked,
                    Tokenized = false,
                    AccountLastUpdatedAt = payload.AccountLastUpdatedAt
                }, true, false, token);
        }

        if (paymentInstrument == null)
            throw new Exception("Payment instrument was not saved");

        payload.PaymentInstrumentId = paymentInstrument.Id;
    }

    private async Task StorePaymentInstrumentLocally(AchTransferRequest payload, CancellationToken token)
    {
        var paymentInstrumentId = await _paymentInstrumentsService.CreateBankAccountAsync(payload.Mid,
            new CreateBankAccountDTO
            {
                OwnerId = payload.PayerId ?? Guid.Empty,
                FullName = payload.Sender.Name,
                Token = payload.Sender.ProcessorToken,
                BankName = payload.Sender.InstitutionName,
                AccountNumber = payload.Sender.AccountNumber,
                RoutingNumber = payload.Sender.RoutingNumber,
                AccountType = payload.Sender.AccountType.ToString(),
                IsVerified = payload.IsVerifiedAch,
                IsTokenized = payload.IsVerifiedAch
            });

        // if (paymentInstrument == null)
        //     throw new Exception("Payment instrument was not saved");

        payload.PaymentInstrumentId = paymentInstrumentId;
    }

    #endregion

    #region CARD PAYMENT

    public async Task<SaleResult> SaleAsync(SaleRequest payload, OrchestrationOptions options, Gateway? gateway,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", payload.Mid)
            .Baggage("OrderId", payload.OrderId)
            .Baggage("GatewayId", gateway?.Id);

        var response = new SaleResult();
        int? nextGatewayOrder = null;

        try
        {
            var merchant = await GetMerchantAsync(payload.Mid);

            await DetokenizePaymentInstrument(payload, token);

            if (options.PaymentRoutingStrategy == PaymentRoutingStrategyTypes.WeightedLoadBalancing.ToString() ||
                options.PaymentRoutingStrategy == PaymentRoutingStrategyTypes.RandomLoadBalancing.ToString())
            {
                var supportedGateway = await SetupLoadBalancer(payload.OrderId, options, merchant);

                // Set the gateway in the payload
                InitiatePaymentProvider(supportedGateway);
                payload.SupportedGateway = supportedGateway;
                payload.Gateway = null; // Clear the gateway in the payload (should be only for cascading payments)
            }
            else
            {
                gateway = options.IsCascadingPayment.HasValue && options.IsCascadingPayment.Value
                    ? await GetNextGatewayAsync(merchant.Mid, options, null, CancellationToken.None)
                    : await GetGatewayAsync(merchant.Mid, options, CancellationToken.None);

                if (gateway == null)
                    throw new GatewayNotFoundException("Gateway must be set for Authorize");

                workspan
                    .Baggage("GatewayId", gateway?.Id)
                    .Baggage("GatewayNameIdentifier", gateway?.NameIdentifier)
                    .Baggage("GatewayProcessorId", gateway?.ProcessorId)
                    .Baggage("SupportedGatewayId", gateway?.SupportedGatewayId)
                    .Baggage("SupportedGatewayNameIdentifier", gateway?.SupportedGateway?.NameIdentifier)
                    .Baggage("SupportedGatewayProcessorId", gateway?.SupportedGateway?.ProcessorId);

                await _activityService.CreateActivityAsync(
                    PaymentsOrchestrationActivities.Payments_Orchestration_StrategyApplied,
                    set: set => set
                        .CorrelationId(payload.OrderId)
                        .TenantId(merchant.Mid)
                        .Meta(meta => meta
                            .SetValue("Strategy", options.PaymentRoutingStrategy)
                            .SetValue("SelectedGatewayName", gateway.SupportedGateway.Name)
                            .SetValue("SelectedGatewayId", gateway.SupportedGateway.Id)
                        ),
                    data: new
                    {
                        OrchestrationOptions = options,
                        SelectedGateway = new {gateway.SupportedGateway.Name, gateway.SupportedGateway.Id},
                        UseWeightedLoadBalancing = false
                    });

                if (options is not null)
                    options.PreviousOrder = gateway?.Order;

                var nextGateway = await GetNextGatewayAsync(merchant.Mid, options, null, CancellationToken.None);
                nextGatewayOrder = nextGateway?.Order;

                workspan
                    .Baggage("NextGatewayId", nextGateway?.Id)
                    .Baggage("NextGatewayOrder", nextGatewayOrder);


                InitiatePaymentProvider(gateway);
                payload.Gateway = gateway;
                payload.SupportedGateway = gateway.SupportedGateway;
            }

            if (!_paymentProvider.SupportsCreditCards)
                throw new NotSupportedException("This provider does not support credit cards");

            await SetupDescriptor(payload.Descriptor, payload.CreditCard, gateway.SupportedGateway, merchant);

            var accountUpdated = await TryUpdateAccountAsync(payload, response);

            await StorePaymentInstrumentLocally(payload, token);


            #region Processing Authorization Modifiers

            if (payload.Modifiers != null)
            {
                //log modifiers
                // TODO: Remove (ReductCvv) and (ReductDevice) and (ReductAvs) and (Reduct3ds) from the log message
                if (payload.Modifiers.RedactCvv)
                {
                    workspan.Log.Information("RedactCvv is set to true (ReductCvv)");
                    payload.CreditCard.VerificationValue = null;
                }

                if (payload.Modifiers.RedactAvs)
                {
                    workspan.Log.Information("RedactAvs is set to true (ReductAvs)");
                    payload.BillingAddress.Address1 = null;
                    payload.BillingAddress.Zip = null;
                }

                if (payload.Modifiers.Redact3ds)
                {
                    workspan.Log.Information("Redact3ds is set to true (Reduct3ds)");
                    payload.ThreeDS = null;
                }

                if (payload.Modifiers.RedactSchemeTransactionId)
                {
                    workspan.Log.Information("RedactSchemeTransactionId is set to true (RedactSchemaTransactionId)");
                    payload.NetworkReferenceData = null;
                }

                if (payload.Modifiers.RedactDevice)
                {
                    workspan.Log.Information("RedactDevice is set to true (ReductDevice)");
                    payload.Device = null;
                }

                if (payload.Modifiers.RedactIp)
                {
                    workspan.Log.Information("RedactIp is set to true");
                    if (payload.Device != null)
                    {
                        payload.Device.IpAddress = null;
                    }
                }

                if (payload.Modifiers.UseBillingAsCardHolderIfMissing)
                {
                    workspan.Log.Information(
                        "UseBillingAsCardHolderIfMissing is set to true (UseBillingAsCardHolderIfMissing)");

                    if (string.IsNullOrWhiteSpace(payload.CreditCard.FirstName) &&
                        string.IsNullOrWhiteSpace(payload.CreditCard.LastName) &&
                        (payload.BillingAddress != null &&
                         (!string.IsNullOrWhiteSpace(payload.BillingAddress.FirstName) ||
                          !string.IsNullOrWhiteSpace(payload.BillingAddress.LastName))))
                    {
                        payload.CreditCard.FirstName = payload.BillingAddress.FirstName;
                        payload.CreditCard.LastName = payload.BillingAddress.LastName;
                    }
                }
            }

            #endregion

            NormalizeExpiry(payload.CreditCard);

            Stopwatch paymentProviderExecutionTime = Stopwatch.StartNew();

            response = await _paymentProvider.SaleAsync(payload, token);
            await CheckInternalResponseCodeMapping(payload, response);

            paymentProviderExecutionTime.Stop();

            var trx = new Transaction
            {
                ParentId = default,
                PaymentMethodId = payload.PaymentInstrumentId,
                ProviderTransactionToken = response.ProviderTransactionToken,
                DynamicDescriptor = payload.Descriptor?.Name,
                Amount = payload.Amount,
                AuthorizationAmount = payload.Amount,
                DiscountAmount = payload.Discount,
                Currency = payload.CurrencyCode,
                ResponseCode = response.ProviderResponseCode,
                ResponseMessage = response.ProviderResponseMessage,
                InternalResponseCode = response.InternalResponseCode,
                InternalResponseMessage = response.InternalResponseMessage,
                InternalResponseGroup = response.InternalResponseGroup,
                AvsResultCode = response.AvsCode,
                CvvResultCode = response.CvvCode,
                CavvResultCode = response.CavvCode,
                AuthorizationId = response.AuthorizationCode,
                Type = TransactionType.Debit.ToString(),
                PaymentType = nameof(PaymentMethodType.CreditCard),
                Status = response.Status.HasValue
                    ? response.Status.Value
                    : response.Success
                        ? TransactionStatus.Completed
                        : TransactionStatus.Failed,

                ProviderId = gateway?.Id ?? Guid.Empty,
                ProviderName = payload.SupportedGateway.NameIdentifier,
                ProcessorName = payload.SupportedGateway.ProcessorPlatform,
                ProcessorId = payload.SupportedGateway.ProcessorId,
                SupportedGatewayId = payload.SupportedGateway.Id,

                PayerId = payload.PayerId ?? Guid.Empty,
                SiteId = payload.SiteId ?? Guid.Empty,
                Merchant = merchant,
                OrderId = payload.OrderId,
                IsRecurring = false,
                Meta = ParseMeta(response.RawResult),
                SchemeTransactionId = response.SchemeTransactionId,
                SchemeTransactionIdUsed = response.SchemeTransactionIdUsed,
                NetworkTokenUsed = response.NetworkTokenUsed,
                AccountUpdaterUsed = accountUpdated,
            };

            await _dbContext.Transactions.AddAsync(trx, token);
            await _dbContext.SaveChangesAsync(token);

            try
            {
                if (response.Success)
                {
                    var saleEvent = new PaymentDebitedEvent
                    {
                        OrderId = payload.OrderId,
                        TransactionId = trx.Id,
                        Mid = merchant.Mid,
                        TransactionType = trx.Type.ToLower(),
                        PaymentMethodType = nameof(PaymentMethodType.CreditCard),
                        Descriptor = trx.DynamicDescriptor,
                        Description = $"Debit with card that ends with {payload.CreditCard.Number.GetLast(4)}",
                        AmountFormatted = Formatters.LongToDecimal(payload.Amount)
                            .ToString(CultureInfo.InvariantCulture),
                        Amount = trx.Amount,
                        AuthorizationAmount = trx.AuthorizationAmount,
                        DiscountAmount = trx.DiscountAmount,
                        FeeAmount = trx.FeeAmount,
                        PaymentDate = trx.CreatedOn,

                        ProviderId = gateway?.Id ?? Guid.Empty,
                        SupportedGatewayId = payload.SupportedGateway.Id,
                        Provider = payload.SupportedGateway.NameIdentifier,
                        Processor = payload.SupportedGateway.ProcessorPlatform,
                        ProcessorId = payload.SupportedGateway.ProcessorId,

                        ProviderResponseCode = trx.ResponseCode,
                        ProviderTransactionToken = trx.ProviderTransactionToken,
                        ProviderResponseDescription = trx.ResponseMessage,
                        InternalResponseCode = trx.InternalResponseCode,
                        InternalResponseMessage = trx.InternalResponseMessage,
                        InternalResponseGroup = trx.InternalResponseGroup,
                        CvvCode = trx.CvvResultCode,
                        AvsCode = trx.AvsResultCode,
                        ProcessorResponseCode = trx.ResponseCode,
                        ProcessorResponseDescription = trx.ResponseMessage,
                        NetworkTokenUsed = trx.NetworkTokenUsed,
                        Bin = trx.PaymentMethod?.Bin,
                        Last4 = trx.PaymentMethod?.Last4,
                        AccountUpdaterUsed = accountUpdated
                    };

                    await _publisher.Publish(saleEvent, token);
                    await PublishUpdatedCard(response, workspan);

                    await _activityService.CreateActivityAsync(PaymentsActivities.Payments_SalePayment_Succeeded,
                        data: saleEvent,
                        set => set
                            .CorrelationId(payload.OrderId)
                            .TenantId(payload.Mid)
                            .Meta(meta => meta
                                .TransactionReference(trx.Id)
                                .SetValue("Amount", trx.Amount)
                                .SetValue("DiscountAmount", trx.DiscountAmount)
                                .SetValue("AccountUpdaterUsed", accountUpdated)
                                .SetValue("CIT", payload.IsCit)
                                .AddProviderResponseMetadata(trx, paymentProviderExecutionTime)
                                .SetValue("PaymentInstrumentId", payload.PaymentInstrumentId)
                                .SetValue("PaymentMethodType", trx.PaymentType)
                                .SetValue("Bin", trx.PaymentMethod?.Bin)
                                .SetValue("Last4", trx.PaymentMethod?.Last4)
                            ));
                }
                else
                {
                    var paymentDebitFailedEvent = new PaymentDebitFailedEvent
                    {
                        OrderId = payload.OrderId,
                        TransactionId = trx.Id,
                        Mid = merchant.Mid,
                        TransactionType = trx.Type.ToString().ToLower(),
                        //PaymentInstrumentId = trx.PaymentMethodId,
                        Descriptor = trx.DynamicDescriptor,
                        Description = $"Debit FAILED for a card that ends with {payload.CreditCard.Number.GetLast(4)}",
                        AmountFormatted = Formatters.LongToDecimal(payload.Amount)
                            .ToString(CultureInfo.InvariantCulture),
                        Amount = trx.Amount,
                        AuthorizationAmount = trx.AuthorizationAmount,
                        DiscountAmount = trx.DiscountAmount,
                        FeeAmount = trx.FeeAmount,
                        PaymentDate = trx.CreatedOn,

                        ProviderId = gateway?.Id ?? Guid.Empty,
                        Provider = payload.SupportedGateway.NameIdentifier,
                        Processor = payload.SupportedGateway.ProcessorPlatform,
                        ProcessorId = payload.SupportedGateway.ProcessorId,
                        SupportedGatewayId = payload.SupportedGateway.Id,

                        ProviderResponseCode = trx.ResponseCode,
                        ProviderTransactionToken = trx.ProviderTransactionToken,
                        ProviderResponseDescription = trx.ResponseMessage,
                        InternalResponseCode = trx.InternalResponseCode,
                        InternalResponseMessage = trx.InternalResponseMessage,
                        InternalResponseGroup = trx.InternalResponseGroup,
                        CvvCode = trx.CvvResultCode,
                        AvsCode = trx.AvsResultCode,
                        ProcessorResponseCode = trx.ResponseCode,
                        ProcessorResponseDescription = trx.ResponseMessage,
                        NetworkTokenUsed = trx.NetworkTokenUsed,
                        AccountUpdaterUsed = accountUpdated
                    };

                    await _publisher.Publish(paymentDebitFailedEvent, token);

                    await _activityService.CreateActivityAsync(PaymentsActivities.Payments_SalePayment_Failed,
                        data: paymentDebitFailedEvent,
                        set => set
                            .CorrelationId(payload.OrderId)
                            .TenantId(payload.Mid)
                            .Meta(meta => meta
                                .TransactionReference(trx.Id)
                                .SetValue("Amount", trx.Amount)
                                .SetValue("DiscountAmount", trx.DiscountAmount)
                                .SetValue("AccountUpdaterUsed", accountUpdated)
                                .SetValue("CIT", payload.IsCit)
                                .AddProviderResponseMetadata(trx, paymentProviderExecutionTime)
                                .SetValue("PaymentInstrumentId", payload.PaymentInstrumentId)
                                .SetValue("PaymentMethodType", trx.PaymentType)
                                .SetValue("Bin", trx.PaymentMethod?.Bin)
                                .SetValue("Last4", trx.PaymentMethod?.Last4)
                            ));
                }
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e,
                    $"Publish failed");
            }

            response.TransactionId = trx.Id;
            response.ProviderTransactionToken = trx.ProviderTransactionToken;
            response.OrderId = trx.OrderId;

            response.GatewayOrder = gateway?.Order ?? 0;
            response.NextGatewayOrder = nextGatewayOrder;

            response.ProviderId = gateway?.Id ?? Guid.Empty;

            response.Provider = payload.SupportedGateway.NameIdentifier;
            response.ProcessorId = payload.SupportedGateway.ProcessorId;
            response.SupportedGatewayId = payload.SupportedGateway.Id;

            response.InternalResponseCode = trx.InternalResponseCode;
            response.InternalResponseMessage = trx.InternalResponseMessage;
            response.InternalResponseGroup = trx.InternalResponseGroup;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "Sale failed");

            await _activityService.CreateActivityAsync(PaymentsErrorActivities.Payments_SalePayment_Error,
                set => set
                    .TenantId(payload.Mid)
                    .CorrelationId(payload.OrderId)
                    .Data(e)
                    .Meta(meta => meta
                        .SetValue("Amount", payload.Amount)
                        .SetValue("DiscountAmount", payload.Discount)
                        .Error(e.Message)
                    ));

            ConvertExceptionToError(e, response);
        }

        return response;
    }

    private async Task CheckInternalResponseCodeMapping(MerchantBaseRequest payload, BaseResult response)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>();

        if (response.InternalResponseCode is null or "-1")
        {
            workspan
                .Tag("Provider", response.Provider)
                .Tag("ResponseCode", response.ProviderResponseCode)
                .Tag("ResponseMessage", response.ProviderResponseMessage)
                .Tag("Errors", response.Errors)
                .Log.Error("Internal response code mapping is missing");

            await _activityService.CreateActivityAsync(
                PaymentsErrorActivities.ResponseCodeMapping_MappingMissing, set =>
                    set
                        .CorrelationId(payload.OrderId)
                        .TenantId(payload.Mid)
                        .Data(response.Errors)
                        .Meta(meta => meta
                            .ServiceProvider(response.Provider)
                            .SetValue("ResponseCode", response.ProviderResponseCode)
                            .SetValue("ResponseMessage", response.ProviderResponseMessage))
            );
        }
    }

    private async Task<bool> TryUpdateAccountAsync(IAuthorizationRequest payload,
        IAuthorizationResult authorizationResult)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", payload.Mid)
            .Baggage("OrderId", payload.OrderId);

        try
        {
            var hasUpdatedCard = false;
            if (payload.TryUseAccountUpdater)
            {
                // check if gateway has SupportNetworkTokenPayment capability and request contains network Token
                if (payload.NetworkTokenInfo?.Token is not null
                    && CapabilitiesHelper.CanPayWithNetworkToken(
                        payload.Gateway?.Capabilities?.SupportExternalNetworkTokenPayment,
                        payload.SupportedGateway?.Capabilities?.SupportExternalNetworkTokenPayment))
                {
                    return false; //!!!
                }

                var card = new FlexCharge.Payments.Services.AccountUpdater.Models.AccountUpdaterCard()
                {
                    CardHolderName = payload.CreditCard.FirstName + " " + payload.CreditCard.LastName,
                    CardNumber = payload.CreditCard.Number,
                    ExpiryMonth = payload.CreditCard.Month,
                    ExpiryYear = payload.CreditCard.Year
                };

                var updateResponse = await _realTimeAccountUpdater.UpdateCardAsync(
                    card,
                    Guid.Parse(payload.Token),
                    payload.OrderId,
                    payload.Mid);

                if (updateResponse == null)
                {
                    // nothing to do here - account updater has been skipped or fatal error in UpdateCardAsync method
                }
                else if (updateResponse.Result != null)
                {
                    hasUpdatedCard =
                        updateResponse.Result.FlexMessage == FlexAccountUpdaterMessage.AccountNumberUpdated ||
                        updateResponse.Result.FlexMessage == FlexAccountUpdaterMessage.ExpirationUpdated;

                    if (hasUpdatedCard)
                    {
                        var newCardNumber = updateResponse.Result.AccountUpdaterCard.CardNumber;
                        payload.CreditCard.Number = newCardNumber;
                        payload.CreditCard.Month = updateResponse.Result.AccountUpdaterCard.ExpiryMonth;
                        payload.CreditCard.Year = updateResponse.Result.AccountUpdaterCard.ExpiryYear;
                        payload.CreditCard.Last4 = newCardNumber.GetLast(4);
                        payload.CreditCard.Bin = newCardNumber.GetFirst(8);
                        payload.CreditCard.CardNumberMasked = newCardNumber.Mask(6, newCardNumber.Length - 10, '*');
                        payload.AccountLastUpdatedAt = DateTime.UtcNow;
                        payload.CreditCard.VerificationValue = null;
                    }

                    workspan.Log.Information(
                        "TryUpdateAccountAsync result received: {Message}",
                        updateResponse.Result.FlexMessage);
                }
                else if (updateResponse.ProviderErrors?.Count > 0)
                {
                    workspan.Log.Information(
                        "TryUpdateAccountAsync result - provider errors: {Errors}",
                        JsonConvert.SerializeObject(updateResponse.ProviderErrors));
                }
                else if (updateResponse.FlexValidationErrors?.Count > 0)
                {
                    workspan.Log.Information(
                        "TryUpdateAccountAsync result - flex validation errors  with {Errors}",
                        JsonConvert.SerializeObject(updateResponse.FlexValidationErrors));
                }
                else
                {
                    workspan.Log.Fatal("TryUpdateAccountAsync unknown error");
                }

                authorizationResult.AccountUpdaterMessage = updateResponse?.Result?.FlexMessage;
            }

            workspan.Log.Information("TryUpdateAccountAsync result: {Result}",
                hasUpdatedCard ? "card updated" : "card not updated");

            return hasUpdatedCard;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
        }

        return false;
    }

    public async Task<AuthResult> AuthorizeAsync(AuthorizationRequest payload, OrchestrationOptions options,
        Gateway? gateway = null,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", payload.Mid)
            .Baggage("OrderId", payload.OrderId)
            .Baggage("GatewayId", gateway?.Id);

        var response = new AuthResult();
        try
        {
            var merchant = await GetMerchantAsync(payload.Mid);

            await DetokenizePaymentInstrument(payload, token);

            if (options is not null)
                options.Brand = payload.CreditCard.CardBrand;

            //Get next gateway if cascading payment
            int? nextGatewayOrder = null;

            if (options.PaymentRoutingStrategy == PaymentRoutingStrategyTypes.WeightedLoadBalancing.ToString() ||
                options.PaymentRoutingStrategy == PaymentRoutingStrategyTypes.RandomLoadBalancing.ToString())
            {
                var supportedGateway = await SetupLoadBalancer(payload.OrderId, options, merchant);

                // Set the gateway in the payload
                InitiatePaymentProvider(supportedGateway);
                payload.SupportedGateway = supportedGateway;
                payload.Gateway = null; // Clear the gateway in the payload (should be only for cascading payments)
            }
            else
            {
                gateway = options?.IsCascadingPayment.HasValue == true && options.IsCascadingPayment.Value
                    ? await GetNextGatewayAsync(merchant.Mid, options, null, CancellationToken.None)
                    : await GetGatewayAsync(merchant.Mid, options, CancellationToken.None);

                if (gateway == null)
                    throw new GatewayNotFoundException("There is no gateway available for this transaction");

                workspan
                    .Baggage("GatewayId", gateway?.Id)
                    .Baggage("GatewayNameIdentifier", gateway?.NameIdentifier)
                    .Baggage("GatewayProcessorId", gateway?.ProcessorId)
                    .Baggage("SupportedGatewayId", gateway?.SupportedGatewayId)
                    .Baggage("SupportedGatewayNameIdentifier", gateway?.SupportedGateway?.NameIdentifier)
                    .Baggage("SupportedGatewayProcessorId", gateway?.SupportedGateway?.ProcessorId);

                await _activityService.CreateActivityAsync(
                    PaymentsOrchestrationActivities.Payments_Orchestration_StrategyApplied,
                    set: set => set
                        .CorrelationId(payload.OrderId)
                        .TenantId(merchant.Mid)
                        .Meta(meta => meta
                            .SetValue("Strategy", options.PaymentRoutingStrategy)
                            .SetValue("SelectedGatewayName", gateway.SupportedGateway.Name)
                            .SetValue("SelectedGatewayId", gateway.SupportedGateway.Id)
                        ),
                    data: new
                    {
                        OrchestrationOptions = options,
                        SelectedGateway = new {gateway.SupportedGateway.Name, gateway.SupportedGateway.Id},
                        UseWeightedLoadBalancing = false
                    });

                if (options is not null)
                    options.PreviousOrder = gateway?.Order;

                var nextGateway = await GetNextGatewayAsync(merchant.Mid, options, null, CancellationToken.None);
                nextGatewayOrder = nextGateway?.Order;

                workspan
                    .Baggage("NextGatewayId", nextGateway?.Id)
                    .Baggage("NextGatewayOrder", nextGatewayOrder);


                InitiatePaymentProvider(gateway);
                payload.Gateway = gateway;
                payload.SupportedGateway = gateway.SupportedGateway;
            }

            if (!_paymentProvider.SupportsCreditCards)
                throw new FlexChargeException("GatewayError", "This provider does not support credit cards");

            if (payload.Amount <= 0)
                throw new ArgumentOutOfRangeException(nameof(payload.Amount), "Amount must be greater than zero");

            await SetupDescriptor(payload.Descriptor, payload.CreditCard, payload.SupportedGateway, merchant);

            var accountUpdaterUsed = await TryUpdateAccountAsync(payload, response);

            await StorePaymentInstrumentLocally(payload, token);

            #region Processing Authorization Modifiers

            if (payload.Modifiers != null)
            {
                //log modifiers
                // TODO: Remove (ReductCvv) and (ReductDevice) and (ReductAvs) and (Reduct3ds) from the log message
                if (payload.Modifiers.RedactCvv)
                {
                    workspan.Log.Information("RedactCvv is set to true (ReductCvv)");
                    payload.CreditCard.VerificationValue = null;
                }

                if (payload.Modifiers.RedactAvs)
                {
                    workspan.Log.Information("RedactAvs is set to true (ReductAvs)");
                    payload.BillingAddress.Address1 = null;
                    payload.BillingAddress.Zip = null;
                }

                if (payload.Modifiers.Redact3ds)
                {
                    workspan.Log.Information("Redact3ds is set to true (Reduct3ds)");
                    payload.ThreeDS = null;
                }

                if (payload.Modifiers.RedactSchemeTransactionId)
                {
                    workspan.Log.Information("RedactSchemeTransactionId is set to true (RedactSchemaTransactionId)");
                    payload.NetworkReferenceData = null;
                }

                if (payload.Modifiers.RedactDevice)
                {
                    workspan.Log.Information("RedactDevice is set to true (ReductDevice)");
                    payload.Device = null;
                }

                if (payload.Modifiers.RedactIp)
                {
                    workspan.Log.Information("RedactIp is set to true");
                    if (payload.Device != null)
                    {
                        payload.Device.IpAddress = null;
                    }
                }

                if (payload.Modifiers.UseBillingAsCardHolderIfMissing)
                {
                    workspan.Log.Information(
                        "UseBillingAsCardHolderIfMissing is set to true (UseBillingAsCardHolderIfMissing)");

                    if (string.IsNullOrWhiteSpace(payload.CreditCard.FirstName) &&
                        string.IsNullOrWhiteSpace(payload.CreditCard.LastName) &&
                        (payload.BillingAddress != null &&
                         (!string.IsNullOrWhiteSpace(payload.BillingAddress.FirstName) ||
                          !string.IsNullOrWhiteSpace(payload.BillingAddress.LastName)))
                       )
                    {
                        payload.CreditCard.FirstName = payload.BillingAddress.FirstName;
                        payload.CreditCard.LastName = payload.BillingAddress.LastName;
                    }
                }
            }

            #endregion

            NormalizeExpiry(payload.CreditCard);

            Stopwatch paymentProviderExecutionTime = Stopwatch.StartNew();

            response = await _paymentProvider.AuthorizeAsync(payload, token);
            await CheckInternalResponseCodeMapping(payload, response);

            paymentProviderExecutionTime.Stop();

            #region Save Transaction

            var trx = new Transaction
            {
                ParentId = default,
                PaymentMethodId = payload.PaymentInstrumentId,
                ProviderTransactionToken = response.ProviderTransactionToken,
                DynamicDescriptor = payload.Descriptor?.Name,
                Amount = payload.Amount,
                AuthorizationAmount = payload.Amount,
                DiscountAmount = payload.Discount,
                Currency = payload.CurrencyCode,
                ResponseCode = response.ProviderResponseCode,
                ResponseMessage = response.ProviderResponseMessage,
                InternalResponseCode = response.InternalResponseCode,
                InternalResponseMessage = response.InternalResponseMessage,
                InternalResponseGroup = response.InternalResponseGroup,
                AvsResultCode = response.AvsCode,
                CvvResultCode = response.CvvCode,
                CavvResultCode = response.CavvCode,
                AuthorizationId = response.AuthorizationCode,
                Type = TransactionType.Authorization.ToString(),
                PaymentType = nameof(PaymentMethodType.CreditCard),
                Status = response.Status.HasValue
                    ? response.Status.Value
                    : response.Success
                        ? TransactionStatus.Completed
                        : TransactionStatus.Failed,
                ProviderId = payload.Gateway?.Id ?? Guid.Empty,
                ProviderName = payload.SupportedGateway.NameIdentifier,
                ProcessorName = payload.SupportedGateway.ProcessorPlatform,
                ProcessorId = payload.SupportedGateway.ProcessorId,
                SupportedGatewayId = payload.SupportedGateway.Id,
                PayerId = payload.PayerId ?? Guid.Empty,
                SiteId = payload.SiteId ?? Guid.Empty,
                Merchant = merchant,
                OrderId = payload.OrderId,
                IsRecurring = false,
                Meta = ParseMeta(response.RawResult),
                SchemeTransactionId = response.SchemeTransactionId,
                SchemeTransactionIdUsed = response.SchemeTransactionIdUsed,
                NetworkTokenUsed = response.NetworkTokenUsed,
                AccountUpdaterUsed = accountUpdaterUsed,
            };

            await _dbContext.Transactions.AddAsync(trx, token);
            await _dbContext.SaveChangesAsync(token);

            #endregion

            #region Publish Events

            try
            {
                if (response.Success)
                {
                    var authEvent = new PaymentAuthorizedEvent
                    {
                        IsCIT = payload.IsCit,
                        OrderId = payload.OrderId,
                        TransactionId = trx.Id,
                        Mid = merchant.Mid,
                        PaymentInstrumentId = payload.PaymentInstrumentId,
                        TransactionType = nameof(TransactionType.Authorization).ToLower(),
                        PaymentMethodType = nameof(PaymentMethodType.CreditCard),
                        Descriptor = trx.DynamicDescriptor,
                        Description = $"Authorization with card that ends with {payload.CreditCard.Number.GetLast(4)}",
                        AmountFormatted = Formatters.LongToDecimal(payload.Amount)
                            .ToString(CultureInfo.InvariantCulture),
                        Amount = trx.Amount,
                        TransactionStatus = trx.Status.ToString(),
                        AuthorizationAmount = trx.AuthorizationAmount,
                        DiscountAmount = trx.DiscountAmount,
                        FeeAmount = trx.FeeAmount,
                        PaymentDate = trx.CreatedOn,

                        ProviderId = gateway?.Id,
                        Provider = payload.SupportedGateway.NameIdentifier,
                        Processor = payload.SupportedGateway.ProcessorPlatform,
                        ProcessorId = payload.SupportedGateway.ProcessorId,
                        SupportedGatewayId = payload.SupportedGateway.Id,

                        ProviderResponseCode = trx.ResponseCode,
                        ProviderTransactionToken = trx.ProviderTransactionToken,
                        ProviderResponseDescription = trx.ResponseMessage,
                        InternalResponseCode = trx.InternalResponseCode,
                        InternalResponseMessage = trx.InternalResponseMessage,
                        InternalResponseGroup = trx.InternalResponseGroup,
                        CvvCode = trx.CvvResultCode,
                        AvsCode = trx.AvsResultCode,
                        ProcessorResponseCode = trx.ResponseCode,
                        ProcessorResponseDescription = trx.ResponseMessage,
                        NetworkTokenUsed = trx.NetworkTokenUsed,
                        AccountUpdaterUsed = accountUpdaterUsed,
                        Bin = trx.PaymentMethod?.Bin,
                        Last4 = trx.PaymentMethod?.Last4,
                    };

                    await _publisher.Publish(authEvent, token);

                    await _activityService.CreateActivityAsync(
                        PaymentsActivities.Payments_AuthorizePayment_Succeeded, data: authEvent, set =>
                            set
                                .CorrelationId(payload.OrderId)
                                .TenantId(payload.Mid)
                                .Meta(meta => meta
                                    .TransactionReference(trx.Id)
                                    .SetValue("Amount", trx.Amount)
                                    .SetValue("DiscountAmount", trx.DiscountAmount)
                                    .SetValue("3DS", "false")
                                    .SetValue("AccountUpdaterUsed", accountUpdaterUsed)
                                    .SetValue("CIT", payload.IsCit)
                                    .AddProviderResponseMetadata(trx, paymentProviderExecutionTime)
                                    .SetValue("PaymentInstrumentId", payload.PaymentInstrumentId)
                                    .SetValue("PaymentMethodType", trx.PaymentType)
                                    .SetValue("Bin", trx.PaymentMethod?.Bin)
                                    .SetValue("Last4", trx.PaymentMethod?.Last4)
                                ));
                }
                else
                {
                    var authorizeFailedEvent = new PaymentAuthorizeFailedEvent
                    {
                        IsCIT = payload.IsCit,
                        OrderId = payload.OrderId,
                        TransactionId = trx.Id,
                        Mid = merchant.Mid,
                        TransactionType = nameof(TransactionType.Authorization).ToLower(),
                        PaymentMethodType = nameof(PaymentMethodType.CreditCard),
                        Descriptor = trx.DynamicDescriptor,
                        Description =
                            $"Authorization FAILED for a card that ends with {payload.CreditCard.Number.GetLast(4)}",
                        AmountFormatted = Formatters.LongToDecimal(payload.Amount)
                            .ToString(CultureInfo.InvariantCulture),
                        Amount = trx.Amount,
                        AuthorizationAmount = trx.AuthorizationAmount,
                        DiscountAmount = trx.DiscountAmount,
                        FeeAmount = trx.FeeAmount,
                        PaymentDate = trx.CreatedOn,

                        ProviderId = gateway?.Id,
                        Provider = payload.SupportedGateway.NameIdentifier,
                        Processor = payload.SupportedGateway.ProcessorPlatform,
                        ProcessorId = payload.SupportedGateway.ProcessorId,
                        SupportedGatewayId = payload.SupportedGateway.Id,

                        ProviderResponseCode = trx.ResponseCode,
                        ProviderTransactionToken = trx.ProviderTransactionToken,
                        ProviderResponseDescription = trx.ResponseMessage,
                        InternalResponseCode = trx.InternalResponseCode,
                        InternalResponseMessage = trx.InternalResponseMessage,
                        InternalResponseGroup = trx.InternalResponseGroup,
                        CvvCode = trx.CvvResultCode,
                        AvsCode = trx.AvsResultCode,
                        ProcessorResponseCode = trx.ResponseCode,
                        ProcessorResponseDescription = trx.ResponseMessage,

                        NetworkTokenUsed = trx.NetworkTokenUsed,
                        AccountUpdaterUsed = accountUpdaterUsed
                    };

                    await _publisher.Publish(authorizeFailedEvent, token);

                    await _activityService.CreateActivityAsync(
                        PaymentsActivities.Payments_AuthorizePayment_Failed, data: authorizeFailedEvent,
                        set =>
                            set
                                .CorrelationId(payload.OrderId)
                                .TenantId(payload.Mid)
                                .Meta(meta => meta
                                    .TransactionReference(trx.Id)
                                    .SetValue("Amount", trx.Amount)
                                    .SetValue("DiscountAmount", trx.DiscountAmount)
                                    .SetValue("3DS", "false")
                                    .SetValue("AccountUpdaterUsed", accountUpdaterUsed)
                                    .SetValue("CIT", payload.IsCit)
                                    .AddProviderResponseMetadata(trx, paymentProviderExecutionTime)
                                    .SetValue("PaymentInstrumentId", payload.PaymentInstrumentId)
                                    .SetValue("PaymentMethodType", trx.PaymentType)
                                    .SetValue("Bin", trx.PaymentMethod?.Bin)
                                    .SetValue("Last4", trx.PaymentMethod?.Last4)
                                ));
                }

                await PublishUpdatedCard(response, workspan);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, $"Publish failed");
            }

            #endregion

            response.PaymentInstrumentId = payload.PaymentInstrumentId;
            response.TransactionId = trx.Id;
            response.ProviderTransactionToken = trx.ProviderTransactionToken;
            response.OrderId = trx.OrderId;
            response.ProcessingStrategy = options?.PaymentRoutingStrategy;

            response.GatewayOrder = gateway?.Order ?? 0;
            response.NextGatewayOrder = nextGatewayOrder;

            response.ProviderId = gateway?.Id ?? Guid.Empty;
            response.Provider = payload.SupportedGateway?.NameIdentifier;
            response.ProcessorId = payload.SupportedGateway?.ProcessorId;
            response.SupportedGatewayId = payload.SupportedGateway.Id;

            response.InternalResponseCode = trx.InternalResponseCode;
            response.InternalResponseMessage = trx.InternalResponseMessage;
            response.InternalResponseGroup = trx.InternalResponseGroup;
        }
        catch (GatewayNotFoundException ex)
        {
            workspan.Log.Warning("Gateway not found. Code: {Code} Message: {Message}", ex.Code, ex.Message);
            ConvertExceptionToError(ex, response);
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "Authorize failed");

            await _activityService.CreateActivityAsync(PaymentsErrorActivities.Payments_AuthorizePayment_Error,
                set => set
                    .TenantId(payload.Mid)
                    .CorrelationId(payload.OrderId)
                    .Data(e)
                    .Meta(meta => meta
                        .SetValue("Amount", payload.Amount)
                        .SetValue("DiscountAmount", payload.Discount)
                        .SetValue("3DS", "false")
                        .Error(e.Message)));

            ConvertExceptionToError(e, response);
        }

        workspan.Log.Information("AuthorizeAsync response: {Response}", JsonConvert.SerializeObject(response));

        return response;
    }

    private async Task<SupportedGateway> SetupLoadBalancer(Guid correlationId, OrchestrationOptions options,
        Merchant merchant)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", merchant.Mid)
            .Baggage("OrderId", correlationId)
            .Baggage("PaymentRoutingStrategy", options.PaymentRoutingStrategy);

        //Consider optimization for basic queries ...brand, supported currency, etc
        var supportedGateways = await GetSupportedGateways(merchant.Pid);

        // enrich orchestration options with merchant specific data
        options.UsedSponsoredBanks = supportedGateways
            .Where(x => options.AlreadyTriedProviders != null
                        && options.AlreadyTriedProviders.Any(xy => xy.SupportedGatewayId == x.Id))
            .Select(x => x.SponsorBank).ToList();

        var filteredGateways = SupportedGatewayFilterHelper.FilterGateways(supportedGateways, options);

        if (!filteredGateways.SupportedGateways.Any())
        {
            var errorMessage = "Merchant has no supported gateways for this transaction";
            workspan.Log.Fatal(errorMessage);

            await _activityService.CreateActivityAsync(
                PaymentsOrchestrationActivities.Payments_Orchestration_NoSupportedGateways,
                set: set => set.CorrelationId(correlationId),
                data: new
                {
                    OrchestrationOptions = options,
                    InitialGateways = supportedGateways.Select(x => new {x.Name, x.Id}),
                    FailedFilters = filteredGateways.RejectReasonsJson.Select(x => x),
                    FilteredGateways = filteredGateways.SupportedGateways.Select(x => new {x.Name, x.Id}),
                    RelaxedFilters = filteredGateways.RemovedFiltersList.Select(x => x)
                });

            throw new GatewayNotFoundException(errorMessage);
        }

        // Calculate weights based on the metrics of the gateways
        var gatewayWeights =
            SupportedGatewayWeightCalculator.CalculateWeights(filteredGateways.SupportedGateways, options.Brand);

        var useWeightedLoadBalancing = options.PaymentRoutingStrategy ==
                                       PaymentRoutingStrategyTypes.WeightedLoadBalancing.ToString();

        // Select the gateway based on the weights
        var selectedGateway = SupportedGatewaySelector.SelectGateway(gatewayWeights, useWeightedLoadBalancing);


        // If the gateway is null, throw an exception
        if (selectedGateway == null)
            throw new GatewayNotFoundException("There is no supported gateway available for this transaction");

        await _activityService.CreateActivityAsync(
            PaymentsOrchestrationActivities.Payments_Orchestration_StrategyApplied,
            set: set => set
                .CorrelationId(correlationId)
                .TenantId(merchant.Mid)
                .Meta(meta => meta
                    .SetValue("Strategy", options.PaymentRoutingStrategy)
                    .SetValue("SelectedGatewayName", selectedGateway.Name)
                    .SetValue("SelectedGatewayId", selectedGateway.Id)
                ),
            data: new
            {
                OrchestrationOptions = options,
                InitialGateways = supportedGateways.Select(x => new {x.Name, x.Id}),
                FailedFilters = filteredGateways.RejectReasonsJson.Select(x => x),
                FilteredGateways = filteredGateways.SupportedGateways.Select(x => new {x.Name, x.Id}),
                GatewayWeights = gatewayWeights.Select(x => new {x.Key.Name, x.Key.Id, x.Value}),
                RelaxedFilters = filteredGateways.RemovedFiltersList.Select(x => x),
                SelectedGateway = new {selectedGateway.Name, selectedGateway.Id},
                UseWeightedLoadBalancing = useWeightedLoadBalancing
            });

        return selectedGateway;
    }

    // private async Task SetupDescriptor(DescriptorDTO descriptor, SaleRequestCreditCard card, Gateway gateway,
    //     Merchant merchant)
    // {
    //     var descriptorBuilderResult =
    //         await RunDescriptorBuilder(card.Bin, descriptor.Name, gateway, CancellationToken.None);
    //
    //     descriptor.Name = descriptorBuilderResult.descriptor;
    //     descriptor.Prefix = descriptorBuilderResult.prefix;
    //     descriptor.Suffix = descriptorBuilderResult.suffix;
    //
    //     //TODO: test if this doesnt effect any other provider 
    //     if (gateway.NameIdentifier is GatewayTypesConstants.Nmi_v2
    //         or GatewayTypesConstants.FlexChargeDummy)
    //     {
    //         Workspan.Current.Log.Information("PAYLOAD DESCRIPTOR: {descriptor}",
    //             JsonConvert.SerializeObject(descriptor));
    //
    //         if (CapabilitiesHelper.CanSetDescriptorAddress(
    //                 gateway.Capabilities.SupportDynamicDescriptorAddress,
    //                 gateway.SupportedGateway.Capabilities.SupportDynamicDescriptorAddress))
    //         {
    //             //Make sure Country is properly set 840/USA/US 
    //             var supportedFormat = GetCountryCodeFormat(gateway.SupportedGateway);
    //
    //             if (supportedFormat != CountryFormat.Unknown &&
    //                 _geoServices.TryGetCountry(merchant.Descriptor_Country, CountryFormat.Alpha3,
    //                     out var countryObj))
    //             {
    //                 descriptor.Country = supportedFormat switch
    //                 {
    //                     CountryFormat.Alpha2 => countryObj?.TwoLetterCode,
    //                     CountryFormat.Alpha3 => countryObj?.ThreeLetterCode,
    //                     CountryFormat.ISO => countryObj?.NumericCode,
    //                     _ => null
    //                 };
    //             }
    //             else
    //                 descriptor.Country = null;
    //
    //             if (descriptor.Country == null)
    //                 Workspan.Current.Log.Warning(
    //                     "CanSetDescriptorAddress == true but Country is not set for descriptor");
    //
    //             descriptor.Country ??= merchant.Descriptor_Country;
    //             descriptor.Address ??= merchant.Descriptor_Address;
    //             descriptor.State ??= merchant.Descriptor_State;
    //             descriptor.Postal ??= merchant.Descriptor_Postal;
    //             descriptor.Phone ??= merchant.Descriptor_Phone;
    //             descriptor.City ??= merchant.Descriptor_City;
    //         }
    //         else
    //         {
    //             descriptor.Country = null;
    //             descriptor.Address = null;
    //             descriptor.State = null;
    //             descriptor.Postal = null;
    //             descriptor.Phone = null;
    //             descriptor.City = null;
    //         }
    //
    //         if (CapabilitiesHelper.CanSetDescriptorUrl(
    //                 gateway.Capabilities.SupportDynamicDescriptorUrl,
    //                 gateway.SupportedGateway.Capabilities.SupportDynamicDescriptorUrl))
    //         {
    //             descriptor.Url ??= merchant.Descriptor_Url;
    //         }
    //         else
    //         {
    //             descriptor.Url = null;
    //         }
    //
    //         Workspan.Current.Log.Information("PAYLOAD DESCRIPTOR AFTER ADDRESS MANIPULATION: {descriptor}",
    //             JsonConvert.SerializeObject(descriptor));
    //     }
    // }

    // public async Task<AuthResult> Authorize3dAsync(AuthorizationRequest payload, Gateway gateway = null,
    //     CancellationToken token = default)
    // {
    //     using var workspan = Workspan.Start<PaymentOrchestrator>()
    //         .Baggage("OrderId", payload.OrderId)
    //         .Baggage("GatewayId", gateway?.Id);
    //
    //     var response = new AuthResult();
    //     try
    //     {
    //         var merchant = await GetMerchantAsync(payload.Mid);
    //
    //         if (gateway == null)
    //             throw new FlexChargeException("Gateway must be set and for Authorize");
    //
    //         InitiatePaymentProvider(gateway);
    //         payload.Gateway = gateway;
    //
    //         if (!_paymentProvider.SupportsCreditCards)
    //             throw new NotSupportedException("This provider does not support credit cards");
    //
    //         await DetokenizePaymentInstrument(payload, token);
    //         await StorePaymentInstrumentLocally(payload, token);
    //         await SetupDescriptor(payload.Descriptor, payload.CreditCard, gateway, merchant);
    //
    //         #region Processing Authorization Modifiers
    //
    //         if (payload.Modifiers != null)
    //         {
    //             //log modifiers
    //             // TODO: Remove (ReductCvv) and (ReductDevice) and (ReductAvs) and (Reduct3ds) from the log message
    //             if (payload.Modifiers.RedactCvv)
    //             {
    //                 workspan.Log.Information("RedactCvv is set to true (ReductCvv)");
    //                 payload.CreditCard.VerificationValue = null;
    //             }
    //
    //             if (payload.Modifiers.RedactAvs)
    //             {
    //                 workspan.Log.Information("RedactAvs is set to true (ReductAvs)");
    //                 payload.BillingAddress.Address1 = null;
    //                 payload.BillingAddress.Zip = null;
    //             }
    //
    //             if (payload.Modifiers.Redact3ds)
    //             {
    //                 workspan.Log.Information("Redact3ds is set to true (Reduct3ds)");
    //                 payload.ThreeDS = null;
    //             }
    //
    //             if (payload.Modifiers.RedactSchemeTransactionId)
    //             {
    //                 workspan.Log.Information("RedactSchemeTransactionId is set to true (RedactSchemaTransactionId)");
    //                 payload.NetworkReferenceData = null;
    //             }
    //
    //             if (payload.Modifiers.RedactDevice)
    //             {
    //                 workspan.Log.Information("RedactDevice is set to true (ReductDevice)");
    //                 payload.Device = null;
    //             }
    //
    //             if (payload.Modifiers.RedactIp)
    //             {
    //                 workspan.Log.Information("RedactIp is set to true");
    //                 if (payload.Device != null)
    //                 {
    //                     payload.Device.IpAddress = null;
    //                 }
    //             }
    //
    //             if (payload.Modifiers.UseBillingAsCardHolderIfMissing)
    //             {
    //                 workspan.Log.Information(
    //                     "UseBillingAsCardHolderIfMissing is set to true (UseBillingAsCardHolderIfMissing)");
    //
    //                 if (string.IsNullOrWhiteSpace(payload.CreditCard.FirstName) &&
    //                     string.IsNullOrWhiteSpace(payload.CreditCard.LastName) &&
    //                     (payload.BillingAddress != null &&
    //                      (!string.IsNullOrWhiteSpace(payload.BillingAddress.FirstName) ||
    //                       !string.IsNullOrWhiteSpace(payload.BillingAddress.LastName)))
    //                    )
    //                 {
    //                     payload.CreditCard.FirstName = payload.BillingAddress.FirstName;
    //                     payload.CreditCard.LastName = payload.BillingAddress.LastName;
    //                 }
    //             }
    //         }
    //
    //         #endregion
    //
    //         var accountUpdaterUsed = await TryUpdateAccountAsync(payload, response);
    //
    //         NormalizeExpiry(payload.CreditCard);
    //
    //         Stopwatch paymentProviderExecutionTime = Stopwatch.StartNew();
    //
    //         response = await _paymentProvider.AuthorizeAsync(payload, token);
    //         await CheckInternalResponseCodeMapping(payload, response);
    //
    //         paymentProviderExecutionTime.Stop();
    //
    //         var trx = new Transaction
    //         {
    //             ParentId = default,
    //             PaymentMethodId = payload.PaymentInstrumentId,
    //             ProviderTransactionToken = response.ProviderTransactionToken,
    //             DynamicDescriptor = payload.Descriptor?.Name,
    //             Amount = payload.Amount,
    //             AuthorizationAmount = payload.Amount,
    //             Currency = payload.CurrencyCode,
    //             ResponseCode = response.ProviderResponseCode,
    //             ResponseMessage = response.ProviderResponseMessage,
    //             InternalResponseCode = response.InternalResponseCode,
    //             InternalResponseMessage = response.InternalResponseMessage,
    //             InternalResponseGroup = response.InternalResponseGroup,
    //             AvsResultCode = response.AvsCode,
    //             CvvResultCode = response.CvvCode,
    //             CavvResultCode = response.CavvCode,
    //             AuthorizationId = response.AuthorizationCode,
    //             Type = TransactionType.Authorization.ToString(),
    //             PaymentType = nameof(PaymentMethodType.CreditCard),
    //             Status = response.Status.HasValue
    //                 ? response.Status.Value
    //                 : response.Success
    //                     ? TransactionStatus.Completed
    //                     : TransactionStatus.Failed,
    //             ProviderId = (Guid) gateway?.Id,
    //             ProviderName = gateway?.NameIdentifier,
    //             ProcessorName = gateway?.NameIdentifier,
    //             ProcessorId = gateway?.ProcessorId,
    //             PayerId = payload.PayerId ?? Guid.Empty,
    //             SiteId = payload.SiteId ?? Guid.Empty,
    //             Merchant = merchant,
    //             OrderId = payload.OrderId,
    //             IsRecurring = false,
    //             Meta = ParseMeta(response.RawResult),
    //             SchemeTransactionId = response.SchemeTransactionId,
    //             SchemeTransactionIdUsed = response.SchemeTransactionIdUsed,
    //             NetworkTokenUsed = response.NetworkTokenUsed,
    //             AccountUpdaterUsed = accountUpdaterUsed,
    //             SupportedGatewayId = gateway.SupportedGatewayId
    //         };
    //
    //         await _dbContext.Transactions.AddAsync(trx, token);
    //         await _dbContext.SaveChangesAsync(token);
    //
    //         try
    //         {
    //             if (response.Success)
    //             {
    //                 var authEvent = new PaymentAuthorizedEvent
    //                 {
    //                     IsCIT = payload.IsCit,
    //                     OrderId = payload.OrderId,
    //                     TransactionId = trx.Id,
    //                     Mid = merchant.Mid,
    //                     TransactionType = nameof(TransactionType.Authorization).ToLower(),
    //                     PaymentMethodType = nameof(PaymentMethodType.CreditCard),
    //                     Descriptor = trx.DynamicDescriptor,
    //                     Description = response.Success
    //                         ? $"Authorization with card that ends with {payload.CreditCard.Number.GetLast(4)}"
    //                         : $"Authorization FAILED for a card that ends with {payload.CreditCard.Number.GetLast(4)}",
    //                     AmountFormatted = Formatters.LongToDecimal(payload.Amount)
    //                         .ToString(CultureInfo.InvariantCulture),
    //                     Amount = trx.Amount,
    //                     FeeAmount = trx.FeeAmount,
    //                     PaymentDate = DateTime.UtcNow,
    //                     ProviderId = gateway?.Id,
    //                     Provider = gateway?.NameIdentifier,
    //                     ProviderResponseCode = response.ProviderResponseCode,
    //                     ProviderResponseDescription = response.ProviderResponseMessage,
    //                     InternalResponseCode = response.InternalResponseCode,
    //                     InternalResponseMessage = response.InternalResponseMessage,
    //                     InternalResponseGroup = response.InternalResponseGroup,
    //                     CvvCode = response.CvvCode,
    //                     AvsCode = response.AvsCode,
    //                     ProcessorResponseCode = default(string),
    //                     ProcessorResponseDescription = default(string),
    //                     Processor = gateway?.NameIdentifier,
    //                     ProcessorId = gateway?.ProcessorId,
    //                     NetworkTokenUsed = trx.NetworkTokenUsed,
    //                     Bin = trx.PaymentMethod?.Bin,
    //                     Last4 = trx.PaymentMethod?.Last4,
    //                     AccountUpdaterUsed = accountUpdaterUsed,
    //                 };
    //
    //                 await _publisher.Publish(authEvent, token);
    //
    //                 await _activityService.CreateActivityAsync(
    //                     PaymentsActivities.Payments_AuthorizePayment_Succeeded,
    //                     data: authEvent,
    //                     set =>
    //                         set
    //                             .CorrelationId(payload.OrderId)
    //                             .TenantId(payload.Mid)
    //                             .Meta(meta => meta
    //                                 .TransactionReference(trx.Id)
    //                                 .SetValue("Amount", trx.Amount)
    //                                 .SetValue("DiscountAmount", trx.DiscountAmount)
    //                                 .SetValue("3DS", "true")
    //                                 .SetValue("CIT", payload.IsCit)
    //                                 .SetValue("AccountUpdaterUsed", accountUpdaterUsed)
    //                                 .AddProviderResponseMetadata(trx, paymentProviderExecutionTime)
    //                             ));
    //             }
    //             else
    //             {
    //                 var paymentAuthorizeFailedEvent = new PaymentAuthorizeFailedEvent
    //                 {
    //                     IsCIT = payload.IsCit,
    //                     OrderId = payload.OrderId,
    //                     TransactionId = trx.Id,
    //                     Mid = merchant.Mid,
    //                     TransactionType = nameof(TransactionType.Authorization).ToLower(),
    //                     PaymentMethodType = nameof(PaymentMethodType.CreditCard),
    //                     Descriptor = trx.DynamicDescriptor,
    //                     Description = response.Success
    //                         ? $"Authorization with card that ends with {payload.CreditCard.Number.GetLast(4)}"
    //                         : $"Authorization FAILED for a card that ends with {payload.CreditCard.Number.GetLast(4)}",
    //                     AmountFormatted = Formatters.LongToDecimal(payload.Amount)
    //                         .ToString(CultureInfo.InvariantCulture),
    //                     Amount = trx.Amount,
    //                     FeeAmount = trx.FeeAmount,
    //                     PaymentDate = DateTime.UtcNow,
    //                     ProviderId = gateway?.Id,
    //                     Provider = gateway?.NameIdentifier,
    //                     ProviderResponseCode = response.ProviderResponseCode,
    //                     ProviderResponseDescription = response.ProviderResponseMessage,
    //                     InternalResponseCode = response.InternalResponseCode,
    //                     InternalResponseMessage = response.InternalResponseMessage,
    //                     InternalResponseGroup = response.InternalResponseGroup,
    //                     CvvCode = response.CvvCode,
    //                     AvsCode = response.AvsCode,
    //                     ProcessorResponseCode = default(string),
    //                     ProcessorResponseDescription = default(string),
    //                     Processor = gateway?.NameIdentifier,
    //                     ProcessorId = gateway?.ProcessorId,
    //                     NetworkTokenUsed = trx.NetworkTokenUsed,
    //                     AccountUpdaterUsed = accountUpdaterUsed
    //                 };
    //
    //                 await _publisher.Publish(paymentAuthorizeFailedEvent, token);
    //
    //                 await _activityService.CreateActivityAsync(
    //                     PaymentsActivities.Payments_AuthorizePayment_Failed,
    //                     data: paymentAuthorizeFailedEvent,
    //                     set => set
    //                         .CorrelationId(payload.OrderId)
    //                         .TenantId(payload.Mid)
    //                         .Meta(meta => meta
    //                             .TransactionReference(trx.Id)
    //                             .SetValue("Amount", trx.Amount)
    //                             .SetValue("DiscountAmount", trx.DiscountAmount)
    //                             .SetValue("CIT", payload.IsCit)
    //                             .SetValue("AccountUpdaterUsed", accountUpdaterUsed)
    //                             .AddProviderResponseMetadata(trx, paymentProviderExecutionTime)
    //                         ));
    //             }
    //         }
    //         catch (Exception e)
    //         {
    //             workspan.RecordFatalException(e, $"Publish failed");
    //         }
    //
    //         response.PaymentInstrumentId = payload.PaymentInstrumentId;
    //         response.TransactionId = trx.Id;
    //         response.OrderId = trx.OrderId;
    //         response.GatewayOrder = gateway?.Order ?? 0;
    //         response.ProviderId = gateway?.Id ?? Guid.Empty;
    //         response.Provider = gateway?.NameIdentifier;
    //         response.ProcessorId = gateway?.ProcessorId;
    //         response.InternalResponseCode = trx.InternalResponseCode;
    //         response.InternalResponseMessage = trx.InternalResponseMessage;
    //         response.InternalResponseGroup = trx.InternalResponseGroup;
    //     }
    //     catch (Exception e)
    //     {
    //         workspan.RecordFatalException(e, "Authorize3d failed");
    //
    //         await _activityService.CreateActivityAsync(PaymentsErrorActivities.Payments_AuthorizePayment_Error,
    //             set => set
    //                 .TenantId(payload.Mid)
    //                 .CorrelationId(payload.OrderId)
    //                 .Data(e)
    //                 .Meta(meta => meta
    //                     .SetValue("Amount", payload.Amount)
    //                     .SetValue("DiscountAmount", payload.Discount)
    //                     .SetValue("3DS", "false")
    //                     .Error(e.Message)));
    //
    //         ConvertExceptionToError(e, response);
    //     }
    //
    //     return response;
    // }

    public async Task<CapturePaymentResult> CaptureAsync(CapturePaymentRequest payload,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", payload.Mid)
            .Baggage("OrderId", payload.OrderId)
            .LogEnterAndExit();

        var merchant = await GetMerchantAsync(payload.Mid);
        if (merchant == null)
            throw new FlexChargeException("Merchant not found");

        var relatedAuthorization = await GetRelatedAuthorizationAsync(payload.TransactionId);

        if (relatedAuthorization == null)
            throw new FlexChargeException("Authorization not found");

        if (relatedAuthorization.Status != TransactionStatus.Completed)
            throw new FlexChargeException("Authorization failed. Cannot capture. blocked by FlexCharge gateway");

        var supportedGateway =
            await GetSupportedGatewayById(relatedAuthorization.SupportedGatewayId.GetValueOrDefault(), token);

        var gateway = await GetGatewayByIdAsync(payload.Mid, relatedAuthorization.ProviderId, token);

        if (supportedGateway == null)
            ArgumentNullException.ThrowIfNull("SupportedGateway must be configured for Capture");

        workspan
            .Baggage("SupportedGateway", supportedGateway?.Id)
            .Baggage("GatewayId", gateway?.Id)
            .Baggage("GatewayNameIdentifier", gateway?.NameIdentifier)
            .Baggage("GatewayProcessorId", gateway?.ProcessorId)
            .Baggage("SupportedGatewayId", supportedGateway?.Id)
            .Baggage("SupportedGatewayNameIdentifier", supportedGateway?.NameIdentifier)
            .Baggage("SupportedGatewayProcessorId", supportedGateway?.ProcessorId);

        InitiatePaymentProvider(supportedGateway);
        payload.Gateway = gateway;
        payload.SupportedGateway = supportedGateway;

        if (!_paymentProvider.SupportsCreditCards)
            throw new NotSupportedException("This provider does not support credit cards");

        var capturePaymentResult = new CapturePaymentResult();
        try
        {
            payload.CurrencyCode = relatedAuthorization.Currency;
            payload.OrderId = relatedAuthorization.OrderId;
            payload.Amount = relatedAuthorization.Amount;
            payload.TransactionToken = relatedAuthorization.ProviderTransactionToken;
            payload.Descriptor = new DescriptorDTO
            {
                Name = relatedAuthorization.DynamicDescriptor,
                Phone = relatedAuthorization.DynamicDescriptorPhone,
                Address = merchant.Descriptor_Address,
                City = merchant.Descriptor_City,
                State = merchant.Descriptor_State,
                Country = merchant.Descriptor_Country,
                Mcc = merchant.Descriptor_Mcc,
                Postal = merchant.Descriptor_Postal
            };

            Stopwatch paymentProviderExecutionTime = Stopwatch.StartNew();

            capturePaymentResult = await _paymentProvider.CaptureAsync(payload, token);

            paymentProviderExecutionTime.Stop();

            var trx = new Transaction
            {
                ParentId = relatedAuthorization.Id,
                Amount = relatedAuthorization.Amount,
                AuthorizationAmount = relatedAuthorization.AuthorizationAmount,
                DiscountAmount = relatedAuthorization.DiscountAmount,
                Currency = relatedAuthorization.Currency,
                ResponseCode = capturePaymentResult.ProviderResponseCode,
                ResponseMessage = capturePaymentResult.ProviderResponseMessage,
                DynamicDescriptor = payload.Descriptor?.Name,
                // AvsResultCode = string.Empty,
                // CvvResultCode = string.Empty,
                // CavvResultCode = string.Empty,
                Type = TransactionType.Capture.ToString(),
                PaymentType = nameof(PaymentMethodType.CreditCard),
                Status = capturePaymentResult.Status.HasValue
                    ? capturePaymentResult.Status.Value
                    : capturePaymentResult.Success
                        ? TransactionStatus.Completed
                        : TransactionStatus.Failed,
                ProviderTransactionToken = capturePaymentResult.ProviderTransactionToken,

                ProviderId = gateway?.Id ?? Guid.Empty,
                ProviderName = payload.SupportedGateway?.NameIdentifier,
                ProcessorName = payload.SupportedGateway?.ProcessorPlatform,
                ProcessorId = payload.SupportedGateway?.ProcessorId,
                SupportedGatewayId = payload.SupportedGateway?.Id,

                PaymentMethodId = relatedAuthorization.PaymentMethodId,
                PayerId = relatedAuthorization.PayerId,
                SiteId = relatedAuthorization.SiteId,
                Merchant = merchant,
                OrderId = relatedAuthorization.OrderId,
                IsRecurring = false,
                NetworkTokenUsed = relatedAuthorization.NetworkTokenUsed,
                SchemeTransactionIdUsed = relatedAuthorization.SchemeTransactionIdUsed,
                AuthorizationId = relatedAuthorization.AuthorizationId,
                Meta = ParseMeta(capturePaymentResult.RawResult)
            };

            //Saving successful transactions only
            await _dbContext.Transactions.AddAsync(trx, token);
            await _dbContext.SaveChangesAsync(token);

            try
            {
                if (capturePaymentResult.Success)
                {
                    var captureEvent = new PaymentCapturedEvent
                    {
                        OrderId = relatedAuthorization.OrderId,
                        TransactionId = trx.Id,
                        RelatedTransactionId = relatedAuthorization.Id,
                        Mid = merchant.Mid,

                        PaymentInstrumentId = trx.PaymentMethodId.Value,
                        Bin = trx.PaymentMethod?.Bin,
                        Last4 = trx.PaymentMethod?.Last4,
                        TransactionType = nameof(TransactionType.Capture).ToLower(),
                        PaymentMethodType = nameof(PaymentMethodType.CreditCard),
                        Descriptor = trx.DynamicDescriptor!,
                        Description = $"Captured trx ref: {trx.Id}",
                        AmountFormatted = Formatters.LongToDecimal(relatedAuthorization.Amount).ToString(),
                        Amount = relatedAuthorization.Amount,
                        AuthorizationAmount = relatedAuthorization.AuthorizationAmount,
                        DiscountAmount = relatedAuthorization.DiscountAmount,
                        PaymentDate = DateTime.UtcNow,

                        ProviderId = gateway?.Id ?? Guid.Empty,
                        Provider = payload.SupportedGateway?.NameIdentifier,
                        ProcessorId = payload.SupportedGateway?.ProcessorId,
                        SupportedGatewayId = payload.SupportedGateway.Id,

                        ProviderResponseCode = capturePaymentResult.ProviderResponseCode,
                        ProviderResponseDescription = capturePaymentResult.ProviderResponseMessage,
                        ProcessorResponseCode = default(string),
                        ProcessorResponseDescription = default(string),

                        NetworkTokenUsed = relatedAuthorization.NetworkTokenUsed
                    };

                    await _publisher.Publish(captureEvent, token);

                    await _activityService.CreateActivityAsync(
                        PaymentsActivities.Payments_CapturePayment_Succeeded,
                        data: captureEvent,
                        set => set
                            .CorrelationId(payload.OrderId)
                            .TenantId(payload.Mid)
                            .Meta(meta => meta
                                .TransactionReference(trx.Id)
                                .SetValue("Amount", trx.Amount)
                                .SetValue("DiscountAmount", trx.DiscountAmount)
                                .AddProviderResponseMetadata(trx, paymentProviderExecutionTime)
                                .SetValue("PaymentInstrumentId", payload.PaymentInstrumentId)
                                .SetValue("PaymentMethodType", trx.PaymentType)
                                .SetValue("Bin", trx.PaymentMethod?.Bin)
                                .SetValue("Last4", trx.PaymentMethod?.Last4)
                            ));
                }
                else
                {
                    var paymentCaptureFailedEvent = new PaymentCaptureFailedEvent
                    {
                        OrderId = relatedAuthorization.OrderId,
                        TransactionId = trx.Id,
                        RelatedTransactionId = relatedAuthorization.Id,
                        Mid = merchant.Mid,

                        PaymentInstrumentId = trx.PaymentMethodId.Value,
                        TransactionType = nameof(TransactionType.Capture).ToLower(),
                        PaymentMethodType = nameof(PaymentMethodType.CreditCard),
                        Descriptor = trx.DynamicDescriptor!,
                        Description = $"Capture FAILED for trx ref: {trx.Id}",
                        AmountFormatted = Formatters.LongToDecimal(relatedAuthorization.Amount).ToString(),
                        Amount = relatedAuthorization.Amount,
                        AuthorizationAmount = relatedAuthorization.AuthorizationAmount,
                        DiscountAmount = relatedAuthorization.DiscountAmount,
                        PaymentDate = DateTime.UtcNow,

                        ProviderId = gateway?.Id ?? Guid.Empty,
                        SupportedGatewayId = payload.SupportedGateway.Id,
                        Provider = payload.SupportedGateway.NameIdentifier,
                        ProcessorId = payload.SupportedGateway.ProcessorId,

                        ProviderResponseCode = capturePaymentResult.ProviderResponseCode,
                        ProviderResponseDescription = capturePaymentResult.ProviderResponseMessage,
                        ProcessorResponseCode = default(string),
                        ProcessorResponseDescription = default(string),
                        NetworkTokenUsed = relatedAuthorization.NetworkTokenUsed
                    };

                    await _publisher.Publish(paymentCaptureFailedEvent, token);

                    await _activityService.CreateActivityAsync(
                        PaymentsActivities.Payments_CapturePayment_Failed,
                        data: paymentCaptureFailedEvent,
                        set => set
                            .CorrelationId(payload.OrderId)
                            .TenantId(payload.Mid)
                            .Meta(meta => meta
                                .TransactionReference(trx.Id)
                                .SetValue("Amount", trx.Amount)
                                .SetValue("DiscountAmount", trx.DiscountAmount)
                                .AddProviderResponseMetadata(trx, paymentProviderExecutionTime)
                                .SetValue("PaymentInstrumentId", payload.PaymentInstrumentId)
                                .SetValue("PaymentMethodType", trx.PaymentType)
                                .SetValue("Bin", trx.PaymentMethod?.Bin)
                                .SetValue("Last4", trx.PaymentMethod?.Last4)
                            ));
                }
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "Captured event publish failed");
            }

            capturePaymentResult.PaymentInstrumentId = trx.PaymentMethodId ?? Guid.Empty;
            capturePaymentResult.TransactionId = trx.Id;

            capturePaymentResult.GatewayOrder = gateway?.Order ?? 0;
            capturePaymentResult.ProviderId = gateway?.Id ?? Guid.Empty;

            capturePaymentResult.Provider = supportedGateway?.NameIdentifier;
            capturePaymentResult.ProcessorId = supportedGateway?.ProcessorId;
            capturePaymentResult.SupportedGatewayId = supportedGateway.Id;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "Capture failed");

            await _activityService.CreateActivityAsync(PaymentsErrorActivities.Payments_CapturePayment_Error,
                set => set
                    .TenantId(payload.Mid)
                    .CorrelationId(payload.OrderId)
                    .Data(e)
                    .Meta(meta => meta
                        .TransactionReference(payload.TransactionId)
                        .SetValue("Amount", payload.Amount)
                        .SetValue("DiscountAmount", payload.Discount)
                        .Error(e.Message)));

            ConvertExceptionToError(e, capturePaymentResult);
        }

        return capturePaymentResult;
    }

    public async Task<IVerifyInstrumentResult> VerifyAsync(IVerifyInstrumentRequest payload,
        OrchestrationOptions options, Gateway gateway = null,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", payload.Mid)
            .Baggage("OrderId", payload.OrderId)
            .Baggage("GatewayId", gateway?.Id)
            .Baggage("GatewayNameIdentifier", gateway?.NameIdentifier)
            .Baggage("GatewayProcessorId", gateway?.ProcessorId)
            .Baggage("SupportedGatewayId", gateway?.SupportedGatewayId)
            .Baggage("SupportedGatewayNameIdentifier", gateway?.SupportedGateway?.NameIdentifier)
            .Baggage("SupportedGatewayProcessorId", gateway?.SupportedGateway?.ProcessorId)
            .LogEnterAndExit();

        var merchant = await GetMerchantAsync(payload.Mid);
        if (merchant == null)
            throw new FlexChargeException("Merchant not found");

        if (options.PaymentRoutingStrategy == PaymentRoutingStrategyTypes.WeightedLoadBalancing.ToString() ||
            options.PaymentRoutingStrategy == PaymentRoutingStrategyTypes.RandomLoadBalancing.ToString())
        {
            var supportedGateway = await SetupLoadBalancer(payload.OrderId, options, merchant);

            // Set the gateway in the payload
            InitiatePaymentProvider(supportedGateway);
            payload.SupportedGateway = supportedGateway;
            payload.Gateway = null; // Clear the gateway in the payload (should be only for cascading payments)
        }
        else
        {
            gateway = options.IsCascadingPayment.HasValue && options.IsCascadingPayment.Value
                ? await GetNextGatewayAsync(merchant.Mid, options, null, CancellationToken.None)
                : await GetGatewayAsync(merchant.Mid, options, CancellationToken.None);

            if (gateway == null)
                throw new GatewayNotFoundException("Gateway must be set for Authorize");

            await _activityService.CreateActivityAsync(
                PaymentsOrchestrationActivities.Payments_Orchestration_StrategyApplied,
                set: set => set
                    .CorrelationId(payload.OrderId)
                    .TenantId(merchant.Mid)
                    .Meta(meta => meta
                        .SetValue("Strategy", options.PaymentRoutingStrategy)
                        .SetValue("SelectedGatewayName", gateway.SupportedGateway.Name)
                        .SetValue("SelectedGatewayId", gateway.SupportedGateway.Id)
                    ),
                data: new
                {
                    OrchestrationOptions = options,
                    SelectedGateway = new {gateway.SupportedGateway.Name, gateway.SupportedGateway.Id},
                    UseWeightedLoadBalancing = false
                });

            if (options is not null)
                options.PreviousOrder = gateway?.Order;

            InitiatePaymentProvider(gateway);
            payload.Gateway = gateway;
            payload.SupportedGateway = gateway.SupportedGateway;
        }

        if (!_paymentProvider.SupportsCreditCards)
            throw new NotSupportedException("This provider does not support credit cards");

        if (!_paymentProvider.SupportsCreditCardVerification)
            throw new NotSupportedException("This provider does not support credit cards 0 amount verification");

        IVerifyInstrumentResult response = new VerifyInstrumentResult();
        try
        {
            await DetokenizePaymentInstrument(payload, token);

            var accountUpdaterUsed = await TryUpdateAccountAsync(payload, response);

            await StorePaymentInstrumentLocally(payload, token);

            response = await _paymentProvider.VerifyAsync(payload, token);

            await PublishUpdatedCard(response, workspan);

            var trx = new Transaction
            {
                ProviderTransactionToken = response.ProviderTransactionToken,
                AuthorizationId = response.AuthorizationCode,
                Amount = payload.Amount,
                AuthorizationAmount = payload.Amount,
                Currency = payload.CurrencyCode,
                ResponseCode = response.ProviderResponseCode,
                ResponseMessage = response.ProviderResponseMessage,
                AvsResultCode = response.AvsCode,
                CvvResultCode = response.CvvCode,
                CavvResultCode = response.CavvCode,
                Type = TransactionType.Verify.ToString(),
                PaymentType = nameof(PaymentMethodType.CreditCard),
                Status = response.Status.HasValue
                    ? response.Status.Value
                    : response.Success
                        ? TransactionStatus.Completed
                        : TransactionStatus.Failed,

                ProviderId = gateway?.Id ?? Guid.Empty,
                ProviderName = payload.SupportedGateway.NameIdentifier,
                ProcessorName = payload.SupportedGateway.ProcessorPlatform,
                ProcessorId = payload.SupportedGateway.ProcessorId,
                SupportedGatewayId = payload.SupportedGateway.Id,

                PayerId = payload.PayerId ?? Guid.Empty,
                SiteId = payload.SiteId ?? Guid.Empty,
                Merchant = merchant,
                PaymentMethodId = response.PaymentInstrumentId,
                OrderId = payload.OrderId,
                IsRecurring = false,
                Meta = ParseMeta(response.RawResult),
                SchemeTransactionId = response.SchemeTransactionId,
                SchemeTransactionIdUsed = response.SchemeTransactionIdUsed,
                NetworkTokenUsed = response.NetworkTokenUsed,
                AccountUpdaterUsed = accountUpdaterUsed
            };

            await _dbContext.Transactions.AddAsync(trx, token);
            await _dbContext.SaveChangesAsync(token);

            try
            {
                var piEvent = new PaymentInstrumentVerifiedEvent
                {
                    OrderId = payload.OrderId,
                    TransactionId = default(Guid),
                    Mid = merchant.Mid,
                    Type = TransactionType.Verify.ToString().ToLower(),
                    Descriptor = trx.DynamicDescriptor,
                    Description =
                        $"Verifying card that ends with {response.Last4Digits} was successful",
                    Timestamp = DateTime.UtcNow,
                    ProviderResponseCode = response.ProviderResponseCode,
                    ProviderResponseDescription = response.ProviderResponseMessage,
                    CvvCode = response.CvvCode,
                    AvsCode = response.AvsCode,
                    ProcessorResponseCode = default(string),
                    ProcessorResponseDescription = default(string),

                    ProviderId = (Guid) gateway?.Id,
                    Provider = payload.SupportedGateway.NameIdentifier,
                    Processor = payload.SupportedGateway.ProcessorPlatform,
                    ProcessorId = payload.SupportedGateway.ProcessorId,
                    SupportedGatewayId = payload.SupportedGateway.Id,
                };

                await _publisher.Publish(piEvent, token);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, $"Publish failed");
            }

            response.PaymentInstrumentId = payload.PaymentInstrumentId;
            response.TransactionId = trx.Id;
            response.OrderId = trx.OrderId;

            response.GatewayOrder = gateway?.Order ?? 0;
            response.ProviderId = gateway?.Id ?? Guid.Empty;
            response.Provider = payload.SupportedGateway.NameIdentifier;
            response.ProcessorId = payload.SupportedGateway.ProcessorId;
            response.SupportedGatewayId = payload.SupportedGateway.Id;

            return response;
        }
        catch (Exception e)
        {
            workspan.Log.Fatal(e, "Verify failed");
            throw;
        }
    }

    public async Task<IVerifyInstrumentResult> VerifyAmountAsync(AuthorizationRequest payload,
        OrchestrationOptions options,
        Gateway gateway = null,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", payload.Mid)
            .Baggage("OrderId", payload.OrderId)
            .Baggage("GatewayId", gateway?.Id)
            .Baggage("GatewayNameIdentifier", gateway?.NameIdentifier)
            .Baggage("GatewayProcessorId", gateway?.ProcessorId)
            .Baggage("SupportedGatewayId", gateway?.SupportedGatewayId)
            .Baggage("SupportedGatewayNameIdentifier", gateway?.SupportedGateway?.NameIdentifier)
            .Baggage("SupportedGatewayProcessorId", gateway?.SupportedGateway?.ProcessorId)
            .LogEnterAndExit();

        var merchant = await GetMerchantAsync(payload.Mid);
        if (merchant == null)
            throw new FlexChargeException("Merchant not found");

        if (options.PaymentRoutingStrategy == PaymentRoutingStrategyTypes.WeightedLoadBalancing.ToString() ||
            options.PaymentRoutingStrategy == PaymentRoutingStrategyTypes.RandomLoadBalancing.ToString())
        {
            var supportedGateway = await SetupLoadBalancer(payload.OrderId, options, merchant);

            // Set the gateway in the payload
            InitiatePaymentProvider(supportedGateway);
            payload.SupportedGateway = supportedGateway;
            payload.Gateway = null; // Clear the gateway in the payload (should be only for cascading payments)
        }
        else
        {
            gateway = options.IsCascadingPayment.HasValue && options.IsCascadingPayment.Value
                ? await GetNextGatewayAsync(merchant.Mid, options, null, CancellationToken.None)
                : await GetGatewayAsync(merchant.Mid, options, CancellationToken.None);

            if (gateway == null)
                throw new GatewayNotFoundException("Gateway must be set for Authorize");

            await _activityService.CreateActivityAsync(
                PaymentsOrchestrationActivities.Payments_Orchestration_StrategyApplied,
                set: set => set
                    .CorrelationId(payload.OrderId)
                    .TenantId(merchant.Mid)
                    .Meta(meta => meta
                        .SetValue("Strategy", options.PaymentRoutingStrategy)
                        .SetValue("SelectedGatewayName", gateway.SupportedGateway.Name)
                        .SetValue("SelectedGatewayId", gateway.SupportedGateway.Id)
                    ),
                data: new
                {
                    OrchestrationOptions = options,
                    SelectedGateway = new {gateway.SupportedGateway.Name, gateway.SupportedGateway.Id},
                    UseWeightedLoadBalancing = false
                });

            if (options is not null)
                options.PreviousOrder = gateway?.Order;

            InitiatePaymentProvider(gateway);
            payload.Gateway = gateway;
            payload.SupportedGateway = gateway.SupportedGateway;
        }

        var verifyAmountResult = new VerifyInstrumentResult();
        try
        {
            var accountUpdaterUsed = await TryUpdateAccountAsync(payload, verifyAmountResult);
            var authorizeAsync = await this.AuthorizeAsync(payload, options, gateway, token);
            if (authorizeAsync.Success)
            {
                // Calling Void in thread pool to speedup processing
                _backgroundWorkerCommandQueue.Enqueue(new VoidPaymentCommand(authorizeAsync.TransactionId,
                    payload.Mid,
                    payload.OrderId, payload.Amount, payload.CurrencyCode));
            }
            else
            {
                workspan.AddError("ERRORS: {Error}",
                    JsonConvert.SerializeObject(authorizeAsync));
                verifyAmountResult.AddErrorWithCode(authorizeAsync.ProviderResponseMessage,
                    authorizeAsync.ProviderResponseCode);
            }

            verifyAmountResult.CvvCode = authorizeAsync.CvvCode;
            verifyAmountResult.AvsCode = authorizeAsync.AvsCode;
            verifyAmountResult.ProcessorId = authorizeAsync.ProcessorId;
            verifyAmountResult.ProviderId = authorizeAsync.ProviderId;
            verifyAmountResult.Provider = authorizeAsync.Provider;
            verifyAmountResult.TransactionId = authorizeAsync.TransactionId;
            verifyAmountResult.PaymentInstrumentId = payload.PaymentInstrumentId;
            verifyAmountResult.PaymentInstrumentId = payload.PaymentInstrumentId;
            verifyAmountResult.ProviderResponseCode = authorizeAsync.ProviderResponseCode;
            verifyAmountResult.NetworkTokenUsed = authorizeAsync.NetworkTokenUsed;
            return verifyAmountResult;
        }
        catch (Exception e)
        {
            workspan.Log.Fatal(e, "VerifyAmount failed");
            throw;
        }
    }

    public async Task<ICreditPaymentResult> CreditAsync(ICreditPaymentRequest payload, Gateway gateway = null,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", payload.Mid)
            .Baggage("OrderId", payload.OrderId)
            .Baggage("TransactionId", payload.TransactionId)
            .Baggage("GatewayId", gateway?.Id)
            .Baggage("GatewayNameIdentifier", gateway?.NameIdentifier)
            .Baggage("GatewayProcessorId", gateway?.ProcessorId)
            .Baggage("SupportedGatewayId", gateway?.SupportedGatewayId)
            .Baggage("SupportedGatewayNameIdentifier", gateway?.SupportedGateway?.NameIdentifier)
            .Baggage("SupportedGatewayProcessorId", gateway?.SupportedGateway?.ProcessorId)
            .Request(payload)
            .LogEnterAndExit();

        try
        {
            bool tryStandaloneCredit = false;

            var merchant = await GetMerchantAsync(payload.Mid);
            if (merchant == null)
                throw new FlexChargeException("Merchant not found");

            var relatedTransaction = await GetRelatedTransactionAsync(payload.TransactionId);
            payload.CurrencyCode = relatedTransaction.Currency;
            payload.ProviderTransactionToken = relatedTransaction.ProviderTransactionToken;

            var supportedGateway =
                await GetSupportedGatewayById(relatedTransaction.SupportedGatewayId.GetValueOrDefault(), token);

            workspan
                .Baggage("SupportedGatewayId", supportedGateway?.Id)
                .Baggage("SupportedGatewayNameIdentifier", supportedGateway?.NameIdentifier)
                .Baggage("SupportedGatewayProcessorId", supportedGateway?.ProcessorId);

            //looking for recovery fallback gateway for credit
            if (supportedGateway == null)
            {
                tryStandaloneCredit = true;
                workspan.Log.Warning("No supported gateway found. Trying to use standalone credit");
            }

            if (!tryStandaloneCredit)
            {
                workspan.Log.Information("Credit started");

                return await CreditImplementationAsync(
                    payload,
                    false,
                    supportedGateway,
                    relatedTransaction,
                    gateway,
                    merchant,
                    token);
            }
            else
            {
                workspan.Log.Information("Standalone credit started");

                return await StandaloneCreditAsync(payload, relatedTransaction, token);
            }
        }
        catch (Exception e)
        {
            if (e is FlexValidationException)
            {
                workspan.Log.Warning(e, "Credit failed");
            }
            else
            {
                workspan.RecordFatalException(e, "Credit failed");
            }

            await _activityService.CreateActivityAsync(PaymentsErrorActivities.Payments_CreditPayment_Error,
                set => set
                    .TenantId(payload.Mid)
                    .CorrelationId(payload.OrderId)
                    .Data(e)
                    .Meta(meta => meta
                        .TransactionReference(payload.TransactionId)
                        .SetValue("Amount", payload.Amount)
                        .Error(e.Message)));

            throw;
        }
    }

    public async Task<ICreditPaymentResult> StandaloneCreditAsync(ICreditPaymentRequest payload,
        Transaction relatedTransaction = null,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", payload.Mid)
            .Baggage("OrderId", payload.OrderId)
            .Baggage("TransactionId", payload.TransactionId)
            .Baggage("SupportedGatewayId", payload.SupportedGatewayId)
            .Request(payload)
            .LogEnterAndExit();

        try
        {
            if (relatedTransaction == null)
            {
                relatedTransaction = await GetRelatedTransactionAsync(payload.TransactionId);
            }
            else
            {
                if (relatedTransaction.Id != payload.TransactionId)
                    throw new FlexChargeException("TransactionId does not match related transaction");
            }

            var merchant = await _dbContext.Merchants.FirstAsync(m => m.Mid == payload.Mid);

            workspan
                .Baggage("Pid", merchant.Pid);

            SupportedGateway supportedGateway;

            if (payload.SupportedGatewayId != null)
            {
                supportedGateway = await _dbContext.SupportedGateways.SingleOrDefaultAsync(g =>
                    g.Id == payload.SupportedGatewayId);

                if (supportedGateway == null)
                    throw new FlexChargeException("Specified standalone credit gateway not found");

                if (supportedGateway.Capabilities.SupportStandaloneCredit != true)
                    throw new FlexChargeException("Specified gateway does not support standalone credit");

                if (!supportedGateway.IsActive)
                    throw new FlexChargeException("Specified gateway is not active");
            }
            else
            {
                bool useSandboxSupportedGateway = !EnvironmentHelper.IsInProduction;

                supportedGateway = await _dbContext.SupportedGateways.FirstOrDefaultAsync(g =>
                    g.Capabilities.SupportStandaloneCredit == true &&
                    g.PartnerId == merchant.Pid &&
                    g.Sandbox == useSandboxSupportedGateway &&
                    g.IsActive
                );

                if (supportedGateway == null)
                    throw new FlexChargeException("No active standalone credit gateway found");

                workspan
                    .Baggage("SupportedGatewayId", supportedGateway.Id);
            }

            // !!!Attention!!! This check is very important !!!
            // We cannot allow to use a gateway that does not belong to the partner
            // because it can be used to credit any transaction, even if the initial funds was received outside of our system
            if (supportedGateway.PartnerId != merchant.Pid)
                throw new FlexChargeException("Standalone credit gateway does not belong to the partner");


            return await CreditImplementationAsync(payload, true, supportedGateway, relatedTransaction, null, merchant,
                token);
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "Standalone credit failed");

            await _activityService.CreateActivityAsync(PaymentsErrorActivities.Payments_CreditPayment_Error,
                set => set
                    .TenantId(payload.Mid)
                    .CorrelationId(payload.OrderId)
                    .Data(e)
                    .Meta(meta => meta
                        .TransactionReference(payload.TransactionId)
                        .SetValue("Amount", payload.Amount)
                        .Error(e.Message)));

            throw;
        }
    }


    private async Task<ICreditPaymentResult> CreditImplementationAsync(ICreditPaymentRequest payload,
        bool standaloneRefund,
        SupportedGateway supportedGateway,
        Transaction relatedTransaction,
        Gateway gateway = null,
        Merchant merchant = null,
        CancellationToken token = default)
    {
        var workspan = Workspan.Current!;

        workspan
            .Baggage("SupportedGatewayId", supportedGateway.Id)
            .Baggage("StandaloneRefund", standaloneRefund)
            .Baggage("Provider", supportedGateway.NameIdentifier)
            .Baggage("ProcessorId", supportedGateway.ProcessorId)
            .LogEnterAndExit();


        if (!standaloneRefund)
        {
            payload.Gateway = gateway;

            workspan
                .Baggage("GatewayId", gateway?.Id);
        }
        else
        {
            payload.Gateway = null; // Standalone credit does not require gateway
        }

        await using var @lock = await _distributedLockService
            .AcquireLockAsync(LockKeyFactory.CreateRefundLockKey(payload.OrderId),
                TimeSpan.FromMinutes(1),
                maxRetryDuration: TimeSpan.FromMinutes(1));

        // Locking database transaction for refund
        await using var lockingTransaction = await _dbContext.Database.BeginTransactionAsync();

        // Using SelectForUpdateBlockingTag to ensure that there is no double refund
        var lockedTransactionToRefund = await _dbContext
            .Transactions
            .TagWith(SelectForUpdateCommandInterceptor.SelectForUpdateBlockingTag)
            .SingleOrDefaultAsync(t => t.Id == payload.TransactionId, token);

        ICreditPaymentResult creditPaymentResult = null;

        await DetokenizePaymentInstrument(payload, token);

        _paymentProvider = null;

        InitiatePaymentProvider(supportedGateway);

        if (!_paymentProvider.SupportsCreditCards)
            throw new FlexValidationException(ValidationCodes.ProviderNotSupportCreditCards,
                ValidationCodes.GetMessage(ValidationCodes.ProviderNotSupportCreditCards));

        var amount = await ValidateAndCorrectRefundAmountAsync(payload.Amount, relatedTransaction, supportedGateway);
        payload.Amount = amount;
        payload.SupportedGateway = supportedGateway;
        payload.SupportedGatewayId = supportedGateway?.Id;

        Stopwatch paymentProviderExecutionTime = Stopwatch.StartNew();

        string providerName, processorId, processorName;
        if (standaloneRefund)
        {
            #region Standalone Refund

            workspan.Log.Information("Standalone Refund started");


            creditPaymentResult = await _paymentProvider!.StandaloneCreditAsync(payload, supportedGateway, token);

            providerName = supportedGateway.NameIdentifier;
            processorId = supportedGateway.ProcessorId;
            processorName = supportedGateway.NameIdentifier;

            if (creditPaymentResult.Success)
            {
                workspan.Log.Information("Standalone Refund Success");
                creditPaymentResult.SupportedGatewayId = supportedGateway.Id;
            }
            else
            {
                workspan.Log.Information("Standalone Refund Failed ");
            }

            #endregion
        }
        else
        {
            #region Regular Refund

            workspan.Log.Information("Regular Refund Started");

            providerName = gateway?.NameIdentifier;
            processorId = gateway?.ProcessorId;
            processorName = gateway?.NameIdentifier;

            creditPaymentResult = await _paymentProvider.CreditAsync(payload, token);

            if (creditPaymentResult.Success)
            {
                workspan.Log.Information("Regular Refund Succeeded");
            }
            else
            {
                workspan.Log.Information("Regular Refund Failed");
            }

            #endregion
        }

        paymentProviderExecutionTime.Stop();

        string transactionType = payload switch
        {
            {IsPredispute: true} => nameof(TransactionType.RefundPreDispute),
            {IsConsumerRequestedRefund: true} => nameof(TransactionType.RefundByConsumer),
            {IsEarlyWarningRequestedRefund: true} => nameof(TransactionType.RefundByEarlyWarning),
            _ when standaloneRefund => nameof(TransactionType.Credit),
            _ => nameof(TransactionType.Refund)
        };

        if (creditPaymentResult?.ProviderResponseCode == "VOID")
        {
            if (payload.IsConsumerRequestedRefund)
            {
                transactionType = nameof(TransactionType.VoidedByConsumer);
            }
            else
            {
                transactionType = nameof(TransactionType.Void);
            }
        }

        var trx = new Transaction
        {
            ParentId = standaloneRefund ? default : relatedTransaction.Id, //todo-discuss
            PaymentMethodId =
                relatedTransaction
                    .PaymentMethodId, // standaloneRefund ? payload.PaymentInstrumentId : relatedTransaction.PaymentMethodId,
            ProviderTransactionToken = creditPaymentResult?.ProviderTransactionToken,
            Amount = payload.Amount,
            AuthorizationAmount = payload.Amount,
            Currency = payload.CurrencyCode,
            ResponseCode = creditPaymentResult?.ProviderResponseCode,
            ResponseMessage = creditPaymentResult?.ProviderResponseMessage,
            DynamicDescriptor = standaloneRefund ? null : relatedTransaction.DynamicDescriptor,
            Type = transactionType,
            PaymentType = nameof(PaymentMethodType.CreditCard),
            Note = payload.Reason,
            Status = creditPaymentResult?.Status.HasValue == true
                ? creditPaymentResult.Status.Value
                : creditPaymentResult?.Success == true
                    ? TransactionStatus.Completed
                    : TransactionStatus.Failed,

            ProviderId = gateway?.Id ?? Guid.Empty,
            ProviderName = supportedGateway?.NameIdentifier,
            ProcessorName = supportedGateway?.NameIdentifier,
            ProcessorId = supportedGateway?.ProcessorId,
            SupportedGatewayId = supportedGateway.Id,

            PayerId = payload.PayerId ?? Guid.Empty,
            SiteId = payload.SiteId ?? Guid.Empty,
            Merchant = merchant,
            OrderId = relatedTransaction.OrderId,
            IsRecurring = false,
            //NetworkTokenUsed = standaloneRefund.  relatedTransaction.NetworkTokenUsed, todo-later check
            Meta = ParseMeta(creditPaymentResult?.RawResult),
        };

        await _dbContext.Transactions.AddAsync(trx, token);
        await _dbContext.SaveChangesAsync(token);

        await lockingTransaction.CommitAsync(token);


        await PublishCreditEvents(token, creditPaymentResult, trx, merchant, trx, paymentProviderExecutionTime,
            payload.IsPredispute, payload.IsConsumerRequestedRefund);

        creditPaymentResult.PaymentInstrumentId = payload.PaymentInstrumentId;
        creditPaymentResult.InternalTransactionId = trx.Id;

        creditPaymentResult.Provider = supportedGateway?.NameIdentifier;
        creditPaymentResult.SupportedGatewayId = supportedGateway.Id;
        creditPaymentResult.ProcessorId = supportedGateway?.ProcessorId;
        creditPaymentResult.CreditedAmount = payload.Amount;

        return creditPaymentResult;
    }


    /// <summary>
    /// 
    /// </summary>
    /// <param name="desiredRefundAmount"></param>
    /// <param name="relatedTransaction"></param>
    /// <returns></returns>
    /// <exception cref="FlexValidationException"></exception>
    /// <remarks>Throws exceptions on validation errors</remarks>
    ///
    private void NormalizeExpiry(SaleRequestCreditCard card)
    {
        if (card is null)
            throw new NullReferenceException("Credit card is null");

        //normalize expiration date
        if (card.Year < 100)
            card.Year += 2000;
    }

    private async Task DetokenizePaymentInstrument(IAuthorizationRequest payload, CancellationToken token)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("OrderId", payload.OrderId)
            .Baggage("Mid", payload.Mid);

        try
        {
            if (payload.Token != null)
            {
                var paymentInstrument = await _paymentInstrumentsService.DetokenizeAsync(new DeTokenizeInstrumentCommand
                    {
                        VaultKeyId = Guid.Parse(payload.Token),
                        Mid = payload.Mid,
                        Amount = payload.Amount,
                        IsCit = payload.IsCit,
                        GetNetworkToken = true,
                        ECI = payload.ThreeDS?.EcommerceIndicator,
                        CAVV = payload.ThreeDS?.AuthenticationValue,
                        GetLatestInstrument = payload.TryUseAccountUpdater
                    }
                );

                payload.NetworkTokenInfo = paymentInstrument.NetworkTokenInfo;
                if (payload.NetworkTokenInfo is not null)
                {
                    workspan.Log.Information("Detokenized Network token is not null");
                }

                payload.CreditCard = new SaleRequestCreditCard
                {
                    Id = paymentInstrument.Id,
                    FirstName = paymentInstrument.CardHolderFirstName,
                    LastName = paymentInstrument.CardHolderLastName,
                    Number = paymentInstrument.Number,
                    VerificationValue = paymentInstrument.VerificationValue,
                    Month = paymentInstrument.ExpirationMonth,
                    Year = paymentInstrument.ExpirationYear,
                    Bin = paymentInstrument.Bin,
                    Last4 = paymentInstrument.Last4,
                    CardNumberMasked = paymentInstrument.CardNumberMasked,
                    CardBrand = paymentInstrument.CardBrand
                };
                payload.AccountLastUpdatedAt = paymentInstrument.AccountLastUpdatedAt;
            }
        }
        catch (Exception e)
        {
            workspan.Log.Fatal(e, "DetokenizePaymentInstrument with AuthorizationRequest failed");
            throw;
        }
    }

    private async Task DetokenizePaymentInstrument(ICreditPaymentRequest payload, CancellationToken token)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("OrderId", payload.OrderId)
            .Baggage("Mid", payload.Mid);

        try
        {
            if (payload.PaymentInstrumentId != Guid.Empty)
            {
                var paymentInstrument =
                    await _dbContext.PaymentInstruments.SingleOrDefaultAsync(p => p.Id == payload.PaymentInstrumentId);
                var paymentInstrumentResponse = await _paymentInstrumentsService.DetokenizeAsync(
                    new DeTokenizeInstrumentCommand
                    {
                        VaultKeyId =
                            payload
                                .PaymentInstrumentId, //  payload.PaymentInstrumentId, // Guid.Parse(paymentInstrument.Token),
                        Mid = payload.Mid,
                        Amount = payload.Amount,
                        IsCit = false,
                        GetNetworkToken = true, //todo-check?
                        GetLatestInstrument = false
                    }
                );

                if (paymentInstrumentResponse is null)
                {
                    workspan.Log.Information(
                        "DetokenizePaymentInstrument for refund- paymentInstrumentResponse not found");
                    throw new Exception("credit paymentInstrument not found");
                }
                else
                {
                    workspan.Log.Information(
                        "DetokenizePaymentInstrument for refund - paymentInstrumentResponse found");


                    payload.NetworkTokenInfo = paymentInstrumentResponse.NetworkTokenInfo;

                    var billingAddress = paymentInstrumentResponse.BillingAddress;

                    payload.BillingAddress = new()
                    {
                        FirstName = paymentInstrumentResponse.CardHolderFirstName,
                        LastName = paymentInstrumentResponse.CardHolderLastName,
                        Address1 = billingAddress?.Address1,
                        Address2 = billingAddress?.Address2,
                        City = billingAddress?.City,
                        State = billingAddress?.StateCode,
                        Zip = billingAddress?.Zip,
                        Country = billingAddress?.CountryCode,
                        PhoneNumber = billingAddress?.Phone,
                    };

                    payload.CreditCard = new SaleRequestCreditCard
                    {
                        Id = paymentInstrumentResponse.Id,
                        FirstName = paymentInstrumentResponse.CardHolderFirstName,
                        LastName = paymentInstrumentResponse.CardHolderLastName,
                        Number = paymentInstrumentResponse.Number,
                        VerificationValue = paymentInstrumentResponse.VerificationValue,
                        Month = paymentInstrumentResponse.ExpirationMonth,
                        Year = paymentInstrumentResponse.ExpirationYear,
                        Bin = paymentInstrumentResponse.Bin,
                        Last4 = paymentInstrumentResponse.Last4,
                        CardNumberMasked = paymentInstrumentResponse.CardNumberMasked,
                        CardBrand = paymentInstrumentResponse.CardBrand
                    };

                    if (payload.CreditCard is not null)
                    {
                        workspan.Log.Information("DetokenizePaymentInstrument for refund- found credit card");
                    }
                    else
                    {
                        workspan.Log.Information("DetokenizePaymentInstrument for refund - credit card not found");
                    }
                }
            }
        }
        catch (Exception e)
        {
            workspan.Log.Fatal(e, "DetokenizePaymentInstrument with CreditPaymentRequest failed");
            throw;
        }
    }


    private async Task<int> ValidateAndCorrectRefundAmountAsync(int desiredRefundAmount, Transaction relatedTransaction,
        SupportedGateway? supportedGateway = null)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>();

        //log amounts
        workspan.Log.Information(
            "Related transaction Amount: {Amount} Auth Amount: {AuthorizationAmount} Discount Amount: {DiscountAmount}",
            relatedTransaction.Amount, relatedTransaction.AuthorizationAmount, relatedTransaction.DiscountAmount);

        var activity = await GetTransactionActivityAsync(relatedTransaction.Id);
        var orderTransactions = await GetOrderTransactionActivityAsync(relatedTransaction.OrderId);

        new RefundValidator(supportedGateway, relatedTransaction, activity, orderTransactions,
                _paymentProvider.SupportPartiallyRefund)
            .ValidateAndThrow(ref desiredRefundAmount);

        return desiredRefundAmount;
    }


    private async Task PublishCreditEvents(CancellationToken token, ICreditPaymentResult response,
        Transaction relatedTransaction,
        Merchant merchant, Transaction trx, Stopwatch paymentProviderExecutionTime,
        bool isPredispute = false, bool isConsumerInitiated = false)
    {
        using var workspan = Workspan.Start<SpreedlyPaymentProvider>()
            .Tag(nameof(ICreditPaymentResult), response)
            .Tag(nameof(relatedTransaction), relatedTransaction)
            .Tag(nameof(merchant), merchant)
            .Tag(nameof(trx), trx);

        try
        {
            workspan.Log.Information(
                "Publishing {Response}",
                JsonConvert.SerializeObject(response));

            //create a string builder for description based on dispute or consumer initiated
            var description = $"Transaction Ref: {relatedTransaction.Id}";

            if (isPredispute)
            {
                description += " was disputed";
            }
            else
            {
                if (isConsumerInitiated)
                    description = $"{relatedTransaction.Note} | Transaction Ref: {relatedTransaction.Id}";
                else
                    description += " was refunded";
            }

            if (response.Success)
            {
                var creditEvent = new PaymentCreditedEvent
                {
                    OrderId = relatedTransaction.OrderId,
                    Mid = merchant.Mid,
                    TransactionType = isPredispute
                        ? nameof(TransactionType.Chargeback).ToLower()
                        : nameof(TransactionType.Refund).ToLower(),
                    PaymentMethodType = relatedTransaction.PaymentType, // CreditCard / Ach
                    Descriptor = trx.DynamicDescriptor,
                    Description = description,
                    AmountFormatted = $"{Formatters.IntToDecimal(trx.Amount)} {trx.Currency}",
                    Amount = trx.Amount,
                    DiscountAmount = trx.DiscountAmount,
                    PaymentDate = DateTime.UtcNow,
                    ProviderId = trx.ProviderId,
                    Provider = response.Provider,
                    ProviderResponseCode = response.ProviderResponseCode,
                    ProviderResponseDescription = response.ProviderResponseMessage,
                    ProcessorResponseCode = trx.ResponseCode,
                    ProcessorResponseDescription = trx.ResponseMessage,
                    TransactionId = trx.Id,
                    Processor = trx.ProcessorName,
                    ProcessorId = trx.ProcessorId,
                    IsPreDisputeRefund = isPredispute,
                    IsConsumerInitiatedRefund = isConsumerInitiated,
                    NetworkTokenUsed = relatedTransaction.NetworkTokenUsed,
                    Bin = trx.PaymentMethod?.Bin,
                    Last4 = trx.PaymentMethod?.Last4,
                };

                workspan.Log.Information(
                    "Publishing _publisher.Publish<PaymentCreditedEvent> OrderID: {OrderId}",
                    relatedTransaction.OrderId);

                await _publisher.Publish(creditEvent, token);

                await _activityService.CreateActivityAsync(
                    PaymentsActivities.Payments_CreditPayment_Succeeded,
                    data: creditEvent,
                    set => set
                        .CorrelationId(creditEvent.OrderId)
                        .TenantId(creditEvent.Mid)
                        .Meta(meta => meta
                            .TransactionReference(trx.Id)
                            .SetValue("Amount", trx.Amount)
                            .SetValue("DiscountAmount", trx.DiscountAmount)
                            .AddProviderResponseMetadata(trx, paymentProviderExecutionTime)
                            .SetValue("PaymentMethodType", trx.PaymentType)
                            .SetValue("Bin", trx.PaymentMethod?.Bin)
                            .SetValue("Last4", trx.PaymentMethod?.Last4)
                        ));
            }
            else
            {
                workspan.Log.Information(
                    "Publishing _publisher.Publish<PaymentCreditFailedEvent> OrderID: {OrderId}",
                    relatedTransaction.OrderId);

                var creditFailedEvent = new PaymentCreditFailedEvent
                {
                    OrderId = relatedTransaction.OrderId,
                    Mid = merchant.Mid,
                    TransactionType = isPredispute
                        ? nameof(TransactionType.Chargeback).ToLower()
                        : nameof(TransactionType.Refund).ToLower(),
                    PaymentMethodType = relatedTransaction.PaymentType, // CreditCard / Ach
                    Descriptor = trx.DynamicDescriptor,
                    Description = isPredispute
                        ? $"Transaction {relatedTransaction.Id} dispute failed due to {response.ProviderResponseMessage}"
                        : $"Transaction {relatedTransaction.Id} refund failed due to {response.ProviderResponseMessage}",
                    AmountFormatted = $"{Formatters.IntToDecimal(trx.Amount)} {trx.Currency}",
                    Amount = trx.Amount,
                    DiscountAmount = trx.DiscountAmount,
                    PaymentDate = DateTime.UtcNow,
                    ProviderId = trx.ProviderId,
                    Provider = response.Provider,
                    ProviderResponseCode = response.ProviderResponseCode,
                    ProviderResponseDescription = response.ProviderResponseMessage,
                    // CvvCode = response.CvvCode,
                    // AvsCode = response.AvsCode,
                    ProcessorId = trx.ProcessorId,
                    ProcessorResponseCode = default(string),
                    ProcessorResponseDescription = default(string),
                    IsPreDisputeRefund = isPredispute,
                    NetworkTokenUsed = trx.NetworkTokenUsed
                };

                await _publisher.Publish(creditFailedEvent, token);

                await _activityService.CreateActivityAsync(
                    PaymentsActivities.Payments_CreditPayment_Failed,
                    data: creditFailedEvent,
                    set => set
                        .CorrelationId(creditFailedEvent.OrderId)
                        .TenantId(creditFailedEvent.Mid)
                        .Meta(meta => meta
                            .TransactionReference(trx.Id)
                            .SetValue("Amount", trx.Amount)
                            .AddProviderResponseMetadata(trx, paymentProviderExecutionTime)
                            .SetValue("PaymentMethodType", trx.PaymentType)
                            .SetValue("Bin", trx.PaymentMethod?.Bin)
                            .SetValue("Last4", trx.PaymentMethod?.Last4)
                        ));
            }
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "OrderID: {OrderId}", relatedTransaction.OrderId);

            throw;
        }
    }


    public async Task<IVoidPaymentResult> VoidAsync(IVoidPaymentRequest payload,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", payload.Mid)
            .Baggage("OrderId", payload.OrderId)
            .LogEnterAndExit();

        try
        {
            var merchant = await GetMerchantAsync(payload.Mid);

            var relatedTransaction = await GetRelatedTransactionAsync(payload.TransactionId);

            var supportedGateway =
                await GetSupportedGatewayById(relatedTransaction.SupportedGatewayId.GetValueOrDefault(), token);

            workspan
                .Baggage("GatewayId", payload.Gateway?.Id)
                .Baggage("GatewayNameIdentifier", payload.Gateway?.NameIdentifier)
                .Baggage("GatewayProcessorId", payload.Gateway?.ProcessorId)
                .Baggage("SupportedGatewayId", supportedGateway?.Id)
                .Baggage("SupportedGatewayNameIdentifier", supportedGateway?.NameIdentifier)
                .Baggage("SupportedGatewayProcessorId", supportedGateway?.ProcessorId);

            InitiatePaymentProvider(supportedGateway);

            //payload.Gateway = gateway;

            if (!_paymentProvider.SupportsCreditCards)
                throw new FlexChargeException("This provider does not support credit cards");

            payload.SupportedGateway = supportedGateway;
            payload.CurrencyCode = relatedTransaction.Currency;
            payload.Amount = relatedTransaction.Amount;
            payload.ProviderTransactionToken = relatedTransaction.ProviderTransactionToken;
            payload.OrderId = relatedTransaction.OrderId;
            payload.PaymentInstrumentId = relatedTransaction.PaymentMethodId ??= Guid.Empty;
            payload.PayerId = relatedTransaction.PayerId;
            payload.Descriptor = new DescriptorDTO
            {
                Name = relatedTransaction.DynamicDescriptor
            };

            Stopwatch paymentProviderExecutionTime = Stopwatch.StartNew();

            IVoidPaymentResult response = null;
            try
            {
                response = await _paymentProvider.VoidAsync(payload, token);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
            }

            paymentProviderExecutionTime.Stop();


            var trx = new Transaction
            {
                ParentId = relatedTransaction.Id,
                PaymentMethodId = payload.PaymentInstrumentId,
                OrderId = payload.OrderId,
                PayerId = payload.PayerId ?? Guid.Empty,
                SiteId = payload.SiteId ?? Guid.Empty,
                ProviderId = relatedTransaction.ProviderId,
                AuthorizationId = response?.AuthorizationCode,
                ProviderTransactionToken = response?.ProviderTransactionToken,
                DynamicDescriptor = payload.Descriptor?.Name,
                Amount = relatedTransaction.Amount,
                DiscountAmount = relatedTransaction.DiscountAmount,
                AuthorizationAmount = relatedTransaction.Amount,
                Currency = relatedTransaction.Currency,
                ResponseCode = response?.ProviderResponseCode,
                ResponseMessage = response?.ProviderResponseMessage,
                Type = TransactionType.Void.ToString(),
                PaymentType = nameof(PaymentMethodType.CreditCard),
                Status = response.Status.HasValue
                    ? response.Status.Value
                    : response.Success
                        ? TransactionStatus.Completed
                        : TransactionStatus
                            .Failed, // response is {Success: true} ? TransactionStatus.Completed : TransactionStatus.Failed,
                ProviderName = supportedGateway.NameIdentifier,
                ProcessorName = supportedGateway.ProcessorPlatform,
                ProcessorId = supportedGateway.ProcessorId,
                SupportedGatewayId = supportedGateway.Id,

                Merchant = merchant,
                IsRecurring = false,
                NetworkTokenUsed = relatedTransaction.NetworkTokenUsed,
                Meta = ParseMeta(response?.RawResult),
            };

            await _dbContext.Transactions.AddAsync(trx, token);
            await _dbContext.SaveChangesAsync(token);

            try
            {
                workspan.Log.Information(
                    "Publishing VoidEvents");

                if (response.Success)
                {
                    workspan.Log.Information(
                        "Publishing _publisher.Publish<PaymentVoidedEvent> OrderID: {OrderId}",
                        relatedTransaction.OrderId);

                    var voidEvent = new PaymentVoidedEvent
                    {
                        OrderId = relatedTransaction.OrderId,
                        Mid = merchant.Mid,
                        PaymentType = nameof(TransactionType.Void),
                        PaymentMethodType = relatedTransaction.PaymentType, // CreditCard, Ach
                        Descriptor = trx.DynamicDescriptor,
                        Description =
                            $"Transaction {relatedTransaction.Id} was Voided",
                        AmountFormatted = $"{Formatters.IntToDecimal(trx.Amount)} {trx.Currency}",
                        Amount = trx.Amount,
                        DiscountAmount = trx.DiscountAmount,
                        PaymentDate = DateTime.UtcNow,
                        RelatedTransactionId = relatedTransaction.Id,
                        NetworkTokenUsed = relatedTransaction.NetworkTokenUsed,
                        Bin = trx.PaymentMethod?.Bin,
                        Last4 = trx.PaymentMethod?.Last4,
                    };

                    await _publisher.Publish(voidEvent, token);

                    await _activityService.CreateActivityAsync(
                        PaymentsActivities.Payments_VoidPayment_Succeeded,
                        data: voidEvent,
                        set => set
                            .CorrelationId(voidEvent.OrderId)
                            .TenantId(voidEvent.Mid)
                            .Meta(meta => meta
                                .TransactionReference(trx.Id)
                                .SetValue("Amount", trx.Amount)
                                .SetValue("DiscountAmount", trx.DiscountAmount)
                                .AddProviderResponseMetadata(trx, paymentProviderExecutionTime)
                                .SetValue("PaymentInstrumentId", payload.PaymentInstrumentId)
                                .SetValue("PaymentMethodType", trx.PaymentType)
                                .SetValue("Bin", trx.PaymentMethod?.Bin)
                                .SetValue("Last4", trx.PaymentMethod?.Last4)
                            ));
                }
                else
                {
                    workspan.Log.Information(
                        "Publishing _publisher.Publish<PaymentVoidFailedEvent> OrderID: {OrderId}",
                        relatedTransaction.OrderId);

                    var paymentVoidFailedEvent = new PaymentVoidFailedEvent
                    {
                        OrderId = relatedTransaction.OrderId,
                        Mid = merchant.Mid,
                        PaymentType = nameof(TransactionType.Void),
                        PaymentMethodType = relatedTransaction.PaymentType, // CreditCard, Ach
                        Descriptor = trx.DynamicDescriptor,
                        Description =
                            $"Transaction {relatedTransaction.Id} Void failed due to {response.ProviderResponseMessage}",
                        AmountFormatted = $"{Formatters.IntToDecimal(trx.Amount)} {trx.Currency}",
                        Amount = trx.Amount,
                        DiscountAmount = trx.DiscountAmount,
                        PaymentDate = DateTime.UtcNow,
                        TransactionId = relatedTransaction.Id,
                        NetworkTokenUsed = relatedTransaction.NetworkTokenUsed
                    };

                    await _publisher.Publish(paymentVoidFailedEvent, token);

                    await _activityService.CreateActivityAsync(
                        PaymentsActivities.Payments_VoidPayment_Failed,
                        data: paymentVoidFailedEvent,
                        set => set
                            .CorrelationId(paymentVoidFailedEvent.OrderId)
                            .TenantId(paymentVoidFailedEvent.Mid)
                            .Meta(meta => meta
                                .TransactionReference(trx.Id)
                                .SetValue("Amount", trx.Amount)
                                .SetValue("DiscountAmount", trx.DiscountAmount)
                                .AddProviderResponseMetadata(trx, paymentProviderExecutionTime)
                                .SetValue("PaymentInstrumentId", payload.PaymentInstrumentId)
                                .SetValue("PaymentMethodType", trx.PaymentType)
                                .SetValue("Bin", trx.PaymentMethod?.Bin)
                                .SetValue("Last4", trx.PaymentMethod?.Last4)
                            ));
                }
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "Publishing OrderID: {OrderId}", relatedTransaction.OrderId);
                throw;
            }

            return response;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);

            await _activityService.CreateActivityAsync(PaymentsErrorActivities.Payments_VoidPayment_Error,
                set => set
                    .TenantId(payload.Mid)
                    .CorrelationId(payload.OrderId)
                    .Data(e)
                    .Meta(meta => meta
                        .TransactionReference(payload.TransactionId)
                        .SetValue("Amount", payload.Amount)
                        .SetValue("DiscountAmount", 0)
                        .Error(e.Message)
                    ));

            throw;
        }
    }

    public async Task<IRefundCancelResult> CancelRefundAsync(ICancelRefundRequest payload,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", payload.Mid)
            .Baggage("OrderId", payload.OrderId)
            .Baggage("GatewayId", payload.GatewayId)
            .Baggage("SupportedGatewayId", payload.SupportedGatewayId)
            .LogEnterAndExit();
        //.Baggage(nameof(payload.Gateway), payload.Gateway);

        try
        {
            var relatedTransaction = await GetRelatedTransactionAsync(payload.RefundTransactionId);

            var supportedGateway =
                await GetSupportedGatewayById(relatedTransaction.SupportedGatewayId.GetValueOrDefault(), token);

            workspan
                .Baggage("SupportedGatewayId", supportedGateway?.Id)
                .Baggage("SupportedGatewayNameIdentifier", supportedGateway?.NameIdentifier)
                .Baggage("SupportedGatewayProcessorId", supportedGateway?.ProcessorId);


            var merchant = await GetMerchantAsync(payload.Mid);

            InitiatePaymentProvider(supportedGateway);

            var result = await _paymentProvider.CancelRefundAsync(payload, token);

            var trx = new Transaction
            {
                ParentId = payload.RefundTransactionId,
                PaymentMethodId = relatedTransaction.PaymentMethodId,
                ProviderTransactionToken = result?.ProviderTransactionToken,
                Amount = relatedTransaction.Amount,
                AuthorizationAmount = relatedTransaction.Amount,
                Currency = relatedTransaction.Currency,
                ResponseCode = result?.ProviderResponseCode,
                ResponseMessage = result?.ProviderResponseMessage,
                // DynamicDescriptor = relatedTransaction.DynamicDescriptor,
                Type = nameof(TransactionType.CancelRefund),
                PaymentType = nameof(PaymentMethodType.CreditCard),
                Note = payload.Reason,
                Status = result.Status.HasValue
                    ? result.Status.Value
                    : result.Success
                        ? TransactionStatus.Completed
                        : TransactionStatus.Failed,

                ProviderId = relatedTransaction.ProviderId,
                ProviderName = supportedGateway?.NameIdentifier,
                ProcessorName = supportedGateway?.ProcessorPlatform,
                ProcessorId = supportedGateway?.ProcessorId,
                SupportedGatewayId = supportedGateway.Id,

                PayerId = relatedTransaction.PayerId,
                SiteId = relatedTransaction.SiteId ?? Guid.Empty,
                Merchant = merchant,
                OrderId = relatedTransaction.OrderId,
                IsRecurring = false,
                NetworkTokenUsed = relatedTransaction.NetworkTokenUsed,
                SchemeTransactionIdUsed = relatedTransaction.SchemeTransactionIdUsed,
                Meta = ParseMeta(result.RawResult),
            };

            await _dbContext.Transactions.AddAsync(trx, token);
            await _dbContext.SaveChangesAsync(token);

            if (result.Success)
            {
                var cancelRefundEvent = new PaymentRefundCancelledEvent
                {
                    OrderId = relatedTransaction.OrderId,
                    Mid = merchant.Mid,
                    Type = TransactionType.Refund.ToString().ToLower(),
                    Descriptor = trx.DynamicDescriptor,
                    Description = payload.Reason,
                    AmountFormatted = $"{Formatters.IntToDecimal(trx.Amount)} {trx.Currency}",
                    Amount = trx.Amount,
                    PaymentDate = DateTime.UtcNow,
                    ProviderId = trx.ProviderId,
                    Provider = result.Provider,
                    ProviderResponseCode = result.ProviderResponseCode,
                    ProviderResponseDescription = result.ProviderResponseMessage,
                    ProcessorResponseCode = trx.ResponseCode,
                    ProcessorResponseDescription = trx.ResponseMessage,
                    TransactionId = trx.Id,
                    Processor = trx.ProcessorName,
                    ProcessorId = trx.ProcessorId,
                    NetworkTokenUsed = relatedTransaction.NetworkTokenUsed,
                };

                workspan.Log.Information(
                    "Publishing _publisher.Publish<CancelRefundEvent> OrderID: {OrderId}",
                    relatedTransaction.OrderId);

                await _publisher.Publish(cancelRefundEvent, token);
            }

            return result;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }

    #endregion

    #region ACH TRANSFERS

    public async Task<AchTransferResult> AchCreditAsync(AchTransferRequest payload,
        Gateway gateway, CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", payload.Mid)
            .Baggage("OrderId", payload.OrderId)
            .Baggage("GatewayId", payload.Gateway?.Id)
            .Request(payload)
            .LogEnterAndExit();

        return await AchCreditInternalAsync(payload, null, gateway: gateway, token: token);
    }

    public async Task<AchTransferResult> AchRefundAsync(AchTransferRequest payload,
        Guid transactionToRefundId,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", payload.Mid)
            .Baggage("OrderId", payload.OrderId)
            .Baggage("TransactionToRefundId", transactionToRefundId)
            .Baggage("GatewayId", payload.Gateway?.Id)
            .Request(payload)
            .LogEnterAndExit();

        try
        {
            // Locking database transaction for refund
            await using var lockingTransaction = await _dbContext.Database.BeginTransactionAsync();

            // Using SelectForUpdateBlockingTag to ensure that there is no double refund
            var lockedTransactionToRefund = await _dbContext
                .Transactions
                .TagWith(SelectForUpdateCommandInterceptor.SelectForUpdateBlockingTag)
                .SingleOrDefaultAsync(t => t.Id == transactionToRefundId, token);

            var relatedTransaction = await GetRelatedTransactionAsync(transactionToRefundId);

            var gateway = await GetGatewayByIdAsync(payload.Mid, relatedTransaction.ProviderId, token);

            workspan
                .Baggage("GatewayId", gateway?.Id)
                .Baggage("GatewayNameIdentifier", gateway?.NameIdentifier)
                .Baggage("GatewayProcessorId", gateway?.ProcessorId)
                .Baggage("SupportedGatewayId", gateway?.SupportedGatewayId)
                .Baggage("SupportedGatewayNameIdentifier", gateway?.SupportedGateway?.NameIdentifier)
                .Baggage("SupportedGatewayProcessorId", gateway?.SupportedGateway?.ProcessorId);

            var amount = await ValidateAndCorrectRefundAmountAsync(payload.Amount, relatedTransaction);
            payload.Amount = amount;

            return await AchCreditInternalAsync(payload, relatedTransaction, gateway, token);
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "AchRefund failed");

            await _activityService.CreateActivityAsync(PaymentsErrorActivities.Payments_AchCreditPayment_Error,
                set => set
                    .TenantId(payload.Mid)
                    .CorrelationId(payload.OrderId)
                    .Data(e)
                    .Meta(meta => meta
                        .SetValue("Amount", payload.Amount)
                        .SetValue("DiscountAmount", payload.Discount)
                        .SetValue("VerifiedAch", payload.IsVerifiedAch)
                        .Error(e.Message)
                    ));

            var response = new AchTransferResult();
            ConvertExceptionToError(e, response);

            return response;
        }
    }

    private async Task<AchTransferResult> AchCreditInternalAsync(AchTransferRequest payload,
        Transaction? transactionToRefund,
        Gateway gateway = null, CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .LogEnterAndExit();

        try
        {
            bool isRefund = transactionToRefund != null;

            if (gateway == null)
                throw new GatewayNotFoundException("Gateway must be set for AchCredit");

            var merchant = await GetMerchantAsync(payload.Mid);

            InitiatePaymentProvider(gateway);
            payload.Gateway = gateway;

            workspan
                .Baggage("GatewayId", gateway?.Id);

            if (!_paymentProvider.SupportsAch)
                throw new FlexValidationException(ValidationCodes.ProviderNotSupportAchTransfers,
                    ValidationCodes.GetMessage(ValidationCodes.ProviderNotSupportAchTransfers));

            if (isRefund)
            {
                // Note: Refunds should work even if gateway is not active for new payments
                if (!gateway.IsActive)
                    throw new FlexValidationException(ValidationCodes.GatewayNotActive,
                        ValidationCodes.GetMessage(ValidationCodes.GatewayNotActive));
            }

            if (payload.RetainInstrument)
                await StorePaymentInstrumentLocally(payload, token);

            #region Create Transaction in Initialized state

            var trx = new Transaction
            {
                ParentId = isRefund ? transactionToRefund.Id : default,
                PaymentMethodId = payload.PaymentInstrumentId,
                DynamicDescriptor = payload.CompanyEntryDescription,
                Amount = payload.Amount,
                AuthorizationAmount = payload.Amount,
                DiscountAmount = payload.Discount,
                Currency = payload.CurrencyCode,
                Type = nameof(TransactionType.Credit),
                PaymentType = nameof(PaymentMethodType.Ach),
                Status = TransactionStatus.Initialized,
                ProviderId = gateway?.Id ?? Guid.Empty,
                ProviderName = gateway?.NameIdentifier,
                ProcessorName = gateway?.NameIdentifier,
                ProcessorId = gateway?.ProcessorId,
                PayerId = payload.PayerId ?? Guid.Empty,
                SiteId = payload.SiteId ?? Guid.Empty,
                Merchant = merchant,
                OrderId = payload.OrderId,
                IsRecurring = false,
                SupportedGatewayId = gateway.SupportedGatewayId
            };

            await _dbContext.Transactions.AddAsync(trx, token);
            await _transactionMonitoringService.StartMonitoringTransactionAsync(_dbContext, trx, saveChanges: false);
            await _dbContext.SaveChangesAsync(token);

            #endregion

            //ACH Credit
            workspan.Log.Information("ACH Credit Started");

            Stopwatch paymentProviderExecutionTime = Stopwatch.StartNew();

            var response = await _paymentProvider.AchCreditAsync(payload, token);
            await CheckInternalResponseCodeMapping(payload, response);

            paymentProviderExecutionTime.Stop();

            if (response.Success)
            {
                workspan.Log.Information("ACH Credit Success");
            }
            else
            {
                workspan.Log.Information("ACH Credit Failed");
            }

            #region Update Transaction

            trx.ParentId = default;
            trx.ProviderTransactionToken = response.ProviderTransactionToken;
            trx.ResponseCode = response.ProviderResponseCode;
            trx.ResponseMessage = response.ProviderResponseMessage;
            trx.InternalResponseCode = response.InternalResponseCode;
            trx.InternalResponseMessage = response.InternalResponseMessage;
            trx.InternalResponseGroup = response.InternalResponseGroup;
            trx.Status = response.Status.HasValue
                ? response.Status.Value
                : response.Success
                    ? TransactionStatus.InProcess
                    : TransactionStatus.Failed;
            trx.PayerId = payload.PayerId ?? Guid.Empty;
            trx.SiteId = payload.SiteId ?? Guid.Empty;
            trx.OrderId = payload.OrderId;
            trx.Meta = ParseMeta(response.RawResult);

            await _dbContext.SaveChangesAsync(token);

            #endregion

            response.TransactionId = trx.Id;
            response.PaymentInstrumentId = trx.PaymentMethodId;

            #region Publish Events

            try
            {
                if (response.Success)
                {
                    var paymentCreditedEvent = new PaymentCreditedEvent()
                    {
                        OrderId = payload.OrderId,
                        TransactionId = response.TransactionId,
                        Mid = payload.Mid,
                        TransactionType = nameof(TransactionType.Credit).ToLower(),
                        PaymentMethodType = nameof(PaymentMethodType.Ach),
                        Descriptor = trx.DynamicDescriptor,
                        Description = payload.IsVerifiedAch ? "Ach Verified Credit" : "Ach Credit",
                        AmountFormatted = Formatters.LongToDecimal(payload.Amount)
                            .ToString(CultureInfo.InvariantCulture),
                        Amount = trx.Amount,
                        //AuthorizationAmount = trx.AuthorizationAmount,
                        DiscountAmount = trx.DiscountAmount,
                        PaymentDate = trx.CreatedOn,
                        ProviderId = (Guid) gateway?.Id,
                        Provider = gateway?.NameIdentifier,
                        ProviderResponseCode = trx.ResponseCode,
                        ProviderTransactionToken = trx.ProviderTransactionToken,
                        ProviderResponseDescription = trx.ResponseMessage,
                        ProcessorResponseCode = trx.ResponseCode,
                        ProcessorResponseDescription = trx.ResponseMessage,
                        Processor = gateway?.NameIdentifier,
                        ProcessorId = gateway?.ProcessorId,

                        PaymentInstrumentId = response.PaymentInstrumentId
                    };

                    await _publisher.Publish(paymentCreditedEvent, token);

                    await _activityService.CreateActivityAsync(PaymentsActivities.Payments_AchCreditPayment_Succeeded,
                        data: paymentCreditedEvent,
                        set => set
                            .TenantId(payload.Mid)
                            .CorrelationId(payload.OrderId)
                            .Meta(meta => meta
                                .TransactionReference(trx.Id)
                                .SetValue("Amount", trx.Amount)
                                .SetValue("DiscountAmount", trx.DiscountAmount)
                                .SetValue("VerifiedAch", payload.IsVerifiedAch)
                                .AddProviderResponseMetadata(trx, paymentProviderExecutionTime)
                            ));
                }
                else
                {
                    var paymentCreditFailedEvent = new PaymentCreditFailedEvent
                    {
                        OrderId = payload.OrderId,
                        TransactionId = response.TransactionId,
                        Mid = payload.Mid,
                        TransactionType = nameof(TransactionType.Credit).ToLower(),
                        PaymentMethodType = nameof(PaymentMethodType.Ach),
                        Descriptor = trx.DynamicDescriptor,
                        Description = payload.IsVerifiedAch ? "Ach Verified Credit" : "Ach Credit",
                        AmountFormatted = Formatters.LongToDecimal(payload.Amount)
                            .ToString(CultureInfo.InvariantCulture),
                        Amount = trx.Amount,
                        //AuthorizationAmount = trx.Discount,
                        DiscountAmount = trx.DiscountAmount,
                        PaymentDate = trx.CreatedOn,
                        ProviderId = (Guid) gateway?.Id,
                        Provider = gateway?.NameIdentifier,
                        ProviderResponseCode = trx.ResponseCode,
                        ProviderTransactionToken = trx.ProviderTransactionToken,
                        ProviderResponseDescription = trx.ResponseMessage,
                        ProcessorResponseCode = trx.ResponseCode,
                        ProcessorResponseDescription = trx.ResponseMessage,
                        Processor = gateway?.NameIdentifier,
                        ProcessorId = gateway?.ProcessorId,

                        PaymentInstrumentId = response.PaymentInstrumentId,
                    };

                    await _publisher.Publish(paymentCreditFailedEvent, token);

                    await _activityService.CreateActivityAsync(PaymentsActivities.Payments_AchCreditPayment_Failed,
                        data: paymentCreditFailedEvent,
                        set => set
                            .TenantId(payload.Mid)
                            .CorrelationId(payload.OrderId)
                            .Meta(meta => meta
                                .TransactionReference(trx.Id)
                                .SetValue("Amount", trx.Amount)
                                .SetValue("DiscountAmount", trx.DiscountAmount)
                                .SetValue("VerifiedAch", payload.IsVerifiedAch)
                                .AddProviderResponseMetadata(trx, paymentProviderExecutionTime)
                            ));
                }
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, $"Publish failed");
            }

            #endregion

            return response;
        }
        catch (GatewayNotFoundException ex)
        {
            workspan.Log.Warning("Gateway not found. Code: {Code} Message: {Message}", ex.Code, ex.Message);

            var response = new AchTransferResult();
            ConvertExceptionToError(ex, response);

            return response;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "AchCredit failed");

            await _activityService.CreateActivityAsync(PaymentsErrorActivities.Payments_AchCreditPayment_Error,
                set => set
                    .TenantId(payload.Mid)
                    .CorrelationId(payload.OrderId)
                    .Data(e)
                    .Meta(meta => meta
                        .SetValue("Amount", payload.Amount)
                        .SetValue("DiscountAmount", payload.Discount)
                        .SetValue("VerifiedAch", payload.IsVerifiedAch)
                        .Error(e.Message)
                    ));

            var response = new AchTransferResult();
            ConvertExceptionToError(e, response);

            return response;
        }
    }

    public async Task<AchTransferResult> AchDebitAsync(AchTransferRequest payload, OrchestrationOptions options,
        Gateway gateway = null, CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", payload.Mid)
            .Baggage("GatewayId", payload.Gateway?.Id)
            .Baggage("OrderId", payload.OrderId)
            .Tag("TransferRequest", payload)
            .LogEnterAndExit();

        try
        {
            var merchant = await GetMerchantAsync(payload.Mid);

            // gateway = options?.IsCascadingPayment.HasValue == true && options.IsCascadingPayment.Value
            //     ? await GetNextGatewayAsync(merchant.Mid, options, null, CancellationToken.None)
            //     : await GetGatewayAsync(merchant.Mid, options, CancellationToken.None);


            if (gateway == null)
                throw new GatewayNotFoundException("Gateway must be set for AchDebit");

            if (options is not null)
                options.PreviousOrder = gateway?.Order;

            // var nextGateway =
            //     await GetNextGatewayAsync(merchant.Mid, options, null, CancellationToken.None);

            InitiatePaymentProvider(gateway);
            payload.Gateway = gateway;

            if (!_paymentProvider.SupportsAch)
                throw new FlexChargeException("GatewayError", "This provider does not support ACH transfers");

            if (payload.Amount <= 0)
                throw new ArgumentOutOfRangeException(nameof(payload.Amount), "Amount must be greater than zero");

            if (payload.RetainInstrument)
                await StorePaymentInstrumentLocally(payload, token);

            #region Create Transaction in Initialized state

            var trx = new Transaction
            {
                ParentId = default,
                PaymentMethodId = payload.PaymentInstrumentId,
                DynamicDescriptor = payload.CompanyEntryDescription,
                Amount = payload.Amount,
                AuthorizationAmount = payload.Amount,
                DiscountAmount = payload.Discount,
                Currency = payload.CurrencyCode,
                Type = nameof(TransactionType.Debit),
                PaymentType = nameof(PaymentMethodType.Ach),
                Status = TransactionStatus.Initialized,
                ProviderId = gateway?.Id ?? Guid.Empty,
                ProviderName = gateway?.NameIdentifier,
                ProcessorName = gateway?.NameIdentifier,
                ProcessorId = gateway?.ProcessorId,
                PayerId = payload.PayerId ?? Guid.Empty,
                SiteId = payload.SiteId ?? Guid.Empty,
                Merchant = merchant,
                OrderId = payload.OrderId,
                IsRecurring = false,
                SupportedGatewayId = gateway.SupportedGatewayId
            };

            await _dbContext.Transactions.AddAsync(trx, token);
            await _transactionMonitoringService.StartMonitoringTransactionAsync(_dbContext, trx, saveChanges: false);
            await _dbContext.SaveChangesAsync(token);

            #endregion

            Stopwatch paymentProviderExecutionTime = Stopwatch.StartNew();

            //ACH Debit
            var response = await _paymentProvider.AchDebitAsync(payload, token);
            await CheckInternalResponseCodeMapping(payload, response);

            paymentProviderExecutionTime.Stop();

            #region Update Transaction

            trx.ProviderTransactionToken = response.ProviderTransactionToken;
            trx.ResponseCode = response.ProviderResponseCode;
            trx.ResponseMessage = response.ProviderResponseMessage;
            trx.InternalResponseCode = response.InternalResponseCode;
            trx.InternalResponseMessage = response.InternalResponseMessage;
            trx.InternalResponseGroup = response.InternalResponseGroup;
            trx.Status = response.Status.HasValue
                ? response.Status.Value
                : response.Success
                    ? TransactionStatus.InProcess
                    : TransactionStatus.Failed;
            trx.PayerId = payload.PayerId ?? Guid.Empty;
            trx.SiteId = payload.SiteId ?? Guid.Empty;
            trx.OrderId = payload.OrderId;
            trx.Meta = ParseMeta(response.RawResult);

            await _dbContext.SaveChangesAsync(token);

            #endregion

            response.TransactionId = trx.Id;
            response.PaymentInstrumentId = trx.PaymentMethodId;

            #region Publish Events

            try
            {
                if (response.Success)
                {
                    var paymentDebitedEvent = new PaymentDebitedEvent()
                    {
                        OrderId = payload.OrderId,
                        TransactionId = response.TransactionId,
                        Mid = payload.Mid,
                        TransactionType = nameof(TransactionType.Debit).ToLower(),
                        PaymentMethodType = nameof(PaymentMethodType.Ach),
                        Descriptor = trx.DynamicDescriptor,
                        Description = payload.IsVerifiedAch ? "Ach Verified Debit" : "Ach Debit",
                        AmountFormatted = Formatters.LongToDecimal(payload.Amount)
                            .ToString(CultureInfo.InvariantCulture),
                        Amount = trx.Amount,
                        //AuthorizationAmount = trx.AuthorizationAmount,
                        DiscountAmount = trx.DiscountAmount,
                        FeeAmount = trx.FeeAmount,
                        PaymentDate = trx.CreatedOn,
                        ProviderId = gateway?.Id,
                        Provider = gateway?.NameIdentifier,
                        ProviderResponseCode = trx.ResponseCode,
                        ProviderTransactionToken = trx.ProviderTransactionToken,
                        ProviderResponseDescription = trx.ResponseMessage,
                        InternalResponseCode = trx.InternalResponseCode,
                        InternalResponseMessage = trx.InternalResponseMessage,
                        InternalResponseGroup = trx.InternalResponseGroup,
                        ProcessorResponseCode = trx.ResponseCode,
                        ProcessorResponseDescription = trx.ResponseMessage,
                        Processor = gateway?.NameIdentifier,
                        ProcessorId = gateway?.ProcessorId,

                        PaymentInstrumentId = response.PaymentInstrumentId
                    };

                    await _publisher.Publish(paymentDebitedEvent, token);

                    await _activityService.CreateActivityAsync(PaymentsActivities.Payments_AchDebitPayment_Succeeded,
                        data: paymentDebitedEvent,
                        set => set
                            .TenantId(payload.Mid)
                            .CorrelationId(payload.OrderId)
                            .Meta(meta => meta
                                .TransactionReference(trx.Id)
                                .SetValue("Amount", trx.Amount)
                                .SetValue("DiscountAmount", trx.DiscountAmount)
                                .SetValue("VerifiedAch", payload.IsVerifiedAch)
                                .AddProviderResponseMetadata(trx, paymentProviderExecutionTime)
                            ));
                }
                else
                {
                    var paymentDebitFailedEvent = new PaymentDebitFailedEvent
                    {
                        OrderId = payload.OrderId,
                        TransactionId = response.TransactionId,
                        Mid = payload.Mid,
                        TransactionType = nameof(TransactionType.Debit).ToLower(),
                        PaymentMethodType = nameof(PaymentMethodType.Ach),
                        Descriptor = trx.DynamicDescriptor,
                        Description = payload.IsVerifiedAch ? "Ach Verified Debit" : "Ach Debit",
                        AmountFormatted = Formatters.LongToDecimal(payload.Amount)
                            .ToString(CultureInfo.InvariantCulture),
                        Amount = trx.Amount,
                        //AuthorizationAmount = trx.Discount,
                        DiscountAmount = trx.DiscountAmount,
                        FeeAmount = trx.FeeAmount,
                        PaymentDate = trx.CreatedOn,
                        ProviderId = gateway?.Id,
                        Provider = gateway?.NameIdentifier,
                        ProviderResponseCode = trx.ResponseCode,
                        ProviderTransactionToken = trx.ProviderTransactionToken,
                        ProviderResponseDescription = trx.ResponseMessage,
                        InternalResponseCode = trx.InternalResponseCode,
                        InternalResponseMessage = trx.InternalResponseMessage,
                        InternalResponseGroup = trx.InternalResponseGroup,
                        ProcessorResponseCode = trx.ResponseCode,
                        ProcessorResponseDescription = trx.ResponseMessage,
                        Processor = gateway?.NameIdentifier,
                        ProcessorId = gateway?.ProcessorId,

                        PaymentInstrumentId = response.PaymentInstrumentId,
                    };

                    await _publisher.Publish(paymentDebitFailedEvent, token);

                    await _activityService.CreateActivityAsync(PaymentsActivities.Payments_AchDebitPayment_Failed,
                        data: paymentDebitFailedEvent,
                        set => set
                            .TenantId(payload.Mid)
                            .CorrelationId(payload.OrderId)
                            .Meta(meta => meta
                                .TransactionReference(trx.Id)
                                .SetValue("Amount", trx.Amount)
                                .SetValue("DiscountAmount", trx.DiscountAmount)
                                .SetValue("VerifiedAch", payload.IsVerifiedAch)
                                .AddProviderResponseMetadata(trx, paymentProviderExecutionTime)
                            ));
                }
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, $"Publish failed");
            }

            #endregion

            return response;
        }
        catch (GatewayNotFoundException ex)
        {
            workspan.Log.Warning("Gateway not found. Code: {Code} Message: {Message}", ex.Code, ex.Message);

            var response = new AchTransferResult();
            ConvertExceptionToError(ex, response);

            return response;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "AchDebit failed");

            await _activityService.CreateActivityAsync(PaymentsErrorActivities.Payments_AchDebitPayment_Error,
                set => set
                    .TenantId(payload.Mid)
                    .CorrelationId(payload.OrderId)
                    .Data(e)
                    .Meta(meta => meta
                        .SetValue("Amount", payload.Amount)
                        .SetValue("DiscountAmount", payload.Discount)
                        .SetValue("VerifiedAch", payload.IsVerifiedAch)
                        .Error(e.Message)
                    ));

            var response = new AchTransferResult();
            ConvertExceptionToError(e, response);

            return response;
        }
    }

    public async Task<AchCancelResult> AchCancelAsync(AchCancelPaymentRequest payload,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", payload.Mid)
            .Baggage("OrderId", payload.OrderId)
            .Baggage("TransactionId", payload.TransactionId)
            .Baggage("ProviderTransactionToken", payload.ProviderTransactionToken)
            .Request(payload)
            .LogEnterAndExit();

        try
        {
            var relatedTransaction = await GetRelatedTransactionAsync(payload.TransactionId);

            if (relatedTransaction == null)
                throw new FlexChargeException("TransactionNotFound",
                    $"Transaction not found. TransactionId: {payload.TransactionId}");

            var merchant = await GetMerchantAsync(payload.Mid);
            var gateway = await GetGatewayByIdAsync(payload.Mid, relatedTransaction.ProviderId, token);

            workspan
                .Baggage("GatewayId", gateway?.Id)
                .Baggage("GatewayNameIdentifier", gateway?.NameIdentifier)
                .Baggage("GatewayProcessorId", gateway?.ProcessorId)
                .Baggage("SupportedGatewayId", gateway?.SupportedGatewayId)
                .Baggage("SupportedGatewayNameIdentifier", gateway?.SupportedGateway?.NameIdentifier)
                .Baggage("SupportedGatewayProcessorId", gateway?.SupportedGateway?.ProcessorId);

            InitiatePaymentProvider(gateway);

            payload.Gateway = gateway;

            if (!_paymentProvider.SupportsAch)
                throw new FlexChargeException("GatewayError", "This provider does not support ACH transfers");

            payload.CurrencyCode = relatedTransaction.Currency;
            payload.Amount = relatedTransaction.Amount;
            payload.ProviderTransactionToken = relatedTransaction.ProviderTransactionToken;

            payload.OrderId = relatedTransaction.OrderId;
            payload.PaymentInstrumentId = relatedTransaction.PaymentMethodId ??= Guid.Empty;
            payload.PayerId = relatedTransaction.PayerId;

            payload.Descriptor = new DescriptorDTO
            {
                Name = relatedTransaction.DynamicDescriptor
            };

            string transactionType;
            switch (relatedTransaction.Type)
            {
                case nameof(TransactionType.Debit):
                    transactionType = nameof(TransactionType.CancelDebit);
                    break;
                case nameof(TransactionType.Credit):
                    transactionType = nameof(TransactionType.CancelCredit);
                    break;
                default:
                    workspan.Log.Fatal("Invalid ACH transaction type to cancel: {TransactionType}",
                        relatedTransaction.Type);
                    transactionType = nameof(TransactionType.CancelDebit);
                    break;
            }

            #region Create Transaction in Initialized state

            var trx = new Transaction
            {
                ParentId = relatedTransaction.Id,
                PaymentMethodId = payload.PaymentInstrumentId,
                OrderId = payload.OrderId,
                PayerId = payload.PayerId ?? Guid.Empty,
                SiteId = payload.SiteId ?? Guid.Empty,
                ProviderId = gateway?.Id ?? Guid.Empty,
                //AuthorizationId = response?.AuthorizationCode,
                DynamicDescriptor = payload.Descriptor?.Name,
                Amount = relatedTransaction.Amount,
                DiscountAmount = relatedTransaction.DiscountAmount,
                AuthorizationAmount = relatedTransaction.Amount,
                Currency = relatedTransaction.Currency,
                Type = transactionType,
                PaymentType = nameof(PaymentMethodType.Ach),
                Status = TransactionStatus.Initialized,
                ProviderName = gateway?.NameIdentifier,
                ProcessorName = gateway?.NameIdentifier,
                ProcessorId = gateway?.ProcessorId,
                Merchant = merchant,
                IsRecurring = false,
                NetworkTokenUsed = relatedTransaction.NetworkTokenUsed,
                SupportedGatewayId = gateway?.SupportedGatewayId
                //Meta = ParseMeta(response?.RawResult),
            };

            await _dbContext.Transactions.AddAsync(trx, token);
            await _transactionMonitoringService.StartMonitoringTransactionAsync(_dbContext, trx, saveChanges: false);
            await _dbContext.SaveChangesAsync(token);

            #endregion

            Stopwatch paymentProviderExecutionTime = Stopwatch.StartNew();

            var response = await _paymentProvider.AchCancelAsync(payload, token);

            paymentProviderExecutionTime.Stop();

            #region Update Transaction

            trx.ProviderTransactionToken = response?.ProviderTransactionToken;
            trx.ResponseCode = response?.ProviderResponseCode;
            trx.ResponseMessage = response?.ProviderResponseMessage;
            trx.InternalResponseCode = response?.InternalResponseCode;
            trx.InternalResponseMessage = response?.InternalResponseMessage;
            trx.InternalResponseGroup = response?.InternalResponseGroup;
            trx.Status = response.Status.HasValue
                ? response.Status.Value
                : response.Success
                    ? TransactionStatus.Completed
                    : TransactionStatus
                        .Failed;
            trx.Meta = ParseMeta(response?.RawResult);

            await _dbContext.SaveChangesAsync(token);

            #endregion

            response.TransactionId = trx.Id;

            try
            {
                workspan.Log.Information(
                    "Publishing CancelEvents");

                #region Publish Events and Activities

                if (response.Success)
                {
                    workspan.Log.Information(
                        "Publishing _publisher.Publish<PaymentCanceledEvent> OrderID: {OrderId}",
                        relatedTransaction.OrderId);

                    var paymentCanceledEvent = new PaymentCanceledEvent
                    {
                        OrderId = relatedTransaction.OrderId,
                        Mid = merchant.Mid,
                        PaymentType = transactionType.ToLower(),
                        PaymentMethodType = relatedTransaction.PaymentType, // CreditCard, Ach
                        Descriptor = trx.DynamicDescriptor,
                        Description =
                            $"Transaction {relatedTransaction.Id} was Canceled",
                        AmountFormatted = $"{Formatters.IntToDecimal(trx.Amount)} {trx.Currency}",
                        Amount = trx.Amount,
                        DiscountAmount = trx.DiscountAmount,
                        PaymentDate = DateTime.UtcNow,
                        RelatedTransactionId = relatedTransaction.Id,
                        TransactionId = trx.Id
                    };

                    await _publisher.Publish(paymentCanceledEvent, token);

                    await _activityService.CreateActivityAsync(
                        PaymentsActivities.Payments_AchCancel_Succeeded,
                        data: paymentCanceledEvent,
                        set => set
                            .CorrelationId(paymentCanceledEvent.OrderId)
                            .TenantId(paymentCanceledEvent.Mid)
                            .Meta(meta => meta
                                .TransactionReference(trx.Id)
                                .SetValue("Amount", trx.Amount)
                                .SetValue("DiscountAmount", trx.DiscountAmount)
                                .SetValue("VerifiedAch", payload.IsVerifiedAch)
                                .AddProviderResponseMetadata(trx, paymentProviderExecutionTime)
                            ));
                }
                else
                {
                    workspan.Log.Information(
                        "Publishing _publisher.Publish<PaymentCancelFailedEvent> OrderID: {OrderId}",
                        relatedTransaction.OrderId);

                    var paymentCancelFailedEvent = new PaymentCancelFailedEvent
                    {
                        OrderId = relatedTransaction.OrderId,
                        Mid = merchant.Mid,
                        PaymentType = transactionType.ToLower(),
                        PaymentMethodType = relatedTransaction.PaymentType, // CreditCard, Ach
                        Descriptor = trx.DynamicDescriptor,
                        Description =
                            $"Transaction {relatedTransaction.Id} Cancel failed due to {response.ProviderResponseMessage}",
                        AmountFormatted = $"{Formatters.IntToDecimal(trx.Amount)} {trx.Currency}",
                        Amount = trx.Amount,
                        DiscountAmount = trx.DiscountAmount,
                        PaymentDate = DateTime.UtcNow,
                        RelatedTransactionId = relatedTransaction.Id,
                        TransactionId = trx.Id,
                    };

                    await _publisher.Publish(paymentCancelFailedEvent, token);

                    await _activityService.CreateActivityAsync(
                        PaymentsActivities.Payments_AchCancel_Failed,
                        data: paymentCancelFailedEvent,
                        set => set
                            .CorrelationId(paymentCancelFailedEvent.OrderId)
                            .TenantId(paymentCancelFailedEvent.Mid)
                            .Meta(meta => meta
                                .TransactionReference(trx.Id)
                                .SetValue("Amount", trx.Amount)
                                .SetValue("DiscountAmount", trx.DiscountAmount)
                                .SetValue("VerifiedAch", payload.IsVerifiedAch)
                                .AddProviderResponseMetadata(trx, paymentProviderExecutionTime)
                            ));
                }

                #endregion
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "Publish failed");
            }

            return response;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);

            await _activityService.CreateActivityAsync(PaymentsErrorActivities.Payments_AchCancelPayment_Error,
                set => set
                    .TenantId(payload.Mid)
                    .CorrelationId(payload.OrderId)
                    .Data(e)
                    .Meta(meta => meta
                        .TransactionReference(payload.TransactionId)
                        .SetValue("Amount", payload.Amount)
                        .SetValue("DiscountAmount", 0)
                        .Error(e.Message)
                    ));

            var response = new AchCancelResult();
            ConvertExceptionToError(e, response);

            return response;
        }
    }

    #endregion


    public async Task<PaymentStatusResult> GetPaymentStatusAsync(Guid mid, string paymentId,
        PaymentMethodType methodType, string provider,
        CancellationToken token = default)
    {
        throw new NotImplementedException();
    }

    public async Task<FingerprintInstrumentResult> FingerprintPaymentInstrumentAsync(Guid mid,
        FingerprintInstrumentRequest payload,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>();

        var getVaultByIdCommand = new GetByVaultIdCommand
        {
            PaymentInstrumentToken = Guid.Parse(payload.Token),
            Mid = mid,
            UseLatestInstrument = payload.UseLatestInstrument
        };

        GetByVaultIdCommandResponse vaultInfo;
        // try
        // {
        //     var response = await _grpcVaultServiceClient.GetByVaultIdAsync(new()
        //     {
        //         Request = JsonConvert.SerializeObject(getVaultByIdCommand)
        //     });
        //
        //     vaultInfo = JsonConvert.DeserializeObject<GetByVaultIdCommandResponse>(response.Response);
        // }
        // catch (Exception e)
        // {
        //     workspan.RecordFatalException(e, "GRPC FAILED - FALLBACK TO MASSTRANSIT");

        vaultInfo = (await _getByVaultIdCommand.GetResponse<GetByVaultIdCommandResponse>(getVaultByIdCommand))
            .Message;
        //}

        return new FingerprintInstrumentResult()
        {
            Fingerprint = vaultInfo.Fingerprint,
            AccountUpdaterMessage = vaultInfo.AccountUpdaterMessage.HasValue
                ? vaultInfo.AccountUpdaterMessage.ToString()
                : null,
            PaymentInstrumentIsActive = vaultInfo.IsAccountUpdaterActive,
            //AccountUpdateExpiry = vaultInfo.Message.AccountUpdaterExpiry,
            AccountUpdaterResultPending = vaultInfo.AccountUpdaterResultPending,
        };
    }


    private async Task SetupDescriptor(DescriptorDTO descriptor, SaleRequestCreditCard card, SupportedGateway gateway,
        Merchant merchant)
    {
        var descriptorBuilderResult =
            await RunDescriptorBuilder(card.Bin, descriptor.Name, gateway, CancellationToken.None);

        descriptor.Name = descriptorBuilderResult.descriptor;
        descriptor.Prefix = descriptorBuilderResult.prefix;
        descriptor.Suffix = descriptorBuilderResult.suffix;

        //TODO: test if this doesnt effect any other provider 
        if (gateway.NameIdentifier is GatewayTypesConstants.Nmi_v2
            or GatewayTypesConstants.FlexChargeDummy)
        {
            Workspan.Current!.Log.Information("PAYLOAD DESCRIPTOR: {descriptor}",
                JsonConvert.SerializeObject(descriptor));

            if (CapabilitiesHelper.CanSetDescriptorAddress(null,
                    gateway.Capabilities.SupportDynamicDescriptorAddress))
            {
                //Make sure Country is properly set 840/USA/US 
                var supportedFormat = GetCountryCodeFormat(gateway);

                if (supportedFormat != CountryFormat.Unknown &&
                    _geoServices.TryGetCountry(merchant.Descriptor_Country, CountryFormat.Alpha3,
                        out var countryObj))
                {
                    descriptor.Country = supportedFormat switch
                    {
                        CountryFormat.Alpha2 => countryObj?.TwoLetterCode,
                        CountryFormat.Alpha3 => countryObj?.ThreeLetterCode,
                        CountryFormat.ISO => countryObj?.NumericCode,
                        _ => null
                    };
                }
                else
                    descriptor.Country = null;

                if (descriptor.Country == null)
                    Workspan.Current.Log.Warning(
                        "Configuration CanSetDescriptorAddress is set to TRUE but Country is not set for descriptor");

                descriptor.Country ??= merchant.Descriptor_Country;
                descriptor.Address ??= merchant.Descriptor_Address;
                descriptor.State ??= merchant.Descriptor_State;
                descriptor.Postal ??= merchant.Descriptor_Postal;
                descriptor.Phone ??= merchant.Descriptor_Phone;
                descriptor.City ??= merchant.Descriptor_City;
            }
            else
            {
                descriptor.Country = null;
                descriptor.Address = null;
                descriptor.State = null;
                descriptor.Postal = null;
                descriptor.Phone = null;
                descriptor.City = null;
            }

            if (CapabilitiesHelper.CanSetDescriptorUrl(
                    null,
                    gateway.Capabilities.SupportDynamicDescriptorUrl))
            {
                descriptor.Url ??= merchant.Descriptor_Url;
            }
            else
            {
                descriptor.Url = null;
            }

            Workspan.Current.Log.Information("PAYLOAD DESCRIPTOR AFTER ADDRESS MANIPULATION: {descriptor}",
                JsonConvert.SerializeObject(descriptor));
        }
    }

    private async Task<Transaction> GetRelatedAuthorizationAsync(Guid transactionId)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>();

        var relatedTransaction = await GetRelatedTransactionAsync(transactionId);
        if (relatedTransaction.Type != TransactionType.Authorization.ToString())
        {
            workspan.Log.Error("{Error}",
                "Cannot capture non authorized type of transaction");
            throw new Exception("Transaction type invalid");
        }

        return relatedTransaction;
    }

    private async Task<Transaction> GetRelatedTransactionAsync(Guid transactionId)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>();

        workspan.Log.Information("GetRelatedTransactionAsync transactionId: {transactionId}", transactionId);

        var relatedTransaction = await _dbContext.Transactions
            .FindAsync(transactionId);
        ArgumentNullException.ThrowIfNull(relatedTransaction);

        return relatedTransaction;
    }

    private async Task<IReadOnlyList<Transaction>> GetTransactionActivityAsync(Guid transactionId)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("TransactionId", transactionId)
            .LogEnterAndExit();

        var relatedTransactions = await _dbContext.Transactions
            .Where(x => x.ParentId == transactionId)
            .ToListAsync();

        return relatedTransactions;
    }

    private async Task<IReadOnlyList<Transaction>> GetOrderTransactionActivityAsync(Guid orderId)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("OrderId", orderId)
            .LogEnterAndExit();

        var relatedTransactions = await _dbContext.Transactions
            .Where(x => x.OrderId == orderId)
            .ToListAsync();

        return relatedTransactions;
    }


    protected T GetRequiredService<T>()
    {
        return _serviceProvider.GetRequiredService<T>();
    }

    private JsonDocument ParseMeta(string rawResult)
    {
        if (string.IsNullOrWhiteSpace(rawResult)) return null;
        try
        {
            return JsonDocument.Parse(rawResult);
        }
        catch (Exception e)
        {
            Workspan.Current?.RecordException(e, "Cannot parse {RawResult} to JsonDocument ", rawResult);
            return null;
        }
    }

    private static void ConvertExceptionToError(Exception e, BaseResult response)
    {
        var fce = e as FlexChargeException;
        if (fce != null && !string.IsNullOrWhiteSpace(fce.Code))
        {
            response.AddErrorWithCode(fce.Code, fce.Message);
        }
        else
        {
            response.AddErrorWithCode(e.Message, e.Message);
        }
    }

    private async Task PublishUpdatedCard(IAuthorizationResult response, Workspan workspan)
    {
        try
        {
            if (response.UpdatedCard is not null)
            {
                var paymentInstrument =
                    await _dbContext.PaymentInstruments.SingleAsync(p => p.Id == response.PaymentInstrumentId);

                if (response.UpdatedCard as CheckoutUpdatedCard is var newcard && newcard is not null)
                {
                    //var newcard = response.UpdatedCard;
                    await _publisher.Publish(new CheckoutCardUpdatedEvent()
                    {
                        Month = newcard.ExpiryMonth,
                        Year = newcard.ExpiryYear,
                        EncryptedCardNumber = newcard.EncryptedCardNumber,
                        PaymentInstrumentId = Guid.Parse(paymentInstrument.Token),
                        EncryptedPrivateKey = newcard.EncryptedPrivateKey,
                        KmsKey = newcard.KmsKey
                    });
                    workspan.Log.Information("Checkout Card updated event published   {PaymentInstrumentId}",
                        response.PaymentInstrumentId);
                }
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
        }
    }


    #region EXTERNAL TOKEN PAYMENTS

    public async Task<RequestResult<ChargePaymentResult>> ChargeAsync(ChargePaymentRequest payload,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", payload.Mid)
            .Baggage("OrderId", payload.OrderId)
            .Baggage("SupportedGatewayId", payload.SupportedGateway?.Id)
            .Baggage("Token", payload.Token);

        ChargePaymentResult chargeResult = null;

        try
        {
            var merchant = await GetMerchantAsync(payload.Mid);

            var supportedGateway = payload.SupportedGateway!;


            workspan
                .Baggage("SupportedGatewayId", supportedGateway!.Id)
                .Baggage("SupportedGatewayNameIdentifier", supportedGateway?.NameIdentifier)
                .Baggage("SupportedGatewayProcessorId", supportedGateway?.ProcessorId);


            InitiatePaymentProvider(supportedGateway);
            //payload.Gateway = gateway;


            // if (!_paymentProvider.SupportsCreditCards)
            //     throw new NotSupportedException("This provider does not support credit cards");

            //await StorePaymentInstrumentLocally(payload, token);

            Stopwatch paymentProviderExecutionTime = Stopwatch.StartNew();

            var response = await _paymentProvider.ChargeAsync(payload, token);
            chargeResult = response.Result;

            await CheckInternalResponseCodeMapping(payload, chargeResult);

            if (response.Succeeded && chargeResult?.Success != true)
                workspan.Log.Fatal("Charge request succeeded but response status is not success - should not happen");


            paymentProviderExecutionTime.Stop();

            var trx = new Transaction
            {
                ParentId = default,
                //PaymentMethodId = payload.PaymentInstrumentId,
                ProviderTransactionToken = chargeResult?.ProviderTransactionToken,
                //DynamicDescriptor = payload.Descriptor?.Name,
                Amount = payload.Amount,
                AuthorizationAmount = payload.Amount,
                DiscountAmount = payload.Discount,
                Currency = payload.CurrencyCode,
                ResponseCode = chargeResult?.ProviderResponseCode,
                ResponseMessage = chargeResult?.ProviderResponseMessage,
                InternalResponseCode = chargeResult?.InternalResponseCode,
                InternalResponseMessage = chargeResult?.InternalResponseMessage,
                InternalResponseGroup = chargeResult?.InternalResponseGroup,
                AvsResultCode = chargeResult?.AvsCode,
                CvvResultCode = chargeResult?.CvvCode,
                CavvResultCode = chargeResult?.CavvCode,
                AuthorizationId = chargeResult?.AuthorizationCode,
                Type = TransactionType.Charge.ToString(),
                PaymentType = nameof(PaymentMethodType.Apm),
                Status = chargeResult?.Status.HasValue == true
                    ? chargeResult.Status.Value
                    : response.Succeeded
                        ? TransactionStatus.Completed
                        : TransactionStatus.Failed,

                ProviderId = payload.Gateway?.Id ?? Guid.Empty,
                ProviderName = supportedGateway!.NameIdentifier,
                ProcessorName = supportedGateway.ProcessorPlatform,
                ProcessorId = supportedGateway.ProcessorId,
                SupportedGatewayId = supportedGateway.Id,

                PayerId = payload.PayerId ?? Guid.Empty,
                SiteId = payload.SiteId ?? Guid.Empty,
                Merchant = merchant,
                OrderId = payload.OrderId,
                IsRecurring = false,
                Meta = ParseMeta(chargeResult?.RawResult),
                SchemeTransactionId = chargeResult?.SchemeTransactionId,
                SchemeTransactionIdUsed = chargeResult?.SchemeTransactionIdUsed == true,
                //NetworkTokenUsed = response.NetworkTokenUsed,
                //AccountUpdaterUsed = accountUpdated
            };

            await _dbContext.Transactions.AddAsync(trx, token);
            await _dbContext.SaveChangesAsync(token);

            try
            {
                if (response.Succeeded && response.Result?.Success == true)
                {
                    var paymentChargedEvent = new PaymentChargedEvent
                    {
                        OrderId = payload.OrderId,
                        TransactionId = trx.Id,
                        Mid = merchant.Mid,
                        TransactionType = trx.Type.ToLower(),
                        PaymentMethodType = nameof(PaymentMethodType.CreditCard),
                        Descriptor = trx.DynamicDescriptor,
                        Description = $"Charge SUCCEEDED",
                        AmountFormatted = Formatters.LongToDecimal(payload.Amount)
                            .ToString(CultureInfo.InvariantCulture),
                        Amount = trx.Amount,
                        AuthorizationAmount = trx.AuthorizationAmount,
                        DiscountAmount = trx.DiscountAmount,
                        FeeAmount = trx.FeeAmount,
                        PaymentDate = trx.CreatedOn,

                        ProviderId = payload.Gateway?.Id ?? Guid.Empty,
                        Provider = supportedGateway.NameIdentifier,
                        Processor = supportedGateway.ProcessorPlatform,
                        ProcessorId = supportedGateway.ProcessorId,
                        SupportedGatewayId = supportedGateway.Id,

                        ProviderResponseCode = trx.ResponseCode,
                        ProviderTransactionToken = trx.ProviderTransactionToken,
                        ProviderResponseDescription = trx.ResponseMessage,
                        InternalResponseCode = trx.InternalResponseCode,
                        InternalResponseMessage = trx.InternalResponseMessage,
                        InternalResponseGroup = trx.InternalResponseGroup,
                        CvvCode = trx.CvvResultCode,
                        AvsCode = trx.AvsResultCode,
                        ProcessorResponseCode = trx.ResponseCode,
                        ProcessorResponseDescription = trx.ResponseMessage,
                        NetworkTokenUsed = trx.NetworkTokenUsed,
                        Bin = trx.PaymentMethod?.Bin,
                        Last4 = trx.PaymentMethod?.Last4,
                        //AccountUpdaterUsed = accountUpdated
                    };

                    await _publisher.Publish(paymentChargedEvent, token);
                    //await PublishUpdatedCard(response, workspan);

                    await _activityService.CreateActivityAsync(
                        PaymentsActivities.Payments_ChargePayment_Succeeded,
                        data: paymentChargedEvent,
                        set => set
                            .CorrelationId(payload.OrderId)
                            .TenantId(payload.Mid)
                            .Meta(meta => meta
                                .TransactionReference(trx.Id)
                                .SetValue("Amount", trx.Amount)
                                .SetValue("DiscountAmount", trx.DiscountAmount)
                                //.SetValue("AccountUpdaterUsed", accountUpdated)
                                .SetValue("CIT", payload.IsCit)
                                .AddProviderResponseMetadata(trx, paymentProviderExecutionTime)
                                .SetValue("PaymentInstrumentId", payload.PaymentInstrumentId)
                                .SetValue("PaymentMethodType", trx.PaymentType)
                                .SetValue("Bin", trx.PaymentMethod?.Bin)
                                .SetValue("Last4", trx.PaymentMethod?.Last4)
                            ));
                }
                else
                {
                    if (response.Result?.Success == true)
                        workspan.Log.Fatal("Charge request failed but response status is success - should not happen");

                    var paymentChargeFailedEvent = new PaymentChargeFailedEvent
                    {
                        OrderId = payload.OrderId,
                        TransactionId = trx.Id,
                        Mid = merchant.Mid,
                        TransactionType = trx.Type.ToLower(),
                        PaymentMethodType = nameof(PaymentMethodType.CreditCard),
                        //PaymentInstrumentId = trx.PaymentMethodId,
                        Descriptor = trx.DynamicDescriptor,
                        Description = $"Charge FAILED",
                        AmountFormatted = Formatters.LongToDecimal(payload.Amount)
                            .ToString(CultureInfo.InvariantCulture),
                        Amount = trx.Amount,
                        AuthorizationAmount = trx.AuthorizationAmount,
                        DiscountAmount = trx.DiscountAmount,
                        FeeAmount = trx.FeeAmount,
                        PaymentDate = trx.CreatedOn,

                        ProviderId = payload.Gateway?.Id ?? Guid.Empty,
                        Provider = supportedGateway.NameIdentifier,
                        Processor = supportedGateway.ProcessorPlatform,
                        ProcessorId = supportedGateway.ProcessorId,
                        SupportedGatewayId = supportedGateway.Id,

                        ProviderResponseCode = trx.ResponseCode,
                        ProviderTransactionToken = trx.ProviderTransactionToken,
                        ProviderResponseDescription = trx.ResponseMessage,
                        InternalResponseCode = trx.InternalResponseCode,
                        InternalResponseMessage = trx.InternalResponseMessage,
                        InternalResponseGroup = trx.InternalResponseGroup,
                        CvvCode = trx.CvvResultCode,
                        AvsCode = trx.AvsResultCode,
                        ProcessorResponseCode = trx.ResponseCode,
                        ProcessorResponseDescription = trx.ResponseMessage,
                        //NetworkTokenUsed = trx.NetworkTokenUsed,
                        //AccountUpdaterUsed = accountUpdated
                    };

                    await _publisher.Publish(paymentChargeFailedEvent, token);

                    await _activityService.CreateActivityAsync(PaymentsActivities.Payments_ChargePayment_Failed,
                        data: paymentChargeFailedEvent,
                        set => set
                            .CorrelationId(payload.OrderId)
                            .TenantId(payload.Mid)
                            .Meta(meta => meta
                                .TransactionReference(trx.Id)
                                .SetValue("Amount", trx.Amount)
                                .SetValue("DiscountAmount", trx.DiscountAmount)
                                //.SetValue("AccountUpdaterUsed", accountUpdated)
                                .SetValue("CIT", payload.IsCit)
                                .AddProviderResponseMetadata(trx, paymentProviderExecutionTime)
                                .SetValue("PaymentInstrumentId", payload.PaymentInstrumentId)
                                .SetValue("PaymentMethodType", trx.PaymentType)
                                .SetValue("Bin", trx.PaymentMethod?.Bin)
                                .SetValue("Last4", trx.PaymentMethod?.Last4)
                            ));
                }
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e,
                    $"Publish failed");
            }

            chargeResult.TransactionId = trx.Id;
            chargeResult.ProviderTransactionToken = trx.ProviderTransactionToken;
            chargeResult.OrderId = trx.OrderId;
            //chargeResult.GatewayOrder = gateway!.Order.Value;
            //chargeResult.NextGatewayOrder = nextGateway?.Order;
            chargeResult.Provider = supportedGateway?.NameIdentifier;
            chargeResult.ProcessorId = supportedGateway?.ProcessorId;
            //chargeResult.ProviderId = gateway?.Id ?? Guid.Empty;
            chargeResult.InternalResponseCode = trx.InternalResponseCode;
            chargeResult.InternalResponseMessage = trx.InternalResponseMessage;
            chargeResult.InternalResponseGroup = trx.InternalResponseGroup;

            return response;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "Charge external token failed");

            await _activityService.CreateActivityAsync(PaymentsErrorActivities.Payments_ChargePayment_Error,
                set => set
                    .TenantId(payload.Mid)
                    .CorrelationId(payload.OrderId)
                    .Data(e)
                    .Meta(meta => meta
                        .SetValue("Amount", payload.Amount)
                        .SetValue("DiscountAmount", payload.Discount)
                        .Error(e.Message)
                    ));

            if (chargeResult == null)
            {
                chargeResult = new ChargePaymentResult();
            }

            ConvertExceptionToError(e, chargeResult);
            chargeResult.CanBeRetried = true;

            return RequestResult.Error(e, chargeResult);
        }
    }

    public async Task<RequestResult<CanChargeExternalTokenResult>> CanChargeExternalTokenAsync(
        CanChargeExternalTokenRequest payload,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", payload.Mid)
            .Baggage("OrderId", payload.OrderId)
            .Baggage("SupportedGatewayId", payload.SupportedGateway?.Id)
            .Baggage("Token", payload.Token);

        RequestResult<CanChargeExternalTokenResult> response;


        try
        {
            var supportedGateway = payload.SupportedGateway!;


            workspan
                .Baggage("SupportedGatewayId", supportedGateway!.Id)
                .Baggage("SupportedGatewayNameIdentifier", supportedGateway?.NameIdentifier)
                .Baggage("SupportedGatewayProcessorId", supportedGateway?.ProcessorId);


            InitiatePaymentProvider(supportedGateway);

            Stopwatch paymentProviderExecutionTime = Stopwatch.StartNew();

            response = await _paymentProvider.CanChargeExternalTokenAsync(payload, token);

            paymentProviderExecutionTime.Stop();

            #region Observabiity

            if (response.Succeeded)
            {
                await _activityService.CreateActivityAsync(
                    PaymentsActivities.Payments_CanChargeExternalToken_Succeeded,
                    set => set
                        .CorrelationId(payload.OrderId)
                        .TenantId(payload.Mid)
                        .Meta(meta => meta
                            .SetValue("CanCharge", response.Result?.CanCharge == true)
                            .ServiceProvider(supportedGateway?.NameIdentifier)
                            .SetValue("SupportedGatewayName", supportedGateway?.Name)
                            .SetValue("SupportedGatewayId", supportedGateway?.Id)
                            .ProcessingTime(paymentProviderExecutionTime.ElapsedMilliseconds)
                        )
                );
            }
            else
            {
                await _activityService.CreateActivityAsync(PaymentsActivities.Payments_CanChargeExternalToken_Failed,
                    set => set
                        .CorrelationId(payload.OrderId)
                        .TenantId(payload.Mid)
                        .Meta(meta => meta
                            .SetValue("CanCharge", response.Result?.CanCharge == true)
                            .ServiceProvider(supportedGateway?.NameIdentifier)
                            .SetValue("SupportedGatewayName", supportedGateway?.Name)
                            .SetValue("SupportedGatewayId", supportedGateway?.Id)
                            .ProcessingTime(paymentProviderExecutionTime.ElapsedMilliseconds)
                        )
                );
            }

            #endregion
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);

            await _activityService.CreateActivityAsync(PaymentsErrorActivities.Payments_CanChargeExternalToken_Error,
                set => set
                    .TenantId(payload.Mid)
                    .CorrelationId(payload.OrderId)
                    .Data(e)
                    .Meta(meta => meta
                        .SetValue("Amount", payload.Amount)
                        .SetValue("DiscountAmount", payload.Discount)
                        .Error(e.Message)
                    ));

            response = RequestResult.Error<CanChargeExternalTokenResult>(e);
        }

        return response;
    }

    public async Task<RequestResult<CancelExternalSubscriptionResult>> CancelExternalSubscriptionAsync(
        CancelExternalSubscriptionRequest payload, CancellationToken token)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", payload.Mid)
            .Baggage("OrderId", payload.OrderId)
            .Baggage("SupportedGatewayId", payload.SupportedGateway?.Id)
            .Baggage("Token", payload.Token)
            .Baggage("ExternalAccountId", payload.ExternalAccountId)
            .Baggage("ExternalSubscriptionId", payload.ExternalSubscriptionId)
            .Baggage("PaymentInstrumentId", payload.PaymentInstrumentId)
            .Baggage("CanCancelActiveSubscriptions", payload.CanCancelActiveSubscription)
            .LogEnterAndExit();

        //var result = new CancelExternalSubscriptionResult();


        try
        {
            //var merchant = await GetMerchantAsync(payload.Mid);

            var supportedGateway = payload.SupportedGateway!;


            workspan
                .Baggage("SupportedGatewayId", supportedGateway!.Id)
                .Baggage("SupportedGatewayNameIdentifier", supportedGateway?.NameIdentifier)
                .Baggage("SupportedGatewayProcessorId", supportedGateway?.ProcessorId);


            InitiatePaymentProvider(supportedGateway);

            SubscriptionGetRequest subscriptionGetRequest = new()
            {
                ExternalAccountId = payload.ExternalAccountId,
                SubscriptionId = null, // no internal id for now for external subscriptions
                ProviderSubscriptionId = payload.ExternalSubscriptionId,
                SupportedGateway = supportedGateway
            };

            if (!payload.CanCancelActiveSubscription)
            {
                var subscriptionState =
                    await _paymentProvider.GetSubscriptionStatusAsync(subscriptionGetRequest, token);
                if (subscriptionState.SubscriptionIsActive == true)
                {
                    workspan.Log.Information("Subscription is active - skipping cancellation");

                    return RequestResult.Success(new CancelExternalSubscriptionResult()
                    {
                        CanBeRetried = false,
                        CancellationSkippedForActiveSubscription = true
                    });
                }
            }

            var result = await _paymentProvider.CancelSubscriptionAsync(subscriptionGetRequest, token);

            workspan
                .Response(result);

            if (result.Succeeded)
            {
                // subscription.IsAlive = false;
                // subscription.CancelledAt = DateTime.UtcNow;
                //await _dbContext.SaveChangesAsync(token);

                workspan.Log.Information("External subscription cancelled");

                await _activityService.CreateActivityAsync(
                    ExternalSubscriptionActivities.ExternalProvider_CancelSubscription_Succeded,
                    set => set
                        .TenantId(payload.Mid)
                        .CorrelationId(payload.OrderId)
                        .Meta(meta => meta
                            //.SetValue("SubscriptionId", subscriptionGetRequest.SubscriptionId)
                            .SetValue("AccountId", payload.ExternalAccountId)
                            .SetValue("SubscriptionId", subscriptionGetRequest.ProviderSubscriptionId)
                        )
                );
            }
            else
            {
                workspan.Log.Warning("External subscription cancellation failed");

                await _activityService.CreateActivityAsync(
                    ExternalSubscriptionActivities.ExternalProvider_CancelSubscription_Failed,
                    set => set
                        .TenantId(payload.Mid)
                        .CorrelationId(payload.OrderId)
                        .Meta(meta => meta
                            //.SetValue("SubscriptionId", subscriptionGetRequest.SubscriptionId)
                            .SetValue("AccountId", payload.ExternalAccountId)
                            .SetValue("SubscriptionId", subscriptionGetRequest.ProviderSubscriptionId)
                        )
                );

                //
                // await _publisher.Publish(new SubscriptionCancellationFailedEvent()
                // {
                //     SubscriptionId = subscription.Id
                // });
            }

            return result;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "Cancel external subscription failed");

            await _activityService.CreateActivityAsync(
                ExternalSubscriptionErrorActivities.ExternalProvider_CancelSubscription_Error,
                set => set
                    .TenantId(payload.Mid)
                    .CorrelationId(payload.OrderId)
                    .Data(e)
                    .Meta(meta => meta
                        //.SetValue("SubscriptionId", subscriptionGetRequest.SubscriptionId)
                        .SetValue("AccountId", payload.ExternalAccountId)
                        .SetValue("SubscriptionId", payload.ExternalSubscriptionId)
                    )
            );

            return RequestResult.Error(e, new CancelExternalSubscriptionResult()
            {
                CanBeRetried = true
            });
        }
    }

    public async Task<RequestResult<MarkInvoiceAsPaidOutOfBandResult>>
        MarkExternalInvoiceAsPaidOutOfBandAndRescuedAsync(
            MarkInvoiceAsPaidOutOfBandRequest payload,
            CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Baggage("Mid", payload.Mid)
            .Baggage("OrderId", payload.OrderId)
            .Baggage("SupportedGatewayId", payload.SupportedGateway?.Id)
            .Baggage("ExternalAccountId", payload.ExternalAccountId)
            .Baggage("InvoiceId", payload.InvoiceId)
            .LogEnterAndExit();

        try
        {
            var supportedGateway = payload.SupportedGateway!;


            workspan
                .Baggage("SupportedGatewayId", supportedGateway!.Id)
                .Baggage("SupportedGatewayNameIdentifier", supportedGateway?.NameIdentifier)
                .Baggage("SupportedGatewayProcessorId", supportedGateway?.ProcessorId);


            InitiatePaymentProvider(supportedGateway);

            MarkInvoiceAsPaidOutOfBandRequest request = new()
            {
                ExternalAccountId = payload.ExternalAccountId,
                InvoiceId = payload.InvoiceId,
                SupportedGateway = supportedGateway
            };

            var result = await _paymentProvider.MarkInvoiceAsPaidOutOfBandAsync(request, token);

            workspan
                .Response(result);

            if (result.Succeeded)
            {
                workspan.Log.Information("External invoice marked as paid out of band");

                await _activityService.CreateActivityAsync(
                    ExternalSubscriptionActivities.ExternalProvider_MarkInvoicePaidOutOfBand_Succeded,
                    set => set
                        .TenantId(payload.Mid)
                        .CorrelationId(payload.OrderId)
                        .Meta(meta => meta
                            //.SetValue("SubscriptionId", subscriptionGetRequest.SubscriptionId)
                            .SetValue("AccountId", payload.ExternalAccountId)
                            .SetValue("InvoiceId", request.InvoiceId)
                        )
                );
            }
            else
            {
                workspan.Log.Warning("External invoice marking as paid out of band failed");

                await _activityService.CreateActivityAsync(
                    ExternalSubscriptionActivities.ExternalProvider_MarkInvoicePaidOutOfBand_Failed,
                    set => set
                        .TenantId(payload.Mid)
                        .CorrelationId(payload.OrderId)
                        .Meta(meta => meta
                            //.SetValue("SubscriptionId", subscriptionGetRequest.SubscriptionId)
                            .SetValue("AccountId", payload.ExternalAccountId)
                            .SetValue("InvoiceId", request.InvoiceId)
                        )
                );
            }

            return result;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "Cannot mark external invoice as paid out of band");

            await _activityService.CreateActivityAsync(
                ExternalSubscriptionErrorActivities.ExternalProvider_MarkInvoicePaidOutOfBand_Error,
                set => set
                    .TenantId(payload.Mid)
                    .CorrelationId(payload.OrderId)
                    .Data(e)
                    .Meta(meta => meta
                        //.SetValue("SubscriptionId", subscriptionGetRequest.SubscriptionId)
                        .SetValue("AccountId", payload.ExternalAccountId)
                        .SetValue("InvoiceId", payload.InvoiceId)
                    )
            );

            return RequestResult.Error(e, new MarkInvoiceAsPaidOutOfBandResult()
            {
                CanBeRetried = true
            });
        }
    }

    #endregion
}