using System;
using System.Collections.Generic;
using System.Linq;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.CardBrands;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.PaymentServices.Models;

namespace FlexCharge.Payments.Services.PaymentServices;

public static class GatewayFilterHelper
{
    /// <summary>
    /// These filters return true if gateway passes the filter
    /// </summary>
    /// <returns></returns>
    private static List<(string name, Func<Gateway, OrchestrationOptions, bool> handler)> CreateDefaultPassFilters()
    {
        return new()
        {
            ("IsActive", (gateway, _) => gateway.IsActive && gateway.SupportedGateway.IsActive),
            // ("Is_Not_Quarantined", Is_Not_Quarantined),
            ("NotBlocked",
                (gateway, options) =>
                    options.BlocklistedProviders?.Contains(gateway.SupportedGateway.NameIdentifier) != true),
            ("NotNullCapabilities", (gateway, _) => AreCapabilitiesPresent(gateway)),
            ("NotCompatibleProviders",
                (gateway, _) => gateway.SupportedGateway.NameIdentifier != GatewayTypesConstants.Svb),
            ("IsCountrySupported", IsCountrySupported),
            ("IsCardBrandSupported", IsCardBrandSupported),
            ("IsCurrencySupported", IsCurrencySupported),
            ("IsSupportingCIT", IsSupportingCIT),
            ("IsSupportingMIT", IsSupportingMIT),
            ("Is_Below_Total_Limit", Is_Below_Total_Count_Limit),
            ("Is_Below_Total_Amount_Limit", Is_Below_Total_Amount_Limit),
            ("Is_Below_MC_CountLimit", Is_Below_MC_CountLimit),
            ("Is_Below_Visa_CountLimit", Is_Below_Visa_CountLimit),
            ("Is_Below_MC_ValueLimit", Is_Below_MC_ValueLimit),
            ("Is_Below_Visa_ValueLimit", Is_Below_Visa_ValueLimit)
        };
    }

    public static IEnumerable<Gateway> FilterGateways(
        IEnumerable<Gateway> gateways,
        Guid? providerId,
        Guid mid,
        OrchestrationOptions? options)
    {
        var gatewayFilters = CreateDefaultPassFilters();

        gatewayFilters.Add(("Filter_By_Provider_Or_Order", Filter_By_Provider_Or_Order));
        return FilterGateways(gateways, gatewayFilters, mid, options);

        bool Filter_By_Provider_Or_Order(Gateway gateway, OrchestrationOptions? options)
        {
            return providerId != null
                ? gateway.Id == providerId
                : gateway.Order == options?.Order;
        }
    }

    public static IEnumerable<Gateway> FilterGateways(
        IEnumerable<Gateway> gateways,
        Guid mid,
        OrchestrationOptions? options)
    {
        var gatewayFilters = CreateDefaultPassFilters();

        return FilterGateways(gateways, gatewayFilters, mid, options);
    }

    public static IEnumerable<Gateway> FilterGateways(
        IEnumerable<Gateway> gateways,
        Guid mid,
        OrchestrationOptions options,
        Func<Gateway, bool>? matchCondition)
    {
        var gatewayFilters = CreateDefaultPassFilters();

        if (matchCondition is not null)
        {
            gatewayFilters.Add(("Filter_By_Match_Condition", (g, _) => matchCondition(g)));
        }

        return FilterGateways(gateways, gatewayFilters, mid, options);
    }


    private static IEnumerable<Gateway> FilterGateways(
        IEnumerable<Gateway> gateways,
        List<(string name, Func<Gateway, OrchestrationOptions, bool> handler)> passFilters,
        Guid mid,
        OrchestrationOptions? options)
    {
        using var workspan = Workspan.Start<PaymentOrchestrator>()
            .Tag("Mid", mid)
            .Tag("Options", options);

        var rejectReasons = new HashSet<string>();
        List<Gateway> gatewaysList = new();

        try
        {
            foreach (var gateway in gateways)
            {
                var isGatewayPassed = true;
                foreach (var (name, passFilter) in passFilters)
                {
                    if (!passFilter(gateway, options))
                    {
                        rejectReasons.Add($"{gateway.SupportedGateway.Name} > {name}");
                        isGatewayPassed = false;
                        break;
                    }
                }

                if (isGatewayPassed)
                {
                    gatewaysList.Add(gateway);
                }
            }

            var reasons = string.Join(',', rejectReasons.ToArray());

            workspan
                .Tag("RejectReasons", reasons);

            if (gatewaysList.Count == 0)
            {
                workspan.Log
                    .Information("Could not find gateway");
            }
            else
            {
                workspan
                    .Tag("Gateways", gatewaysList.Select(x => x.SupportedGateway.Name).ToArray());

                workspan.Log
                    .Information("Gateways found: {GatewaysFound}", gatewaysList.Count);
            }
        }

        catch (Exception e)
        {
            Workspan.Current.RecordException(e);
            throw;
        }

        return gatewaysList;
    }

    static bool Is_Not_Quarantined(Gateway gateway, OrchestrationOptions? options)
    {
        if (gateway.SupportedGateway.Metrics?.Quarantine_AddedOn == null)
            return true;

        if (gateway.SupportedGateway.Metrics.Quarantine_AddedOn.Value.Add(TimeSpan.FromHours(12)) >= DateTime.UtcNow)
            return false;

        return true;
    }

    static bool IsCardBrandSupported(Gateway gateway, OrchestrationOptions? options)
    {
        var brand = options?.Brand.ToString();
        try
        {
            if (brand == null)
                return true;

            if (gateway.Capabilities?.CardBrands != null)
                return gateway.Capabilities?.CardBrands?.Contains(brand) == true;

            if (gateway.SupportedGateway.Capabilities?.CardBrands != null)
                return gateway.SupportedGateway.Capabilities.CardBrands?.Contains(brand) == true;

            return true;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }

        return false;
    }


    static bool IsCountrySupported(Gateway gateway, OrchestrationOptions? options)
    {
        var country = options?.Country;

        if (country == null)
            return true;

        if (gateway.Capabilities?.SupportedCountries != null)
            return gateway.Capabilities?.SupportedCountries?.Contains(country) == true;

        if (gateway.SupportedGateway.Capabilities?.SupportedCountries != null)
            return gateway.SupportedGateway.Capabilities.SupportedCountries?.Contains(country) == true;

        return true;
    }

    static bool IsCurrencySupported(Gateway gateway, OrchestrationOptions? options)
    {
        var currency = options?.Currency;

        if (currency == null)
            return true;

        if (gateway.Capabilities?.SupportedCurrencies != null)
            return gateway.Capabilities?.SupportedCurrencies?.Contains(currency) == true;

        if (gateway.SupportedGateway.Capabilities?.SupportedCurrencies != null)
            return gateway.SupportedGateway.Capabilities.SupportedCurrencies?.Contains(currency) == true;

        return true;
    }


    static bool IsSupportingCIT(Gateway gateway, OrchestrationOptions? options)
    {
        if (options?.IsCIT != true) return true;


        if (gateway.Capabilities?.SupportCIT != null)
            return gateway.Capabilities?.SupportCIT == true;

        if (gateway.SupportedGateway?.Capabilities?.SupportCIT != null)
            return gateway.SupportedGateway?.Capabilities?.SupportCIT == true;

        return true;
    }


    static bool IsSupportingMIT(Gateway gateway, OrchestrationOptions? options)
    {
        if (options?.IsCIT == true) return true;

        if (gateway.Capabilities?.SupportMIT != null)
            return gateway.Capabilities?.SupportMIT == true;

        if (gateway.SupportedGateway?.Capabilities?.SupportMIT != null)
            return gateway.SupportedGateway?.Capabilities?.SupportMIT == true;

        return true;
    }

    static bool Is_Below_MC_CountLimit(Gateway gateway, OrchestrationOptions? options)
    {
        if (options?.Brand != CardBrand.MasterCard && options?.Brand != CardBrand.Maestro)
            return true;

        var currentCount = gateway.SupportedGateway?.Metrics?.MasterCardCurrentTransactionsCount;
        var maxCount = gateway.SupportedGateway?.Capabilities?.MasterCard_MonthlyMaxTransactionsCount;

        // Check if currentCount and maxCount are not null before comparing
        if (currentCount.HasValue && maxCount.HasValue)
        {
            return currentCount.Value < maxCount.Value;
        }

        return true;
    }

    static bool Is_Below_Visa_CountLimit(Gateway gateway, OrchestrationOptions? options)
    {
        if (options?.Brand != CardBrand.Visa)
            return true;

        var currentCount = gateway.SupportedGateway?.Metrics?.VisaCurrentTransactionsCount;
        var maxCount = gateway.SupportedGateway?.Capabilities?.Visa_MonthlyMaxTransactionsCount;

        // Check if currentCount and maxCount are not null before comparing
        if (currentCount.HasValue && maxCount.HasValue)
        {
            return currentCount.Value < maxCount.Value;
        }

        return true;
    }

    static bool Is_Below_MC_ValueLimit(Gateway gateway, OrchestrationOptions? options)
    {
        if (options?.Brand != CardBrand.MasterCard && options?.Brand != CardBrand.Maestro)
            return true;

        var currentTransactionsValue = gateway.SupportedGateway?.Metrics?.MasterCardCurrentTransactionsValue;
        var monthlyMaxValue = gateway.SupportedGateway?.Capabilities?.MasterCard_MonthlyMaxValue;

        // Check if currentTransactionsValue and monthlyMaxValue are not null before comparing
        if (currentTransactionsValue.HasValue && monthlyMaxValue.HasValue)
        {
            return currentTransactionsValue.Value < monthlyMaxValue.Value;
        }

        return true;
    }

    static bool Is_Below_Visa_ValueLimit(Gateway gateway, OrchestrationOptions? options)
    {
        if (options?.Brand != CardBrand.Visa)
            return true;

        var currentTransactionsValue = gateway.SupportedGateway?.Metrics?.VisaCurrentTransactionsValue;
        var monthlyMaxValue = gateway.SupportedGateway?.Capabilities?.Visa_MonthlyMaxValue;

        // Check if currentTransactionsValue and monthlyMaxValue are not null before comparing
        if (currentTransactionsValue.HasValue && monthlyMaxValue.HasValue)
        {
            return currentTransactionsValue.Value < monthlyMaxValue.Value;
        }

        return true;
    }


    static bool Is_Below_Total_Count_Limit(Gateway gateway, OrchestrationOptions? options)
    {
        var currentCount = gateway.SupportedGateway?.Metrics?.TotalTransactionCount;
        var maxCount = gateway.SupportedGateway?.Capabilities?.TotalMonthlyMaxTransactionsCount;

        // Check if currentCount and maxCount are not null before comparing
        if (currentCount.HasValue && maxCount.HasValue)
        {
            return currentCount.Value < maxCount.Value;
        }

        return true;
    }

    static bool Is_Below_Total_Amount_Limit(Gateway gateway, OrchestrationOptions? options)
    {
        var currentAmount = gateway.SupportedGateway?.Metrics?.TotalTransactionAmount;
        var maxAmount = gateway.SupportedGateway?.Capabilities?.TotalMonthlyMaxTransactionsValue;

        // Check if currentCount and maxCount are not null before comparing
        if (currentAmount.HasValue && maxAmount.HasValue)
        {
            return currentAmount.Value < maxAmount.Value;
        }

        return true;
    }

    public static bool? Is_Below_Daily_Amount_Limit(Gateway gateway, OrchestrationOptions? options)
    {
        var current = gateway?.SupportedGateway?.Metrics?.TotalDailyTransactionAmount;
        var max = gateway?.SupportedGateway?.Capabilities?.TotalDailyMaxTransactionsValue;

        if (max == null)
            return null;

        // Check if currentCount and maxCount are not null before comparing
        if (current.HasValue && max.HasValue)
        {
            return current.Value < max.Value;
        }

        return true;
    }

    public static bool? Is_Below_Daily_Count_Limit(Gateway gateway, OrchestrationOptions? options)
    {
        var current = gateway?.SupportedGateway?.Metrics?.TotalDailyTransactionCount;
        var max = gateway?.SupportedGateway?.Capabilities?.TotalDailyMaxTransactionsCount;

        if (max == null)
            return null;

        // Check if currentCount and maxCount are not null before comparing
        if (current.HasValue && max.HasValue)
        {
            return current.Value < max.Value;
        }

        return true;
    }

    static bool AreCapabilitiesPresent(Gateway gateway)
    {
        if (gateway?.SupportedGateway?.Capabilities == null)
        {
            Workspan.Current!.Log
                .Fatal("No capabilities defined for gateway: {GatewayName}", gateway.SupportedGateway.NameIdentifier);

            return false;
        }
        else
        {
            return true;
        }
    }
}