using System;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using Adyen.Model.Notification;
using Adyen.Util;
using FlexCharge.Common.Telemetry;
using FlexCharge.Payments.Services.PaymentServices.Providers.Adyen;
using FlexCharge.Utils.JsonConverters;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace FlexCharge.Payments.Controllers.GatewayControllers;

public class AdyenWebhookService
{
    private readonly HmacValidator _hmacValidator;

    private readonly PostgreSQLDbContext _dbContext;

    private JsonSerializerOptions _jsonOptions;


    public AdyenWebhookService(HmacValidator hmacValidator,
        PostgreSQLDbContext dbContext)
    {
        _hmacValidator = hmacValidator;
        _dbContext = dbContext;

        _jsonOptions = new()
        {
            ReferenceHandler = ReferenceHandler.IgnoreCycles,
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
        _jsonOptions.Converters.Add(new JsonStringEnumConverter());
        _jsonOptions.Converters.Add(new IgnoreEmptyStringNullableEnumConverter());
        _jsonOptions.Converters.Add(new NullToEmptyStringConverter());
    }

    public async Task<(bool success, string message)> Webhooks(string requestBody,
        Guid supportedGatewayId)
    {
        var workspan = Workspan
            .Start<AdyenWebhookService>()
            .Tag("supportedGatewayId", supportedGatewayId)
            .LogEnterAndExit();


        // Process the payment (AUTHORISATION) webhook.
        workspan.Log.Information($"Webhook received {requestBody}",
            requestBody);


        try
        {
            var notificationRequest = JsonConvert.DeserializeObject<NotificationRequest>(requestBody);

            var gateway = await _dbContext.SupportedGateways.FindAsync(supportedGatewayId);
            if (gateway == null)
            {
                workspan.Log.Error($"Adyen gateway not found");
                return (false, "Gateway not found");
            }

            var cofig = JsonSerializer.Deserialize<AdyenConfiguration>(gateway.Configuration, _jsonOptions);
            var hmacKey = cofig.WebhookHmacKey;

            if (hmacKey is null)
            {
                workspan.Log.Error($"Adyen hmac key not found");
                return (false, "Adyen hmac key not found");
            }

            // JSON and HTTP POST notifications always contain a single `NotificationRequestItem` object.
            // Read more: https://docs.adyen.com/development-resources/webhooks/understand-notifications#notification-structure.
            NotificationRequestItemContainer container =
                notificationRequest.NotificationItemContainers?.FirstOrDefault();

            if (container == null)
            {
                return (false, "Adyen Container has no notification items.");
            }

            // We always recommend to activate HMAC validation in the webhooks for security reasons.
            // Read more here: https://docs.adyen.com/development-resources/webhooks/verify-hmac-signatures & https://docs.adyen.com/development-resources/webhooks#accept-notifications.
            if (!_hmacValidator.IsValidHmac(container.NotificationItem, hmacKey))
            {
                workspan.Log.Error($"Adyen Error while validating HMAC Key");
                return (false, "not accepted invalid hmac key");
            }

            // Process notification asynchronously.
            await ProcessNotificationAsync(container.NotificationItem);

            // Return a 202 status with an empty response body
            return (true, null);
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "Exception thrown at Adyen webhook");
            return (false, "generic exception");
        }
    }

    private ActionResult<string> Accepted()
    {
        throw new NotImplementedException();
    }


    private Task ProcessNotificationAsync(NotificationRequestItem notification)
    {
        // Regardless of a success or not, you'd probably want to update your backend/database or (preferably) send the event to a queue for further processing.

        if (!notification.Success)
        {
            //Perform  business logic here, process the success:false event to update your backend.
            Workspan.Current.Log.Information($"Webhook unsuccessful: {notification.Reason} \n" +
                                             $"EventCode: {notification.EventCode} \n" +
                                             $"Merchant Reference ::{notification.MerchantReference} \n" +
                                             $"PSP Reference ::{notification.PspReference} \n");

            return Task.CompletedTask;
        }


        // Perform your business logic here, process the success:true event to update your backend. 
        Workspan.Current.Log.Information($"Received successful Webhook with event::\n" +
                                         $"EventCode: {notification.EventCode} \n" +
                                         $"Merchant Reference ::{notification.MerchantReference} \n" +
                                         $"PSP Reference ::{notification.PspReference} \n");
        return Task.CompletedTask;
    }
}