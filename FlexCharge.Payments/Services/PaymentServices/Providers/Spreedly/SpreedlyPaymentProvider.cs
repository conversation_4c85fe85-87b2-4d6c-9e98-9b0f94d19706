using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.CardBrands;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.PaymentInstrumentsServices;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using FlexCharge.Payments.Services.SpreedlyService;
using FlexCharge.Payments.Services.SVBAchServices;
using MassTransit;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Merchant = FlexCharge.Payments.Entities.Merchant;


namespace FlexCharge.Payments.Services.PaymentServices
{
    public class SpreedlyPaymentProvider : PaymentProviderBase
    {
        const int DUPLICATE_ORDER_CHECK_DEFAULT_INTERVAL = 20 * 60; // 20 minutes

        private readonly PostgreSQLDbContext _context;
        private readonly IPaymentInstrumentsService _paymentInstrumentsService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        private readonly ISpreedlyService _spreedlyService;
        public IServiceProvider ServiceProvider { get; private set; }
        private readonly IMapper _mapper;
        private readonly IPublishEndpoint _publisher;
        private readonly IBackgroundWorkerCommandQueue _backgroundWorkerCommandQueue;

        public SpreedlyPaymentProvider(
            IMapper mapper, ISpreedlyService spreedlyService,
            PostgreSQLDbContext context, IHttpContextAccessor httpContextAccessor,
            IPaymentInstrumentsService paymentInstrumentsService, IPublishEndpoint publisher, ISVBService achService,
            IBackgroundWorkerCommandQueue backgroundWorkerCommandQueue)
        {
            _mapper = mapper;
            _spreedlyService = spreedlyService;
            _context = context;
            _httpContextAccessor = httpContextAccessor;
            _paymentInstrumentsService = paymentInstrumentsService;
            _publisher = publisher;
            _backgroundWorkerCommandQueue = backgroundWorkerCommandQueue;
        }

        public override async Task<SaleResult> SaleAsync(SaleRequest payload, CancellationToken token)
        {
            using var workspan = Workspan.Start<SpreedlyPaymentProvider>()
                .Baggage("Mid", payload.Mid)
                .Baggage("OrderId", payload.OrderId)
                .Baggage("GatewayId", payload.Gateway?.Id);


            var response = new SaleResult();

            try
            {
                var merchant = await GetMerchantAsync(payload.Mid);

                var descriptor = default(Descriptors);
                if (payload.UseDynamicDescriptor)
                {
                    descriptor = new Descriptors();

                    if (payload.UseDynamicDescriptor)
                    {
                        descriptor.Descriptor = payload.Descriptor?.Name ?? merchant.Descriptor;
                        descriptor.Descriptor_Phone = merchant.Descriptor_Phone;
                        descriptor.Descriptor_Address = merchant.Descriptor_Address;
                        descriptor.Descriptor_City = merchant.Descriptor_City;
                        descriptor.Descriptor_State = merchant.Descriptor_State;
                        descriptor.Descriptor_Postal = merchant.Descriptor_Postal;
                        descriptor.Descriptor_Country = merchant.Descriptor_Country;
                        descriptor.Descriptor_Mcc = merchant.Descriptor_Mcc;
                        descriptor.Descriptor_Url = merchant.Descriptor_Url;
                        descriptor.Descriptor_Merchant_Id = merchant.Descriptor_Merchant_Id;
                    }
                }

                var gatewayFields = new GatewaySpecificFields();
                var supportGlobal_Three_Ds = false;
                switch (payload.SupportedGateway.NameIdentifier)
                {
                    case "nmi":
                    {
                        supportGlobal_Three_Ds = true;
                        if (payload.UseDynamicDescriptor)
                        {
                            descriptor.Descriptor = payload.Descriptor?.Name ?? merchant.Descriptor;
                            descriptor.Descriptor_Phone = merchant.Descriptor_Phone;
                            descriptor.Descriptor_Address = merchant.Descriptor_Address;
                            descriptor.Descriptor_City = merchant.Descriptor_City;
                            descriptor.Descriptor_State = merchant.Descriptor_State;
                            descriptor.Descriptor_Postal = merchant.Descriptor_Postal;
                            descriptor.Descriptor_Country = merchant.Descriptor_Country;
                            descriptor.Descriptor_Mcc = merchant.Descriptor_Mcc;
                            descriptor.Descriptor_Url = merchant.Descriptor_Url;
                            descriptor.Descriptor_Merchant_Id = merchant.Descriptor_Merchant_Id;
                        }

                        gatewayFields.NmiFields = new NMI
                        {
                            DuplicateCheckInSeconds = payload.DuplicateCheckInSeconds ??
                                                      DUPLICATE_ORDER_CHECK_DEFAULT_INTERVAL,
                            Descriptors = descriptor
                        };
                        break;
                    }
                    case "stripe_payment_intents":
                        supportGlobal_Three_Ds = false;
                        gatewayFields.StripeFields = new StripeGatewayFields
                        {
                            StatementDescriptor = payload.Descriptor.Name ?? string.Empty,
                            //StatementDescriptorSuffix = payload.Descriptor.Name ?? string.Empty,
                        };
                        break;
                    case "safe_charge":
                        break;
                }

                var purchaseRequest = new PurchaseRequest
                {
                    Transaction = new PurchaseTransaction
                    {
                        Amount = payload.Amount,
                        CurrencyCode = payload.CurrencyCode,
                        GatewaySpecificFields = gatewayFields,
                        ContinueCaching = true
                    }
                };

                if (!string.IsNullOrEmpty(payload.ScaAuthenticationToken))
                {
                    purchaseRequest.Transaction.ScaAuthenticationToken = payload.ScaAuthenticationToken;
                }
                else
                {
                    if (payload.IsTokenizedSale && payload.CreditCard == null)
                    {
                        purchaseRequest.Transaction.PaymentMethodToken = payload.Token;
                    }
                    else
                    {
                        purchaseRequest.Transaction.CreditCard = new CreditCard
                        {
                            FirstName = payload.CreditCard.FirstName,
                            LastName = payload.CreditCard.LastName,
                            Number = payload.CreditCard.Number,
                            VerificationValue = payload.CreditCard.VerificationValue,
                            Month = payload.CreditCard.Month.ToString(),
                            Year = payload.CreditCard.Year.ToString()
                        };

                        if (payload.ThreeDS != null &&
                            payload.SupportedGateway.NameIdentifier != "stripe_payment_intents")
                        {
                            purchaseRequest.Transaction.ThreeDsVersion = payload.ThreeDS.ThreeDsVersion;
                            purchaseRequest.Transaction.ThreeDs = new ThreeDs
                            {
                                EcommerceIndicator = payload.ThreeDS.EcommerceIndicator,
                                AuthenticationValue = payload.ThreeDS.AuthenticationValue,
                                DirectoryServerTransactionId = payload.ThreeDS.DirectoryServerTransactionId,
                                Xid = payload.ThreeDS.Xid,
                                AuthenticationValueAlgorithm = payload.ThreeDS.AuthenticationValueAlgorithm,
                                DirectoryResponseStatus = payload.ThreeDS.DirectoryResponseStatus,
                                AuthenticationResponseStatus = payload.ThreeDS.AuthenticationResponseStatus,
                                Enrolled = payload.ThreeDS.Enrolled
                            };
                        }
                    }
                }

                var spreedlyResponse = await _spreedlyService.PurchaseAsync(
                    merchant.SpreedlyEnvironmentKey,
                    merchant.SpreedlySecretKey,
                    payload.SupportedGateway.Identifier,
                    merchant.Mid,
                    purchaseRequest, CancellationToken.None);

                if (!spreedlyResponse.Transaction.Succeeded)
                {
                    response.AddErrorWithCode(spreedlyResponse.Transaction?.State,
                        spreedlyResponse.Transaction?.Message);
                    response.ProviderResponseCode = spreedlyResponse?.Transaction?.State;
                    response.ProviderResponseMessage = spreedlyResponse?.Transaction?.Message;

                    foreach (var error in spreedlyResponse.Errors)
                    {
                        response.AddErrorWithCode(error.Key, error.ErrorCode);

                        //Records last error to db
                        response.ProviderResponseCode = error.Key ?? string.Empty;
                        response.ProviderResponseMessage = error.ErrorCode ?? string.Empty;
                    }

                    workspan.AddError("ERRORS: {Error}",
                        JsonConvert.SerializeObject(spreedlyResponse));
                }
                else
                {
                    response.ProviderResponseCode = spreedlyResponse.Transaction?.Response?.ErrorCode ?? "0";
                    response.ProviderResponseMessage = spreedlyResponse.Transaction?.Response?.Message;
                    response.ProviderTransactionToken = spreedlyResponse.Transaction?.Token;
                    response.PaymentInstrumentId = payload.PaymentInstrumentId;
                    response.AuthorizationCode = response.AuthorizationCode;
                    response.ProviderTransactionToken = spreedlyResponse.Transaction?.Token;
                    response.CvvCode = spreedlyResponse.Transaction?.Response?.CvvCode;
                    response.AvsCode = spreedlyResponse.Transaction?.Response?.AvsCode;
                }

                response.Provider = payload.SupportedGateway.NameIdentifier;
                response.SupportedGatewayId = payload.SupportedGateway.Id;
                response.RawResult = JsonConvert.SerializeObject(spreedlyResponse);
            }
            catch (Exception e)
            {
                response.RawResult = JsonConvert.SerializeObject(e);
                workspan.RecordException(e,
                    $"Payload: {JsonConvert.SerializeObject(payload)}");
                response.AddError(e.Message);
            }

            response.Provider = CurrentPaymentProvider;
            return response;
        }

        public override async Task<AuthResult> AuthorizeAsync(AuthorizationRequest payload,
            CancellationToken token)
        {
            using var workspan = Workspan.Start<SpreedlyPaymentProvider>()
                .Baggage("Mid", payload.Mid)
                .Baggage("OrderId", payload.OrderId)
                .Baggage("GatewayId", payload.Gateway?.Id);


            var response = new AuthResult();
            try
            {
                var merchant = await GetMerchantAsync(payload.Mid);

                var gatewayFields = new GatewaySpecificFields();
                var descriptor = new Descriptors();
                var supportGlobal_Three_Ds = false;
                switch (payload.SupportedGateway.NameIdentifier)
                {
                    case GatewayTypesConstants.Spreedly_Nmi:
                    {
                        supportGlobal_Three_Ds = true;
                        if (payload.Descriptor != null)
                        {
                            descriptor.Descriptor = payload.Descriptor?.Name ?? merchant.Descriptor;
                            descriptor.Descriptor_Phone = merchant.Descriptor_Phone;
                            descriptor.Descriptor_Address = merchant.Descriptor_Address;
                            descriptor.Descriptor_City = merchant.Descriptor_City;
                            descriptor.Descriptor_State = merchant.Descriptor_State;
                            descriptor.Descriptor_Postal = merchant.Descriptor_Postal;
                            descriptor.Descriptor_Country = merchant.Descriptor_Country;
                            descriptor.Descriptor_Mcc = merchant.Descriptor_Mcc;
                            descriptor.Descriptor_Url = merchant.Descriptor_Url;
                            descriptor.Descriptor_Merchant_Id = merchant.Descriptor_Merchant_Id;
                        }

                        gatewayFields.NmiFields = new NMI
                        {
                            DuplicateCheckInSeconds = payload.DuplicateCheckInSeconds ??
                                                      DUPLICATE_ORDER_CHECK_DEFAULT_INTERVAL,
                            Descriptors = descriptor
                        };
                        break;
                    }
                    case GatewayTypesConstants.Spreedly_Stripe_PI:
                        if (payload.Descriptor != null)
                        {
                            gatewayFields.StripeFields = new StripeGatewayFields
                            {
                                StatementDescriptor = payload.Descriptor?.Name ?? string.Empty,
                                //StatementDescriptorSuffix = payload.Descriptor?.Name ?? string.Empty,
                            };
                        }

                        break;
                }

                ShippingAddressDto shippingAddressDto = new ShippingAddressDto();
                if (payload.ShippingAddress != null)
                {
                    shippingAddressDto.Name =
                        payload.BillingAddress?.FirstName + " " + payload.BillingAddress?.LastName;
                    shippingAddressDto.Address1 = payload.ShippingAddress?.Address1;
                    shippingAddressDto.Address2 = payload.ShippingAddress?.Address2;
                    shippingAddressDto.City = payload.ShippingAddress?.City;
                    shippingAddressDto.State = payload.ShippingAddress?.State;
                    shippingAddressDto.Zip = payload.ShippingAddress?.Zip;
                    shippingAddressDto.Country = payload.ShippingAddress?.Country;
                    shippingAddressDto.PhoneNumber = payload.ShippingAddress?.PhoneNumber;
                }

                var authorizeRequest = new AuthorizeRequest
                {
                    Transaction = new AuthorizeTransaction()
                    {
                        Ip = payload.Device?.IpAddress,
                        Email = payload.BillingAddress?.Email,
                        RetainOnSuccess = true,
                        ContinueCaching = true,
                        Amount = payload.Amount,
                        CurrencyCode = payload.CurrencyCode,
                        OrderId = payload.OrderId.ToString(),
                        MerchantNameDescriptor = descriptor.Descriptor ?? string.Empty,
                        BillingAddress = new BillingAddressDto
                        {
                            Name = payload.BillingAddress?.FirstName + " " + payload.BillingAddress?.LastName,
                            Address1 = payload.BillingAddress?.Address1,
                            Address2 = payload.BillingAddress?.Address2,
                            City = payload.BillingAddress?.City,
                            State = payload.BillingAddress?.State,
                            Zip = payload.BillingAddress?.Zip,
                            Country = payload.BillingAddress?.Country,
                            PhoneNumber = payload.BillingAddress?.PhoneNumber
                        },
                        ShippingAddress = shippingAddressDto,
                        GatewaySpecificFields = gatewayFields
                    }
                };

                if (supportGlobal_Three_Ds && !string.IsNullOrEmpty(payload.ScaAuthenticationToken))
                    authorizeRequest.Transaction.ScaAuthenticationToken = payload.ScaAuthenticationToken;
                else
                {
                    if (!string.IsNullOrEmpty(payload.Token) && payload.CreditCard == null)
                    {
                        authorizeRequest.Transaction.PaymentMethodToken = payload.Token;
                    }
                    else
                    {
                        authorizeRequest.Transaction.CreditCard = new CreditCard
                        {
                            FirstName = payload.CreditCard.FirstName,
                            LastName = payload.CreditCard.LastName,
                            Number = payload.CreditCard.Number,
                            VerificationValue = payload.CreditCard.VerificationValue,
                            Month = payload.CreditCard.Month.ToString(),
                            Year = payload.CreditCard.Year.ToString()
                        };
                    }

                    if (payload.ThreeDS != null && payload.SupportedGateway.NameIdentifier != "stripe_payment_intents")
                    {
                        authorizeRequest.Transaction.ThreeDsVersion = payload.ThreeDS.ThreeDsVersion;
                        authorizeRequest.Transaction.ThreeDs = new ThreeDs
                        {
                            EcommerceIndicator = payload.ThreeDS.EcommerceIndicator,
                            AuthenticationValue = payload.ThreeDS.AuthenticationValue,
                            DirectoryServerTransactionId = payload.ThreeDS.DirectoryServerTransactionId,
                            Xid = payload.ThreeDS.Xid,
                            AuthenticationValueAlgorithm = payload.ThreeDS.AuthenticationValueAlgorithm,
                            DirectoryResponseStatus = payload.ThreeDS.DirectoryResponseStatus,
                            AuthenticationResponseStatus = payload.ThreeDS.AuthenticationResponseStatus,
                            Enrolled = payload.ThreeDS.Enrolled
                        };
                    }
                }


                var spreedlyResponse = await _spreedlyService.AuthorizeAsync(
                    merchant.Mid,
                    merchant.SpreedlyEnvironmentKey,
                    merchant.SpreedlySecretKey,
                    payload.SupportedGateway.Identifier,
                    authorizeRequest, CancellationToken.None);

                var overridenErrorCode = string.Empty;
                var overridenErrorMessage = string.Empty;
                switch (payload.SupportedGateway.NameIdentifier)
                {
                    case GatewayTypesConstants.Spreedly_Stripe_PI:

                        spreedlyResponse.Transaction.Response ??= new SpreedlyService.Response();

                        if (spreedlyResponse.Transaction.Succeeded)
                        {
                            spreedlyResponse.Transaction.MessageKey = "succeeded";
                            spreedlyResponse.Transaction.Message = "Succeeded";

                            response.ProviderResponseCode = spreedlyResponse.Transaction?.Response?.ErrorCode ??
                                                            spreedlyResponse.Transaction?.MessageKey;
                            response.ProviderResponseMessage = spreedlyResponse.Transaction?.Response?.Message ??
                                                               spreedlyResponse.Transaction?.Message;
                        }
                        else
                        {
                            spreedlyResponse.Transaction.MessageKey =
                                spreedlyResponse.Transaction?.GatewaySpecificResponseFields?.StripePaymentIntents
                                    ?.DeclineCode ?? spreedlyResponse.Transaction?.Response?.ErrorCode;
                            spreedlyResponse.Transaction.Message =
                                spreedlyResponse.Transaction?.Response?.Message;

                            //Temp fix for blocked transactions
                            if (!string.IsNullOrEmpty(spreedlyResponse.Transaction?.GatewaySpecificResponseFields
                                    ?.StripePaymentIntents?.OutcomeType) &&
                                spreedlyResponse.Transaction?.GatewaySpecificResponseFields?.StripePaymentIntents
                                    ?.OutcomeType == "blocked")
                            {
                                overridenErrorCode = "do_not_try_again";
                                overridenErrorMessage = spreedlyResponse.Transaction.Message + " - " +
                                                        spreedlyResponse.Transaction
                                                            ?.GatewaySpecificResponseFields
                                                            ?.StripePaymentIntents?.OutcomeSellerMessage;
                            }
                        }

                        spreedlyResponse.Transaction.Response.CvvCode =
                            spreedlyResponse.Transaction?.GatewaySpecificResponseFields?.StripePaymentIntents
                                ?.CvcCheck == "pass"
                                ? "M"
                                : "N";

                        spreedlyResponse.Transaction.Response.AvsCode =
                            spreedlyResponse.Transaction?.GatewaySpecificResponseFields?.StripePaymentIntents is
                                {AddressLine1Check: "pass", AddressPostalCodeCheck: "pass"}
                                ? "M"
                                : "N";

                        break;
                }

                if (!spreedlyResponse.Transaction.Succeeded)
                {
                    workspan.Log.Information("Transaction failed: {Error}",
                        JsonConvert.SerializeObject(spreedlyResponse));

                    response.AddErrorWithCode(spreedlyResponse.Transaction?.Response?.ErrorCode ??
                                              spreedlyResponse.Transaction?.MessageKey,
                        spreedlyResponse.Transaction?.Response?.Message ??
                        spreedlyResponse.Transaction?.Message);
                }
                else
                {
                    response.ProviderTransactionToken = spreedlyResponse.Transaction?.Token;
                    response.PaymentInstrumentId = payload.PaymentInstrumentId;
                    response.AuthorizationCode = response.AuthorizationCode;
                    response.ProviderTransactionToken = spreedlyResponse.Transaction?.Token;

                    workspan.Log.Information("Transaction succeeded: {Response}",
                        JsonConvert.SerializeObject(spreedlyResponse));
                }

                response.CvvCode = spreedlyResponse.Transaction?.Response?.CvvCode;
                response.AvsCode = spreedlyResponse.Transaction?.Response?.AvsCode;

                response.ProviderResponseCode = spreedlyResponse.Transaction?.Response?.ErrorCode ??
                                                spreedlyResponse.Transaction?.MessageKey;
                response.ProviderResponseMessage = spreedlyResponse.Transaction?.Response?.Message ??
                                                   spreedlyResponse.Transaction?.Message;

                //temp override in case of high risk transactions
                if (overridenErrorCode != string.Empty)
                {
                    response.ProviderResponseCode = overridenErrorCode;
                    response.ProviderResponseMessage = overridenErrorMessage;
                }


                response.RawResult = JsonConvert.SerializeObject(spreedlyResponse);
            }
            catch (Exception e)
            {
                response.RawResult = JsonConvert.SerializeObject(e);
                workspan.RecordException(e,
                    $"Payload: {JsonConvert.SerializeObject(payload)}");
                response.AddError(e.Message);
            }

            response.SupportedGatewayId = payload.SupportedGateway.Id;
            response.Provider = payload.SupportedGateway.NameIdentifier;
            response.ProcessorId = payload.SupportedGateway.ProcessorId;
            return response;
        }

        public override async Task<CapturePaymentResult> CaptureAsync(CapturePaymentRequest payload,
            CancellationToken token)
        {
            using var workspan = Workspan.Start<SpreedlyPaymentProvider>()
                .Baggage("Mid", payload.Mid)
                .Baggage("OrderId", payload.OrderId)
                .Baggage("GatewayId", payload.Gateway?.Id);

            var response = new CapturePaymentResult();
            try
            {
                var merchant = await GetMerchantAsync(payload.Mid);

                var spreedlyResponse = await _spreedlyService.CaptureAsync(
                    merchant.Mid,
                    merchant.SpreedlyEnvironmentKey,
                    merchant.SpreedlySecretKey,
                    payload.TransactionToken,
                    new CaptureRequest
                    {
                    }, CancellationToken.None);

                if (!spreedlyResponse.Success)
                {
                    foreach (var error in spreedlyResponse.Errors)
                    {
                        response.AddErrorWithCode(error.Key, error.ErrorCode);

                        //Records last error to db
                        response.ProviderResponseCode = error.Key ?? string.Empty;
                        response.ProviderResponseMessage = error.ErrorCode ?? string.Empty;
                    }

                    workspan.AddError("ERRORS: {Error}",
                        JsonConvert.SerializeObject(spreedlyResponse));
                }
                else
                {
                    response.ProviderResponseCode = spreedlyResponse.Transaction?.MessageKey;
                    response.ProviderResponseMessage = spreedlyResponse.Transaction?.Message;
                    response.ProviderTransactionToken = spreedlyResponse.Transaction?.Token;
                }

                response.Provider = payload.SupportedGateway.NameIdentifier;
                response.RawResult = JsonConvert.SerializeObject(spreedlyResponse);
            }
            catch (Exception e)
            {
                response.RawResult = JsonConvert.SerializeObject(e);
                workspan.RecordException(e,
                    $"Payload: {JsonConvert.SerializeObject(payload)}");
                response.AddError(e.Message);
            }

            response.SupportedGatewayId = payload.SupportedGateway.Id;
            response.ProcessorId = payload.SupportedGateway.ProcessorId;
            response.Provider = payload.SupportedGateway.NameIdentifier;
            return response;
        }

        public override async Task<IVerifyInstrumentResult> VerifyAsync(IVerifyInstrumentRequest payload,
            CancellationToken token)
        {
            using var workspan = Workspan.Start<SpreedlyPaymentProvider>()
                .Baggage("Mid", payload.Mid)
                .Baggage("OrderId", payload.OrderId)
                .Baggage("GatewayId", payload.Gateway?.Id);

            workspan.Log.Information("ENTERED: PaymentService > VerifyAsync > Mid: {Mid}  Payload:{Payload}",
                payload.Mid,
                JsonConvert.SerializeObject(payload));

            var response = new VerifyInstrumentResult();
            try
            {
                var merchant = await GetMerchantAsync(payload.Mid);
                // var paymentInstrument =
                //     await _paymentInstrumentsService.GetMethodByIdAsync(mid, payload.PayerId,
                //         payload.PaymentInstrumentId, token);

                if (payload.Gateway is null)
                {
                    #region Obtaining default gateway

                    workspan.Log.Information(
                        $"Gateway is not specified -> looking for default gateway. Payload: {JsonConvert.SerializeObject(payload)}");

                    var defaultGateway = merchant.RelatedGateways?.SingleOrDefault(x => x.Default);
                    if (defaultGateway is null)
                    {
                        workspan.AddError("Merchant id: {Mid} has no default gateway. Payload: {SerializeObject}",
                            payload.Mid,
                            JsonConvert.SerializeObject(payload));

                        throw new NullReferenceException($"Merchant id: {payload.Mid} has no default gateway.");
                    }

                    payload.Gateway = defaultGateway;

                    #endregion
                }

                // var pi = await _spreedlyService.GetPaymentInstrumentAsync(merchant.SpreedlyEnvironmentKey,
                //     merchant.SpreedlySecretKey, merchant.Mid, payload.Token, token);
                //
                // if (!pi.Success)
                // {
                //     workspan.Log.Information(
                //         $"Couldn't retrieve payment instrument from external provider");
                //
                //     throw new HttpRequestException("Couldn't retrieve payment instrument from external provider");
                // }

                //Retain
                // var retainedMethod = await _spreedlyService.RetainPaymentInstrumentAsync(
                //     merchant.Mid,
                //     merchant.SpreedlyEnvironmentKey,
                //     merchant.SpreedlySecretKey,
                //     payload.Token, token);

                var verifyRequest = new VerifyRequest
                {
                    Transaction = new VerifyTransaction()
                    {
                        RetainOnSuccess = true,
                        BillingAddress = new BillingAddressDto
                        {
                            Address1 = payload.BillingAddress.Address1,
                            Address2 = payload.BillingAddress.Address2,
                            City = payload.BillingAddress.City,
                            State = payload.BillingAddress.State,
                            Zip = payload.BillingAddress.Zip,
                            Country = payload.BillingAddress.Country,
                            PhoneNumber = payload.BillingAddress.PhoneNumber
                        },
                        ContinueCaching = true
                    }
                };

                if (!string.IsNullOrEmpty(payload.Token) && payload.CreditCard == null)
                {
                    verifyRequest.Transaction.PaymentMethodToken = payload.Token;
                }
                else
                {
                    verifyRequest.Transaction.CreditCard = new CreditCard
                    {
                        FirstName = payload.CreditCard.FirstName,
                        LastName = payload.CreditCard.LastName,
                        Number = payload.CreditCard.Number,
                        VerificationValue = payload.CreditCard.VerificationValue,
                        Month = payload.CreditCard.Month.ToString(),
                        Year = payload.CreditCard.Year.ToString()
                    };
                }

                var spreedlyResponse = await _spreedlyService.VerifyAsync(
                    merchant.SpreedlyEnvironmentKey,
                    merchant.SpreedlySecretKey,
                    merchant.Mid,
                    payload.SupportedGateway.Identifier,
                    verifyRequest, CancellationToken.None);

                workspan.Log.Information(
                    "VerifyAsync > Spreedly response: {SpreedlyResponse}",
                    JsonConvert.SerializeObject(spreedlyResponse));

                response.ProviderResponseCode = spreedlyResponse.Transaction.MessageKey;
                response.ProviderResponseMessage = spreedlyResponse.Transaction.Message;
                response.CvvCode = spreedlyResponse.Transaction?.Response?.CvvCode ?? string.Empty;
                response.AvsCode = spreedlyResponse.Transaction?.Response?.AvsCode ?? string.Empty;

                response.Last4Digits = payload.CreditCard?.Last4;

                if (spreedlyResponse.Transaction.Response.Success)
                {
                    response.IsVerified = spreedlyResponse.Transaction.Response.Success;
                    response.BinNumber = spreedlyResponse.Transaction.PaymentMethod.FirstSixDigits;
                }
                else
                    workspan.Log.Error(spreedlyResponse.Transaction.Message);

                workspan.Log.Information("Should Retain method? {StorageStateCondition} StorageState {StorageState}",
                    spreedlyResponse.Transaction.PaymentMethod.StorageState != "retained",
                    spreedlyResponse.Transaction.PaymentMethod.StorageState);
            }
            catch (Exception e)
            {
                response.RawResult = JsonConvert.SerializeObject(e);
                workspan.RecordException(e,
                    $"Payload: {JsonConvert.SerializeObject(payload)}");
                response.AddError(e.Message);
            }

            return response;
        }

        /// <summary>
        /// Authorize and void an amount
        /// </summary>
        /// <param name="mid"></param>
        /// <param name="payload"></param>
        /// <param name="amount"></param>
        /// <param name="gateway"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        public async Task<IVerifyInstrumentResult> VerifyAmountAsync(Guid mid,
            AuthorizationRequest payload,
            Gateway? gateway,
            CancellationToken token)
        {
            using var workspan = Workspan.Start<SpreedlyPaymentProvider>()
                .Baggage("Mid", payload.Mid)
                .Baggage("OrderId", payload.OrderId)
                .Baggage("GatewayId", payload.Gateway?.Id);

            workspan.Log.Information("ENTERED: PaymentService > VerifyAmountAsync > Mid: {Mid}  Payload:{Payload}", mid,
                JsonConvert.SerializeObject(payload));

            var response = new VerifyAmountResult();
            try
            {
                /// Remove this code once you have a processing engine with propper error codes
                ///override gateway stripe gateway - temp solution untill ewe will have a better processing engine for errors.

                gateway = await _context.Gateways
                    .Where(x => x.Merchant.Mid == mid &&
                                x.SupportedGateway.NameIdentifier == GatewayTypesConstants.Stripe)
                    .Include(x => x.SupportedGateway)
                    .SingleOrDefaultAsync(token);

                if (gateway is null)
                {
                    response.AddError("Gateway not found (Stripe gateway is not configured on this merchant)");
                    response.IsGatewayFound = false;
                    return response;
                }
                else
                {
                    response.IsGatewayFound = true;
                }
                /// Remove this code once processing is fixed 

                var authResult = await this.AuthorizeAsync(payload, token);
                if (authResult.Success)
                {
                    // Calling Void in thread pool to speedup processing
                    _backgroundWorkerCommandQueue.Enqueue(new VoidPaymentCommand(authResult.TransactionId, mid,
                        payload.OrderId, payload.Amount, payload.CurrencyCode));
                }
                else
                {
                    workspan.AddError("ERRORS: {Error}",
                        JsonConvert.SerializeObject(authResult));
                    response.AddErrorWithCode(authResult.ProviderResponseCode, authResult.ProviderResponseMessage);
                }

                response.CvvCode = authResult.CvvCode;
                response.AvsCode = authResult.AvsCode;
                response.TransactionId = authResult.TransactionId;
                response.ProviderResponseCode = authResult.ProviderResponseCode;
                response.RawResult = authResult.RawResult;
                response.ProviderTransactionToken = authResult.ProviderTransactionToken;
                response.ProviderResponseMessage = authResult.ProviderResponseMessage;
            }
            catch (Exception e)
            {
                response.RawResult = JsonConvert.SerializeObject(e);
                workspan.RecordException(e,
                    $"Payload: {JsonConvert.SerializeObject(payload)}");
                response.AddError(e.Message);
            }

            response.Provider = gateway.SupportedGateway.NameIdentifier;
            response.SupportedGatewayId = gateway.SupportedGateway.Id;
            return response;
        }

        public async Task RetainPaymentInstrumentAsync(Guid mid, string paymentInstrumentToken,
            //Gateway? gateway,
            CancellationToken token)
        {
            using var workspan = Workspan.Start<SpreedlyPaymentProvider>();
            workspan.Log.Information(
                "ENTERED: PaymentService > RetainPaymentInstrumentAsync > Mid: {Mid}  Token:{Token}",
                mid,
                paymentInstrumentToken);

            try
            {
                var merchant = await GetMerchantAsync(mid);

                var retainedMethod = await _spreedlyService.RetainPaymentInstrumentAsync(
                    merchant.Mid,
                    merchant.SpreedlyEnvironmentKey,
                    merchant.SpreedlySecretKey,
                    paymentInstrumentToken, token);

                if (!retainedMethod.Success)
                {
                    workspan.Log.Error("Cannot retain method > Mid: {Mid}  Token:{Token} Message:{Message}", mid,
                        paymentInstrumentToken, retainedMethod.Transaction.Message);

                    throw new BadHttpRequestException(
                        $"Cannot retain method Something went wrong Message:{retainedMethod.Transaction.Message}");
                }
            }
            catch (Exception e)
            {
                workspan.RecordException(e,
                    $"Cannot retain method");
                throw;
            }
        }

        public override async Task<ICreditPaymentResult> CreditAsync(ICreditPaymentRequest payload,
            CancellationToken token)
        {
            using var workspan = Workspan.Start<SpreedlyPaymentProvider>()
                .Baggage("Mid", payload.Mid)
                .Baggage("OrderId", payload.OrderId)
                .Baggage("GatewayId", payload.Gateway?.Id);


            var response = new CreditPaymentResult();
            try
            {
                var merchant = await GetMerchantAsync(payload.Mid);

                var spreedlyResponse = await _spreedlyService.CreditAsync(
                    merchant.Mid,
                    merchant.SpreedlyEnvironmentKey,
                    merchant.SpreedlySecretKey,
                    payload.ProviderTransactionToken,
                    new CreditRequest()
                    {
                        Transaction = new CreditTransaction
                        {
                            Amount = payload.Amount,
                            CurrencyCode = payload.CurrencyCode,
                            OrderId = payload.OrderId.ToString(),
                            Description = $"Crediting orderID {payload.OrderId}",
                            ContinueCaching = true
                        }
                    }, CancellationToken.None);

                if (!spreedlyResponse.Success || !spreedlyResponse.Transaction.Succeeded)
                {
                    if (!spreedlyResponse.Transaction.Succeeded)
                        response.AddErrorWithCode(spreedlyResponse.Transaction.State,
                            spreedlyResponse.Transaction.Message);

                    foreach (var error in spreedlyResponse.Errors)
                    {
                        response.AddErrorWithCode(error.Key, error.ErrorCode);

                        //Records last error to db
                        response.ProviderResponseCode = error.Key ?? string.Empty;
                        response.ProviderResponseMessage = error.ErrorCode ?? string.Empty;
                    }

                    workspan.AddError("SPREEDLY RESPONSE ERRORS: {Error}",
                        JsonConvert.SerializeObject(spreedlyResponse));
                }
                else
                {
                    response.Provider = this.CurrentPaymentProvider;
                    response.ProviderResponseCode = spreedlyResponse.Transaction?.Response?.ErrorCode ?? "0";
                    response.ProviderResponseMessage = spreedlyResponse.Transaction?.Response?.Message;
                    response.ProviderTransactionToken = spreedlyResponse.Transaction?.Token;
                    response.PaymentInstrumentId = payload.PaymentInstrumentId;
                }

                response.Provider = this.CurrentPaymentProvider;
                response.RawResult = JsonConvert.SerializeObject(spreedlyResponse);
            }
            catch (Exception e)
            {
                response.RawResult = JsonConvert.SerializeObject(e);
                workspan.RecordException(e,
                    $"Payload: {JsonConvert.SerializeObject(payload)}");
                response.AddError(e.Message);
            }

            response.Provider = this.CurrentPaymentProvider;
            return response;
        }

        public override async Task<IVoidPaymentResult> VoidAsync(IVoidPaymentRequest payload, CancellationToken token)
        {
            using var workspan = Workspan.Start<SpreedlyPaymentProvider>()
                .Baggage("Mid", payload.Mid)
                .Baggage("OrderId", payload.OrderId)
                .Baggage("GatewayId", payload.Gateway?.Id);

            var response = new VoidPaymentResult();
            try
            {
                var merchant = await GetMerchantAsync(payload.Mid);

                var spreedlyResponse = await _spreedlyService.VoidAsync(
                    merchant.Mid,
                    merchant.SpreedlyEnvironmentKey,
                    merchant.SpreedlySecretKey,
                    new VoidRequest
                    {
                        TransactionToken = payload.ProviderTransactionToken
                    }, CancellationToken.None);

                if (!spreedlyResponse.Success)
                {
                    if (!spreedlyResponse.Transaction.Succeeded)
                        response.AddErrorWithCode(spreedlyResponse.Transaction.State,
                            spreedlyResponse.Transaction.Message);


                    foreach (var error in spreedlyResponse.Errors)
                    {
                        response.AddErrorWithCode(error.Key, error.ErrorCode);

                        //Records last error to db
                        response.ProviderResponseCode = error.Key ?? string.Empty;
                        response.ProviderResponseMessage = error.ErrorCode ?? string.Empty;
                    }

                    workspan.AddError("ERRORS: {Error}",
                        JsonConvert.SerializeObject(spreedlyResponse));
                }
                else
                {
                    response.ProviderResponseCode = spreedlyResponse.Transaction?.Response?.ErrorCode ?? "0";
                    response.ProviderResponseMessage = spreedlyResponse.Transaction?.Response?.Message;
                    response.ProviderTransactionToken = spreedlyResponse.Transaction?.Token;
                    response.PaymentInstrumentId = payload.PaymentInstrumentId;
                }

                response.Provider = this.CurrentPaymentProvider;
                response.RawResult = JsonConvert.SerializeObject(spreedlyResponse);
            }
            catch (Exception e)
            {
                response.RawResult = JsonConvert.SerializeObject(e);
                workspan.RecordException(e,
                    $"Payload: {JsonConvert.SerializeObject(payload)}");
                response.AddError(e.Message);
            }

            response.Provider = this.CurrentPaymentProvider;
            return response;
        }

        #region Commented

        // public async Task<FingerprintInstrumentResult> FingerprintPaymentInstrumentAsync(Guid mid,
        //     FingerprintInstrumentRequest payload,
        //     //Gateway? gateway,
        //     CancellationToken token)
        // {
        //     using var workspan = Workspan.Start<SpreedlyPaymentProvider>()
        //                         .Baggage("Mid", payload.Mid)
        //                         .Baggage("OrderId", payload.OrderId)
        //                         .Baggage("GatewayId", payload.Gateway?.Id);
        //
        //     var response = new FingerprintInstrumentResult();
        //     try
        //     {
        //         var merchant = await GetMerchantAsync(mid);
        //
        //         var pi = await _spreedlyService.GetPaymentInstrumentAsync(merchant.SpreedlyEnvironmentKey,
        //             merchant.SpreedlySecretKey, merchant.Mid, payload.Token, token);
        //
        //         if (!pi.Success)
        //         {
        //             workspan.Log.Error("Couldn't retrieve payment instrument from external provider");
        //             throw new HttpRequestException("Couldn't retrieve payment instrument from external provider");
        //         }
        //
        //         //Retain
        //         // var retainedMethod = await _spreedlyService.RetainPaymentInstrumentAsync(
        //         //     merchant.Mid,
        //         //     merchant.SpreedlyEnvironmentKey,
        //         //     merchant.SpreedlySecretKey,
        //         //     payload.Token, token);
        //
        //         response.StatusCode = pi.StatusCode;
        //         response.Status = pi.Status;
        //         response.Fingerprint = pi.payment_method.fingerprint;
        //
        //         // if (retainedMethod.Transaction.Succeeded)
        //         // {
        //         //     response.Fingerprint = retainedMethod.Transaction.PaymentMethod.Fingerprint;
        //         // }
        //         // else
        //         // {
        //         //     workspan.AddError(retainedMethod.Transaction.Message);
        //         //     response.AddError(retainedMethod.Transaction.Message);
        //         // }
        //
        //
        //         #region Commented
        //
        //         // var savedMethod = await _paymentInstrumentsService.SavePaymentMethodAsync(merchant.Mid,
        //         //     new CreateNewPaymentInstrumentDTO
        //         //     {
        //         //         First_Name = pi.payment_method.first_name,
        //         //         Last_Name = pi.payment_method.last_name,
        //         //         Number = pi.payment_method.token,
        //         //         Verification_Value = pi.payment_method.verification_value,
        //         //         Month = pi.payment_method.month,
        //         //         Year = pi.payment_method.year,
        //         //         Tokenized = true
        //         //     }, true, token);
        //         //
        //         // if (!savedMethod.Success)
        //         // {
        //         //     response.AddError("Couldn't save payment instrument");
        //         //     
        //         //     _logger.LogError(
        //         //         "ERROR: PaymentService > Sale > Couldn't save payment instrument for token {Token}",
        //         //         pi.payment_method.token);
        //         //     //return response;
        //         // }
        //
        //
        //         // //Parallel.Invoke(async () =>
        //         // //{
        //         //     try
        //         //     {
        //         //         await _publisher.Publish<PaymentInstrumentVerifiedEvent>(new
        //         //         {
        //         //             OrderId = payload.OrderId,
        //         //             TransactionId = default(Guid),
        //         //             Mid = merchant.Mid,
        //         //             Type = (string) TransactionType.Verify.ToString(),
        //         //             Description =
        //         //                 $"Fingerprinted card that ends with {retainedMethod.Transaction?.PaymentMethod.LastFourDigits}",
        //         //             Timestamp = (DateTime) retainedMethod.Transaction?.CreatedAt
        //         //         }, token);
        //         //     }
        //         //     catch (Exception e)
        //         //     {
        //         //         _logger.LogError(e,
        //         //             $"EXCEPTION: PaymentService > FingerprintPaymentInstrumentAsync => Publish failed");
        //         //     }
        //         // //});
        //
        //         #endregion
        //
        //         return response;
        //     }
        //     catch (Exception e)
        //     {
        //         workspan.RecordException(e,
        //             $"Payload: {JsonConvert.SerializeObject(payload)}");
        //         throw;
        //     }
        // }

        #endregion

        private async Task<Merchant> GetMerchantAsync(Guid mid)
        {
            var merchant = await _context.Merchants.SingleOrDefaultAsync(x =>
                x.Mid == mid);

            ArgumentNullException.ThrowIfNull(merchant, $"Merchant id {mid} Not found");
            return merchant;
        }

        public override async Task<(bool CanHandle, Guid? SupportedGatewayId)> CanHandleWebhookEventAsync(string body,
            IHeaderDictionary headers, CancellationToken token)
        {
            return (false, null);
        }

        //public bool IsSandbox { get; }
        public override bool SupportCapture => true;
        public override bool SupportPartiallyRefund => true;
        public override bool SupportRefund => true;
        public override bool SupportVoid => true;
        public override bool SupportTokenization => true;
        public override bool RequirePostProcessPayment => true;
        public override bool SupportPayouts => false;
        public override string CurrentPaymentProvider => "spreedly";
        public override bool SupportsAch => false;
        public override bool SupportsCreditCards => true;
        public override bool SupportsCreditCardVerification => true;
        public override bool SupportsExternalThreeDS => true;
        public override bool SupportsSubscription => false;
        public override bool SupportsStandaloneCredit => false;
        public override CardBrand[] NetworkTokenCardBrands => null;
    }
}