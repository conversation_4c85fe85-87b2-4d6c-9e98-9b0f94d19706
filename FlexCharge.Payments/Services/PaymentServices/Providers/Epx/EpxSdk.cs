using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Net.Mime;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Payments.Services.PaymentServices.Providers.Epx.Models.Capture;
using FlexCharge.Payments.Services.PaymentServices.Providers.Epx.Models.CreateToken;
using FlexCharge.Payments.Services.PaymentServices.Providers.Epx.Models.Refund;
using FlexCharge.Payments.Services.PaymentServices.Providers.Epx.Models.Reverse;
using FlexCharge.Payments.Services.PaymentServices.Providers.Epx.Models.Sales;
using FlexCharge.Payments.Services.PaymentServices.Providers.Epx.Models.Verify;
using FlexCharge.Payments.Services.PaymentServices.Providers.Epx.Models.Void;
using FlexCharge.Utils;


namespace FlexCharge.Payments.Services.PaymentServices.Providers.Epx;

public class EpxSdk
{
    private HttpClient _httpClient;
    private JsonSerializerOptions _serializeOptions;
    private string _fourPartKey;
    private string _secretKey;


    public EpxSdk(HttpClient httpClient)
    {
        _httpClient = httpClient;

        _serializeOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            WriteIndented = true
        };
        _serializeOptions.Converters.Add(new JsonStringEnumConverter());
        var baseEndpoint = EnvironmentHelper.IsInProduction //!isSandbox
            ? "https://epi.epx.com"
            : "https://epi.epxuap.com";

        _httpClient.BaseAddress = new Uri(baseEndpoint);
    }

    public async Task<EpxResponseObjects> SaleOrAuthorizeAsync(EpxSalesRequest request, EpxConfig epxConfig,
        CancellationToken token) =>
        await PostOrPutRequestAsync<EpxSalesRequest, EpxResponseObjects>(
            "/sale", request, epxConfig, token, true);


    public async Task<EpxResponseObjects> VerifyAsync(EpxVerificationRequest request, EpxConfig epxConfig,
        CancellationToken token) =>
        await PostOrPutRequestAsync<EpxVerificationRequest, EpxResponseObjects>(
            "/avs", request, epxConfig, token, true);


    public async Task<EpxResponseObjects> CaptureByBricAsync(EPXCaptureRequest request,
        string bric, EpxConfig epxConfig,
        CancellationToken token) =>
        await PostOrPutRequestAsync<EPXCaptureRequest, EpxResponseObjects>(
            $"/sale/{bric}/capture", request, epxConfig, token, false);


    public async Task<EpxResponseObjects> VoidByBricAsync(EpxVoidRequest request,
        string bric, EpxConfig epxConfig,
        CancellationToken token) =>
        await PostOrPutRequestAsync<EpxVoidRequest, EpxResponseObjects>(
            $"/reverse/{bric}", request, epxConfig, token, true);


    public async Task<object> CreateBricAsync(EpxCreateBricRequest request,
        EpxConfig epxConfig,
        CancellationToken token) =>
        await PostOrPutRequestAsync<EpxCreateBricRequest, object>(
            $"/storage", request, epxConfig, token, true);


    public async Task<EpxResponseObjects> RefundByBricAsync(EpxRefundRequest request,
        string bric, EpxConfig epxConfig,
        CancellationToken token) =>
        await PostOrPutRequestAsync<EpxRefundRequest, EpxResponseObjects>(
            $"/refund/{bric}", request, epxConfig, token, true);

    /// <summary>
    /// This endpoint will stop an authorization, sale, capture, or refund transaction
    /// prior to settlement. If the transaction has already been settled,
    /// this function will no longer be available.
    /// </summary>
    /// <param name="request"></param>
    /// <param name="bric"></param>
    /// <param name="epxConfig"></param>
    /// <param name="token"></param>
    /// <returns></returns>
    public async Task<EpxResponseObjects> ReverseCaptureOrRefundOrAuthorizationByBricAsync(EpxReverseRequest request,
        string bric, EpxConfig epxConfig,
        CancellationToken token) =>
        await PostOrPutRequestAsync<EpxReverseRequest, EpxResponseObjects>(
            $"/void/{bric}", request, epxConfig, token, false);

    public async Task<string> GetTransactionDetailByBric(string bric, EpxConfig epxConfig,
        CancellationToken token) =>
        await GetAsync(
            $"/sale/{bric}", epxConfig, token);


    private async Task<string> GetAsync(string url, EpxConfig epxConfig,
        CancellationToken token = default)
    {
        var reqHeaders = GetAuthorizationHeadersAsync(epxConfig, string.Empty, url);
        using var httpResponseMessage = await _httpClient.GetAsync(url, reqHeaders, token);
        var result = await httpResponseMessage.Content.ReadAsStringAsync(token);
        return result;
    }


    private async Task<TRes> PostOrPutRequestAsync<TReq, TRes>(string url,
        TReq request, EpxConfig epxConfig,
        CancellationToken token,
        bool isPost)
    {
        using var workspan = Workspan.Start<EpxSdk>()
            .Tag(nameof(url), url).LogEnterAndExit();

        var data = JsonSerializer.Serialize(request, _serializeOptions);
        var requestJson = new StringContent(
            data,
            Encoding.UTF8,
            MediaTypeNames.Application.Json);
        try
        {
            requestJson.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            var text = await requestJson.ReadAsStringAsync(token);
            var reqHeaders = GetAuthorizationHeadersAsync(epxConfig, text, url);
            using var httpResponseMessage =
                await (isPost
                    ? _httpClient.PostAsync(url, reqHeaders, requestJson, token)
                    : _httpClient.PutAsync(url, reqHeaders, requestJson, token));


#if DEBUG
            var serializeOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
            };
            var mmmReq = JsonSerializer.Serialize(request, request.GetType(),
                serializeOptions
            );
            var mmm = await httpResponseMessage.Content.ReadAsStringAsync();


#endif

            var result = await httpResponseMessage.Content.ReadFromJsonAsync<TRes>(_serializeOptions);
            workspan.Log.Information("SendRequestAsync > Response: {responseContent}", result);

            return result;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    private (string name, string value)[] GetAuthorizationHeadersAsync(EpxConfig epxConfig,
        string contentText, string url)
    {
        List<(string name, string value)> result = new();
        var path = new Uri(url).AbsolutePath;
        var text = path + contentText;
        var key = Encoding.UTF8.GetBytes(epxConfig.SecretKey);
        using HashAlgorithm hashAlgorithm = new HMACSHA256(key);
        byte[] messageBuffer = new UTF8Encoding(false).GetBytes(text);
        var hash = hashAlgorithm.ComputeHash(messageBuffer);
        var strHash = Convert.ToHexString(hash);
        result.Add(("epi-signature", strHash));
        result.Add(("epi-Id",
            $"{epxConfig.Customer}-{epxConfig.Merchant}-{epxConfig.Dba}-{epxConfig.Terminal}"));
        result.Add(("epi-trace", Guid.NewGuid().ToString()));
        return result.ToArray();
    }
}