namespace FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.Response.AcquirerResponse;

public class AcquirerResponseCrx:IAcquirerResponse
{
    /// <summary>
    /// This is the response ID assigned by Credorax.
    /// </summary>
    public string ResponseId { get; set; }
    /// <summary>
    /// This is the request ID assigned by Paysafe..
    /// </summary>
    public string RequestId { get; set; }

    public string Description { get; set; }
    /// <summary>
    /// This is the authorization code
    /// </summary>
    public string AuthCode { get; set; }
    /// <summary>
    /// This is the transaction date and time.
    /// </summary>
    public string TxnDateTime { get; set; }
    
    /// <summary>
    /// Bank net transaction Id/Merch Tran Ref
    /// </summary>
    public string ReferenceNbr { get; set; }
    /// <summary>
    ///This is the raw response reason code returned by Credorax.
    /// </summary>
    public string ResponseReasonCode { get; set; }
    /// <summary>
    ///This is the raw cvv2 result code.
    /// </summary>
    public string Cvv2Result { get; set; }
    
    
}

