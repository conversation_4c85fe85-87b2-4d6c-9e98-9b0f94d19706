namespace FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.Request.Authentication;

public enum ExemptionIndicator
{
    /// <summary>
    /// This exemption is valid only for transactions lower or equal to 30 euro,
    /// not exceeding five transactions in a row or 100 euro total amount transactions
    /// with no SCA (Strong Customer Authentication)
    /// </summary>
    LOW_VALUE_EXEMPTION,

    /// <summary>
    /// The exemption applies specifically to transactions that are 100 euros or lower.
    /// However, there could be additional criteria that need to be considered.
    /// Please reach out to your account manager for further details.
    /// </summary>
    TRA_EXEMPTION
}