using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paysafe.ErrorCodes
{
    public enum AuthorizationErrors
    {
        AccountNotConfiguredForLowValueExemption = 3064,
        AmountExceedsLowValueAmount = 3065,
        LowValueNotAllowedForStoredCredentials = 3066,
        ExemptionIndicatorExcludesOtherAuthFields = 3067,

        /// <summary>
        ///   Your request has failed the AVS check. Note that the amount has stil
        ///   l been reserved on the Customer's card and will be released in 3–5 business days.
        ///   Please ensure the billing address is accurate before retrying the transaction.
        /// </summary>
        AvsCheckFailed = 3007,
        CardTypeNotConfiguredInAccount = 3008,
        DeclinedByIssuingBank = 3009,

        /// <summary>
        ///     Your request has been declined by the issuing bank 
        ///     because the card used is a restricted card.
        ///     Contact the cardholder's credit card company for further investigation.
        /// </summary>
        RestrictedCard = 3011,
        CardExpiryDateIsInvalid = 3012,
        CreditCardAccountHasProblems = 3013,
        UnknownDeclineByIssuingBank = 3014,

        /// <summary>
        /// The bank has requested that you process the transaction manually 
        /// by calling the cardholder's credit card company.
        /// </summary>
        MustCallToCreditCardCompany = 3015,

        /// <summary>
        ///  The bank has requested that you process the transaction
        ///  manually by calling the cardholder's credit card company.
        /// </summary>
        StolenCard = 3016,
        InvalidCardNumber = 3017,

        /// <summary>
        /// The bank has requested that you retry the transaction.
        /// </summary>
        MustRetryTransaction = 3018,

        /// <summary>
        /// Your request has failed the CVV check. 
        /// Please note that the amount may still have been reserved 
        /// on the Customer's card, in which case it will be released in 3–5 business days.
        /// </summary>
        FailedCvvCheck = 3019,

        /// <summary>
        /// The bank has requested that you retry the transaction.
        /// </summary>
        MustRetryTransaction2 = 3020,
        RequestConfirmationNumberNotFound = 3021,
        InsufficientFunds = 3022,

        /// <summary>
        /// 	Your request has been declined by the issuing bank due to its proprietary card activity regulations.
        /// </summary>
        DeclinedByRegulations = 3023,
        TransactionNotPermittedByBank = 3024,
        InvalidDataByGateway = 3025,
        InvalidAccountTypeByGateway = 3026,
        LimitExceededByGateway = 3027,
        SystemErrorByGateway = 3028,
        RejectedByGateway = 3029,
        UnauthorizedByGateway = 3030,
        ReferenceIsNotOnHold = 3031,
        CardOnNegativeDatabase = 3032,
        ExceededPinAttempts = 3035,
        InvalidIssuer = 3036,
        InvalidRequest = 3037,
        CustomerCancellation = 3038,
        InvalidAuthenticationValue = 3039,
        RequestTypeNotPermittedOnCard = 3040,
        Timeout = 3041,
        CryptographicError = 3042,
        DuplicateRequest = 3043,
        InvalidDateFormat = 3044,
        AmountSetToZero = 3045,
        AmountWasSetToZero = 3046,
        AmountExceedsFloorLimit = 3047,
        AmountLessThanFloorLimit = 3048,
        CreditCardExpired = 3049,
        FraudulentActivitySuspected = 3050,
        ContactAcquirerForMoreInformation = 3051,
        CreditCardIsRestricted = 3052,
        CallAcquirerForMoreInformation = 3053,
        SuspectedFraud = 3054,
        TransactionTypeIsNotSupported = 3055,
        OriginalTransactionTypeDoesNotMatch = 3056,
        CallIssuerDoNotRetryTransaction = 3057,
        StrongCustomerAuthenticationRequired = 3060
    }
}