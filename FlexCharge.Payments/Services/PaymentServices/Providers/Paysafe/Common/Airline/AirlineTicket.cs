using System.ComponentModel.DataAnnotations;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.Airline;

public class AirlineTicket
{
    [MinLength(1)]
    [MaxLength(20)]
    public string TicketNumber { get; set; }
    
    /// <summary>
    /// ndicates whether this ticket is non-refundable. This entry should be supplied on CPS/Passenger Transport 1 or 2 transactions if the ticket was purchased as a non-refundable ticket.
    /// Valid values are: false - No restriction : default,
    /// true - Restricted (non-refundable) ticket.
    /// </summary>
    public bool isRestrictedTicket { get; set; }
    
    [StringLength(40)]
    public string CityOfTicketIssuing { get; set; }
    
    public TicketDeliveryMethod? TicketDeliveryMethod { get; set; }

    /// <summary>
    /// Specifies whether the purchaser is the ticket holder or agency.
    /// </summary>
    public bool IsAgencyCard { get; set; }

    /// <summary>
    /// Ticket's issue date. Sometime it is different that the date of the transaction (can be before that).
    /// Date format = YYYY-MM-DD, ISO 8601. Expected: 2021-01-26
    /// </summary>
    public string TicketIssueDate { get; set; }

    public int TicketPrice { get; set; }
    /// <summary>
    /// The number of the passengers which tickets are with the same PNR.
    /// </summary>
    [Range(0, 10)]
    public int NumberOfPax {get; set; }
}