
using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.Response;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.Response.Errors;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Refund;

public class PaysafeRefundResponse:PaysafeRefundRequest, IPaysafeResponse
{
    public string Id { get; set; }
    public string ChildAccountNum { get; set; }
    public string TxnTime { get; set; }
    public Status Status { get; set; }
    public Error Error { get; set; }
    public int[] RiskReasonCode { get; set; }
    public PurchaseReturnAuthorization PurchaseReturnAuthorization { get; set; }
    public Link[] Links { get; set; }
    // [JsonExtensionData]
    // private IDictionary<string, JToken> _additionalData;
    [JsonExtensionData]
    private IDictionary<string, JsonElement> _additionalData;
}