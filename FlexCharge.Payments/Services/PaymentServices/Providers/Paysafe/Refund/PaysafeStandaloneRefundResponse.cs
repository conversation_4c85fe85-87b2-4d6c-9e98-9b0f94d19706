using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.Response;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.Response.Errors;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Refund;

public class PaysafeStandaloneRefundResponse : PaysafeStandaloneRefundRequestWithCardNumber, IPaysafeResponse
{
    public string Id { get; set; }
    public Error Error { get; set; }
    public string RiskReasonCode { get; set; }
    public ActionLink[] Links { get; set; }
    public Status Status { get; set; }
}