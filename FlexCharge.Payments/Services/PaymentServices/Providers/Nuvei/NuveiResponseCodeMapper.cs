using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace FlexCharge.Payments.Services.PaymentServices;

public static class NuveiResponseCodeMapper
{
    private static readonly Dictionary<string, string> TextMap = new()
    {
        {CleanText("Call Issuer"), "1002"},
        {CleanText("Invalid Merchant"), "1156"},
        {CleanText("Pick-up card"), "1157"},
        {CleanText("Do not honor"), "1158"},
        {CleanText("Decline"), "1158"},
        {CleanText("External error in processing"), ""},
        {CleanText("Pick-up card, special condition (fraud account)"), "1159"},
        {CleanText("Invalid transaction"), "1162"},
        {CleanText("Invalid amount"), "1153"},
        {CleanText("Invalid card number"), "1102"},
        {CleanText("No such issuer"), "1164"},
        {CleanText("Unable to locate record on file"), "1165"},
        {CleanText("File temporarily not available"), "1166"},
        {CleanText("Format error"), "1167"},
        {CleanText("No credit account"), "1168"},
        {CleanText("Lost card, pick-up"), "1169"},
        {CleanText("Stolen card, pick-up"), "1170"},
        {CleanText("Insufficient funds"), "2015"},
        {CleanText("No checking account"), "1171"},
        {CleanText("No saving account"), "1172"},
        {CleanText("Expired card"), "1005"},
        {CleanText("Incorrect PIN"), "1173"},
        {CleanText("Transaction not permitted to cardholder"), "1174"},
        {CleanText("Transaction not permitted on terminal"), "1175"},
        {CleanText("Suspected fraud"), "1176"},
        {CleanText("Exceeds withdrawal limit"), "1177"},
        {CleanText("Restricted card"), "1233"},
        {CleanText("Restricted card/bank account"), "1233"},
        {CleanText("Error in decryption of PIN block"), "1178"},
        {CleanText("Exceeds withdrawal frequency"), "1179"},
        {CleanText("Invalid transaction, contact card issuer"), "1180"},
        {CleanText("PIN not changed"), "1181"},
        {CleanText("PIN tries exceeded"), "1182"},
        {CleanText("Cryptographic error found in PIN"), ""},
        {CleanText("Transaction cannot be completed"), "1246"},
        {CleanText("Duplicate transaction"), "1196"},
        {CleanText("Timeout/retry"), "1004"},
        {CleanText("invalid CVV2"), "1023"},
        {CleanText("Revocation of authorization order"), "1200"},
        {CleanText("Revocation of all authorization orders"), "1202"},
        {CleanText("Routing error"), "FC-0090"},
        {CleanText("Already reversed"), "FC-0005"},
        {CleanText("Closed account"), "FC-0005"},
        {CleanText("No current account"), "FC-0005"},
        {CleanText("Invalid \"To\" account specified"), "FC-0006"},
        {CleanText("Invalid \"From\" account specified"), "FC-0006"},
        {CleanText("Invalid account specified"), "FC-0006"},
        {CleanText("System not available"), "FC-0006"},
        {CleanText("Issuer or switch inoperative"), "FC-0091"},
        {CleanText("Cannot verify PIN"), "FC-0070"},
        {CleanText("PIN unacceptable. Retry"), "FC-0070"}
    };

    public static string GetCodeForText(string text)
    {
        var cleanText = CleanText(text);

        return TextMap.ContainsKey(cleanText)
            ? TextMap[cleanText]
            : "000"; // Returns "000" if the error message is not recognized
    }

    private static string CleanText(string inputText)
    {
        var cleanText = Regex
            .Replace(inputText, "[^a-zA-Z0-9]", "")
            .Trim()
            .ToLower();
        return cleanText;
    }
}