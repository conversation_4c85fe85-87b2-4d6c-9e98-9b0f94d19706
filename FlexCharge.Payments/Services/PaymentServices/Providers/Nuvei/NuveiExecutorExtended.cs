using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Nuvei;
using Nuvei.Request;
using Nuvei.Request.Common;
using Nuvei.Response;
using Nuvei.Response.Payment;
using Nuvei.Response.Transaction;
using Nuvei.Utils.Exceptions;
using Nuvei.Utils.Serialization;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Nuvei;

public class NuveiExecutorExtended : INuveiRequestExecutor
{
    private static readonly TimeSpan DefaultTimeoutTimeSpan = new TimeSpan(0, 0, 30);
    private readonly HttpClient _httpClient;

    private static JsonSerializerSettings SerializerSettings
    {
        get
        {
            JsonSerializerSettings serializerSettings = new JsonSerializerSettings();
            serializerSettings.DateFormatHandling = DateFormatHandling.MicrosoftDateFormat;
            CamelCasePropertyNamesContractResolver contractResolver = new CamelCasePropertyNamesContractResolver();
            contractResolver.NamingStrategy = (NamingStrategy) new CustomNamingStrategy();
            serializerSettings.ContractResolver = (IContractResolver) contractResolver;
            return serializerSettings;
        }
    }

    public NuveiExecutorExtended(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<GetSessionTokenResponse> GetSessionToken(
        GetSessionTokenRequest getSessionTokenRequest)
    {
        return await this.PostAsync<GetSessionTokenResponse, GetSessionTokenRequest>(
            (NuveiBaseRequest) getSessionTokenRequest);
    }

    public async Task<PaymentResponse> Payment(PaymentRequest paymentRequest) =>
        await this.PostAsync<PaymentResponse, PaymentRequest>((NuveiBaseRequest) paymentRequest);

    public async Task<SettleTransactionResponse> SettleTransaction(
        SettleTransactionRequest settleTransactionRequest)
    {
        return await this.PostAsync<SettleTransactionResponse, SettleTransactionRequest>(
            (NuveiBaseRequest) settleTransactionRequest);
    }

    public async Task<VoidTransactionResponse> VoidTransaction(
        VoidTransactionRequest voidTransactionRequest)
    {
        return await this.PostAsync<VoidTransactionResponse, VoidTransactionRequest>(
            (NuveiBaseRequest) voidTransactionRequest);
    }

    public async Task<RefundTransactionResponse> RefundTransaction(
        RefundTransactionRequest refundTransactionRequest)
    {
        return await this.PostAsync<RefundTransactionResponse, RefundTransactionRequest>(
            (NuveiBaseRequest) refundTransactionRequest);
    }

    public async Task<GetPaymentStatusResponse> GetPaymentStatus(
        GetPaymentStatusRequest getPaymentStatusRequest)
    {
        return await this.PostAsync<GetPaymentStatusResponse, GetPaymentStatusRequest>(
            (NuveiBaseRequest) getPaymentStatusRequest);
    }

    public async Task<OpenOrderResponse> OpenOrder(OpenOrderRequest openOrderRequest) =>
        await this.PostAsync<OpenOrderResponse, OpenOrderRequest>((NuveiBaseRequest) openOrderRequest);

    public async Task<InitPaymentResponse> InitPayment(InitPaymentRequest initPaymentRequest) =>
        await this.PostAsync<InitPaymentResponse, InitPaymentRequest>((NuveiBaseRequest) initPaymentRequest);

    public async Task<Authorize3dResponse> Authorize3d(Authorize3dRequest authorize3dRequest) =>
        await this.PostAsync<Authorize3dResponse, Authorize3dRequest>((NuveiBaseRequest) authorize3dRequest);

    public async Task<Verify3dResponse> Verify3d(Verify3dRequest verify3dRequest) =>
        await this.PostAsync<Verify3dResponse, Verify3dRequest>((NuveiBaseRequest) verify3dRequest);

    public async Task<PayoutResponse> Payout(PayoutRequest payoutRequest) =>
        await this.PostAsync<PayoutResponse, PayoutRequest>((NuveiBaseRequest) payoutRequest);

    public async Task<GetCardDetailsResponse> GetCardDetails(GetCardDetailsRequest request) =>
        await this.PostAsync<GetCardDetailsResponse, GetCardDetailsRequest>((NuveiBaseRequest) request);

    public async Task<GetDCCResponse> GetDCCDetails(GetDCCRequest request) =>
        await this.PostAsync<GetDCCResponse, GetDCCRequest>((NuveiBaseRequest) request);

    public async Task<GetMerchantPaymentMethodsResponse> GetMerchantPaymentMethods(
        GetMerchantPaymentMethodsRequest request)
    {
        return await this.PostAsync<GetMerchantPaymentMethodsResponse, GetMerchantPaymentMethodsRequest>(
            (NuveiBaseRequest) request);
    }

    public async Task<T1> PostAsync<T1, T2>(NuveiBaseRequest request) => await GetResponseData<T1>(await _httpClient
        .PostAsync(request.RequestUri.ToString(), CreateHttpContent<NuveiBaseRequest>(request)).ConfigureAwait(false));

    private static HttpContent CreateHttpContent<T>(T content) => (HttpContent) new StringContent(
        JsonConvert.SerializeObject((object) content, SerializerSettings), Encoding.UTF8, "application/json");

    private static async Task<T> GetResponseData<T>(HttpResponseMessage response)
    {
        T responseData;
        try
        {
            response.EnsureSuccessStatusCode();
            responseData = JsonConvert.DeserializeObject<T>(await response.Content.ReadAsStringAsync());
        }
        catch (HttpRequestException ex)
        {
            throw new NuveiException(ex.Message);
        }

        return responseData;
    }
}