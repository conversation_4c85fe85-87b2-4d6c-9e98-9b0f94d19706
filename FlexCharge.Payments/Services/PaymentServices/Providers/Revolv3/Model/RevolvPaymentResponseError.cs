using System.Collections.Generic;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Revolv3.Model;

public class RevolvPaymentResponseError
{
    public string message { get; set; }
    public List<string> errors { get; set; }
    public List<RevolveFluentValidationError> fluentValidatorErrors { get; set; }
    public string errorCode { get; set; }
    public object formattedMessagePlaceholderValues { get; set; }
}