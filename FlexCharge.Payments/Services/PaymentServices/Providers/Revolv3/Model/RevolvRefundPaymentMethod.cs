namespace FlexCharge.Payments.Services.PaymentServices.Providers.Revolv3.Model;

public class RevolvRefundPaymentMethod
{
    public RevolvBillingAddress billingAddress { get; set; }
    public string billingFirstName { get; set; }
    public string billingLastName { get; set; }
    public CardDetails paymentMethodCreditCardDetails { get; set; }
    public AchDetails paymentMethodCAchDetails { get; set; }
    public string merchantPaymentMethodRefId { get; set; }


    public class AchDetails : IRevolvePaymentType
    {
        public string accountNumberLast4Digits { get; set; }
        public int accountNumberLength { get; set; }
        public string accountType { get; set; }
    }

    public class CardDetails : IRevolvePaymentType
    {
        public string binNumber { get; set; }
        public string paymentLast4Digit { get; set; }
        public string paymentExpirationDate { get; set; }
    }
}