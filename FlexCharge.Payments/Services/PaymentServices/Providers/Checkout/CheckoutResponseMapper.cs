using System.Collections.Generic;
using System.ComponentModel;
using FlexCharge.Common.Shared.Payments;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Checkout;

public class CheckoutResponseMapper : IndexTableProviderResponseMapperBase
{
    public static void Initialize()
    {
    }

    static CheckoutResponseMapper()
    {
        IndexTable = new Dictionary<string, MappedResponse>
        {
            {"10000", InternalResponseMapper.GetMappedResponse("0")},
            {"10008", InternalResponseMapper.GetMappedResponse("0")},
            {"10011", InternalResponseMapper.GetMappedResponse("0")},
            {"10076", InternalResponseMapper.GetMappedResponse("0")},
            {"10077", InternalResponseMapper.GetMappedResponse("0")},
            {"10081", InternalResponseMapper.GetMappedResponse("0")},
            {"10100", InternalResponseMapper.GetMappedResponse("0")},
            {"10200", InternalResponseMapper.GetMappedResponse("0")},
            {"20001", InternalResponseMapper.GetMappedResponse("1")},
            {"20002", InternalResponseMapper.GetMappedResponse("2")},
            {"20003", InternalResponseMapper.GetMappedResponse("3")},
            {"20005", InternalResponseMapper.GetMappedResponse("5")},
            {"20006", InternalResponseMapper.GetMappedResponse("6")},
            {"20009", InternalResponseMapper.GetMappedResponse("9")},
            {"20010", InternalResponseMapper.GetMappedResponse("10")},
            {"20012", InternalResponseMapper.GetMappedResponse("12")},
            {"20013", InternalResponseMapper.GetMappedResponse("13")},
            {"20014", InternalResponseMapper.GetMappedResponse("14")},
            {"20017", InternalResponseMapper.GetMappedResponse("17")},
            {"20018", InternalResponseMapper.GetMappedResponse("18")},
            {"20019", InternalResponseMapper.GetMappedResponse("19")},
            {"20020", InternalResponseMapper.GetMappedResponse("20")},
            {"20021", InternalResponseMapper.GetMappedResponse("21")},
            {"20022", InternalResponseMapper.GetMappedResponse("22")},
            {"20023", InternalResponseMapper.GetMappedResponse("23")},
            {"20024", InternalResponseMapper.GetMappedResponse("24")},
            {"20025", InternalResponseMapper.GetMappedResponse("25")},
            {"20026", InternalResponseMapper.GetMappedResponse("26")},
            {"20027", InternalResponseMapper.GetMappedResponse("27")},
            {"20028", InternalResponseMapper.GetMappedResponse("28")},
            {"20029", InternalResponseMapper.GetMappedResponse("29")},
            {"20030", InternalResponseMapper.GetMappedResponse("30")},
            {"20031", InternalResponseMapper.GetMappedResponse("31")},
            {"20032", InternalResponseMapper.GetMappedResponse("32")},
            {"20038", InternalResponseMapper.GetMappedResponse("38")},
            {"20039", InternalResponseMapper.GetMappedResponse("39")},
            {"20040", InternalResponseMapper.GetMappedResponse("40")},
            {"20042", InternalResponseMapper.GetMappedResponse("42")},
            {"20044", InternalResponseMapper.GetMappedResponse("44")},
            {"20046", InternalResponseMapper.GetMappedResponse("46")},
            {"20051", InternalResponseMapper.GetMappedResponse("51")},
            {"20052", InternalResponseMapper.GetMappedResponse("52")},
            {"20053", InternalResponseMapper.GetMappedResponse("53")},
            {"20054", InternalResponseMapper.GetMappedResponse("54")},
            {"20055", InternalResponseMapper.GetMappedResponse("55")},
            {"20056", InternalResponseMapper.GetMappedResponse("56")},
            {"20057", InternalResponseMapper.GetMappedResponse("57")},
            {"20058", InternalResponseMapper.GetMappedResponse("58")},
            {"20059", InternalResponseMapper.GetMappedResponse("59")},
            {"20060", InternalResponseMapper.GetMappedResponse("60")},
            {"20061", InternalResponseMapper.GetMappedResponse("61")},
            {"20062", InternalResponseMapper.GetMappedResponse("62")},
            {"20063", InternalResponseMapper.GetMappedResponse("63")},
            {"20064", InternalResponseMapper.GetMappedResponse("64")},
            {"20065", InternalResponseMapper.GetMappedResponse("65")},
            {"20066", InternalResponseMapper.GetMappedResponse("66")},
            {"20067", InternalResponseMapper.GetMappedResponse("67")},
            {"20068", InternalResponseMapper.GetMappedResponse("68")},
            {"20075", InternalResponseMapper.GetMappedResponse("75")},
            {"20078", InternalResponseMapper.GetMappedResponse("78")},
            {"20082", InternalResponseMapper.GetMappedResponse("82")},
            {"20083", InternalResponseMapper.GetMappedResponse("83")},
            {"20084", InternalResponseMapper.GetMappedResponse("84")},
            {"20085", InternalResponseMapper.GetMappedResponse("85")},
            {"20086", InternalResponseMapper.GetMappedResponse("86")},
            {"20087", InternalResponseMapper.GetMappedResponse("87")},
            {"20088", InternalResponseMapper.GetMappedResponse("88")},
            {"20089", InternalResponseMapper.GetMappedResponse("89")},
            {"20090", InternalResponseMapper.GetMappedResponse("90")},
            {"20091", InternalResponseMapper.GetMappedResponse("91")},
            {"20092", InternalResponseMapper.GetMappedResponse("92")},
            {"20093", InternalResponseMapper.GetMappedResponse("93")},
            {"20094", InternalResponseMapper.GetMappedResponse("94")},
            {"20095", InternalResponseMapper.GetMappedResponse("95")},
            {"20096", InternalResponseMapper.GetMappedResponse("95")},
            {"20097", InternalResponseMapper.GetMappedResponse("95")},
            {"20098", InternalResponseMapper.GetMappedResponse("95")},
            {"20099", InternalResponseMapper.GetMappedResponse("95")},
            {"2006P", InternalResponseMapper.GetMappedResponse("95")},
            {"200N0", InternalResponseMapper.GetMappedResponse("N0")},
            {"200N7", InternalResponseMapper.GetMappedResponse("N7")},
            {"200O5", InternalResponseMapper.GetMappedResponse("95")},
            {"200P1", InternalResponseMapper.GetMappedResponse("95")},
            {"200P9", InternalResponseMapper.GetMappedResponse("95")},
            {"200R1", InternalResponseMapper.GetMappedResponse("95")},
            {"200R3", InternalResponseMapper.GetMappedResponse("R3")},
            {"200S4", InternalResponseMapper.GetMappedResponse("95")},
            {"200T2", InternalResponseMapper.GetMappedResponse("95")},
            {"200T3", InternalResponseMapper.GetMappedResponse("95")},
            {"200T5", InternalResponseMapper.GetMappedResponse("95")},
            {"20100", InternalResponseMapper.GetMappedResponse("95")},
            {"20101", InternalResponseMapper.GetMappedResponse("95")},
            {"20102", InternalResponseMapper.GetMappedResponse("95")},
            {"20103", InternalResponseMapper.GetMappedResponse("95")},
            {"20104", InternalResponseMapper.GetMappedResponse("95")},
            {"20105", InternalResponseMapper.GetMappedResponse("95")},
            {"20106", InternalResponseMapper.GetMappedResponse("95")},
            {"20107", InternalResponseMapper.GetMappedResponse("95")},
            {"20108", InternalResponseMapper.GetMappedResponse("95")},
            {"20109", InternalResponseMapper.GetMappedResponse("95")},
            {"20110", InternalResponseMapper.GetMappedResponse("95")},
            {"20111", InternalResponseMapper.GetMappedResponse("95")},
            {"20112", InternalResponseMapper.GetMappedResponse("95")},
            {"20113", InternalResponseMapper.GetMappedResponse("95")},
            {"20114", InternalResponseMapper.GetMappedResponse("95")},
            {"20115", InternalResponseMapper.GetMappedResponse("95")},
            {"20116", InternalResponseMapper.GetMappedResponse("95")},
            {"20117", InternalResponseMapper.GetMappedResponse("95")},
            {"20118", InternalResponseMapper.GetMappedResponse("95")},
            {"20119", InternalResponseMapper.GetMappedResponse("95")},
            {"20120", InternalResponseMapper.GetMappedResponse("95")},
            {"20121", InternalResponseMapper.GetMappedResponse("95")},
            {"20122", InternalResponseMapper.GetMappedResponse("95")},
            {"20123", InternalResponseMapper.GetMappedResponse("95")},
            {"20124", InternalResponseMapper.GetMappedResponse("95")},
            {"20150", InternalResponseMapper.GetMappedResponse("95")},
            {"20151", InternalResponseMapper.GetMappedResponse("95")},
            {"20152", InternalResponseMapper.GetMappedResponse("95")},
            {"20153", InternalResponseMapper.GetMappedResponse("95")},
            {"20154", InternalResponseMapper.GetMappedResponse("95")},
            {"20155", InternalResponseMapper.GetMappedResponse("95")},
            {"20156", InternalResponseMapper.GetMappedResponse("95")},
            {"20157", InternalResponseMapper.GetMappedResponse("95")},
            {"20158", InternalResponseMapper.GetMappedResponse("95")},
            {"20179", InternalResponseMapper.GetMappedResponse("95")},
            {"20182", InternalResponseMapper.GetMappedResponse("95")},
            {"20183", InternalResponseMapper.GetMappedResponse("95")},
            {"20193", InternalResponseMapper.GetMappedResponse("95")},
            {"30004", InternalResponseMapper.GetMappedResponse("4")},
            {"30007", InternalResponseMapper.GetMappedResponse("7")},
            {"30015", InternalResponseMapper.GetMappedResponse("15")},
            {"30016", InternalResponseMapper.GetMappedResponse("98")},
            {"30017", InternalResponseMapper.GetMappedResponse("98")},
            {"30018", InternalResponseMapper.GetMappedResponse("98")},
            {"30019", InternalResponseMapper.GetMappedResponse("98")},
            {"30020", InternalResponseMapper.GetMappedResponse("13")},
            {"30021", InternalResponseMapper.GetMappedResponse("61")},
            {"30022", InternalResponseMapper.GetMappedResponse("61")},
            {"30033", InternalResponseMapper.GetMappedResponse("54")},
            {"30034", InternalResponseMapper.GetMappedResponse("59")},
            {"30035", InternalResponseMapper.GetMappedResponse("98")},
            {"30036", InternalResponseMapper.GetMappedResponse("62")},
            {"30037", InternalResponseMapper.GetMappedResponse("98")},
            {"30038", InternalResponseMapper.GetMappedResponse("75")},
            {"30041", InternalResponseMapper.GetMappedResponse("41")},
            {"30043", InternalResponseMapper.GetMappedResponse("43")},
            {"30044", InternalResponseMapper.GetMappedResponse("98")},
            {"30045", InternalResponseMapper.GetMappedResponse("98")},
            {"30046", InternalResponseMapper.GetMappedResponse("83")},
            {"40101", InternalResponseMapper.GetMappedResponse("96")},
            {"40201", InternalResponseMapper.GetMappedResponse("96")},
            {"40202", InternalResponseMapper.GetMappedResponse("96")},
            {"40203", InternalResponseMapper.GetMappedResponse("96")},
            {"40204", InternalResponseMapper.GetMappedResponse("96")},
            {"40205", InternalResponseMapper.GetMappedResponse("96")},

            {"HTTP422", InternalResponseMapper.GetMappedResponse("V01")},
        };
    }

    public static MappedResponse GetMappedResponse(string providerResponseCode)
    {
        return GetMappedResponse(providerResponseCode, IndexTable);
    }

    private static Dictionary<string, MappedResponse> IndexTable { get; }
}