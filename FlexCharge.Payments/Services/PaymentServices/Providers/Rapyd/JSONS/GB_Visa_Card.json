{"status": {"error_code": "", "status": "SUCCESS", "message": "", "response_code": "", "operation_id": "4df17e80-fcd5-457c-93fe-d8b7923c0c2a"}, "data": {"type": "gb_visa_card", "fields": [{"name": "number", "type": "string", "regex": "", "is_required": true, "instructions": "card number"}, {"name": "expiration_month", "type": "string", "regex": "", "is_required": true, "instructions": "expiration month as string, 01-12"}, {"name": "expiration_year", "type": "string", "regex": "", "is_required": true, "instructions": "expiration year in to digits as string, 18-99"}, {"name": "cvv", "type": "string", "regex": "", "is_required": true, "instructions": "card cvv"}, {"name": "name", "type": "string", "regex": "", "is_required": true, "instructions": "card holder name"}, {"name": "network_reference_id", "type": "string", "regex": "", "description": "Network Reference Id", "is_required": false}, {"name": "recurrence_type", "type": "string", "regex": "(recurring|installment|unscheduled)", "description": "Indicates how the token will be used. Required when this payment method is added to the customer profile. Values: 'unscheduled'=Individual unrelated payments. 'installment'=Regular payments for a defined number of payment cycles. 'recurring'=Regular payments for an indefinite period.", "is_required": false, "is_updatable": false}, {"name": "number_type", "type": "string", "regex": "(fpan|tpan)", "description": "Determining if number is funding PAN or token PAN", "is_required": false}], "payment_method_options": [{"name": "3d_required", "type": "boolean", "regex": "", "description": "Allows the client to determine whether the customer is required to complete 3DS authentication for the transaction", "is_required": false, "is_updatable": false}, {"name": "3d_version", "type": "String", "regex": "(1.0.2|2.1.0|2.2.0)", "description": "3D Secure version of the transaction.", "is_required": false, "is_updatable": false}, {"name": "cavv", "type": "String", "regex": "", "description": "Cardholder Authentication Verification Value represented as a 20-byte value that is base64 encoded.", "is_required": false, "is_updatable": false}, {"name": "eci", "type": "String", "regex": "(01|02|05|06|07|08)", "description": "Electronic Commerce Indicator (ECI) from MPI Plugin(3-D Secure 1.0) or 3DS Server(3-D Secure 2.0).", "is_required": false, "is_updatable": false}, {"name": "xid", "type": "String", "regex": "", "description": "3D Secure XID, Base64 encoded.Required for VISA 1.0", "is_required": false, "is_updatable": false}, {"name": "ds_trans_id", "type": "String", "regex": "", "description": "The Directory Server (DS) Transaction ID. Required Mastercard 2.0.", "is_required": false, "is_updatable": false}, {"name": "tavv", "type": "String", "regex": "", "description": "Token Authentication Verification Value represented as a 20-byte value that is base64 encoded.", "is_required": false, "is_updatable": false}], "payment_options": [{"name": "capture", "type": "boolean", "regex": "", "description": "Determines when the payment is processed for capture.", "is_required": false, "is_updatable": false}, {"name": "complete_payment_url", "type": "string", "regex": "", "description": "the complete_payment_url field must be filled in.", "is_required": true, "is_updatable": false}, {"name": "error_payment_url", "type": "string", "regex": "", "description": "the error_payment_url field must be filled in.", "is_required": true, "is_updatable": false}, {"name": "customer", "type": "string", "regex": "", "description": "ID of a customer object, a string starting with ‘cus_‘. The customer object must contain the fields listed as required, and can contain additional fields listed here.If the customer object does not exist yet, use ‘Create Customer‘", "is_required": false, "is_updatable": false}, {"name": "initiation_type", "type": "string", "regex": "(customer_present|installment|moto|recurring|unscheduled)", "description": "This indicates how the transaction was initiated", "is_required": false, "is_updatable": false}], "minimum_expiration_seconds": 600, "maximum_expiration_seconds": 604800}}