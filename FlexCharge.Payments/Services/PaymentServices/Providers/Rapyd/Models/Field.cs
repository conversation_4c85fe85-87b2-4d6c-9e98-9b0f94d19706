using System.Text.Json.Serialization;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd.Models.PaymentMethodTypes;

public class Field
{
    [JsonPropertyName("name")] public string Name { get; set; }

    [JsonPropertyName("type")] public string Type { get; set; }

    [JsonPropertyName("regex")] public string Regex { get; set; }

    [JsonPropertyName("description")] public string Description { get; set; }

    [JsonPropertyName("is_required")] public bool IsRequired { get; set; }

    [JsonPropertyName("is_updatable")] public bool IsUpdatable { get; set; }
}