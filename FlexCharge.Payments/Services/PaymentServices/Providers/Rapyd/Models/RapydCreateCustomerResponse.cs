using FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd.Cards;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd.Models;

public class RapydCreateCustomerResponse
{
    public RapydAddress[] addresses { get; set; }
    public string business_vat_id { get; set; }
    public string category { get; set; }
    public long created_at { get; set; }
    public string customer { get; set; }
    public string default_payment_method { get; set; }
    public bool delinquent { get; set; }
    public string description { get; set; }
    public object discount { get; set; }
    public string email { get; set; }
    public string ewallet { get; set; }
    public RapydCard[] fields { get; set; }
    public string id { get; set; }
    public string invoice_prefix { get; set; }
    public object metadata { get; set; }
    public string name { get; set; }
    public RapydNextAction? next_action { get; set; }
    public RapydPaymentMethod[] payment_methods { get; set; }
    public string phone_number { get; set; }
    public object[] subscriptions { get; set; }
}