using System.Text.Json.Serialization;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd.Models.PaymentMethodTypes.ListTypes;

public class RapydListTypesResponse
{
    [JsonPropertyName("amount_range_per_currency")]
    public AmountRange[] Amount_range_per_currency { get; set; }

    [JsonPropertyName("category")] public PaymentMethodCategory Category { get; set; }
    [JsonPropertyName("country")] public string Country { get; set; }
    [JsonPropertyName("currencies")] public string[] Currencies { get; set; }
}