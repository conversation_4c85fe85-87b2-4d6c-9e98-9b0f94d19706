using FluentValidation;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd.Models;

public class RapydCaptureRequest
{
    public decimal amount { get; set; }
    public string receipt_email { get; set; }
    public string statement_descriptor { get; set; }

    public class Validator : AbstractValidator<RapydCaptureRequest>
    {
        public Validator()
        {
            RuleFor(x => x.amount).GreaterThan(0);
            RuleFor(x => x.receipt_email).EmailAddress().When(x => !string.IsNullOrWhiteSpace(x.receipt_email))
                .WithSeverity(Severity.Warning);
            RuleFor(x => x.statement_descriptor).NotEmpty().WithSeverity(Severity.Warning);
            ;
        }
    }
}