using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Validation.FluentValidation;
using FlexCharge.Common.Logging;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.CardBrands;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd.Cards;
using FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd.Errors;
using FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd.Models;
using FlexCharge.Utils;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;
using PhoneNumbers;
using IResult = FlexCharge.Payments.Services.PaymentServices.Interfaces.IResult;
using JsonConverter = Newtonsoft.Json.JsonConverter;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd;

public class RapydPaymentProvider : PaymentProviderBase
{
    private readonly PostgreSQLDbContext _context;

    private RapydSdk _sdk;


    private JsonSerializerOptions _jsonOptions;

    private JsonSerializerSettings _jsonSerializeSettings = new()
    {
        ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore,
        ContractResolver = new CamelCasePropertyNamesContractResolver(),
        Converters = new List<JsonConverter>() {new StringEnumConverter()}
    };

    public RapydPaymentProvider(
        PostgreSQLDbContext context,
        RapydSdk sdk)
    {
        _context = context;
        _sdk = sdk;
        _jsonOptions = new()
        {
            ReferenceHandler = ReferenceHandler.IgnoreCycles,
            WriteIndented = true,
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
        _jsonOptions.Converters.Add(new JsonStringEnumConverter());
    }


    public override async Task<AuthResult> AuthorizeAsync(AuthorizationRequest authRequest,
        CancellationToken token = default)
    {
        return await AuthorizeOrSaleAsync<AuthResult, AuthorizationRequest>(authRequest, false, token);
    }

    public override async Task<CapturePaymentResult> CaptureAsync(CapturePaymentRequest captureRequest,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<RapydPaymentProvider>().LogEnterAndExit();

        var config = await GetCredentialsAsync(captureRequest.SupportedGateway.Sandbox,
            captureRequest.SupportedGateway.Id, token);

        CapturePaymentResult retval = new();
        retval.Provider = this.CurrentPaymentProvider;
        retval.TransactionId = captureRequest.TransactionId;
        retval.PaymentInstrumentId = captureRequest.PaymentInstrumentId;

        try
        {
            RapydCaptureRequest request = new()
            {
                amount = Formatters.IntToDecimalFixed2Digits(captureRequest.Amount),
                //receipt_email = captureRequest.
                statement_descriptor = captureRequest.Descriptor.Name.TruncateAndRemoveAlphaNumeric(22),
            };

            var validationResult = new RapydCaptureRequest.Validator().Validate(request);
            validationResult.ThrowOnErrors(logErrors: true);

            workspan.Log.LogRedacted(captureRequest,
                json => (template: "Rapyd {CaptureRequest} in {method} ", new object[] {json, "Capture"}),
                _jsonOptions,
                logRequest => logRequest.Gateway = null
            );

            var responseWrapper =
                await _sdk.CaptureAsync(request, captureRequest.TransactionToken, config,
                    captureRequest.OrderId.ToString(), token);

            var response = responseWrapper.Data;

            workspan.Log.LogRedacted(responseWrapper.Data,
                json => (template: "Rapyd {Response} in {method} ", logparams: new[] {json, "Capture"}),
                _jsonOptions);


            UpdateResult(responseWrapper, retval, captureRequest.OrderId.ToString(), workspan);

            retval.Status = response.status switch
            {
                _ when response is null || !string.IsNullOrEmpty(response.error_message) => TransactionStatus.Failed,
                RapydPaymentStatus.CLO => TransactionStatus.Completed,
                RapydPaymentStatus.CAN => TransactionStatus.Canceled,
                RapydPaymentStatus.ERR => TransactionStatus.Failed,
                RapydPaymentStatus.EXP => TransactionStatus.Failed,
                RapydPaymentStatus.REV => TransactionStatus.Canceled,
                RapydPaymentStatus.ACT => TransactionStatus.Completed, // not used, WAITING FOR 3DS
            };

            retval.ProcessorId =
                response?.payment_method_data?.network_reference_id; //todo-check GatewayResponse?.Processor;
        }
        catch (Exception e)
        {
            ProcessException(e, retval, workspan);
        }

        return retval;
    }

    public override async Task<SaleResult> SaleAsync(SaleRequest request, CancellationToken token = default)
    {
        return await AuthorizeOrSaleAsync<SaleResult, SaleRequest>(request, true, token);
    }

    public override async Task<ICreditPaymentResult> CreditAsync(ICreditPaymentRequest creditRequest,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<RapydPaymentProvider>().LogEnterAndExit();

        var config = await GetCredentialsAsync(creditRequest.SupportedGateway.Sandbox,
            creditRequest.SupportedGateway.Id, token);


        CreditPaymentResult result = new();
        result.Provider = this.CurrentPaymentProvider;
        result.InternalTransactionId = creditRequest.TransactionId;
        result.PaymentInstrumentId = creditRequest.PaymentInstrumentId;
        result.ProcessorId = creditRequest.SupportedGateway.ProcessorId;
        result.SupportedGatewayId = creditRequest.SupportedGateway.Id;

        workspan.Log.LogRedacted(creditRequest,
            json => (template: "Rapyd {CreditRequest} in {method} ", new[] {json, "Refund"}),
            _jsonOptions,
            logRequest =>
            {
                var creditCard = logRequest.CreditCard;

                if (creditCard != null)
                {
                    creditCard.Number = creditCard.Number?.Substring(0, 8);
                    creditCard.VerificationValue = StringHelpers.MaskAll(creditCard.VerificationValue, '*');
                    logRequest.CreditCard = creditCard;
                }

                logRequest.Gateway = null;
            });


        // var responseWrapper = await _sdk.AuthorizeOrSaleAsync(request, config, authRequest.OrderId.ToString(), token);


        try
        {
            var toRefund = false;
            var settlmentWrapper = await _sdk.RetrievePayment(creditRequest.ProviderTransactionToken, config,
                Guid.NewGuid().ToString(), token);

            // workspan.Log.LogAsJson(responseWrapper,
            //     json => (template: "Rapyd {Response} in {method} ", logparams:  new[]{ json, "Authorize"}), 
            //     _jsonOptions);
            var settlement = settlmentWrapper?.Data;

            bool availableToRefund = settlement?.status == RapydPaymentStatus.CLO;
            if (settlement is not null)
            {
                workspan.Log.Information("Rapyd settlment {Status} ", settlement.status);
                workspan.Log.Information("Rapyd settlment {AvailableToRefund} ", availableToRefund);
            }

            if (settlement?.amount < Formatters.IntToDecimalFixed2Digits(creditRequest.Amount))
            {
                throw new Exception("Rapyd - cannot refund more than settled");
            }
            else if (settlement is not null && !availableToRefund)
            {
                throw new Exception("Rapyd - payment  not available for refund");
            }


            if (settlement is null)
            {
                workspan.Log.Warning("Rapyd settlement is null");
                toRefund = true;
            }

            else if (settlement.amount >= Formatters.IntToDecimalFixed2Digits(creditRequest.Amount))
            {
                toRefund = true;
            }
            // else if (settlment.Status == SettlementStatus.RECEIVED
            //          || settlment.Status == SettlementStatus.PENDING
            //          || settlment.Status == SettlementStatus.INITIATED
            //          || settlment.Status == SettlementStatus.PROCESSING
            //         )
            // {
            //     CancelSettlementRequest cr = new()
            //     {
            //         Status = CompleteStatus.CANCELLED
            //     };
            //
            //     var cancelResponse = await _sdk.CancelSettlementAsync(cr, creditRequest.ProviderTransactionToken,
            //         urlPrefix,
            //         userName, password, token);
            //     LogResponse(cancelResponse, workspan);
            //
            //     if (cancelResponse.Status == CompleteStatus.CANCELLED) // cancel succeeded
            //     {
            //         result.ProviderResponseCode = "VOID";
            //         result.ProviderResponseMessage = "VOID";
            //         result.Status = TransactionStatus.Completed;
            //         result.RawResult = JsonConvert.SerializeObject(cancelResponse, _jsonSerializeSettings);
            //         LogResponse(cancelResponse, workspan);
            //         result.ProviderTransactionToken = cancelResponse.Id;
            //         workspan.Log.Information("Capture with {ProviderToken} voided",
            //             creditRequest.ProviderTransactionToken);
            //     }
            //     else
            //     {
            //         toRefund = true;
            //     }
            else
            {
                toRefund = true;
            }

            if (toRefund)
            {
                RapydRefundRequest request = new()
                {
                    amount = Formatters.IntToDecimalFixed2Digits(creditRequest.Amount),
                    currency = creditRequest.CurrencyCode,
                    merchant_reference_id = creditRequest.OrderId.ToString(),
                    metadata = GetMetadata(creditRequest),
                    payment = creditRequest.ProviderTransactionToken,
                    reason = "refund"
                    // Amount = creditRequest.Amount,
                    // MerchantRefNum = creditRequest.TransactionId.ToString(),
                    // DupCheck = true,
                    // Splitpay = null,
                    // GatewayReconciliationId = Guid.NewGuid().ToString(),
                };
                var validationResult = new RapydRefundRequest.Validator().Validate(request);
                validationResult.ThrowOnErrors(logErrors: true);

                var responseWrapper =
                    await _sdk.RefundAsync(request, config, Guid.NewGuid().ToString(),
                        token);
                var response = responseWrapper.Data;
                workspan.Log.LogRedacted(response,
                    json => (template: "Rapyd {Response} in {method} ", logparams: new[] {json, "Refund"}),
                    _jsonOptions);


                UpdateResult(response, result, creditRequest.OrderId.ToString());

                result.Status = response.status switch
                {
                    //_ when response.Error is not null => TransactionStatus.Failed,
                    RapydRefundStatus.Error or RapydRefundStatus.Rejected => TransactionStatus.Failed,
                    RapydRefundStatus.Completed => TransactionStatus.Completed,
                    RapydRefundStatus.Pending => TransactionStatus.Initialized,
                };
                if (response.status == RapydRefundStatus.Rejected)
                {
                    result.ProviderResponseCode = "VOID";
                }
            }
        }
        catch (Exception e)
        {
            ProcessException(e, result, workspan);
        }

        return result;

        void UpdateResult(RapydRefundResponse response, Interfaces.IResult result, string orderId)
        {
            var workspan = Workspan.Current;

            if (response.status == RapydRefundStatus.Error)
            {
                result.ProviderResponseCode = RapydRefundStatus.Error.ToString();
                result.ProviderResponseMessage = "Payment is  not in closed state";

                result.AddErrorWithCode(result.ProviderResponseCode, result.ProviderResponseMessage);
                workspan.Log.Information("RapydPaymentProvider refund for {orderId} failed with {ResponseSummary}",
                    orderId,
                    result.ProviderResponseMessage);
            }
            else
            {
                result.ProviderResponseCode = "COMPLETED";
                result.ProviderResponseMessage = "COMPLETED";
            }

            result.RawResult = JsonConvert.SerializeObject(response, _jsonSerializeSettings);
            result.ProviderTransactionToken = response.id;
        }
    }

    public override async Task<IVoidPaymentResult> VoidAsync(IVoidPaymentRequest voidPaymentRequest,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<RapydPaymentProvider>().LogEnterAndExit();

        //todo-move to orchestrator
        var validationResult = voidPaymentRequest.Validate(validator =>
        {
            validator.RuleFor(x => x.TransactionId).NotEmpty();
            validator.RuleFor(x => x.PaymentInstrumentId).NotEmpty();
            validator.RuleFor(x => x.ProviderTransactionToken).NotEmpty();
            validator.RuleFor(x => x.OrderId).NotEmpty();
            validator.RuleFor(x => x.Mid).NotEmpty();
            validator.RuleFor(x => x.Amount).GreaterThan(0);
            validator.RuleFor(x => x.CurrencyCode).NotEmpty();
            validator.RuleFor(x => x.Gateway).NotNull();
            // RuleFor(x => x.Reason).NotEmpty();
        });

        validationResult.ThrowOnErrors(logErrors: true);


        var config = await GetCredentialsAsync(voidPaymentRequest.SupportedGateway.Sandbox,
            voidPaymentRequest.SupportedGateway.Id, token);

        VoidPaymentResult result = new();
        result.ProcessorId = voidPaymentRequest.SupportedGateway.ProcessorId;
        result.SupportedGatewayId = voidPaymentRequest.SupportedGateway.Id;
        result.Provider = this.CurrentPaymentProvider;
        result.PaymentInstrumentId = voidPaymentRequest.PaymentInstrumentId;

        var paymentWrapper = await _sdk.RetrievePayment(voidPaymentRequest.ProviderTransactionToken, config,
            Guid.NewGuid().ToString(), token);

        var payment = paymentWrapper.Data;
        var toVoid = payment?.status == RapydPaymentStatus.ACT;

        if (toVoid)
        {
            workspan.Log.LogRedacted(voidPaymentRequest,
                json => (template: "Rapyd {VoidRequest} in {method} ", new[] {json, "Void"}),
                _jsonOptions,
                logRequest => logRequest.Gateway = null
            );

            try
            {
                var responseWrapper =
                    await _sdk.VoidAsync(voidPaymentRequest.ProviderTransactionToken,
                        config, Guid.NewGuid().ToString(), token);

                var response = responseWrapper.Data;

                workspan.Log.LogRedacted(responseWrapper.Data,
                    json => (template: "Rapyd {Response} in {method} ", logparams: new[] {json, "Capture"}),
                    _jsonOptions);
                UpdateResult(responseWrapper, result, voidPaymentRequest.OrderId.ToString(), workspan);


                result.Status = response.status switch
                {
                    _ when response is null || !string.IsNullOrEmpty(response.error_message) =>
                        TransactionStatus.Failed,
                    RapydPaymentStatus.CLO => TransactionStatus.Completed,
                    RapydPaymentStatus.CAN => TransactionStatus.Canceled,
                    RapydPaymentStatus.ERR => TransactionStatus.Failed,
                    RapydPaymentStatus.EXP => TransactionStatus.Failed,
                    RapydPaymentStatus.REV => TransactionStatus.Canceled,
                    RapydPaymentStatus.ACT => TransactionStatus.Completed, // not used, WATING FOR 3DS
                };
                result.ProviderTransactionToken = response?.id;
                result.RawResult = response is null
                    ? null
                    : JsonSerializer.Serialize(response);

                result.ProviderResponseMessage = response?.status?.ToString();
            }
            catch (Exception e)
            {
                ProcessException(e, result, workspan);
            }
        }
        else
        {
            workspan.Log.Warning("Rapyd payment {OrderId} is not available for void", voidPaymentRequest.OrderId);
        }

        return result;
    }

    public ProcessPaymentResult ProcessRecurringPayment(ProcessPaymentRequest processPaymentRequest)
    {
        throw new NotImplementedException();
    }

    public CancelRecurringPaymentResult CancelRecurringPayment(CancelRecurringPaymentRequest cancelPaymentRequest)
    {
        throw new NotImplementedException();
    }

    public override async Task<IVerifyInstrumentResult> VerifyAsync(IVerifyInstrumentRequest payload,
        CancellationToken token = default)
    {
        //throw new NotImplementedException();
        payload.Amount = 0;
        return await AuthorizeOrSaleAsync<VerifyInstrumentResult, IVerifyInstrumentRequest>(payload, false, token);
    }

    private async Task<RapydConfiguration> GetCredentialsAsync(bool isSandbox,
        Guid supportedGatewayId, CancellationToken token = default)
    {
        var gateway = await _context.SupportedGateways
            .Where(x => x.Id == supportedGatewayId && x.Sandbox == isSandbox)
            .FirstOrDefaultAsync(token);
        if (gateway == null)
            throw new Exception("Rapyd gateway not found");

        return _sdk.GetCredentials(gateway);
    }

    private void UpdateResult(Response<RapydCreatePaymentResponse> responseWrapper, Interfaces.IResult result,
        string orderId,
        Workspan workspan)
    {
        if (!string.IsNullOrWhiteSpace(responseWrapper.status.Error_code))
        {
            result.ProviderResponseCode = responseWrapper.status.Error_code;
            result.ProviderResponseMessage = responseWrapper.status.Message;
            result.AddErrorWithCode(responseWrapper.status.Error_code, responseWrapper.status.Message);
            workspan.Log.Information("RapydPaymentProvider  for {orderId}  failed  with {ResponseSummary}", orderId,
                responseWrapper.status.Message);
        }
        else
        {
            var response = responseWrapper.Data;
            if (!string.IsNullOrWhiteSpace(response.error_code))
            {
                result.ProviderResponseCode = response.error_code;
                result.ProviderResponseMessage = response.error_message;
                result.AddErrorWithCode(response.error_code, response.error_message);
                workspan.Log.Information("RapydPaymentProvider  for {orderId}  failed  with {ResponseSummary}", orderId,
                    response.error_message);
            }
            else
            {
                result.ProviderResponseCode = "COMPLETED";
                result.ProviderResponseMessage = "COMPLETED";
            }
        }

        result.RawResult = JsonConvert.SerializeObject(responseWrapper, _jsonSerializeSettings);
        result.ProviderTransactionToken = responseWrapper.Data?.id;
    }


    private Dictionary<string, string> GetMetadata(IMerchantRequest request)
    {
        return new()
        {
            {"orderId", request.OrderId.ToString()},
            {"merchantId", request.Mid.ToString()}
        };
    }


    private string TransformPhoneToE164(string value, string country)
    {
        if (!string.IsNullOrWhiteSpace(value))
        {
            try
            {
                var phoneNumberUtil = PhoneNumberUtil.GetInstance();
                var intPhoneNumber = phoneNumberUtil.Parse(value, country);
                if (!phoneNumberUtil.IsValidNumber(intPhoneNumber)) return null;
                value = phoneNumberUtil.Format(intPhoneNumber, PhoneNumberFormat.E164);
            }
            catch (NumberParseException)
            {
                value = null; // $"+{value}";
            }

            return value;
        }
        else
        {
            return null;
        }
    }

    private async Task<TRes> AuthorizeOrSaleAsync<TRes, TReq>(TReq authRequest,
        bool isSale,
        CancellationToken token = default)
        where TReq : IAuthorizationRequest
        where TRes : IAuthorizationResult, new()
    {
        using var workspan = Workspan.Start<RapydPaymentProvider>().LogEnterAndExit();
        var amount = Formatters.IntToDecimalFixed2Digits(authRequest.Amount);

        var config = await GetCredentialsAsync(authRequest.SupportedGateway.Sandbox,
            authRequest.SupportedGateway.Id, token);

        var card = authRequest.CreditCard;
        var billingAddress = authRequest.BillingAddress;
        var shippingAddress = authRequest.ShippingAddress;
        var threeDs = authRequest.ThreeDS;
        var reference = Guid.NewGuid();
        var phone = TransformPhoneToE164(billingAddress.PhoneNumber, billingAddress.Country);

        TRes retval = new();

        retval.OrderId = authRequest.OrderId;
        retval.Provider = this.CurrentPaymentProvider;
        retval.BinNumber = authRequest.CreditCard.Bin;
        retval.PaymentInstrumentId = authRequest.PaymentInstrumentId;
        retval.CavvCode = authRequest.ThreeDS?.AuthenticationValue;


        try
        {
            var isForeignExchange = config.BalanceCurrency is not null &&
                                    authRequest.CurrencyCode != config.BalanceCurrency.ToString();

            //var hasNetworkToken = authRequest.NetworkTokenInfo is not null;

            var useSchemeTransactionId =
                !authRequest.IsCit && authRequest.NetworkReferenceData?.TransactionId is not null;

            var request = new RapydCreatePaymentRequest()
            {
                Amount = Formatters.IntToDecimalFixed2Digits(authRequest.Amount),
                PaymentMethod = new()
                {
                    fields = new RapydCard()
                    {
                        Name =
                            $"{card.FirstName?.TruncateAndRemoveAlphaNumeric(80)} {card.LastName?.TruncateAndRemoveAlphaNumeric(80)}",
                        // Number = hasNetworkToken
                        //     ? authRequest.NetworkTokenInfo?.Token
                        //     : card.Number,
                        Number = card.Number,
                        CVV = !authRequest.IsCit //|| hasNetworkToken
                            ? null
                            : card.VerificationValue,
                        ExpirationMonth = card.Month.ToString().PadLeft(2, '0'),
                        ExpirationYear = card.Year.ToString(),
                        NumberType =
                            CardNumberType.fpan, //s !hasNetworkToken ? CardNumberType.fpan : CardNumberType.tpan,
                        NetworkReferenceId = useSchemeTransactionId
                            ? authRequest.NetworkReferenceData?.TransactionId
                            : null
                    },
                    type = card.CardBrand == CardBrand.Visa
                        ? RapydPaymentMethods.gb_visa_card
                        : card.CardBrand == CardBrand.MasterCard
                            ? RapydPaymentMethods.gb_mastercard_card
                            : null,
                },
                Address =
                    (authRequest.BillingAddress?.FirstName is null && authRequest.BillingAddress?.LastName is null)
                    || authRequest.BillingAddress?.Address1 is null // todo- transform in validator instead
                        ? null
                        : new()
                        {
                            name = $"{authRequest.BillingAddress?.FirstName} {authRequest.BillingAddress?.LastName}",
                            city = billingAddress.City,
                            Country = billingAddress.Country,

                            line_1 = billingAddress.Address1,
                            line_2 = billingAddress.Address2,
                            state = billingAddress.State,
                            PhoneNumber = phone,
                            zip = billingAddress.Zip
                        },
                Capture = isSale,
                ClientDetails = authRequest.Device is null
                    ? null
                    : new()
                    {
                        ip_address = authRequest.Device?.IpAddress,
                        //language = authRequest.Device?.
                    },
                CompletePaymentUrl = "https://www.Flex.com/payment", //todo-hack!
                ErrorPaymentUrl = "https://www.Flex.com/error",
                Currency = authRequest.CurrencyCode, // config.BalanceCurrency.ToString(),
                Customer = new RapydCustomer()
                {
                    email = billingAddress?.Email,
                    name =
                        $"{card.FirstName?.TruncateAndRemoveAlphaNumeric(80)} {card.LastName?.TruncateAndRemoveAlphaNumeric(80)}",
                    phone_number = phone
                },
                StatementDescripor = authRequest.Descriptor?.Name.TruncateAndRemoveAlphaNumeric(22),
                Expiration = isForeignExchange
                    ? ((DateTimeOffset) DateTime.UtcNow.AddDays(7)).ToUnixTimeSeconds()
                    : null,

                Metadata = new Dictionary<string, string>
                {
                    {"orderId", authRequest.OrderId.ToString()},
                    {"merchantId", authRequest.Mid.ToString()}
                },

                InitiationType = authRequest.IsCit
                    ? InitiationType.customer_present
                    : InitiationType.unscheduled,
                MerchantReferenceId = Guid.NewGuid().ToString(),
                Ewallet = config.WalletId,
                ReceiptEmail = billingAddress?.Email,

                RequestedCurrency = isForeignExchange
                    ? config.BalanceCurrency.ToString() //  authRequest.CurrencyCode
                    : null,

                FixedSide = isForeignExchange
                    ? RapydFixedSide.sell // RapydFixedSide.buy
                    : null,

                //ewallet = "ewallet_b2d10d543aad0c4a0883fd4b9a034c96",

                PaymentMethodOptions = new()
                {
                    _3d_required = false,
                },
            };

            if (threeDs is not null)
            {
                var options = request.PaymentMethodOptions;
                options._3d_version = threeDs.ThreeDsVersion;
                options.cavv = threeDs.AuthenticationValue;
                options.xid = threeDs.Xid;
                options.ds_trans_id = threeDs.DirectoryServerTransactionId;
                options.eci = threeDs.EcommerceIndicator;
                //threeDs.AuthenticationResponseStatus?? todo-check
            }

            if (useSchemeTransactionId)
            {
                workspan.Log.Information("Rapyd payment with scheme transaction id {SchemeTransactionId}",
                    authRequest.NetworkReferenceData?.TransactionId);
            }

            // request.PaymentMethodOptions.tavv =
            //     authRequest.IsCit
            //         ? authRequest.NetworkTokenInfo?.Cryptogram
            //         : null;

            var validationResult = new RapydCreatePaymentRequest.Validator().Validate(request);
            validationResult.ThrowOnErrors(logErrors: true);

            if (request.PaymentMethod?.fields?.NetworkReferenceId is not null)
            {
                workspan.Log.Information("Rapyd payment with network reference id {NetworkReferenceId}",
                    request.PaymentMethod.fields.NetworkReferenceId);
            }

            workspan.Log.LogRedacted(request,
                json => (template: "Rapyd {AuthRequest} in {method} ", new[] {json, "Authorize"}),
                _jsonOptions,
                logRequest =>
                {
                    var creditCard = logRequest?.PaymentMethod?.fields;

                    if (creditCard != null)
                    {
                        creditCard.Number = creditCard.Number?.Substring(0, 8);
                        creditCard.CVV = StringHelpers.MaskAll(creditCard.CVV, '*');
                        logRequest.PaymentMethod.fields = creditCard;
                    }
                });


            var responseWrapper =
                await _sdk.AuthorizeOrSaleAsync(request, config, authRequest.OrderId.ToString(), token);


            workspan.Log.LogRedacted(responseWrapper,
                json => (template: "Rapyd {Response} in {method} ", logparams: new[] {json, "Authorize"}),
                _jsonOptions);

            var response = responseWrapper.Data;


            UpdateResult(responseWrapper, retval, authRequest.OrderId.ToString(), workspan);


            var resultCodeToMap =
                response is null
                    ? responseWrapper.status.Error_code
                    : !String.IsNullOrWhiteSpace(response.error_code)
                        ? response.error_code
                        : "0"; // response.Status.ToString();

            resultCodeToMap = RapydErrorMapper.MapError(resultCodeToMap);
            var internalResponse = InternalResponseMapper.GetMappedResponse(resultCodeToMap);
            retval.InternalResponseCode = internalResponse?.MappedResponseCode;
            retval.InternalResponseMessage = internalResponse?.MappedResponseMessage;
            retval.InternalResponseGroup = internalResponse?.MappedResponseGroup.ToString();

            retval.TransactionId = reference; // to search? check it
            retval.AuthorizationCode = response?.auth_code;
            retval.AvsCode =
                response?.payment_method_data?.avs_check;
            retval.NetworkTokenUsed = false; //hasNetworkToken;
            // workspan.Log.Information("RapydPaymentProvider  hasNetworkToken  is Used={usage}", hasNetworkToken);

            //switch
            // {
            //     AvsResponse.MATCH => "Y",
            //     AvsResponse.NO_MATCH => "N",
            //     AvsResponse.UNKNOWN => "U",
            //     AvsResponse.MATCH_ZIP_ONLY => "P",
            //     AvsResponse.MATCH_ADDRESS_ONLY => "A",
            //     AvsResponse.NOT_PROCESSED => "R",
            // };

            retval.CvvCode
                = response?.payment_method_data?.cvv_check switch
                {
                    null => null,
                    RapydCvvCheck.pass => "M",
                    RapydCvvCheck.fail => "N",
                    RapydCvvCheck.unavailable => "U",
                    RapydCvvCheck.@unchecked => "P"
                };
            retval.Status = response?.status switch
            {
                _ when response is null || !string.IsNullOrEmpty(response?.error_message) => TransactionStatus.Failed,

                RapydPaymentStatus.CLO => TransactionStatus.Completed,
                RapydPaymentStatus.CAN => TransactionStatus.Canceled,
                RapydPaymentStatus.ERR => TransactionStatus.Failed,
                RapydPaymentStatus.EXP => TransactionStatus.Failed,
                RapydPaymentStatus.REV => TransactionStatus.Canceled,
                RapydPaymentStatus.ACT => TransactionStatus.Completed, // not used, WATING FOR 3DS
            };
            retval.SchemeTransactionIdUsed = useSchemeTransactionId;
            retval.SchemeTransactionId = response?.payment_method_data?.network_reference_id;
        }
        catch (Exception e)
        {
            ProcessException(e, retval, workspan);
        }


        return retval;
    }

    private TransactionStatus GetTransactionStatus(RapydPaymentStatus status)
    {
        return status switch
        {
            RapydPaymentStatus.CLO => TransactionStatus.Completed,
            RapydPaymentStatus.CAN => TransactionStatus.Canceled,
            RapydPaymentStatus.ERR => TransactionStatus.Failed,
            RapydPaymentStatus.EXP => TransactionStatus.Failed,
            RapydPaymentStatus.REV => TransactionStatus.Canceled,
            RapydPaymentStatus.ACT => TransactionStatus.Completed, // not used, WATING FOR 3DS
        };
    }


    void ProcessException(Exception e, IResult result, Workspan workspan)
    {
        result.RawResult = JsonConvert.SerializeObject(e);
        result.Status = TransactionStatus.Failed;


        // if (e.Data.Count > 0)
        // {
        //     foreach (var key in e.Data.Keys)
        //     {
        //         result.AddError(e.Data[key].ToString());
        //     }
        // }

        var fve = e as FlexValidationMultipleErrorsException;
        if (fve != null)
        {
            foreach (var error in fve.Errors)
            {
                result.AddError(error.Message);
            }
        }
        else
        {
            result.AddError(e.Message);
            workspan.RecordException(e);
        }
    }

    public override bool SupportCapture => true;
    public override bool SupportPartiallyRefund => true;
    public override bool SupportRefund => true;
    public override bool SupportVoid => true;
    public override bool SupportTokenization => true;
    public override bool RequirePostProcessPayment => true;
    public override bool SupportPayouts => false;
    public override string CurrentPaymentProvider => GatewayTypesConstants.Rapyd;
    public override bool SupportsAch => false;
    public override bool SupportsCreditCards => true;
    public override bool SupportsCreditCardVerification => true;
    public override bool SupportsSubscription => false;
    public override bool SupportsStandaloneCredit => true;
    public override bool SupportsExternalThreeDS => true;
    public override CardBrand[] NetworkTokenCardBrands => new[] {CardBrand.Visa, CardBrand.MasterCard};
}