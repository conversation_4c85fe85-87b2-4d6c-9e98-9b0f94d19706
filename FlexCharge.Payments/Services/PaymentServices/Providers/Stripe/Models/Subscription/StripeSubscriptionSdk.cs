using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Dafny.Aws.EncryptionSdk;
using FlexCharge.Stripe.Models;
using Dwolla.Client.Models.Requests;
using EntityFramework.Exceptions.Common;
using FlexCharge.Common.Telemetry;
using FlexCharge.Payments;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.PaymentInstrumentsServices;
using FlexCharge.Stripe.Models.Invoices;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Stripe;
using Stripe.TestHelpers;
using CustomerService = Stripe.CustomerService;
using Subscription = FlexCharge.Payments.Entities.Subscription;


public class StripeSubscriptionSdk
{
    private readonly SubscriptionService _subscriptionService = new();
    private RequestOptions _requestOptions;

    private bool IsSandbox;

    public StripeSubscriptionSdk()
    {
    }


    public async Task<(bool SubscriptionCancelled, bool CanBeRetried)> CancelSubscription(string providerSubscriptionId,
        RequestOptions reqOptions,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<StripeSubscriptionSdk>()
            .Baggage("ProviderSubscriptionId", providerSubscriptionId)
            .LogEnterAndExit();

        bool subscriptionCancelled;
        bool canBeRetried;

        try
        {
            var subscriptionService = new SubscriptionService();

            #region Mark Subscription as cancelled by FF

            // Note: metadata can be updated only if subscription is active
            try
            {
                var updateOptions = new SubscriptionUpdateOptions
                {
                    Metadata = new Dictionary<string, string>
                    {
                        {"flx_cancelled_at", DateTime.UtcNow.ToString("u")}
                    }
                };

                await subscriptionService.UpdateAsync(providerSubscriptionId, updateOptions, reqOptions, token);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
            }

            #endregion

            // var sub = await _context.Subscriptions.SingleAsync(s => s.OrderId == req.OrderId, token);
            var subscription = await subscriptionService.CancelAsync(providerSubscriptionId, null,
                reqOptions,
                token);


            subscriptionCancelled = true;
            canBeRetried = false;
        }
        catch (StripeException e)
        {
            workspan.RecordException(e);

            subscriptionCancelled = false;
            canBeRetried = e.StripeError?.Code != "resource_missing";
        }
        catch (Exception e)
        {
            workspan.RecordException(e);

            subscriptionCancelled = false;
            canBeRetried = true;
        }

        return (subscriptionCancelled, canBeRetried);
    }

// public async Task ProcessEvent(string eventType, string rawObject, RequestOptions requestOptions,
//     CancellationToken token)
// {
//     using var workspan = Workspan.Start<StripeSubscriptionSdk>().LogEnterAndExit()
//         .Tag(nameof(eventType), eventType);
//     
//     
//     workspan.Log.Information("Stripe Subscription {Event} received", eventType);
//     try
//     {
//         Charge charge = null;
//         Stripe.SubscriptionSchedule sub = null;
//
//         switch (eventType)
//         {
//             case Events.ChargeCaptured:
//                 charge = JsonSerializer.Deserialize<Charge>(rawObject);
//                 await ChargeCaptured(charge, requestOptions, workspan, token);
//                 workspan.Log.Information("Charge Captured");
//                 break;
//             case Events.ChargeExpired:
//                 charge = JsonSerializer.Deserialize<Charge>(rawObject);
//                 await ChargeFailed(charge, requestOptions, workspan, token);
//                 break;
//             case Events.ChargeFailed:
//                 charge = JsonSerializer.Deserialize<Charge>(rawObject);
//                 await ChargeFailed(charge, requestOptions, workspan, token);
//                 break;
//             case Events.ChargePending:
//                 //nothing
//                 break;
//             case Events.ChargeSucceeded:
//                 charge = JsonSerializer.Deserialize<Charge>(rawObject);
//                 await ChargeCaptured(charge, requestOptions, workspan, token);
//                 break;
//             case Events.InvoicePaid:
//                 break;
//             case Events.InvoiceUpdated:
//                 break;
//             // case Events.InvoiceFinalizationFailed:
//             //     sub = data as Stripe.SubscriptionSchedule;
//             //     SubscriptionUpdate(sub, token);
//             //     break;
//             case Events.InvoiceMarkedUncollectible:
//                 sub = JsonSerializer.Deserialize<SubscriptionSchedule>(rawObject);
//                 await SubscriptionUpdate(sub, workspan, token);
//                 break;
//             case Events.SubscriptionScheduleCompleted:
//                 sub = JsonSerializer.Deserialize<SubscriptionSchedule>(rawObject);
//                 await SubscriptionUpdate(sub, workspan, token);
//                 break;
//             case Events.SubscriptionScheduleUpdated:
//                 sub = JsonSerializer.Deserialize<SubscriptionSchedule>(rawObject);
//                 await SubscriptionUpdate(sub, workspan, token);
//                 break;
//         }
//     }
//     catch (Exception e)
//     {
//         workspan.Log.Error(e, "Failed to process stripe  subscription  event {Event}", eventType);
//         throw;
//     }
//
//     workspan.Log.Information("Processed stripe subscription {Event}", eventType);
// }

    private Transaction Map(Charge charge, Subscription subscription)
    {
        Transaction result = new()
        {
            Amount = (int) charge.AmountCaptured,
            AuthorizationAmount = (int) charge.AmountCaptured,
            Currency = charge.Currency,
            ProviderName = subscription.ProviderName,
            ProviderId = subscription.ProviderId,
            Meta = JsonSerializer.SerializeToDocument(charge),
            Type = nameof(TransactionType.Debit),
            IsExternal = true,
            OrderId = subscription.OrderId,
            SubscriptionId = subscription.Id,
            ProviderTransactionToken = charge.Id,
            PaymentMethodId = subscription.PaymentInstrumentId,
        };
        return result;
    }

// private async Task ChargeCaptured(Charge charge, RequestOptions requestOptions, Workspan workspan,
//     CancellationToken token)
// {
//     try
//     {
//         var invoice = await new InvoiceService().GetAsync(charge.InvoiceId, requestOptions: requestOptions);
//         if (invoice?.SubscriptionId is null) return;
//         var sub = await _context.Subscriptions.SingleOrDefaultAsync(s =>
//             s.ProviderSubsId == invoice.SubscriptionId, token);
//         sub.Success = true;
//         var transaction = Map(charge, sub);
//         _context.Transactions.Add(transaction);
//         transaction.Status = TransactionStatus.Completed;
//         await _context.SaveChangesAsync(token);
//         workspan.Log.Information("ChargeCaptured  for {OrderId}", sub.OrderId);
//     }
//     catch (Exception e)
//     {
//         workspan.RecordException(e);
//     }
// }

// private async Task ChargeFailed(Charge charge, RequestOptions requestOptions, Workspan workspan,
//     CancellationToken token)
// {
//     try
//     {
//         var invoice = await new InvoiceService().GetAsync(charge.InvoiceId, requestOptions: requestOptions);
//         if (invoice.SubscriptionId is null) return;
//         var sub = await _context.Subscriptions.SingleOrDefaultAsync(s =>
//             s.ProviderSubsId == invoice.SubscriptionId);
//         sub.Success = false;
//         var transaction = Map(charge, sub);
//         _context.Transactions.Add(transaction);
//         transaction.Status = TransactionStatus.Failed;
//         await _context.SaveChangesAsync(token);
//         workspan.Log.Information("Charge Failed  for {OrderId}", sub.OrderId);
//     }
//     catch (Exception e)
//     {
//         workspan.RecordException(e);
//     }
// }

// private async Task SubscriptionUpdate(SubscriptionSchedule schedule, Workspan workspan, CancellationToken token)
// {
//     try
//     {
//         var sub = await _context.Subscriptions.SingleOrDefaultAsync(
//             s => s.ProviderSubsId == schedule.SubscriptionId);
//         var status = schedule.Status;
//         if (status == "completed")
//         {
//             sub.Success = true;
//         }
//         else if (status == "not_started")
//         {
//             sub.IsActive = null;
//         }
//         else if (status == "active")
//         {
//             sub.IsActive = true;
//         }
//         else if (status == "canceled")
//         {
//             sub.IsActive = false;
//             sub.Success = false;
//         }
//         else if (status == "released")
//         {
//             sub.IsActive = false;
//         }
//
//         await _context.SaveChangesAsync(token);
//         workspan.Log.Information("Subscription {Status} ofr {OrderId} updated", status, sub.OrderId);
//     }
//     catch (Exception e)
//     {
//         workspan.RecordException(e);
//     }
// }


// public async Task UpdatePaymentMethodAsync(UpdatePaymentMethodRequest req, RequestOptions requestOptions,
//     CancellationToken token)
// {
//     var sub = await _context.Subscriptions.SingleAsync(s => s.Id == req.SubscriptionId, token);
//
//     var card = req.Card;
//     PaymentMethodCreateOptions paymentMethodCreateOptions = new()
//     {
//         Type = "card",
//         Card = new()
//         {
//             Number = card.Number.Replace("-", string.Empty),
//             Token = card.Token,
//             Cvc = card.Cvc,
//             ExpMonth = card.ExpMonth,
//             ExpYear = card.ExpYear < 2000 ? 2000 + card.ExpYear : card.ExpYear
//         },
//         BillingDetails = req.BillingDetails is var billingDetails && billingDetails is null
//             ? null
//             : new()
//             {
//                 Name = billingDetails.Name,
//                 Email = billingDetails.Email,
//                 Address = billingDetails.Address is var address && address is null
//                     ? null
//                     : new()
//                     {
//                         State = address.State,
//                         City = address.City,
//                         Country = address.Country,
//                         Line1 = address.Line1,
//                         Line2 = address.Line2,
//                         PostalCode = address.PostalCode
//                     }
//             }
//     };
//     var paymentMethod = await new PaymentMethodService().CreateAsync(paymentMethodCreateOptions,
//         req.RequestOptions, token);
//     var options = new SubscriptionUpdateOptions
//     {
//         DefaultPaymentMethod = paymentMethod.Id
//     };
//     var service = new SubscriptionService();
//     var subscription = await _subscriptionService.UpdateAsync(sub.ProviderSubsId, options,
//         req.RequestOptions, token);
//     // _context.OneOffSubscriptions.ExecuteUpdate  >> GetByOrderIdAsync(SearchByOrderIdRequest req)
// }

// public async Task UpdateStatusAsync(UpdateStatusRequest req, CancellationToken token)
// {
//     var sub = await _context.Subscriptions.SingleAsync(s => s.Id == req.SubscriptionId, token);
//     (bool active, bool success) state = MapStatus(req.NewStatus);
//     sub.IsActive = state.active;
//     sub.Success = state.success;
//     await _context.SaveChangesAsync(token);
// }

    public async Task<Stripe.Subscription> CreateSubscriptionAsync(CreateStripeSubscriptionRequest request,
        CancellationToken token,
        string testClock = null)
    {
        using var workspan = Workspan.Start<StripeSubscriptionSdk>()
            .Baggage("SubscriptionId", request.InternalSubscriptionId);

#if DEBUG

        if (Environment.GetEnvironmentVariable("STRIPE_CLOCK") is var cid && cid is not null)
        {
            testClock = cid;
        }
        else
        {
            var service = new TestClockService();


            var options = new TestClockCreateOptions
            {
                FrozenTime = DateTime.UtcNow, //  DateTimeOffset. FromUnixTimeSeconds(1635750000).UtcDateTime,
                Name = "Clock1",
            };
            var clock = await service.CreateAsync(options, request.RequestOptions);
            testClock = clock.Id;
        }

#endif


        Stripe.Subscription subscription = null;
        //req.RequestOptions.IdempotencyKey = req.OrderId.ToString();
        var card = request.Card;
        if (card is null) throw new Exception("Card for subscription cannot be null");

        var billingDetails = request.BillingDetails;

        async Task<PaymentMethod> CreatePaymentMethodAsync()
        {
            PaymentMethodCreateOptions paymentMethodCreateOptions = new()
            {
                Type = "card",
                Card = new()
                {
                    Number = card.Number.Replace("-", string.Empty),
                    Token = card.Token,
                    Cvc = card.Cvc,
                    ExpMonth = card.ExpMonth,
                    ExpYear = card.ExpYear < 2000 ? 2000 + card.ExpYear : card.ExpYear
                },
                BillingDetails = billingDetails is null
                    ? null
                    : new()
                    {
                        Name = billingDetails.Name,
                        Email = billingDetails.Email,

                        Address = billingDetails.Address is var address && address is null
                            ? null
                            : new()
                            {
                                State = address.State,
                                City = address.City,
                                Country = address.Country,
                                Line1 = address.Line1,
                                Line2 = address.Line2,
                                PostalCode = address.PostalCode
                            },
                        Phone = billingDetails.Phone,
                    },
            };
            var paymentMethod = await new PaymentMethodService().CreateAsync(paymentMethodCreateOptions,
                request.RequestOptions, token);
            return paymentMethod;
        }

        var paymentMethod = await CreatePaymentMethodAsync();

        async Task<Customer> CreateCustomerAsync()
        {
            var customerOptions = new CustomerCreateOptions
            {
                Email = request.Email,
                // InvoiceSettings = new CustomerInvoiceSettingsOptions
                // {
                //     DefaultPaymentMethod = paymentMethod.Id,
                //     Footer = req.Description // not sure if it is correct
                // },
                PaymentMethod = paymentMethod.Id,
                TestClock = testClock,
                Shipping = request.ShippingAddress is null
                    ? null
                    : new()
                    {
                        Name = request.ShippingAddress.FirstName + " " + request.ShippingAddress.LastName,
                        Address = new()
                        {
                            City = request.ShippingAddress.City,
                            Country = request.ShippingAddress.Country,
                            Line1 = request.ShippingAddress.Address1,
                            Line2 = request.ShippingAddress.Address2,
                            PostalCode = request.ShippingAddress.Zip,
                            State = request.ShippingAddress.State
                        },
                        //Name = req.ShippingAddress.n + " " + req.ShippingAddress.LastName,
                        Phone = request.ShippingAddress.PhoneNumber,
                    },
                Name = request.BillingDetails?.Name,
                Phone = request.BillingDetails?.Phone,
                Address = request.BillingDetails?.Address is var address && address is null
                    ? null
                    : new()
                    {
                        State = address.State,
                        City = address.City,
                        Country = address.Country,
                        Line1 = address.Line1,
                        Line2 = address.Line2,
                        PostalCode = address.PostalCode
                    }
            };

            var customerService = new CustomerService();
            var customer = await customerService.CreateAsync(customerOptions,
                request.RequestOptions, token);
            return customer;
        }

        var customer = await CreateCustomerAsync();

        async Task<Product> CreateProductAsync()
        {
            ProductCreateOptions productCreateOptions = new()
            {
                Name = "Goods and Services",
                StatementDescriptor = request.StatementDescriptor
            };
            var productService = new ProductService();
            var product = await productService.CreateAsync(productCreateOptions, request.RequestOptions, token);
            return product;
        }

        var product = await CreateProductAsync();

        async Task<Price> CreatePriceAsync()
        {
            var priceOptions = new PriceCreateOptions()
            {
                UnitAmount = request.Amount,
                Currency = request.Currency,
                Active = true,
                Product = product.Id,

                Recurring = new()
                {
                    Interval = request.IntervalUnit.ToString().ToLower(),
                    IntervalCount = request.IntervalCount,
                }
            };
            var priceservice = new PriceService();
            var price = await priceservice.CreateAsync(priceOptions, request.RequestOptions, token);

            return price;
        }

        var price = await CreatePriceAsync();

        async Task<Stripe.Subscription> CreateSubAsync()
        {
            var subscriptionOptions = new SubscriptionCreateOptions
            {
                Customer = customer.Id,
                Currency = request.Currency,
                CollectionMethod = "charge_automatically",
                CancelAt = request.CancelAt,
                BillingCycleAnchor = request.StartDate,
                OffSession = !request.IsCit,
                ProrationBehavior = "none",
                Description = request.Description, // todo? add subscriptionId? orderId?

                //CancelAtPeriodEnd = true, // Somehow it's not working correctly

                // TrialEnd = req.StartDate,-cannot do it if not prorated
                Metadata = new Dictionary<string, string>
                {
                    {"OrderId", request.OrderId.ToString()},
                    {"SubscriptionId", request.InternalSubscriptionId.ToString()},
                    {"Mid", request.Mid.ToString()}
                },
                Items = new List<SubscriptionItemOptions>
                {
                    new SubscriptionItemOptions
                    {
                        Price = price.Id,
                    }
                },
            };
            subscriptionOptions.AddExpand("latest_invoice.payment_intent");
            subscriptionOptions.AddExpand("pending_setup_intent");
            var subscription =
                await _subscriptionService.CreateAsync(subscriptionOptions, request.RequestOptions, token);
            return subscription;
        }

        try
        {
            subscription = await CreateSubAsync();

            // var sub = new Subscription();
            // (bool active, bool success) state = MapStatus(subscription.Status);
            // sub.IsActive = state.active;
            // sub.LastPaymentSuccess = state.success;
            // sub.InitialStatus = subscription.Status;
            // sub.Meta = JsonSerializer.SerializeToDocument(subscription);
            // sub.ProviderSubsId = subscription.Id;
            // sub.CancelledAt = subscription.CanceledAt;
            // sub.CancelReason = subscription.CancellationDetails?.Reason;
        }
        catch (StripeException e)
        {
            workspan.Log.Error("Failed to create subscription {ErrorCode} {ErrorMessage}", e.StripeError?.Code,
                e.StripeError?.Message);
            throw;
        }

        return subscription;
    }

    public async Task<Stripe.Subscription> GetSubscriptionByIdAsync(string subscriptionId,
        RequestOptions reqOptions,
        CancellationToken token)
    {
        Stripe.Subscription result = null;
        SubscriptionSearchOptions searchoptions = new()
        {
            Query = $"metadata['SubscriptionId']:'{subscriptionId}'",
            Limit = 100 // suppose we have less subscriptios, otherwise should use HasMore in response
        };
        var response = await _subscriptionService.SearchAsync(searchoptions, reqOptions, token);
        result = response.Data?.FirstOrDefault();
        return result;
    }


    public async Task<List<Stripe.Invoice>> GetInvoicesBySubscriptionIdAsync(string stripeSubscriptionId,
        RequestOptions reqOptions,
        CancellationToken token)
    {
        List<Stripe.Invoice> result = null;
        InvoiceSearchOptions searchoptions = new()
        {
            Query = $"subscription:'{stripeSubscriptionId}'",
            Limit = 100 // suppose we have less subscriptios, otherwise should use HasMore in response
        };
        var response = await new InvoiceService().SearchAsync(searchoptions, reqOptions, token);
        result = response.Data;
        return result;
    }

    public async Task<StripeSubscriptionAndLastInvoiceStatus> GetSubscriptionStateById(
        Guid subscriptionId, RequestOptions reqOptions, Workspan workspan,
        CancellationToken token)
    {
        StripeSubscriptionAndLastInvoiceStatus result = null;
        try
        {
            var subscription = await GetSubscriptionByIdAsync(subscriptionId.ToString(), reqOptions, token);
            if (subscription is not null)
            {
                var invoices = await GetInvoicesBySubscriptionIdAsync(subscription.Id, reqOptions, token);

                result = new()
                {
                    SubscriptionStatus = subscription.Status,
                    LastInvoiceStatus = invoices.MaxBy(i => i.Created)?.Status
                };
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
        }

        return result;


        // if (sub.ProviderSubsId is null || (sub.IsActive == true && sub.LastPaymentSuccess is null))
        // {
        //  
        //   
        //     if (provSub.Status == "incomplete_expired")
        //     {
        //         sub.LastPaymentSuccess = false;
        //     }
        //     else
        //     {
        //       
        //     }
        // }
    }


    public async Task<Stripe.Invoice> CreateInvoiceAsync(CreateStripeInvoiceRequest req,
        Workspan workspan,
        CancellationToken token,
        string testClock = null)
    {
        //req.RequestOptions.IdempotencyKey = req.OrderId.ToString();
        var card = req.Card;
        if (card is null) throw new Exception("Card for invoice cannot be null");

        var billingDetails = req.BillingDetails;

        async Task<PaymentMethod> CreatePaymentMethodAsync()
        {
            PaymentMethodCreateOptions paymentMethodCreateOptions = new()
            {
                Type = "card",
                Card = new()
                {
                    Number = card.Number.Replace("-", string.Empty),
                    Token = card.Token,
                    Cvc = card.Cvc,
                    ExpMonth = card.ExpMonth,
                    ExpYear = card.ExpYear < 2000 ? 2000 + card.ExpYear : card.ExpYear
                },
                BillingDetails = billingDetails is null
                    ? null
                    : new()
                    {
                        Name = billingDetails.Name,
                        Email = billingDetails.Email,
                        Address = billingDetails.Address is var address && address is null
                            ? null
                            : new()
                            {
                                State = address.State,
                                City = address.City,
                                Country = address.Country,
                                Line1 = address.Line1,
                                Line2 = address.Line2,
                                PostalCode = address.PostalCode
                            }
                    },
            };
            var paymentMethod = await new PaymentMethodService().CreateAsync(paymentMethodCreateOptions,
                req.RequestOptions, token);
            return paymentMethod;
        }

        var paymentMethod = await CreatePaymentMethodAsync();

        async Task<Customer> CreateCustomerAsync()
        {
            var customerOptions = new CustomerCreateOptions
            {
                Email = req.Email,
                InvoiceSettings = new CustomerInvoiceSettingsOptions
                {
                    DefaultPaymentMethod = paymentMethod.Id,
                },
                PaymentMethod = paymentMethod.Id,
                TestClock = testClock
            };

            var customerService = new CustomerService();
            var customer = await customerService.CreateAsync(customerOptions,
                req.RequestOptions, token);
            return customer;
        }

        var customer = await CreateCustomerAsync();

        async Task<Product> CreateProductAsync()
        {
            ProductCreateOptions productCreateOptions = new()
            {
                Name = "Goods and Services"
            };
            var productService = new ProductService();
            var product = await productService.CreateAsync(productCreateOptions, req.RequestOptions, token);
            return product;
        }

        var product = await CreateProductAsync();

        async Task<Price> CreatePriceAsync()
        {
            var priceOptions = new PriceCreateOptions()
            {
                UnitAmount = req.Amount,
                Currency = req.Currency,
                Active = true,
                Product = product.Id,
                Recurring = new()
                {
                    Interval = req.IntervalUnit.ToString().ToLower(),
                    IntervalCount = req.IntervalCount,
                }
            };
            var priceservice = new PriceService();
            var price = await priceservice.CreateAsync(priceOptions, req.RequestOptions, token);
            return price;
        }

        var price = await CreatePriceAsync();


        InvoiceCreateOptions ioptions = new()
        {
            Customer = customer.Id,
            Currency = req.Currency,
            CollectionMethod = "charge_automatically",
            AutoAdvance = true,
            DueDate = DateTime.UtcNow,

            ShippingDetails = new()
            {
                //todo
            },
            StatementDescriptor = "", //todo
            Metadata = new()
            {
                {"SubscriptionId", req.SubscriptionId.ToString()},
                {"OrderId", req.OrderId.ToString()},
                {"Mid", req.Mid.ToString()},
            }, //todo
        };
        var iservice = new InvoiceService();
        var invoice = await iservice.CreateAsync(ioptions, req.RequestOptions, token);

        var invoiceItemOptions = new InvoiceItemCreateOptions
        {
            Customer = customer.Id,
            Price = price.Id,
            Invoice = invoice.Id,
            PriceData = new InvoiceItemPriceDataOptions()
            {
                Product = product.Id,
                Currency = req.Currency,
                UnitAmount = req.Amount
            }
        };
        var invoiceItemService = new InvoiceItemService();
        var invItem = await invoiceItemService.CreateAsync(invoiceItemOptions);

        InvoiceSendOptions invoiceSendOptions = new()
        {
        };
        await iservice.SendInvoiceAsync(invItem.Id, invoiceSendOptions, req.RequestOptions, token);
        return invoice;
    }
}