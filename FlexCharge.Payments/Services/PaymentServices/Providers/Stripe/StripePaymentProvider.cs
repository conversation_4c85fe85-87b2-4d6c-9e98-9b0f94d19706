using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Stripe.Models;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Response;
using FlexCharge.Common.Shared.Adapters;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Telemetry;
using FlexCharge.Payments.Entities;
using FlexCharge.Contracts.CardBrands;
using FlexCharge.Payments.DebugHelpers;
using FlexCharge.Payments.DTO.Webhooks;
using FlexCharge.Payments.Services.PaymentInstrumentsServices;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;
using FlexCharge.Payments.Services.PaymentServices.Models;
using FlexCharge.Payments.Services.PaymentServices.Models.ExternalTokenPayments;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.Authentication;
using FlexCharge.Payments.Services.PaymentServices.Providers.Stripe;
using MassTransit;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Stripe;
using JsonSerializer = System.Text.Json.JsonSerializer;
using Subscription = Stripe.Subscription;

namespace FlexCharge.Payments.Services.PaymentServices;

public partial class StripePaymentProvider : PaymentProviderBase
{
    private readonly PostgreSQLDbContext _context;
    private readonly IPaymentInstrumentsService _paymentInstrumentsService;
    private RequestOptions _requestOptions;
    private StripeSubscriptionSdk _stripeSubscriptionSdk;


    private readonly IPublishEndpoint _publishEndpoint;
    private readonly IActivityService _activityService;


    public StripePaymentProvider(IPaymentInstrumentsService paymentInstrumentsService,
        PostgreSQLDbContext context, StripeSubscriptionSdk stripeSubscriptionSdk,
        IPublishEndpoint publishEndpoint, IActivityService activityService)
    {
        _paymentInstrumentsService = paymentInstrumentsService;
        _context = context;
        _stripeSubscriptionSdk = stripeSubscriptionSdk;
        _publishEndpoint = publishEndpoint;
        _activityService = activityService;
    }


    public async Task InitiateProviderAsync(bool? isSandbox, Guid supportedGatewayId, CancellationToken token = default)
    {
        if (_requestOptions != null)
            return;

        SupportedGateway gateway;
        if (isSandbox != null)
        {
            gateway = await _context.SupportedGateways
                .Where(x => x.Id == supportedGatewayId && x.Sandbox == isSandbox)
                .FirstOrDefaultAsync(token);
        }
        else
        {
            gateway = await _context.SupportedGateways
                .Where(x => x.Id == supportedGatewayId)
                .FirstOrDefaultAsync(token);
        }

        if (gateway == null)
            throw new Exception("Stripe gateway not found");

        this.IsSandbox = gateway.Sandbox;

        _requestOptions = new() {ApiKey = gateway.SecretKey}; //, IdempotencyKey = Guid.NewGuid().ToString() };
    }

    #region Commented

    // public async Task InitiateProviderAsync(CancellationToken token)
    // {
    //     if (_requestOptions != null)
    //         return;
    //
    //     this.IsSandbox = !EnvironmentHelper.IsInProduction;
    //
    //     var gateway = await _context.SupportedGateways
    //         .Where(x => x.NameIdentifier == this.CurrentPaymentProvider && x.Sandbox == this.IsSandbox)
    //         .FirstOrDefaultAsync(token);
    //
    //     if (gateway == null)
    //         throw new Exception("Stripe gateway not found");
    //
    //     _requestOptions = new() {ApiKey = gateway.SecretKey}; //, IdempotencyKey = Guid.NewGuid().ToString() };
    // }

    #endregion

    public override async Task<AuthResult> AuthorizeAsync(AuthorizationRequest request,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<StripePaymentProvider>();
        workspan.Log.Information("ENTERED: StripePaymentProvider => AuthorizeAsync");

        var result = new AuthResult();
        var tdsVersions = new List<string>() {"2.2.0", "2.1.0", "1.0.2"};
        var ecis = new List<string>() {"01", "02", "05", "06", "07"};
        var has3ds = request.ThreeDS is not null && request.Use3DS;
        // && tdsVersions.Contains(request.ThreeDS.ThreeDsVersion)
        // && ecis.Contains(request.ThreeDS.EcommerceIndicator);

        PaymentIntent paymentIntent = null;

        try
        {
            await InitiateProviderAsync(request.SupportedGateway.Sandbox, request.SupportedGateway.Id, token);

            var paymentMethod = await new PaymentMethodService().CreateAsync(new PaymentMethodCreateOptions
            {
                Type = "card",
                Card = new PaymentMethodCardOptions
                {
                    Number = request.CreditCard.Number,
                    ExpMonth = request.CreditCard.Month,
                    ExpYear = request.CreditCard.Year,
                    Cvc = request.CreditCard.VerificationValue
                },
                BillingDetails = new PaymentMethodBillingDetailsOptions
                {
                    Address = new AddressOptions()
                    {
                        City = request.BillingAddress.City,
                        Country = request.BillingAddress.Country,
                        Line1 = request.BillingAddress.Address1,
                        Line2 = request.BillingAddress.Address2,
                        PostalCode = request.BillingAddress.Zip,
                        State = request.BillingAddress.State
                    },
                    Name = request.BillingAddress?.FirstName + " " + request.BillingAddress?.LastName,
                    Email = request.BillingAddress?.Email,
                    Phone = request.BillingAddress?.PhoneNumber,
                },
            }, _requestOptions, cancellationToken: token);
            workspan.Log.Information("Stripe payment method created");

            string customerId = null;
            if (request.UseCustomer)
            {
                var cs = await new CustomerService().CreateAsync(new CustomerCreateOptions()
                {
                    Email = request.BillingAddress?.Email,
                    Name = $"{request.BillingAddress?.FirstName} {request.BillingAddress?.LastName}",
                    Phone = request.BillingAddress?.PhoneNumber,

                    Address = new AddressOptions
                    {
                        Line1 = request.BillingAddress?.Address1,
                        Line2 = request.BillingAddress?.Address2,
                        City = request.BillingAddress?.City,
                        State = request.BillingAddress?.State,
                        PostalCode = request.BillingAddress?.Zip,
                        Country = request.BillingAddress?.Country
                    },
                    PaymentMethod = paymentMethod.Id
                }, _requestOptions, cancellationToken: token);
                customerId = cs.Id;
                workspan.Log.Information("Stripe Customer created");
            }

            if (paymentMethod == null)
            {
                throw new FlexValidationException(ValidationCodes.InvalidPaymentMethod,
                    ValidationCodes.GetMessage(ValidationCodes.InvalidPaymentMethod));
            }

            var paymentIntentService = new PaymentIntentService();
            var intentOptions = new PaymentIntentCreateOptions()
            {
                Confirm = true,
                CaptureMethod = "manual",
                Customer = customerId,
                //  OffSession = !request.IsCit,

                //PaymentMethodTypes = new(){"card"},-not use it
                Metadata = new Dictionary<string, string>
                {
                    {"order_id", request.OrderId.ToString()},
                    {"correlation_id", request.OrderId.ToString()},
                    {"payer_id", request.PayerId.ToString()},
                    {"connect_agent", "FlexCharge"},
                },
                PaymentMethod = paymentMethod.Id,
                Amount = request.Amount,
                Currency = request.CurrencyCode,
                StatementDescriptor = request.Descriptor?.Name is null
                    ? null
                    : request.Descriptor.Name.Substring(0,
                        Math.Min(request.Descriptor.Name.Length, 22)),

                Shipping = request.ShippingAddress is var shipping
                           && shipping is null
                    ? null
                    : new()
                    {
                        Address = new AddressOptions()
                        {
                            City = shipping.City,
                            Country = shipping.Country,
                            Line1 = shipping.Address1,
                            Line2 = shipping.Address2,
                            PostalCode = shipping.Zip,
                            State = shipping.State
                        },
                        Name = shipping.FirstName + " " + shipping.LastName,
                        Phone = shipping.PhoneNumber,
                    },

                // AutomaticPaymentMethods = new() -not use it
                // {
                //     Enabled = false,
                //   //  AllowRedirects = "never"
                // },
                ReturnUrl = $"https://core.flex-charge.com/transaction/{Guid.NewGuid().ToString("N")}/redirect",
                UseStripeSdk = false,


                //ErrorOnRequiresAction =true, //not use it
            };

            if (has3ds)
            {
                intentOptions.PaymentMethodOptions =
                    new()
                    {
                        Card = new()
                        {
                            // RequestThreeDSecure = "any",not use it
                            ThreeDSecure =
                                new()
                                {
                                    Cryptogram = request.ThreeDS.AuthenticationValue, //"xgQYYgZVAAAAAAAAAAAAAAAAAAAA"
                                    ElectronicCommerceIndicator = request.ThreeDS.EcommerceIndicator, //o2
                                    Version = request.ThreeDS.ThreeDsVersion, //"2.2.0",//,
                                    TransactionId = request.ThreeDS.ThreeDsVersion.StartsWith("1")
                                        ? request.ThreeDS.Xid
                                        : request.ThreeDS
                                            .DirectoryServerTransactionId, //"1c770353-55f5-4292-ba5f-4c7cfbec8593"//
                                }
                        }
                    };

                // intentOptions.ErrorOnRequiresAction = true; -not use this
                workspan.Log.Information("stripe authorization uses external 3ds");
            }
            // else
            // {
            //     intentOptions.PaymentMethodOptions =
            //         new()
            //         {
            //             Card = new()
            //             {
            //                 RequestThreeDSecure = "any",
            //             }
            //         };
            //    
            //     workspan.Log.Information("stripe authorization uses internal 3ds tries RequestThreeDSecure = any");
            // }

            intentOptions.AddExpand("latest_charge");

            paymentIntent =
                await paymentIntentService.CreateAsync(intentOptions, _requestOptions, cancellationToken: token);

            workspan.Log.Information("Stripe paymentIntent created");
            result.RawResult = JsonConvert.SerializeObject(paymentIntent);
            result.Provider = this.CurrentPaymentProvider;

            //if not succeeded then add error
            if (paymentIntent.Status == "succeeded" || paymentIntent.Status == "requires_capture")
            {
                result.ProviderResponseCode = paymentIntent.LatestCharge?.Status;
                result.ProviderResponseMessage = paymentIntent.LatestCharge?.Description;
                result.ProviderTransactionToken = paymentIntent.Id;

                result.CvvCode =
                    paymentIntent.LatestCharge?.PaymentMethodDetails?.Card?.Checks?.CvcCheck == "pass"
                        ? "M"
                        : "N";

                result.AvsCode =
                    paymentIntent.LatestCharge?.PaymentMethodDetails?.Card?.Checks is
                        {AddressLine1Check: "pass", AddressPostalCodeCheck: "pass"}
                        ? "M"
                        : "N";
            }
            else
            {
                result.AddErrorWithCode(paymentIntent.Status, paymentIntent.Status);
                result.ProviderResponseCode = paymentIntent.Status;
                result.ProviderResponseMessage = paymentIntent.Description;
            }

            var internalResponse = await StripeResponseMapper.GetMappedResponseAsync(result.ProviderResponseCode, null);
            result.InternalResponseCode = internalResponse.MappedResponseCode;
            result.InternalResponseMessage = internalResponse.MappedResponseMessage;
            result.InternalResponseMessage = internalResponse.MappedDeclineTypeResponse.ToString();
        }
        catch (StripeException e)
        {
            var chargeId = e.StripeError?.PaymentIntent?.LatestChargeId;
            var service = new ChargeService();
            result.ProviderResponseCode = e.StripeError?.Code ?? e.Message;
            result.ProviderResponseMessage = e.StripeError?.Message ?? e.Message;
            if (!string.IsNullOrWhiteSpace(chargeId))
            {
                try
                {
                    var charge = await service.GetAsync(chargeId,
                        new ChargeGetOptions()
                        {
                            Expand = new List<string>() {"outcome"}
                        },
                        _requestOptions, token);

                    if (charge?.Outcome?.Type == "blocked")
                    {
                        result.ProviderResponseCode = "do_not_try_again";
                        result.ProviderResponseMessage = result.ProviderResponseMessage + " - " +
                            charge.Outcome?.SellerMessage ?? "";
                    }
                }
                catch (Exception ex)
                {
                    workspan.Log.Information("Charge GetAsync error occurred: {EMessage}", ex.Message);
                }
            }

            result.RawResult = JsonConvert.SerializeObject(e);
            result.Provider = this.CurrentPaymentProvider;
            result.ProviderTransactionToken = e.StripeError?.Charge;

            var internalResponse = await StripeResponseMapper.GetMappedResponseAsync(result.ProviderResponseCode, null);
            result.InternalResponseCode = internalResponse.MappedResponseCode;
            result.InternalResponseMessage = internalResponse.MappedResponseMessage;
            result.InternalResponseMessage = internalResponse.MappedDeclineTypeResponse.ToString();


            switch (e.StripeError?.Type)
            {
                case "card_error":
                    workspan.Log.Information("A payment error occurred: {EMessage}", e.Message);
                    break;
                case "invalid_request_error":
                    workspan.Log.Error("An invalid request occurred");
                    break;
                default:
                    workspan.Log.Error("Another problem occurred, maybe unrelated to Stripe");
                    break;
            }

            if (e.StripeError is not null)
                result.AddErrorWithCode(e.StripeError.Code, e.StripeError.Message);
            if (e.StripeError?.Type != "card_error")
                workspan.RecordException(e);
        }
        catch (Exception e)
        {
            result.RawResult = JsonConvert.SerializeObject(e);
            workspan.Log.Error(e, "An error occurred while processing the credit card payment");
            result.AddError(e.Message);
        }

        return result;
    }

    public override async Task<CapturePaymentResult> CaptureAsync(CapturePaymentRequest request,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<StripePaymentProvider>();
        workspan.Log.Information("ENTERED: StripePaymentProvider => Capture");

        var result = new CapturePaymentResult();
        try
        {
            await InitiateProviderAsync(request.SupportedGateway.Sandbox, request.SupportedGateway.Id, token);

            var piIntentService = new PaymentIntentService();
            var captureResponse = await piIntentService.CaptureAsync(
                request.TransactionToken, null, _requestOptions, cancellationToken: token);

            var intentSuccessful = captureResponse.Status is "succeeded";

            if (!intentSuccessful)
                result.AddErrorWithCode(captureResponse.Status, captureResponse.Description);

            result.RawResult = JsonConvert.SerializeObject(captureResponse);
            result.Provider = this.CurrentPaymentProvider;
            result.ProviderResponseCode = captureResponse.Status;
            result.ProviderResponseMessage = captureResponse.Description;
            result.ProviderTransactionToken = captureResponse.Id;
            result.TransactionId = request.TransactionId;

            return result;
        }
        catch (StripeException e)
        {
            result.RawResult = JsonConvert.SerializeObject(e);
            result.ProviderResponseCode = e.StripeError?.Code ?? e.Message;
            result.ProviderResponseMessage = e.StripeError?.Message ?? e.Message;
            result.ProviderTransactionToken = e.StripeError?.Charge;

            switch (e.StripeError?.Type)
            {
                case "card_error":
                    workspan.Log.Information($"A payment error occurred: {e.Message}");
                    break;
                case "invalid_request_error":
                    workspan.Log.Error("An invalid request occurred.");
                    break;
                default:
                    workspan.Log.Error("Another problem occurred, maybe unrelated to Stripe.");
                    break;
            }

            result.ProviderResponseCode = e.StripeError.Code;
            result.ProviderResponseMessage = e.StripeError.Message;
            result.AddErrorWithCode(e.StripeError.Code, e.StripeError.Message);
        }
        catch (Exception e)
        {
            result.RawResult = JsonConvert.SerializeObject(e);
            workspan.Log.Error(e, "An error occurred while processing the credit card payment.");
            result.AddError(e.Message);
        }

        return result;
    }

    public override async Task<SaleResult> SaleAsync(SaleRequest request, CancellationToken token = default)
    {
        using var workspan = Workspan.Start<StripePaymentProvider>()
            .LogEnterAndExit();

        var tdsVersions = new List<string>() {"2.2.0", "2.1.0", "1.0.2"};
        var ecis = new List<string>() {"01", "02", "05", "06", "07"};

        var has3ds = request.ThreeDS is not null && request.Use3DS;
        // && tdsVersions.Contains(request.ThreeDS.ThreeDsVersion)
        // && ecis.Contains(request.ThreeDS.EcommerceIndicator);

        var result = new SaleResult();
        try
        {
            await InitiateProviderAsync(request.SupportedGateway.Sandbox, request.SupportedGateway.Id, token);


            var pmOptions = new PaymentMethodCreateOptions
            {
                Type = "card",

                Card = new PaymentMethodCardOptions
                {
                    Number = request.CreditCard.Number,
                    ExpMonth = request.CreditCard.Month,
                    ExpYear = request.CreditCard.Year,
                    Cvc = request.CreditCard.VerificationValue,
                },
                BillingDetails = new PaymentMethodBillingDetailsOptions
                {
                    Address = new AddressOptions()
                    {
                        City = request.BillingAddress.City,
                        Country = request.BillingAddress.Country,
                        Line1 = request.BillingAddress.Address1,
                        Line2 = request.BillingAddress.Address2,
                        PostalCode = request.BillingAddress.Zip,
                        State = request.BillingAddress.State
                    },
                    Name = request.BillingAddress.FirstName + " " + request.BillingAddress.LastName,
                    Email = request.BillingAddress?.Email,
                    Phone = request.BillingAddress?.PhoneNumber,
                },
            };

            var pmToken =
                await new PaymentMethodService().CreateAsync(pmOptions, _requestOptions, cancellationToken: token);

            // var Customer = new CustomerCreateOptions()
            // {
            //     Email = request.BillingAddress?.Email,
            //     Name = $"{request.BillingAddress?.FirstName} {request.BillingAddress?.LastName}",
            //     Phone = request.BillingAddress?.PhoneNumber,
            //   
            //     Address = new AddressOptions
            //     {
            //         Line1 = request.BillingAddress?.Address1,
            //         Line2 = request.BillingAddress?.Address2,
            //         City = request.BillingAddress?.City,
            //         State = request.BillingAddress?.State,
            //         PostalCode = request.BillingAddress?.Zip,
            //         Country = request.BillingAddress?.Country
            //     },
            //     PaymentMethod = pmToken.Id
            //     // Metadata = new Dictionary<string, string>
            //     // {
            //     //     { "ip", request.Device?.IpAddress} 
            //     // }
            // };
            //var cs = await new CustomerService().CreateAsync(Customer, _requestOptions, cancellationToken: token);

            var intentOptions = new PaymentIntentCreateOptions()
            {
                Amount = request.Amount,
                Currency = request.CurrencyCode,
                StatementDescriptor = request.Descriptor?.Name?.Substring(0,
                    Math.Min(request.Descriptor.Name.Length, 22)),
                Confirm = true,
                // Customer = cs.Id,
                CaptureMethod = "automatic",
                PaymentMethod = pmToken.Id,
                ReturnUrl = $"https://core.flex-charge.com/transaction/{Guid.NewGuid().ToString("N")}/redirect",
                //AutomaticPaymentMethods = 
                Metadata = new Dictionary<string, string>
                {
                    {"order_id", request.OrderId.ToString()},
                    {"correlation_id", request.OrderId.ToString()},
                    {"payer_id", request.PayerId.ToString()},
                    {"connect_agent", "FlexCharge"},
                },
                Shipping = request.ShippingAddress is var shipping
                           && shipping is null
                    ? null
                    : new()
                    {
                        Address = new AddressOptions()
                        {
                            City = shipping.City,
                            Country = shipping.Country,
                            Line1 = shipping.Address1,
                            Line2 = shipping.Address2,
                            PostalCode = shipping.Zip,
                            State = shipping.State
                        },
                        Name = shipping.FirstName + " " + shipping.LastName,
                        Phone = shipping.PhoneNumber,
                    },
                //OffSession = !request.IsCit,
            };
            if (has3ds) //  && request.IsCit)
            {
                intentOptions.PaymentMethodOptions =
                    new()
                    {
                        Card = new()
                        {
                            // RequestThreeDSecure = "any",not use it
                            ThreeDSecure =
                                new()
                                {
                                    Cryptogram = request.ThreeDS.AuthenticationValue, //"xgQYYgZVAAAAAAAAAAAAAAAAAAAA"
                                    ElectronicCommerceIndicator = request.ThreeDS.EcommerceIndicator, //o2
                                    Version = request.ThreeDS.ThreeDsVersion, //"2.2.0",//,

                                    TransactionId = request.ThreeDS.ThreeDsVersion.StartsWith("1")
                                        ? request.ThreeDS.Xid
                                        : request.ThreeDS
                                            .DirectoryServerTransactionId, //"1c770353-55f5-4292-ba5f-4c7cfbec8593"//
                                }
                        }
                    };
                // intentOptions.ErrorOnRequiresAction = true; -not use this
                workspan.Log.Information("stripe authorization uses  external 3ds");
            }

            var service = new PaymentIntentService();
            var paymentIntent = await service.CreateAsync(intentOptions, _requestOptions, cancellationToken: token);

            var PaymentIntentConfirmOptions = new PaymentIntentConfirmOptions
            {
                PaymentMethod = pmToken.Id
            };
            PaymentIntentConfirmOptions.AddExpand("latest_charge");

            paymentIntent = await service.ConfirmAsync(
                paymentIntent.Id,
                PaymentIntentConfirmOptions, _requestOptions, cancellationToken: token);

            // paymentIntent = await piIntentService.CaptureAsync(
            //     piCreate.Id, cancellationToken: token);

            if (paymentIntent.Status != "succeeded")
                result.AddError(paymentIntent.Status);

            result.RawResult = JsonConvert.SerializeObject(paymentIntent);
            result.Provider = this.CurrentPaymentProvider;
            result.ProviderResponseCode = paymentIntent.Status;
            result.ProviderResponseMessage = paymentIntent.Description;
            result.ProviderTransactionToken = paymentIntent.Id;

            result.CvvCode =
                paymentIntent.LatestCharge?.PaymentMethodDetails?.Card?.Checks?.CvcCheck == "pass"
                    ? "M"
                    : "N";

            result.AvsCode =
                paymentIntent.LatestCharge?.PaymentMethodDetails?.Card?.Checks is
                    {AddressLine1Check: "pass", AddressPostalCodeCheck: "pass"}
                    ? "M"
                    : "N";

            var mapped = await StripeResponseMapper.GetMappedResponseAsync(result.ProviderResponseCode, null);
            result.InternalResponseCode = mapped.MappedResponseCode;
            result.InternalResponseMessage = mapped.MappedResponseMessage;
            result.InternalResponseMessage = mapped.MappedDeclineTypeResponse.ToString();
        }
        catch (StripeException e)
        {
            switch (e.StripeError?.Type)
            {
                case "card_error":
                    workspan.Log.Information("A payment error occurred: {EMessage}", e.Message);
                    break;
                case "invalid_request_error":
                    workspan.Log.Error("An invalid request occurred");
                    break;
                default:
                    workspan.Log.Error("Another problem occurred, maybe unrelated to Stripe");
                    break;
            }

            result.RawResult = JsonConvert.SerializeObject(e);
            result.ProviderResponseCode = e.StripeError?.Code ?? e.Message;
            result.ProviderResponseMessage = e.StripeError?.Message ?? e.Message;
            result.ProviderTransactionToken = e.StripeError?.Charge;
            if (e.StripeError is not null)
                result.AddErrorWithCode(e.StripeError.Code, e.StripeError.Message);

            var mapped = await StripeResponseMapper.GetMappedResponseAsync(result.ProviderResponseCode, null);
            result.InternalResponseCode = mapped.MappedResponseCode;
            result.InternalResponseMessage = mapped.MappedResponseMessage;
            result.InternalResponseMessage = mapped.MappedDeclineTypeResponse.ToString();
        }
        catch (Exception e)
        {
            result.RawResult = JsonConvert.SerializeObject(e);
            workspan.RecordException(e, "An error occurred while processing the credit card payment");
            result.AddError(e.Message);
        }

        return result;
    }

    public override async Task<ICreditPaymentResult> CreditAsync(ICreditPaymentRequest request,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<StripePaymentProvider>();
        workspan.Log.Information("ENTERED: StripePaymentProvider => CreditAsync");

        var result = new CreditPaymentResult();
        try
        {
            await InitiateProviderAsync(request.SupportedGateway.Sandbox, request.SupportedGateway.Id, token);

            var stripeIntentResult = await new RefundService().CreateAsync(new RefundCreateOptions
            {
                Amount = request.Amount > 0 ? request.Amount : null,
                Currency = null,
                PaymentIntent = request.ProviderTransactionToken,
                Reason = request.IsEarlyWarningRequestedRefund ? "fraudulent" : null,
            }, _requestOptions, cancellationToken: token);

            const string successStatusText = "succeeded";
            const string pendingStatusText = "pending";

            if (stripeIntentResult.Status != successStatusText && stripeIntentResult.Status != pendingStatusText)
                result.AddError(stripeIntentResult.Status);

            result.Provider = this.CurrentPaymentProvider;
            result.ProviderResponseCode = stripeIntentResult.Status;
            result.ProviderResponseMessage = stripeIntentResult.Description;
            result.ProviderTransactionToken = stripeIntentResult.Id;
            result.RawResult = JsonConvert.SerializeObject(stripeIntentResult);

            result.PaymentInstrumentId = request.PaymentInstrumentId;
        }
        catch (StripeException e)
        {
            switch (e.StripeError?.Type)
            {
                case "card_error":
                    workspan.Log.Information($"A payment error occurred: {e.Message}");
                    break;
                case "invalid_request_error":
                    workspan.Log.Error("An invalid request occurred.");
                    break;
                default:
                    workspan.Log.Error("Another problem occurred, maybe unrelated to Stripe.");
                    break;
            }

            result.RawResult = JsonConvert.SerializeObject(e);
            result.ProviderResponseCode = e.StripeError?.Code ?? e.Message;
            result.ProviderResponseMessage = e.StripeError?.Message ?? e.Message;
            result.ProviderTransactionToken = e.StripeError?.Charge;
            if (e.StripeError is not null)
                result.AddErrorWithCode(e.StripeError.Code, e.StripeError.Message);
        }
        catch (Exception e)
        {
            result.RawResult = JsonConvert.SerializeObject(e);
            workspan.Log.Error(e, "An error occurred while processing the credit card payment.");
            result.AddError(e.Message);
        }

        return result;
    }

    public override async Task<IVoidPaymentResult> VoidAsync(IVoidPaymentRequest request,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<StripePaymentProvider>();
        workspan.Log.Information("ENTERED: StripePaymentProvider => AuthorizeAsync");

        var result = new VoidPaymentResult();
        try
        {
            await InitiateProviderAsync(request.SupportedGateway.Sandbox, request.SupportedGateway.Id, token);

            var service = new PaymentIntentService();

            // var intent = await service.GetAsync(request.ProviderTransactionToken, cancellationToken: token);
            // if (intent.Status is not "requires_capture" or "requires_confirmation"
            //     or "requires_action" or "requires_payment_method" or "requires_source" or "processing")
            // {
            //     result.AddError("Payment is not in a state that can be cancelled.");
            //     result.ProviderResponseMessage = "Payment is not in a state that can be cancelled.";
            //     return result;
            // }

            var stripeIntentResult =
                await service.CancelAsync(request.ProviderTransactionToken, null, _requestOptions,
                    cancellationToken: token);

            bool succeeded = stripeIntentResult.Status == "canceled";

            if (!succeeded)
                result.AddError(stripeIntentResult.Status);

            result.Provider = this.CurrentPaymentProvider;
            result.ProviderResponseCode = stripeIntentResult.Status;
            result.ProviderResponseMessage = stripeIntentResult.Description;
            result.ProviderTransactionToken = stripeIntentResult.Id;
            result.RawResult = JsonConvert.SerializeObject(stripeIntentResult);
            result.CustomProperties = new Dictionary<string, string>()
            {
                {"cancellationReason", stripeIntentResult?.CancellationReason},
            };

            result.PaymentInstrumentId = request.PaymentInstrumentId;
        }
        catch (StripeException e)
        {
            switch (e.StripeError?.Type)
            {
                case "card_error":
                    workspan.Log.Information("A payment error occurred: {EMessage}", e.Message);
                    break;
                case "invalid_request_error":
                    workspan.Log.Error("An invalid request occurred");
                    break;
                default:
                    workspan.Log.Error("Another problem occurred, maybe unrelated to Stripe");
                    break;
            }

            result.RawResult = JsonConvert.SerializeObject(e);
            result.ProviderResponseCode = e.StripeError?.Code ?? e.Message;
            result.ProviderResponseMessage = e.StripeError?.Message ?? e.Message;
            result.ProviderTransactionToken = e.StripeError?.Charge;
            if (e.StripeError is not null)
                result.AddErrorWithCode(e.StripeError.Code, e.StripeError.Message);
        }
        catch (Exception e)
        {
            result.RawResult = JsonConvert.SerializeObject(e);
            workspan.Log.Error(e, "An error occurred while processing the credit card payment");
            result.AddError(e.Message);
        }

        return result;
    }

    #region Commented

    // public async Task<List<Subscription>> GetSubscriptionsById(SubscriptionGetRequest request,
    //     CancellationToken token)
    // {
    //     using var workspan = Workspan.Start<StripePaymentProvider>().LogEnterAndExit();
    //
    //     await InitiateProviderAsync(request.Gateway.IsSandbox, request.Gateway.SupportedGatewayId, token);
    //
    //     var subscriptions =
    //         await _stripeSubscriptionSdk.GetSubscriptionsByIdAsync(request.SubscriptionId.ToString(), _requestOptions,
    //             token);
    //
    //     return subscriptions;
    // }

    #endregion

    public async Task<List<SubscriptionInvoice>> GetSubscriptionInvoices(SubscriptionGetRequest request,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<StripePaymentProvider>()
            .LogEnterAndExit();

        await InitiateProviderAsync(request.SupportedGateway.Sandbox, request.SupportedGateway.Id, token);

        var invoices =
            await _stripeSubscriptionSdk.GetInvoicesBySubscriptionIdAsync(request.ProviderSubscriptionId,
                _requestOptions,
                token);

        var result = invoices.Select(i => new SubscriptionInvoice()
        {
            Amount = i.AmountDue,
            AttemptCount = i.AttemptCount,
            Status = i.Status == "open" ? InvoiceStatus.Paid : InvoiceStatus.Unpaid,
            CreatedOn = i.DueDate
        }).ToList();

        return result;
    }

    public override async Task<SubscriptionAndLastInvoiceStatus> GetSubscriptionStatusAsync(
        SubscriptionGetRequest getRequest,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<StripePaymentProvider>()
            .Baggage("ProviderSubscriptionId", getRequest.ProviderSubscriptionId)
            .Baggage("Provider", CurrentPaymentProvider)
            .Baggage("SubscriptionId", getRequest.SubscriptionId)
            .Baggage("ProviderSubscriptionId", getRequest.ProviderSubscriptionId)
            .Baggage("Provider", CurrentPaymentProvider)
            .LogEnterAndExit();

        var result = new SubscriptionAndLastInvoiceStatus();

        await InitiateProviderAsync(getRequest.SupportedGateway.Sandbox, getRequest.SupportedGateway.Id, token);

        try
        {
            bool? isSubscriptionAlive = null;
            bool? lastInvoicePaid = null;
            bool? isSubscriptionCancelled = null;

            if (getRequest.ExternalAccountId != null)
                _requestOptions.StripeAccount = getRequest.ExternalAccountId;

            SubscriptionService subscriptionService = new SubscriptionService();
            var subscription = await subscriptionService.GetAsync(getRequest.ProviderSubscriptionId,
                new SubscriptionGetOptions()
                {
                    Expand = new List<string>()
                    {
                        "latest_invoice"
                    }
                },
                requestOptions: _requestOptions, cancellationToken: token);

            if (subscription != null)
            {
                isSubscriptionAlive = CheckIfSubscriptionIsAlive(subscription.Status, isSubscriptionAlive,
                    ref isSubscriptionCancelled);

                var lastInvoiceStatus = subscription.LatestInvoice.Status;
                if (lastInvoiceStatus != null)
                {
                    lastInvoicePaid = CheckIfInvoicePaid(lastInvoiceStatus);
                }
            }

            result = new()
            {
                SubscriptionIsActive = isSubscriptionAlive,
                SubscriptionIsCancelled = isSubscriptionCancelled,
                LastInvoiceSuccess = lastInvoicePaid,
            };
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
        }

        return result;
    }

    public override async Task<SubscriptionAndLastInvoiceStatus> GetAndSyncSubscriptionAsync(
        SubscriptionGetRequest request,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<StripePaymentProvider>()
            .Baggage("ProviderSubscriptionId", request.ProviderSubscriptionId)
            .Baggage("Provider", CurrentPaymentProvider)
            .LogEnterAndExit();

        await InitiateProviderAsync(request.SupportedGateway.Sandbox, request.SupportedGateway.Id, token);
        SubscriptionAndLastInvoiceStatus retval = null;
        try
        {
            var subscriptionStatus =
                await _stripeSubscriptionSdk.GetSubscriptionStateById(request.SubscriptionId!.Value, _requestOptions,
                    workspan, token);

            bool? isSubscriptionAlive = null;
            bool? lastInvoicePaid = null;
            bool? isSubscriptionCancelled = null;

            if (subscriptionStatus != null)
            {
                isSubscriptionAlive = CheckIfSubscriptionIsAlive(subscriptionStatus.SubscriptionStatus,
                    isSubscriptionAlive, ref isSubscriptionCancelled);

                var lastInvoiceStatus = subscriptionStatus.LastInvoiceStatus;
                if (lastInvoiceStatus != null)
                {
                    lastInvoicePaid = CheckIfInvoicePaid(lastInvoiceStatus);
                }
            }

            retval = new()
            {
                SubscriptionIsActive = isSubscriptionAlive,
                SubscriptionIsCancelled = isSubscriptionCancelled,
                LastInvoiceSuccess = lastInvoicePaid,
            };
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
        }

        return retval;
    }

    private static bool? CheckIfInvoicePaid(string lastInvoiceStatus)
    {
        using var workspan = Workspan.Current!;

        bool? lastInvoicePaid;
        switch (lastInvoiceStatus)
        {
            case StripeInvoiceStatusValues.Paid:
                lastInvoicePaid = true;
                break;
            case StripeInvoiceStatusValues.Draft:
            case StripeInvoiceStatusValues.Open:
            case StripeInvoiceStatusValues.Uncollectible:
            case StripeInvoiceStatusValues.Void:
                lastInvoicePaid = false;
                break;
            default:
                workspan
                    .Tag("LastInvoiceStatus", lastInvoiceStatus)
                    .Log.Fatal("Unknown Stripe invoice status");

                lastInvoicePaid = null;
                break;
        }

        return lastInvoicePaid;
    }

    private static bool? CheckIfSubscriptionIsAlive(string subscriptionStatus,
        bool? isSubscriptionAlive, ref bool? isSubscriptionCancelled)
    {
        using var workspan = Workspan.Current!;

        switch (subscriptionStatus)
        {
            case SubscriptionStatusValues.IncompleteExpired:
            case SubscriptionStatusValues.Cancelled:
                isSubscriptionAlive = false;
                isSubscriptionCancelled = true;
                break;
            case SubscriptionStatusValues.Incomplete:
            case SubscriptionStatusValues.PastDue:
            case SubscriptionStatusValues.Unpaid:
                isSubscriptionAlive = false;
                isSubscriptionCancelled = false;
                break;
            case SubscriptionStatusValues.Trialing:
            case SubscriptionStatusValues.Active:
                isSubscriptionAlive = true;
                isSubscriptionCancelled = false;
                break;
            default:
                workspan
                    .Tag("SubscriptionStatus", subscriptionStatus)
                    .Log.Fatal("Unknown Stripe subscription status");
                break;
        }

        return isSubscriptionAlive;
    }

    private (bool active, bool success) MapStatus(string subscriptionStatus)
    {
        var result = subscriptionStatus switch
        {
            SubscriptionStatusValues.Incomplete => (true, false),
            SubscriptionStatusValues.Active => (true, true),
            SubscriptionStatusValues.IncompleteExpired => (false, false),
            SubscriptionStatusValues.Unpaid => (false, false),
            SubscriptionStatusValues.Cancelled => (false, false),
            _ => throw new Exception("Unknown subscription state")
        };
        return result;
    }

    public override async Task<CreateSubscriptionResult> CreateSubscriptionAsync(CreateSubscriptionRequest request,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<StripePaymentProvider>()
            .Baggage("Mid", request.Mid)
            .Baggage("OrderId", request.OrderId)
            .Baggage("Provider", CurrentPaymentProvider)
            .LogEnterAndExit();

        await InitiateProviderAsync(request.SupportedGateway.Sandbox, request.SupportedGateway.Id, token);

#if DEBUG
        await SetupNgrokAsync();
#endif

        var card = request.CreditCard;
        CreateStripeSubscriptionRequest req = new()
        {
            InternalSubscriptionId = request.InternalSubscriptionId,
            Amount = request.Amount,
            Currency = request.CurrencyCode,
            ProviderName = request.SupportedGateway.Name,
            Card = new()
            {
                Cvc = card.VerificationValue,
                Number = card.Number,
                ExpMonth = card.Month,
                ExpYear = card.Year
            },
            Email = request.BillingAddress.Email,
            OrderId = request.OrderId,
            Mid = request.Mid,
            StatementDescriptor = request.Descriptor.Name,
            BillingDetails = request.BillingAddress is var billing
                             && billing is null
                ? null
                : new()
                {
                    Address = new()
                    {
                        City = billing.City,
                        Country = billing.Country,
                        Line1 = billing.Address1,
                        Line2 = billing.Address1,
                        State = billing.State,
                        PostalCode = billing.Zip
                    },
                },
            ShippingAddress = request.ShippingAddress,
            IntervalCount = request.IntervalCount,
            IntervalUnit = request.IntervalUnit,
            IsCit = false,
            RequestOptions = _requestOptions,
            LiveMode = !request.SupportedGateway.Sandbox,

            //startdate=default
            PaymentInstrumentId = request.PaymentInstrumentId,
            StartDate = request.StartDate,
            CancelAt = request.CancelAt,
        };

        CreateSubscriptionResult result = new()
        {
            OrderId = request.OrderId,
            Provider = CurrentPaymentProvider,
            PaymentInstrumentId = req.PaymentInstrumentId,
            ProcessorId = request.SupportedGateway.ProcessorId,
            BinNumber = request.CreditCard.Number.Substring(0, 8)
        };

        try
        {
            var subscription = await _stripeSubscriptionSdk.CreateSubscriptionAsync(req, token);


            // var sub = new Subscription();
            (bool active, bool success) state = MapStatus(subscription.Status);
            // sub.IsActive = state.active;
            // sub.LastPaymentSuccess = state.success;
            // sub.InitialStatus = subscription.Status;
            // sub.Meta = JsonSerializer.SerializeToDocument(subscription);
            // sub.ProviderSubsId = subscription.Id;
            // sub.CancelledAt = subscription.CanceledAt;
            // sub.CancelReason = subscription.CancellationDetails?.Reason;

            result.Status = state.success == true
                ? //todo-check
                TransactionStatus.Completed
                : TransactionStatus.InProcess;

            result.CustomerId = subscription.CustomerId;
            result.ProviderId = request.Gateway.Id;
            result.Provider = this.CurrentPaymentProvider;
            result.ProviderResponseCode = subscription.Status;
            result.ProviderResponseMessage = subscription.Status;
            result.ProviderTransactionToken = subscription.Id;
            result.RawResult = JsonConvert.SerializeObject(subscription);
            result.InitialStatus = subscription.Status;
            result.ProcessorId = request.SupportedGateway.ProcessorId;

            result.OrderId = request.OrderId;
            result.PaymentInstrumentId = req.PaymentInstrumentId;
            result.ProcessorId = request.SupportedGateway.ProcessorId;
            result.BinNumber = request.CreditCard?.Number?.Substring(0, 8);


            //result.SubscriptionId = request.,Subscript;
            result.Status = state.success == true
                ? TransactionStatus.Completed
                : state.active == true
                    ? TransactionStatus.InProcess
                    : TransactionStatus.Failed;

            workspan.Log.Information("Subscription for {SubscriptionId} created", request.InternalSubscriptionId);
        }
        catch (StripeException se)
        {
            if (se.StripeError is not null)
                result.AddErrorWithCode(se.StripeError.Code, se.StripeError.Message);

            result.Status = TransactionStatus.Failed;
            result.RawResult = JsonConvert.SerializeObject(se);
            workspan.RecordException(se);
            workspan.RecordError("Stripe Error with {code} and {message}", se.StripeError?.Code,
                se.StripeError?.Message);
        }
        catch (Exception e)
        {
            result.RawResult = JsonConvert.SerializeObject(e);
            result.AddError(e.Message);
            result.Status = TransactionStatus.Failed;
            workspan.RecordException(e);
        }

        return result;
    }


#if DEBUG
    private async Task SetupNgrokAsync()
    {
        // full-woodcock-formerly.ngrok-free.app is static ngrok domain
        // So url is full-woodcock-formerly.ngrok-free.app/webhooks
        // Stripe should has this url in webhook settings
        // 5022 is port for Payments MS
        string webhooksDomain =
            await NgrokHelper.GetNgrokWebhooksDomainAsync(false, "full-woodcock-formerly.ngrok-free.app", 5022);
    }
#endif


    public override async Task<RequestResult<CancelExternalSubscriptionResult>> CancelSubscriptionAsync(
        SubscriptionGetRequest getRequest, CancellationToken token)
    {
        using var workspan = Workspan.Start<StripePaymentProvider>()
            .Baggage("SubscriptionId", getRequest.SubscriptionId)
            .Baggage("ProviderSubscriptionId", getRequest.ProviderSubscriptionId)
            .Baggage("Provider", CurrentPaymentProvider)
            .LogEnterAndExit();

        await InitiateProviderAsync(getRequest.SupportedGateway.Sandbox, getRequest.SupportedGateway.Id, token);

        try
        {
            if (getRequest.ExternalAccountId != null)
                _requestOptions.StripeAccount = getRequest.ExternalAccountId;

            var cancelSubscriptionResponse = await _stripeSubscriptionSdk.CancelSubscription(
                getRequest.ProviderSubscriptionId,
                _requestOptions, token);

            if (cancelSubscriptionResponse.SubscriptionCancelled)
            {
                workspan.Log.Information(
                    "External subscription {ProviderSubscriptionId} cancelled",
                    getRequest.ProviderSubscriptionId);

                return RequestResult.Success(new CancelExternalSubscriptionResult());
            }
            else
            {
                return RequestResult.Error("Subscription cancellation failed",
                    new CancelExternalSubscriptionResult()
                    {
                        CanBeRetried = cancelSubscriptionResponse.CanBeRetried
                    });
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e);

            return RequestResult.Error(e,
                new CancelExternalSubscriptionResult()
                {
                    CanBeRetried = true,
                });
        }
    }


    #region ProcessWebHookAsync

    public override async Task<IWebHookResult> HandleWebhookEventAsync(string body, Guid gatewayId,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<StripePaymentProvider>();

        #region HandleCharge

        async Task<SubscriptionChargeUpdate> HandleCharge(Charge charge, bool success, RequestOptions requestOptions,
            CancellationToken token)
        {
            using var workspan = Workspan.Start<StripePaymentProvider>();

            try
            {
                if (charge.InvoiceId is null) return null;
                var invoice = await new InvoiceService().GetAsync(charge.InvoiceId, requestOptions: requestOptions);


                if (invoice?.SubscriptionId is null) return null;


                SubscriptionChargeUpdate update = new()
                {
                    Amount = (int) charge.AmountCaptured,
                    Currency = charge.Currency,
                    ProviderName = CurrentPaymentProvider,
                    Meta = JsonSerializer.SerializeToDocument(charge),
                    Type = nameof(TransactionType.Debit),
                    ProviderTransactionToken = charge.Id,
                    ProviderSubscriptionId = invoice.SubscriptionId,
                    Success = success
                };

                return update;
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return null;
            }
        }

        #endregion

        #region HandleInvoiceUpdate

        async Task<SubscriptionInvoiceUpdate> HandleInvoiceUpdate(Invoice invoice)
        {
            using var workspan = Workspan.Start<StripePaymentProvider>();

            try
            {
                SubscriptionInvoiceUpdate invoiceStatus = new();
                var status = invoice.Status;

                //see: https://stripe.com/docs/api/invoices/object
                //see: https://stripe.com/docs/invoicing/overview#workflow-overview
                switch (status)
                {
                    case "draft":
                        invoiceStatus.IsActive = true;
                        invoiceStatus.LastPaymentSuccess = null;
                        break;
                    case "open":
                        invoiceStatus.IsActive = true;
                        invoiceStatus.LastPaymentSuccess = null;
                        break;
                    case "paid":
                        invoiceStatus.IsActive = true;
                        invoiceStatus.LastPaymentSuccess = true;
                        break;
                    case "void":
                    case "uncollectible":
                        invoiceStatus.IsActive = false;
                        invoiceStatus.LastPaymentSuccess = false;
                        break;
                }

                invoiceStatus.ProviderSubscriptionId = invoice.SubscriptionId;
                workspan.Log.Information("Stripe invoice update webhook processed for {ProviderSubscriptionId}",
                    invoice.SubscriptionId);

                return invoiceStatus;
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return null;
            }
        }

        #endregion

        #region HandleSubscriptionUpdate

        async Task<SubscriptionStatusUpdate> HandleSubscriptionUpdate(Subscription subscription)
        {
            using var workspan = Workspan.Start<StripePaymentProvider>();

            try
            {
                SubscriptionStatusUpdate result = new();

                //see: https://stripe.com/docs/api/subscriptions/object
                switch (subscription.Status)
                {
                    case "incomplete":
                    case "incomplete_expired":
                        result.IsAlive = false;
                        break;

                    case "trialing":
                    case "active":
                    case "past_due":
                        result.IsAlive = true;
                        break;

                    case "canceled":
                    case "unpaid":
                        result.IsAlive = false;
                        break;
                }

                result.ProviderSubscriptionId = subscription.Id;
                workspan.Log.Information("Stripe session update webhook processed for {ProviderSubscriptionId}",
                    subscription.Id);

                return result;
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return null;
            }
        }

        #endregion

        IWebHookResult result = null;
        try
        {
            await InitiateProviderAsync(null, gatewayId, token);

            var stripeEvent = EventUtility.ParseEvent(body, throwOnApiVersionMismatch: false);

            if (stripeEvent.Data?.Object is null)
                return null;

            var rawObject = stripeEvent.Data?.RawObject;

            Charge charge = null;
            SubscriptionSchedule sub = null;
            result = stripeEvent.Type switch
            {
                EventTypes.ChargeCaptured
                    or EventTypes.ChargeSucceeded
                    => await HandleCharge(stripeEvent.Data!.Object as Charge, true, _requestOptions,
                        token),

                EventTypes.ChargeExpired
                    or EventTypes.ChargeFailed
                    => await HandleCharge(stripeEvent.Data!.Object as Charge, false, _requestOptions,
                        token),

                EventTypes.CustomerSubscriptionCreated
                    or EventTypes.CustomerSubscriptionDeleted
                    or EventTypes.CustomerSubscriptionUpdated
                    => await HandleSubscriptionUpdate(stripeEvent.Data!.Object as Subscription),

                EventTypes.InvoiceMarkedUncollectible
                    or EventTypes.InvoicePaid
                    or EventTypes.InvoiceVoided
                    or EventTypes.InvoiceUpdated
                    or EventTypes.InvoiceDeleted
                    or EventTypes.InvoicePaymentSucceeded
                    or EventTypes.InvoicePaymentActionRequired
                    //or EventTypes.InvoicePaymentOverpaid
                    => await HandleInvoiceUpdate(stripeEvent.Data!.Object as Invoice),

                _ => null
            };
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
        }


        return result;
    }

    #endregion


    public override async Task<(bool CanHandle, Guid? SupportedGatewayId)> CanHandleWebhookEventAsync(string body,
        IHeaderDictionary headers,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<StripePaymentProvider>()
            .Baggage("Method", nameof(CanHandleWebhookEventAsync))
            .Baggage("Provider", CurrentPaymentProvider)
            .LogEnterAndExit();

        (bool CanHandle, Guid SupportedGatewayId) retval = (false, default);

        if (!headers.ContainsKey("Stripe-Signature"))
            return retval;

        try
        {
            var gateways =
                await _context.SupportedGateways
                    .Where(g => g.NameIdentifier == this.CurrentPaymentProvider)
                    .ToListAsync();

            var gatewayIdAndSecrets =
                gateways.Where(g => !string.IsNullOrWhiteSpace(g.Configuration))
                    .Select(g =>
                        (g.Id, JsonSerializer.Deserialize<StripeConfig>(g.Configuration))).ToList();


            Guid? supportedGatewayId = null;
            foreach (var gatewayIdAndSecret in gatewayIdAndSecrets)
            {
                try
                {
                    var stripeEvent = EventUtility.ConstructEvent(
                        body,
                        headers["Stripe-Signature"],
                        gatewayIdAndSecret.Item2.WebhookSecret,
                        throwOnApiVersionMismatch: false);

                    supportedGatewayId = gatewayIdAndSecret.Item1;
                    break;
                }
                catch (Exception e)
                {
                    // not compatible with this gateway
                    //workspan.RecordException(e);
                }
            }

            if (supportedGatewayId.HasValue)
            {
                retval = (true, supportedGatewayId.Value);
                workspan.Log.Information("stripe webhook with {Body} received ", body);
            }
            else
            {
                throw new Exception("webhook secret for stripe provider is not set");
            }
        }
        catch (StripeException se)
        {
            workspan.RecordException(se);
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
        }

        return retval;

        // logger.LogInformation($"Webhook call: {stripeEvent.Type}"); switch (stripeEvent.Type) { c
        //             ase "Customer.subscription.trial_will_end": //Send trial will end email. {
    }

    public bool IsSandbox;
    public override bool SupportCapture => true;
    public override bool SupportPartiallyRefund => true;
    public override bool SupportRefund => true;
    public override bool SupportVoid => true;
    public override bool SupportTokenization => true;
    public override bool RequirePostProcessPayment => true;
    public override bool SupportPayouts => false;
    public override string CurrentPaymentProvider => "stripe";
    public override bool SupportsAch => false;
    public override bool SupportsCreditCards => true;
    public override bool SupportsCreditCardVerification => true;
    public override bool SupportsSubscription => true;
    public override bool SupportsStandaloneCredit => false;
    public override bool SupportsExternalThreeDS => true;
    public override CardBrand[] NetworkTokenCardBrands => null;
}