using FlexCharge.Contracts.Commands.Common;

namespace FlexCharge.Payments.Services.PaymentServices.Models;

public class PaymentModifiers
{
    public bool RedactCvv { get; set; } = false;
    public bool RedactDevice { get; set; } = false;
    public bool RedactIp { get; set; } = false;
    public bool RedactAvs { get; set; } = false;
    public bool Redact3ds { get; set; } = false;
    public bool RedactSchemeTransactionId { get; set; } = false;

    public bool UseBillingAsCardHolderIfMissing { get; set; } = false;

    public static PaymentModifiers FromPaymentModifiersModel(PaymentModifiersModel modifiersModel)
    {
        return new PaymentModifiers
        {
            RedactCvv = modifiersModel.RedactCvv,
            RedactDevice = modifiersModel.RedactDevice,
            RedactIp = modifiersModel.RedactIp,
            RedactAvs = modifiersModel.RedactAvs,
            Redact3ds = modifiersModel.Redact3ds,
            RedactSchemeTransactionId = modifiersModel.RedactSchemeTransactionId,
            UseBillingAsCardHolderIfMissing = modifiersModel.UseBillingAsCardHolderIfMissing
        };
    }
}