using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FlexCharge.Common.Response;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;

namespace FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels
{
    public class CreditPaymentResult : BaseResult, ICreditPaymentResult
    {
        public Guid InternalTransactionId { get; set; }
        public string ExternalTransactionId { get; set; }
        public string CreditTransactionResult { get; set; }
        public string CreditTransactionStatusID { get; set; }
        public string CreditTransactionStatusCode { get; set; }
        public string CreditTransactionStatusText { get; set; }

        public int CreditedAmount { get; set; }

        public Guid PaymentInstrumentId { get; set; }
        public Dictionary<string, string> CustomProperties { get; set; }
    }
}