using System;
using FlexCharge.Payments.Entities;

namespace FlexCharge.Payments.Services.PaymentServices.Models.ExternalTokenPayments;

public class MarkInvoiceAsPaidOutOfBandRequest
{
    public Guid Mid { get; set; }
    public Guid OrderId { get; set; }

    public SupportedGateway SupportedGateway { get; set; }
    public string ExternalAccountId { get; set; }
    public string InvoiceId { get; set; }
}

public class MarkInvoiceAsPaidOutOfBandResult
{
    public bool CanBeRetried { get; set; }
}