using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FlexCharge.Payments.Services.PaymentServices;
using FlexCharge.Utils;

namespace FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels
{
    public class TransferId
    {
        public TransferId()
        {

        }

        public TransferId(SaleTransferIdPrefix Source, int InternalTransactionId)
        {
            _source = Source;
            _internalTransactionId = InternalTransactionId;
        }

        private SaleTransferIdPrefix _source { get; set; }
        private int _internalTransactionId { get; set; }

        public int GetID()
        {
            return _internalTransactionId;
        }

        public SaleTransferIdPrefix GetSource()
        {
            return _source;
        }

        public TransferId Split(string transferID)
        {
            //temp fix for getting the PSP prefix seperated from funds transfer ID
            string[] idArray = transferID.Split('-');

            try
            {
                if (idArray.Count() == 1)
                    return new TransferId(SaleTransferIdPrefix.general, int.Parse(idArray[0]));
                else
                    return new TransferId(EnumHelpers.ParseEnum<SaleTransferIdPrefix>(idArray[0]), int.Parse(idArray[1]));
            }
            catch
            {
                return null;
            }
        }

        public override string ToString()
        {
            return $"{_source.ToString()}-{_internalTransactionId}";
        }
    }

    
}
