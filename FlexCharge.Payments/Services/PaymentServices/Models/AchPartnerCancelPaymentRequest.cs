using System;

namespace FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;

public class AchPartnerCancelPaymentRequest : PartnerBaseRequest, IAchCancelPaymentRequest
{
    public bool IsVerifiedAch { get; set; }
    public Guid TransactionId { get; set; }
    public Guid PaymentInstrumentId { get; set; }
    public string ProviderTransactionToken { get; set; }
    public string Reason { get; set; }
}