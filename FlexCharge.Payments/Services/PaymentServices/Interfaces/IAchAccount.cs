using System;
using FlexCharge.Common.SensitiveData.Obfuscation;
using FlexCharge.Payments.Services.SVBAchServices.Models;

namespace FlexCharge.Payments.Services.PaymentServices.Interfaces;

public interface IAchAccount
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string InstitutionName { get; set; }
    public AccountTypeEnum AccountType { get; set; }

    [SensitiveData(ObfuscationType.HeadVisible, LeaveUnobfuscatedSymbols = 4)]
    public string AccountNumber { get; set; }

    [SensitiveData(ObfuscationType.HeadVisible, LeaveUnobfuscatedSymbols = 4)]
    public string RoutingNumber { get; set; }

    // This is the token that is returned from the initial call to the Plaid API
    //public string TempPublicToken { get; set; }
    public string AccountId { get; set; }

    //public int Amount { get; set; }

    // This is the token that is returned Plaid after exchanging the public token for an access token
    public string ProcessorToken { get; set; }
}