using System;
using FlexCharge.Payments.Services.SVBAchServices.Models;

namespace FlexCharge.Payments.Services.PaymentServices.Interfaces;

public interface IAchRequest
{
    public bool IsVerifiedAch { get; set; }

    public string IdentificationNumber { get; set; }

    public string Processor { get; set; }
    public string Provider { get; set; }
    public string Currency { get; set; }
    public string CompanyEntryDescription { get; set; }
    public string EffectiveEntryDate { get; set; }

    public SecCodeEnum SecType { get; set; }

    public IAchAccount Sender { get; set; }
    public IAchAccount Receiver { get; set; }
}