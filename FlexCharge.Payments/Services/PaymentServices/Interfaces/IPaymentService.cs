// using FlexCharge.Payments.Entities;
// using FlexCharge.Payments.Services.PaymentServices.Interfaces;
// using System;
// using System.Collections.Generic;
// using System.Threading;
// using System.Threading.Tasks;
// using FlexCharge.Payments.Services.PaymentServices;
// using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
// using PaymentMethodType = FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels.PaymentMethodType;
//
// namespace FlexCharge.Payments.Services.PaymentServices.Interfaces
// {
//     public interface IPaymentService
//     {
//         Task<Gateway?> GetDefaultGatewayAsync(Guid mid);
//         Task<Gateway?> GetGatewayAsync(Guid mid, int gatewayOrder);
//         Task<Gateway?> GetGatewayAsync(Guid mid, Func<Gateway, bool> matchCondition);
//
//         /// <summary>
//         /// Returns Gateway with order <paramref name="gatewayOrder"/> (if exists) or default gateway if <paramref name="gatewayOrder"/> is null
//         /// </summary>
//         /// <param name="mid"></param>
//         /// <param name="gatewayOrder"></param>
//         /// <returns></returns>
//         Task<Gateway?> GetGatewayAsync(Guid mid, int? gatewayOrder);
//
//         Task RetainPaymentInstrumentAsync(Guid mid, string paymentInstrumentToken, CancellationToken token);
//
//         Task<SaleResult> SaleAsync(Guid mid, SaleRequest request, Gateway gateway = null,
//             CancellationToken token = default);
//
//         Task<AuthResult> AuthorizeAsync(Guid mid, AuthRequest payload, Gateway gateway = null,
//             CancellationToken token = default);
//
//         Task<CapturePaymentResult> CaptureAsync(Guid mid, CapturePaymentRequest payload,
//             CancellationToken token = default);
//
//         Task<VerifyInstrumentResult> VerifyAsync(Guid mid, VerifyInstrumentRequest payload, Gateway gateway = null,
//             CancellationToken token = default);
//         
//         Task<VerifyAmountResult> VerifyAmountAsync(Guid mid, AuthRequest payload, Gateway gateway = null,
//             CancellationToken token = default);
//
//         Task<CreditPaymentResult> CreditAsync(Guid mid, CreditPaymentRequest payload, Gateway gateway = null,
//             CancellationToken token = default);
//
//         Task<VoidPaymentResult> VoidAsync(Guid mid, VoidPaymentRequest payload, Gateway gateway = null,
//             CancellationToken token = default);
//         
//         Task<AchTransferResponse> AchCreditAsync(Guid mid,AchTransferRequest payload,
//             CancellationToken token = default);
//         Task<AchTransferResponse> AchDebitAsync(Guid mid,AchTransferRequest payload,
//             CancellationToken token = default);
//         Task<VoidPaymentResult> AchCancelAsync(Guid mid,object payload,
//             CancellationToken token = default);
//
//         Task<PaymentStatusResult> GetPaymentStatusAsync(Guid mid,string paymentId, PaymentMethodType methodType,
//             string provider, CancellationToken token = default);
//         
//         Task<FingerprintInstrumentResult> FingerprintPaymentInstrumentAsync(Guid mid,
//             FingerprintInstrumentRequest payload, CancellationToken token = default);
//     }
// }