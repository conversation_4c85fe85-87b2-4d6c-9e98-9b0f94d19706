// #if DEBUG
// #define TEST_FORCE_TODAY_IS_PAYOUT_DAY
// #endif
//
// using System;
// using System.Collections.Generic;
// using System.Linq;
// using System.Threading.Tasks;
// using AutoMapper;
// using FlexCharge.Common.Activities;
// using FlexCharge.Common.Cache;
// using FlexCharge.Common.Telemetry;
// using FlexCharge.Contracts.Common;
// using FlexCharge.Contracts.Payouts;
// using FlexCharge.Payments.Activities;
// using FlexCharge.Payments.DTO;
// using FlexCharge.Payments.Entities;
// using FlexCharge.Payments.Entities.Extensions;
// using FlexCharge.Payments.Services.PaymentServices.Interfaces;
// using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
// using FlexCharge.Payments.Services.PayoutServices.Models;
// using FlexCharge.Payments.Services.SVBAchServices.Models;
// using FlexCharge.Utils;
// using Hangfire.Server;
// using MassTransit;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.Extensions.Logging;
// using Microsoft.Extensions.Options;
// using Newtonsoft.Json;
// using Thrift.Protocol;
// using PaymentMethodType = FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels.PaymentMethodType;
//
//
// namespace FlexCharge.Payments.Services.PayoutServices;
//
// public class PayoutService : IPayoutService
// {
//     private PostgreSQLDbContext _dbContext;
//     private IMapper _mapper;
//     private readonly IPublishEndpoint _publisher;
//     private ILogger<PayoutService> _logger;
//     private readonly IActivityService _activityService;
//     private readonly IBigPayloadService _bigPayloadService;
//     public IPaymentService _PaymentService { get; set; }
//
//     public PayoutService(PostgreSQLDbContext context,
//         IMapper mapper,
//         IPublishEndpoint publisher,
//         ILogger<PayoutService> logger, IActivityService activityService, IPaymentService paymentService,
//         IBigPayloadService bigPayloadService)
//     {
//         _dbContext = context;
//         _mapper = mapper;
//         _publisher = publisher;
//         _logger = logger;
//         _activityService = activityService;
//         _bigPayloadService = bigPayloadService;
//         _PaymentService = paymentService;
//     }
//
//     public async Task<FundingInstructionsResponse> GenerateFundingInstruction(FundingInstructionRequest payload)
//     {
//         var res = new FundingInstructionsResponse();
//         return res;
//     }
//
//     #region Commented
//
//     // public async Task GenerateFundingInstruction(int month)
//     // {
//     //     try
//     //     {
//     //         //get payout candidates
//     //         var payoutsCandidates = await _dbContext.Transactions.Include(x => x.Merchant).Where(x =>
//     //             x.BatchId == null &&
//     //             (x.Type == TransactionType.Debit || x.Type == TransactionType.Capture) &&
//     //             x.CreatedOn.Date.Month == month).ToListAsync();
//     //
//     //         //group by merchant
//     //         var transactionsGroupedByMerchant = payoutsCandidates.GroupBy(x => x.Merchant);
//     //
//     //         var today = DateTime.Today;
//     //         var monthh = new DateTime(today.Year, month, 1);
//     //         var first = monthh;
//     //         var last = monthh.AddMonths(1).AddDays(-1);
//     //
//     //         //TODO call merchant MS to get fees
//     //
//     //         //TODO: apply fees on transactions and deduct from total amount
//     //         
//     //         //set create batchType (FIMC)
//     //         var list = new List<Batch>();
//     //         foreach (var transactionsByMerchant in transactionsGroupedByMerchant)
//     //         {
//     //             if (transactionsByMerchant.Key is null)
//     //                 continue;
//     //
//     //             var batch = new Batch
//     //             {
//     //                 Beneficiary = transactionsByMerchant.Key,
//     //                 TotalAmount = transactionsByMerchant.Sum(x => x.Amount),
//     //                 BatchType = FIMovementType.FISC.ToString(),
//     //                 Transactions = transactionsByMerchant.ToList(),
//     //                 PayoutStatus = PayoutStatus.UNPROCESSED,
//     //                 From = first.ToUniversalTime(),
//     //                 To = last.ToUniversalTime(),
//     //             };
//     //
//     //             list.Add(batch);
//     //         }
//     //
//     //         if (!list.Any())
//     //             return;
//     //
//     //         await _dbContext.Batches.AddRangeAsync(list);
//     //         await _dbContext.SaveChangesAsync();
//     //
//     //         //send to ACH provider
//     //         var fis = await GenerateFIForPostedBatches();
//     //         foreach (var fir in fis.FundingInstructions)
//     //         {
//     //             list.SingleOrDefault(x => x.Id == fir.BatchId)!.PayoutStatus =
//     //                 fir.Success ? PayoutStatus.PROCESSING : PayoutStatus.FAILED;
//     //         }
//     //
//     //         _dbContext.Batches.UpdateRange(list);
//     //         await _dbContext.SaveChangesAsync();
//     //     }
//     //     catch (Exception e)
//     //     {
//     //         throw;
//     //     }
//     // }
//
//     #endregion
//
//     private readonly object _GenerateFIForPostedBatchesLock = new object();
//
//     /// <summary>
//     /// 
//     /// </summary>
//     /// <returns></returns>
//     /// <remarks>Method intentionally made sync to use lock {} to ensure that batches are paid only once</remarks>
//     private FundingInstructionsResponse GenerateFundingInstructions()
//     {
//         var fis = new FundingInstructionsResponse();
//         List<Batch> postedBatchesToProcess;
//
//         lock (_GenerateFIForPostedBatchesLock)
//         {
//             async using (await _dbContext.Database.BeginTransactionAsync())
//             {
//                 postedBatchesToProcess = await _dbContext.Batches
//                     .Where(x => x.IsPosted && x.PayoutStatus == PayoutStatus.UNPROCESSED)
//                     .Include(x => x.Beneficiary)
//                     .ToListAsync();
//
//                 foreach (var batch in postedBatchesToProcess)
//                 {
//                     // Get sender information (Flexcharge account)
//
//                     fis.FundingInstructions.Add(new FundingInstruction
//                     {
//                         Success = true,
//                         BatchId = batch.Id,
//                         IsOffline = batch.IsOffline
//                     });
//
//                     batch.PayoutStatus = PayoutStatus.PROCESSING;
//                     batch.StatusText = PayoutStatus.PROCESSING.ToString();
//                 }
//
//                 _dbContext.Batches.UpdateRange(postedBatchesToProcess);
//                 _dbContext.SaveChanges();
//
//                 _dbContext.Database.CommitTransaction();
//             }
//         }
//
//         foreach (var postedBatch in postedBatchesToProcess)
//         {
//             _activityService.CreateActivityAsync(PayoutActivities.PayoutBatches_ProcessingPostedBatch,
//                 set: set => set
//                     .CorrelationId(postedBatch.Id)
//                     .TenantId(postedBatch.Beneficiary.Mid)
//                     .Meta(meta => meta.SetValue("Amount", postedBatch.TotalAmount)));
//         }
//
//         return fis;
//     }
//
// //If this day is payout date (by config) -> run create batches and mark orders in batches as PayoutProcessing
//     private async Task CreatePayoutBatchesInternalAsync(Merchant merchant, Guid uniqueRequestId,
//         DateTime payoutDate, List<PayoutOrderItem> payoutCandidateOrders)
//     {
//         using var workspan = Workspan.Start<PayoutService>();
//
//         workspan.Log.Information("Candiate orders to payout: {OrdersCount}", payoutCandidateOrders.Count);
//
//         var payoutFrequencyType = merchant.ToPayoutFrequencyType();
//
//         var payoutOrders = payoutCandidateOrders
//             .Where(x => payoutFrequencyType.IsOrderInThisPayoutPeriod(merchant, x.Date, payoutDate))
//             .ToList();
//
//         if (payoutOrders.Any())
//         {
//             workspan.Log.Information("Orders to payout: {OrdersCount}", payoutOrders.Count);
//             
//             //TODO call merchant MS to get fees
//
//             //TODO: apply fees on transactions and deduct from total amount
//
//             //set create batchType (FIMC)
//
//             var ordersInBatchIds = payoutOrders.Select(x => x.Id).ToList();
//
//             var payoutRangeStartDateUtc = payoutFrequencyType.GetPayoutRangeStartDate(merchant, payoutDate);
//             var payoutRangeEndDateUtc = payoutFrequencyType.GetPayoutRangeEndDate(merchant, payoutDate);
//
//             #region Sending PayoutRequestAcceptedEvent for orders in this batch
//
//             // creating big payload to avoid expiration issues
//             var payoutRequestAcceptedEventOrdersBigPayload =
//                 await _bigPayloadService.CreateBigPayloadAsync(payoutOrders);
//
//             //Sending only orders in this batch
//             await _publisher.Publish(new PayoutRequestAcceptedEvent
//             {
//                 PayoutOrders = new BigPayloadKey<List<PayoutOrderItem>>(payoutRequestAcceptedEventOrdersBigPayload.Key)
//             });
//
//             #endregion
//
//             try
//             {
//                 var refundsToProcess = await _dbContext.Transactions
//                     .FilterByMid(merchant.Mid)
//                     .OnlyRefunds()
//                     .Where(x => x.BatchId == null) //not added to any batch yet
//                     .OrderBy(x => x.CreatedOn) //to pay refunds with oldest date first
//                     .ToListAsync();
//
//                 var chargebacksToProcess = await _dbContext.Transactions
//                     .FilterByMid(merchant.Mid)
//                     .OnlyChargebacks()
//                     .Where(x => x.BatchId == null) //not added to any batch yet
//                     .OrderBy(x => x.CreatedOn) //to pay chargebacks with oldest date first
//                     .ToListAsync();
//
//                 var batch = new Batch
//                 {
//                     // Unique key and its checked against a DB constraint to avoid adding duplicate batches
//                     ConcurrencyUniqueBatchId =
//                         $"{merchant.Mid}_{payoutRangeStartDateUtc.ToShortDateString()}_{payoutRangeEndDateUtc.ToShortDateString()}",
//                     Beneficiary = merchant,
//                     BatchType = FIMovementType.FISC.ToString(),
//                     Transactions = await _dbContext.Transactions
//                         .Where(x => ordersInBatchIds.Contains(x.OrderId))
//                         .ToListAsync(),
//                     PayoutStatus = PayoutStatus.UNPROCESSED,
//                     StatusText = PayoutStatus.UNPROCESSED.ToString(),
//                     From = payoutRangeStartDateUtc.ToUtcDate(),
//                     To = payoutRangeEndDateUtc.ToUtcDate(),
//                     IsOffline = true,
//
//                     TotalAmount = payoutOrders.Sum(x => x.Amount),
//                     FlexChargeFees = payoutOrders.Sum(x => x.FlexChargeFee),
//                 };
//
//
//                 AddRefundOrChargebackTransactionsToBatch(batch, refundsToProcess);
//                 AddRefundOrChargebackTransactionsToBatch(batch, chargebacksToProcess);
//
//                 await _dbContext.Batches.AddAsync(batch);
//                 await _dbContext.SaveChangesAsync();
//             }
//             catch (Exception e)
//             {
//                 workspan.RecordException(e, true);
//                 await _activityService.CreateActivityAsync(
//                     PayoutErrorActivities.PayoutBatches_CreatingPayoutBatch_Error, data: e);
//
//                 #region Sending PayoutProcessingFailedEvent for orders in this batch
//
//                 // creating big payload to avoid expiration issues
//                 var payoutRequestFailedEventOrdersBigPayload =
//                     await _bigPayloadService.CreateBigPayloadAsync(payoutOrders);
//
//                 //Sending only orders in this batch
//                 await _publisher.Publish(new PayoutProcessingFailedEvent
//                 {
//                     PayoutOrders =
//                         new BigPayloadKey<List<PayoutOrderItem>>(payoutRequestFailedEventOrdersBigPayload.Key)
//                 });
//
//                 #endregion
//             }
//         }
//     }
//
//     /// <summary>
//     /// Fit refunds into the batch while keeping the total amount of the batch above zero
//     /// </summary>
//     /// <param name="batch"></param>
//     /// <param name="refundOrChargeBackTransactions"></param>
//     private static void AddRefundOrChargebackTransactionsToBatch(Batch batch,
//         List<Transaction> refundOrChargeBackTransactions)
//     {
//         if (refundOrChargeBackTransactions.Any())
//         {
//             int remainingPayoutAmount = batch.CalculatePayoutAmount();
//
//             foreach (var refund in refundOrChargeBackTransactions)
//             {
//                 int refundAmount = refund.Amount;
//                 int refundFeeAmount = refund.FeeAmount ?? 0;
//
//                 if (remainingPayoutAmount - refundAmount - refundFeeAmount < 0) break;
//                 else
//                 {
//                     refund.BatchId = batch.Id;
//                     batch.Transactions.Add(refund);
//
//                     batch.Returns += refundAmount;
//                     batch.FlexChargeFees += refundFeeAmount;
//
//                     remainingPayoutAmount -= refundAmount + refundFeeAmount;
//                 }
//             }
//         }
//     }
//
//     public async Task CreatePayoutBatchesAsync(Guid uniqueRequestId, List<PayoutOrderItem> payoutOrders)
//     {
//         using var workspan = Workspan.Start<PayoutService>();
//
//         if (payoutOrders?.Any() == true)
//         {
//             var ordersByMerchant = payoutOrders.GroupBy(x => x.MerchantId).ToList();
//
//             DateTime payoutDate = DateTime.UtcNow.ToUtcDate();
//
// #if TEST_FORCE_TODAY_IS_PAYOUT_DAY
//             payoutDate = new DateTime(payoutDate.Year, payoutDate.Month, 1).AddMonths(1).ToUtcDate();
// #endif
//
//
//             foreach (var merchantOrders in ordersByMerchant)
//             {
//                 var merchant = await _dbContext.Merchants.SingleAsync(x => x.Mid == merchantOrders.Key);
//
//                 if (merchant.IsPayoutDate(payoutDate))
//                 {
//                     workspan.Log.Information("Today is payout day for merchant {Mid}", merchant.Mid);
//
//                     await CreatePayoutBatchesInternalAsync(merchant, uniqueRequestId, payoutDate,
//                         merchantOrders.ToList());
//                 }
//             }
//         }
//     }
//
//
//     public async Task ExecutePayouts()
//     {
//         using var workspan = Workspan.Start<PayoutService>();
//
//         workspan.Log.Information("ENTERED: ExecutePayouts");
//
//         FundingInstructionsResponse payoutCandidates = null;
//
//         try
//         {
//             var executedPayouts = new List<Payout>();
//
//             payoutCandidates = GenerateFundingInstructions();
//
//             var fundingSource = await _dbContext.FundingSources.FirstOrDefaultAsync();
//             if (fundingSource == null)
//             {
//                 throw new Exception("Funding source not found");
//             }
//
//             await using (await _dbContext.Database.BeginTransactionAsync())
//             {
//                 foreach (var fundingInstruction in payoutCandidates.FundingInstructions)
//                 {
//                     try
//                     {
//                         var batch = await _dbContext.Batches.Include(x => x.Beneficiary)
//                             .SingleAsync(x => x.Id == fundingInstruction.BatchId);
//
//                         var merchant = await _dbContext.Merchants.FindAsync(batch.Beneficiary.Id);
//                         if (merchant is null)
//                         {
//                             workspan.RecordError("PAYOUTS: Can't process batch {BatchId} MERCHANT NOT FOUND ID: {Mid}",
//                                 fundingInstruction.BatchId, batch.Beneficiary.Mid);
//                             continue;
//                         }
//
//                         AchTransferResponse payoutResponse = null;
//                         if (!fundingInstruction.IsOffline)
//                         {
//                             payoutResponse = await _PaymentService.AchCreditAsync(batch.Beneficiary.Mid,
//                                 new AchTransferRequest
//                                 {
//                                     IsVerifiedAch = false,
//                                     IdentificationNumber =
//                                         DateTime.Now.Ticks.ToString("x"), //TODO: need to find a 15 characters unique id
//                                     OrderId = batch.Id,
//                                     Currency = "USD",
//                                     Processor = "SVB",
//                                     Provider = "SVB",
//                                     SecType = SecCodeEnum.PPD,
//                                     CompanyEntryDescription = "FXC Payout",
//                                     EffectiveEntryDate = DateTime.Now.ToUniversalTime().ToString("yyyy-MM-dd"),
//                                     Receiver = new AchAccountDTO()
//                                     {
//                                         Name = merchant.Dba,
//                                         AccountType = merchant.AchReceivingAccountType.ToUpper() == "CHECKING"
//                                             ? AccountTypeEnum.CHECKING
//                                             : AccountTypeEnum.SAVINGS,
//                                         AccountNumber = merchant.AchReceivingAccountNumber,
//                                         RoutingNumber = merchant.AchReceivingRoutingNumber,
//                                         Amount = batch.TotalAmount
//                                     },
//                                     Sender = new AchAccountDTO
//                                     {
//                                         Name = fundingSource.AccountName,
//                                         AccountNumber = fundingSource.AccountNumber // "**********"
//                                     }
//                                 });
//
//                             if (!payoutResponse.Success)
//                             {
//                                 workspan.Log.Error("PAYOUTS: Can't process ach for batchID: {BatchId}",
//                                     fundingInstruction.BatchId);
//                             }
//                         }
//
//                         executedPayouts.Add(new Payout()
//                         {
//                             Id = batch.Id,
//                             ReferenceId = !fundingInstruction.IsOffline ? payoutResponse.TransactionId : Guid.Empty,
//                             MerchantId = batch.Beneficiary.Mid,
//                             ExecutedDate = DateTime.UtcNow,
//                             TotalAmount = batch.TotalAmount,
//                             Status = !fundingInstruction.IsOffline
//                                 ? batch.PayoutStatus.ToString()
//                                 : PayoutStatus.SUCCESS.ToString(),
//                         });
//
//                         if (!fundingInstruction.IsOffline)
//                         {
//                             batch.RelatedTransaction = payoutResponse.TransactionId;
//                         }
//                         else
//                         {
//                             batch.PayoutStatus = PayoutStatus.SUCCESS;
//                             batch.StatusText = PayoutStatus.SUCCESS.ToString();
//                         }
//
//                         _dbContext.Batches.Update(batch);
//                         await _dbContext.SaveChangesAsync();
//                     }
//                     catch (Exception e)
//                     {
//                         workspan.RecordException(e, "PAYOUTS: Can't process batch {BatchId}",
//                             fundingInstruction.BatchId);
//                     }
//                 }
//
//                 await _dbContext.Database.CommitTransactionAsync();
//             }
//
//             await _publisher.Publish<PayoutExecutedEvent>(new
//             {
//                 Payouts = executedPayouts
//             });
//         }
//         catch (Exception e)
//         {
//             workspan.RecordException(e, "PAYOUTS: Can't process funding instructions: {fundingInstructions}",
//                 JsonConvert.SerializeObject(payoutCandidates));
//         }
//     }
//
//     public async Task CheckPayoutStatus()
//     {
//         using var workspan = Workspan.Start<PayoutService>();
//
//         var payoutsForValidation = await _dbContext.Batches
//             .Include(x => x.Beneficiary)
//             .Where(x => x.PayoutStatus == PayoutStatus.PROCESSING)
//             .ToListAsync();
//
//         try
//         {
//             foreach (var payout in payoutsForValidation)
//             {
//                 var relatedTransaction =
//                     await _dbContext.Transactions.SingleOrDefaultAsync(x => x.Id == payout.RelatedTransaction);
//                 if (relatedTransaction is null)
//                 {
//                     workspan.Log.Error("Related transaction not found for payout {PayoutId}", payout.Id);
//                     continue;
//                 }
//
//                 var payoutStatus = await _PaymentService.GetPaymentStatusAsync(payout.Beneficiary.Mid,
//                     relatedTransaction.ProviderTransactionToken,
//                     Enum.Parse<PaymentMethodType>(relatedTransaction.PaymentType),
//                     relatedTransaction.ProviderName);
//
//                 if (!payoutStatus.Results.Any()) continue;
//
//                 foreach (var payoutResult in payoutStatus.Results)
//                 {
//                     if (payoutResult.Key.ToString() == relatedTransaction.ProviderTransactionToken)
//                     {
//                         payout.PayoutStatus = payoutResult.Value switch
//                         {
//                             "SUCCEEDED" => PayoutStatus.SUCCESS,
//                             "FAILED" => PayoutStatus.FAILED,
//                             "PENDING" => PayoutStatus.PROCESSING,
//                             "CANCELED" => PayoutStatus.CANCELED,
//                             _ => payout.PayoutStatus
//                         };
//                     }
//
//                     //TODO: add a check for the status of the payout and update the transaction status accordingly (need to check if we want to update transaction record or not) 
//
//                     _dbContext.Batches.Update(payout);
//                     await _dbContext.SaveChangesAsync();
//                 }
//             }
//         }
//         catch (Exception e)
//         {
//             workspan.RecordException(e, "PAYOUTS: Can't CheckPayoutStatus");
//         }
//     }
// }

