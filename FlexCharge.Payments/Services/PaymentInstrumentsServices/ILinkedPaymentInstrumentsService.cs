using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Contracts.Commands;
using FlexCharge.Payments.Services.PaymentInstrumentsServices.Models;

namespace FlexCharge.Payments.Services.PaymentInstrumentsServices;

public interface ILinkedPaymentInstrumentsService
{
    public Task<LinkedPaymentInstruments> GetLinkedPaymentInstruments(List<Guid> ownerIds,
        List<LinkedPaymentInstrumentType> instrumentTypesToGet,
        CancellationToken cancellationToken);
}