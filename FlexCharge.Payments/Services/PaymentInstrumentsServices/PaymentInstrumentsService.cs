using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Metadata;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using EntityFramework.Exceptions.Common;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.Vault;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Commands.Vault;
using FlexCharge.Payments.Activities;
using FlexCharge.Payments.BinChecker.CardBrands;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.PaymentInstrumentsServices.Models;
using FlexCharge.Payments.Services.SpreedlyService;
using FlexCharge.Utils;
using Jaeger.Thrift.Crossdock;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Merchant = FlexCharge.Payments.Entities.Merchant;

namespace FlexCharge.Payments.Services.PaymentInstrumentsServices
{
    public class PaymentInstrumentsService : IPaymentInstrumentsService
    {
        private readonly PostgreSQLDbContext _context;
        private readonly IMapper _mapper;
        private readonly ISpreedlyService _spreedly;
        private readonly IPublishEndpoint _publisher;
        private readonly IRequestClient<DeTokenizeInstrumentCommand> _deTokenizeInstrumentRequestClient;
        private readonly IActivityService _activityService;
        private readonly ICardBrandDetector _cardBrandDetector;

        public PaymentInstrumentsService(PostgreSQLDbContext context,
            IMapper mapper, ISpreedlyService spreedly, IPublishEndpoint publisher,
            IRequestClient<DeTokenizeInstrumentCommand> deTokenizeInstrumentRequestClient,
            IActivityService activityService, ICardBrandDetector cardBrandDetector)
        {
            _context = context;
            _mapper = mapper;
            _spreedly = spreedly;
            _publisher = publisher;
            _deTokenizeInstrumentRequestClient = deTokenizeInstrumentRequestClient;
            _activityService = activityService;
            _cardBrandDetector = cardBrandDetector;
        }

        public async Task<DeTokenizeInstrumentCommandResponse> DetokenizeAsync(
            DeTokenizeInstrumentCommand detokenizeCommand)
        {
            using var workspan = Workspan.Start<PaymentInstrumentsService>();

            DeTokenizeInstrumentCommandResponse detokenizedCard;
            // try
            // {
            //     var deTokenizeResponse = await _grpcVaultServiceClient.DeTokenizeInstrumentAsync(
            //         new()
            //         {
            //             Request = JsonConvert.SerializeObject(detokenizeCommand)
            //         });
            //
            //     detokenizedCard =
            //         JsonConvert.DeserializeObject<DeTokenizeInstrumentCommandResponse>(deTokenizeResponse.Response);
            // }
            // catch (Exception e)
            // {
            //     workspan.RecordFatalException(e, "GRPC FAILED - FALLBACK TO MASSTRANSIT");

            var deTokenizeResponse =
                await _deTokenizeInstrumentRequestClient
                    .GetResponse<DeTokenizeInstrumentCommandResponse>(detokenizeCommand);

            detokenizedCard = deTokenizeResponse.Message;
            // }

            var rows = await _context.PaymentInstruments
                .Where(a => a.Token == detokenizeCommand.VaultKeyId.ToString())
                .ExecuteUpdateAsync(settings => settings
                        .SetProperty(a => a.ExpirationMonth,
                            a => detokenizedCard.ExpirationMonth.ToString())
                        .SetProperty(a => a.ExpirationYear,
                            a => detokenizedCard.ExpirationYear.ToString())
                        .SetProperty(a => a.Last4, a => detokenizedCard.Last4)
                        .SetProperty(a => a.AccountLastUpdatedAt, a => detokenizedCard.AccountLastUpdatedAt)
                        .SetProperty(a => a.Bin, a => detokenizedCard.Bin)
                        .SetProperty(a => a.CardNumberMasked, a => detokenizedCard.CardNumberMasked)
                        .SetProperty(a => a.ValidLuhn, a => detokenizedCard.ValidLuhn)
                    // .SetProperty(a => a.Fingerprint, a => context.Message.Fingerprint)
                    // .SetProperty(a => a.SenseKey, a => detokenizedCard.SenseKey)
                    // .SetProperty(a => a.Email, a => detokenizedCard.Email)
                    // .SetProperty(a => a.Phone, a => detokenizedCard.PhoneNumber)
                    //.SetProperty(a => a.BillingAddress, a => detokenizedCard.BillingAddress)  //todo -constant?
                    //.SetProperty(a => a.ShippingAddress, a => detokenizedCard.ShippingAddress) // todo - constant?
                    //.SetProperty(a => a.Country, a => detokenizedCard.cou)
                    //.SetProperty(a=>a.CardNumberMasked, a=>context.Message.)
                    //.SetProperty(a=>a., a=>context.Message.)
                );
            return detokenizedCard;
        }


        public async Task<IEnumerable<PaymentInstrument>> GetPaymentMethodsAsync(Guid mid, Guid ownerId,
            CancellationToken cancellationToken)
        {
            using var workspan = Workspan.Start<PaymentInstrumentsService>();

            try
            {
                workspan.Log.Information(
                    $"ENTERED: PaymentInstrumentsService > GetPaymentMethodsAsync > OwnerId: {ownerId}");

                var paymentInstruments = await _context.PaymentInstruments.Include(x => x.Merchant)
                    .Where(x => x.OwnerId == ownerId && x.IsActive).ToListAsync();

                ArgumentNullException.ThrowIfNull(paymentInstruments, "paymentInstruments is null");

                // var spreedlyPi = await _spreedly.GetPaymentInstrumentsAsync(MerchantId, OwnerId,CancellationToken);
                //
                // foreach (var pi in spreedlyPi)
                // {
                //     paymentInstruments.Add(new PaymentInstrument
                //     {
                //         Type = PaymentMethodType.Credit,
                //         MerchantId = MerchantId,
                //         OwnerId = OwnerId,
                //     });
                // }

                return paymentInstruments;
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    $"EXCEPTION: PaymentInstrumentsService > GetPaymentMethodsAsync > OwnerId: {ownerId}");
                await _activityService.CreateActivityAsync(
                    PaymentInstrumentErrorActivities.PaymentInstrument_GetPaymentMethod_Error,
                    set => set
                        .TenantId(mid)
                        .Meta(meta => meta
                            .SetValue("OwnerId", ownerId)));
                throw;
            }
        }

        public async Task<PaymentInstrument> GetMethodByIdAsync(Guid mid, Guid ownerId, Guid id,
            CancellationToken cancellationToken)
        {
            using var workspan = Workspan.Start<PaymentInstrumentsService>();

            try
            {
                workspan.Log.Information(
                    $"ENTERED: PaymentInstrumentsService > GetMethodByIDAsync > OwnerId: {ownerId} Id: {id}");

                var merchant = await GetMerchantAsync(mid);

                var defaultGateway = merchant.RelatedGateways?.SingleOrDefault(x => x.Default);
                if (defaultGateway is null)
                {
                    workspan.Log.Information(
                        $"IN: PaymentService > Sale > MERCHANT Gateway is NULL Mid: {mid}");

                    ArgumentNullException.ThrowIfNull(defaultGateway, "defaultGateway is null");
                }

                var pi = await _context.PaymentInstruments.SingleOrDefaultAsync(x =>
                        x.Merchant.Mid == mid && x.Id == id && x.OwnerId == ownerId && x.IsActive,
                    cancellationToken: cancellationToken);

                //TODO add payment instrument verified check
                //TODO add payment instrument locked check

                //ArgumentNullException.ThrowIfNull("paymentInstrument is null");
                return pi;
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    $"EXCEPTION: PaymentInstrumentsService > GetMethodByIDAsync > OwnerId: {ownerId} Id: {id}");
                await _activityService.CreateActivityAsync(
                    PaymentInstrumentErrorActivities.PaymentInstrument_GetMethodById_Error,
                    set => set
                        .TenantId(mid)
                        .Meta(meta => meta
                            .SetValue("Id", id)
                            .SetValue("OwnerId", ownerId)));
                throw;
            }
        }

        public async Task<PaymentInstrument> GetMethodByIdAsync(Guid mid, Guid id,
            CancellationToken cancellationToken)
        {
            using var workspan = Workspan.Start<PaymentInstrumentsService>();

            try
            {
                workspan.Log.Information(
                    $"ENTERED: PaymentInstrumentsService > GetMethodByIDAsync > Merchant: {mid} Id: {id}");

                var pi = await _context.PaymentInstruments.FirstOrDefaultAsync(x =>
                    x.Merchant.Mid == mid && x.Id == id && x.IsActive, cancellationToken: cancellationToken);

                ArgumentNullException.ThrowIfNull(pi, "paymentInstrument is null");
                //var spreedlyPi = await _spreedly.GetPaymentInstrumentAsync(pi.MerchantId, pi.Id,CancellationToken);
                return pi;
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    $"EXCEPTION: PaymentInstrumentsService > GetMethodByIDAsync > Merchant: {mid} Id: {id}");
                await _activityService.CreateActivityAsync(
                    PaymentInstrumentErrorActivities.PaymentInstrument_GetMethodById_Error,
                    set => set
                        .TenantId(mid)
                        .Meta(meta => meta
                            .SetValue("Id", id)));
                throw;
            }
        }

        public async Task DeleteAsync(Guid merchantId, Guid id, CancellationToken token, string modifiedBy = null)
        {
            // _context.PaymentInstruments.Remove(new PaymentInstrument
            // {
            //     Merchant =
            //         new Merchant()
            //         {
            //             Id = merchantId
            //         },
            //     Id = id
            // });

            var entityToDelete = await _context.PaymentInstruments
                .Where(x => x.Id == id && x.Merchant.Id == merchantId)
                .SingleOrDefaultAsync();

            await DeleteAsync(entityToDelete, token, modifiedBy);
        }

        public async Task DeleteAsync(PaymentInstrument entity, CancellationToken token, string modifiedBy = null)
        {
            if (entity != null)
            {
                _context.PaymentInstruments.Remove(entity);

                await _context.SaveChangesAsync(token);
            }
        }

        // public async Task<PaymentInstrument> GetMethodByTokenAsync(Guid mid, Guid ownerId, string token,
        //     CancellationToken cancellationToken)
        // {
        //     _logger.LogInformation(
        //         "ENTERED: PaymentInstrumentsService > GetMethodByTokenAsync > MID: {Mid}  OwnerId: {OwnerId}", mid,
        //         ownerId);
        //     try
        //     {
        //         var merchant = await GetMerchantAsync(mid);
        //
        //         var defaultGateway = merchant.RelatedGateways?.SingleOrDefault(x => x.Default);
        //         if (defaultGateway is null)
        //         {
        //             _logger.LogInformation(
        //                 $"IN: PaymentService > GetMethodByTokenAsync > Gateway is NULL Mid: {mid}");
        //
        //             ArgumentNullException.ThrowIfNull(defaultGateway, "defaultGateway is null");
        //         }
        //
        //
        //         var pi = await _context.PaymentInstruments.SingleOrDefaultAsync(
        //             x => x.Merchant.Mid == mid && x.Token == token && x.OwnerId == ownerId && x.IsActive,
        //             cancellationToken);
        //
        //         if (pi != null)
        //             return pi;
        //
        //         var spreedlyPi = await _spreedly.GetPaymentInstrumentAsync(
        //             merchant.SpreedlyEnvironmentKey,
        //             merchant.SpreedlySecretKey,
        //             mid,
        //             token, cancellationToken);
        //
        //         if (spreedlyPi is null)
        //         {
        //             _logger.LogInformation(
        //                 $"IN: PaymentService > GetMethodByTokenAsync > spreedlyPi is NULL Mid: {mid}");
        //
        //             //ArgumentNullException.ThrowIfNull(spreedlyPi, "spreedlyPi is null");
        //             return null;
        //         }
        //
        //         var instrument = await SavePaymentMethodAsync(mid, new CreateNewPaymentInstrumentDTO
        //         {
        //             First_Name = spreedlyPi.payment_method.first_name,
        //             Last_Name = spreedlyPi.payment_method.last_name,
        //             Number = spreedlyPi.payment_method.token,
        //             Month = spreedlyPi.payment_method.month,
        //             Year = spreedlyPi.payment_method.year
        //         }, tokenized: true, cancellationToken);
        //
        //         pi = await _context.PaymentInstruments.SingleOrDefaultAsync(
        //             x => x.Merchant.Mid == mid && x.Id == instrument.Id && x.OwnerId == ownerId && x.IsActive,
        //             cancellationToken);
        //
        //         return pi;
        //     }
        //     catch (Exception e)
        //     {
        //         _logger.LogError(e,
        //             "EXCEPTION: PaymentInstrumentsService > GetMethodByTokenAsync > MID: {Mid}  OwnerId: {OwnerId}",
        //             mid,
        //             ownerId);
        //
        //         throw;
        //     }
        // }

        public async Task<PaymentInstrument> GetMethodByTokenAsync(Guid mid, string token,
            CancellationToken cancellationToken)
        {
            using var workspan = Workspan.Start<PaymentInstrumentsService>();

            workspan.Log.Information(
                "ENTERED: PaymentInstrumentsService > GetMethodByTokenAsync > MID: {Mid}", mid);
            try
            {
                return await _context.PaymentInstruments.SingleOrDefaultAsync(
                    x => x.Merchant.Mid == mid && x.Token == token && x.IsActive,
                    cancellationToken);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: PaymentInstrumentsService > GetMethodByTokenAsync > MID: {Mid}", mid);

                throw;
            }
        }

        public async Task<Guid> CreateBankAccountAsync(Guid mid, CreateBankAccountDTO payload)
        {
            using var workspan = Workspan.Start<PaymentInstrumentsService>()
                .Baggage("Mid", mid)
                .Payload(payload)
                .LogEnterAndExit();

            PaymentInstrument instrument = null;
            try
            {
                var merchant = await GetMerchantAsync(mid);

                instrument = new PaymentInstrument
                {
                    Type = PaymentMethodType.ECheck,
                    OwnerId = payload.OwnerId,
                    IsVerified = payload.IsVerified,
                    IsActive = true,
                    Token = payload.Token,
                    AccountHolderFirstName = payload.AccountHolderFirstName,
                    AccountHolderLastName = payload.AccountHolderLastName,
                    AccountHolderName = payload.FullName,
                    AccountNumber = payload.AccountNumber,
                    RoutingNumber = payload.RoutingNumber,
                    AccountType = payload.AccountType,
                    AccountHolderType = payload.AccountHolderType,
                    BankName = payload.BankName,
                    Country = payload.Country,
                    Meta = JsonConvert.SerializeObject(payload),
                    Merchant = merchant,
                };
                var pid = await _context.PaymentInstruments.AddAsync(instrument);

                await _context.SaveChangesAsync();

                return pid.Entity.Id;
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                throw;
            }
        }

        // public async Task<Guid> CreateVerifiedBankAccount(Guid mid, CreateBankAccountDTO payload)
        // {
        //     using var workspan = Workspan.Start<PaymentInstrumentsService>();
        //     try
        //     {
        //         workspan.Log.Information(
        //             $"ENTERED: PaymentInstrumentsService > CreateBankAccount > OwnerId: {mid} method: {JsonConvert.SerializeObject(payload)}");
        //         var merchant = await GetMerchantAsync(mid);
        //
        //         var pid = await _context.PaymentInstruments.AddAsync(new PaymentInstrument
        //         {
        //             Type = PaymentMethodType.ECheck,
        //             OwnerId = payload.OwnerId,
        //             IsVerified = payload.IsVerified,
        //             IsActive = true,
        //             Token = payload.Token,
        //             AccountHolderFirstName = payload.AccountHolderFirstName,
        //             AccountHolderLastName = payload.AccountHolderLastName,
        //             AccountHolderName = payload.FullName,
        //             AccountNumber = payload.AccountNumber,
        //             RoutingNumber = payload.RoutingNumber,
        //             AccountType = payload.AccountType,
        //             AccountHolderType = payload.AccountType,
        //             BankName = payload.BankName,
        //             Country = payload.Country,
        //             Meta = JsonConvert.SerializeObject(payload),
        //             Merchant = merchant,
        //         });
        //
        //         await _context.SaveChangesAsync();
        //
        //         return pid.Entity.Id;
        //     }
        //     catch (Exception e)
        //     {
        //         workspan.Log.Error(e,
        //             "EXCEPTION: PaymentInstrumentsService > CreateBankAccount > OwnerId: {Mid} method: {SerializeObject}",
        //             mid, JsonConvert.SerializeObject(payload));
        //         throw;
        //     }
        // }

        public async Task<CreatePaymentInstrumentDTO> SavePaymentMethodAsync(
            Guid mid,
            CreateNewPaymentInstrumentDTO payload,
            bool tokenized = false,
            bool retain = true,
            CancellationToken cancellationToken = default)
        {
            using var workspan = Workspan.Start<PaymentInstrumentsService>();

            var response = new CreatePaymentInstrumentDTO();
            try
            {
                workspan.Log.Information(
                    $"ENTERED: PaymentInstrumentsService > SavePaymentMethodAsync > OwnerId: {mid} method: {JsonConvert.SerializeObject(payload)}");

                var merchant = await GetMerchantAsync(mid);
                PaymentInstrument paymentInstrument;
                CreatePaymentInstrumentResponse method;
                EntityEntry<PaymentInstrument> createdInstrument;
                if (!tokenized)
                {
                    method = await _spreedly.CreatePaymentInstrumentAsync(
                        merchant.SpreedlyEnvironmentKey,
                        merchant.SpreedlySecretKey,
                        merchant.Mid, new CreatePaymentInstrumentRequest()
                        {
                            PaymentMethod = new NewPaymentMethod
                            {
                                CreditCard = new NewCreditCard
                                {
                                    FirstName = payload.First_Name,
                                    LastName = payload.Last_Name,
                                    Number = payload.Number,
                                    VerificationValue = payload.Verification_Value,
                                    Month = payload.Month.ToString(),
                                    Year = payload.Year.ToString(),
                                },
                                Retained = true
                            }
                        }, CancellationToken.None);

                    if (!method.Success)
                    {
                        response.AddError(Consts.PaymentInstrumentCannotBeCreated,
                            nameof(Consts.PaymentInstrumentCannotBeCreated));
                        //return response;
                        throw new Exception(Consts.PaymentInstrumentCannotBeCreated);
                    }

                    paymentInstrument = _mapper.Map<PaymentInstrument>(payload);
                    paymentInstrument.AccountLastUpdatedAt = payload.AccountLastUpdatedAt;
                    paymentInstrument.Merchant = merchant;

                    paymentInstrument.Token = method.Transaction?.PaymentMethod.Token;
                    paymentInstrument.Last4 = method.Transaction?.PaymentMethod.LastFourDigits;
                    paymentInstrument.Bin = method.Transaction?.PaymentMethod.FirstSixDigits;
                    paymentInstrument.CardHolderFirstName = method.Transaction?.PaymentMethod.FirstName;
                    paymentInstrument.CardHolderLastName = method.Transaction?.PaymentMethod.LastName;
                    paymentInstrument.CardNumberMasked = method.Transaction?.PaymentMethod.Number;
                    paymentInstrument.ExpirationMonth = method.Transaction?.PaymentMethod.Month.ToString();
                    paymentInstrument.ExpirationYear = method.Transaction?.PaymentMethod.Year.ToString();
                    paymentInstrument.Type = PaymentMethodType.Credit;
                    paymentInstrument.Meta = JsonConvert.SerializeObject(method);

                    paymentInstrument.IsActive = true;
                    paymentInstrument.IsDeleted = false;
                    createdInstrument =
                        await AddPaymentInstrumentToDatabase(mid, paymentInstrument, cancellationToken);
                }
                else
                {
                    paymentInstrument = _mapper.Map<PaymentInstrument>(payload);
                    paymentInstrument.Merchant = merchant;
                    paymentInstrument.IsActive = true;
                    paymentInstrument.IsDeleted = false;
                    paymentInstrument.AccountLastUpdatedAt = payload.AccountLastUpdatedAt;

                    if (retain)
                    {
                        var retainedMethod = await _spreedly.RetainPaymentInstrumentAsync(mid,
                            merchant.SpreedlyEnvironmentKey,
                            merchant.SpreedlySecretKey, payload.Number, cancellationToken);

                        if (retainedMethod == null)
                        {
                            workspan.Log.Information(
                                $"IN: PaymentService > SavePaymentMethodAsync > retainedMethod is NULL Mid: {mid}");

                            ArgumentNullException.ThrowIfNull(retainedMethod, "retainedMethod is null");
                        }

                        if (!retainedMethod.Success)
                        {
                            workspan.Log.Information(
                                $"IN: PaymentService > SavePaymentMethodAsync > Payment instrument not found. Mid: {mid}");
                            response.AddError(Consts.PaymentInstrumentNotFound,
                                nameof(Consts.PaymentInstrumentNotFound));
                            throw new Exception(Consts.PaymentInstrumentNotFound);
                        }


                        paymentInstrument.Token = retainedMethod.Transaction.PaymentMethod.Token;
                        paymentInstrument.Last4 = retainedMethod.Transaction.PaymentMethod.LastFourDigits;
                        paymentInstrument.Bin = retainedMethod.Transaction.PaymentMethod.FirstSixDigits;
                        paymentInstrument.CardHolderFirstName = retainedMethod.Transaction.PaymentMethod.FirstName;
                        paymentInstrument.CardHolderLastName = retainedMethod.Transaction.PaymentMethod.LastName;
                        paymentInstrument.CardNumberMasked = retainedMethod.Transaction.PaymentMethod.Number;
                        paymentInstrument.ExpirationMonth =
                            retainedMethod.Transaction.PaymentMethod.Month.ToString();
                        paymentInstrument.ExpirationYear = retainedMethod.Transaction.PaymentMethod.Year.ToString();
                        paymentInstrument.Type = PaymentMethodType.Credit;
                        paymentInstrument.Meta = JsonConvert.SerializeObject(retainedMethod);
                    }
                    else
                    {
                        paymentInstrument.Token = payload.Number;
                        paymentInstrument.Last4 = payload.Masked?.GetLast(4);
                        paymentInstrument.Bin = payload.Bin;
                        paymentInstrument.CardHolderFirstName = payload.First_Name;
                        paymentInstrument.CardHolderLastName = payload.Last_Name;
                        paymentInstrument.CardNumberMasked = payload.Masked;
                        paymentInstrument.ExpirationMonth = payload.Month.ToString();
                        paymentInstrument.ExpirationYear = payload.Year.ToString();
                        paymentInstrument.Type = payload.PaymentInstrumentType;
                        paymentInstrument.Meta = JsonConvert.SerializeObject(payload);
                        paymentInstrument.AccountLastUpdatedAt = payload.AccountLastUpdatedAt;
                    }

                    //fill fingerprint
                    // paymentInstrument.Fingerprint = VaultHelper.GetCardFingerprint(payload.Number);

                    createdInstrument =
                        await AddPaymentInstrumentToDatabase(mid, paymentInstrument, cancellationToken);
                }


                await _publisher.Publish(new PaymentInstrumentSaved { }, cancellationToken);

                response.Id = createdInstrument.Entity.Id;
                return response;
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    $"EXCEPTION: PaymentInstrumentsService > SavePaymentMethodAsync > OwnerId: {mid} method: {JsonConvert.SerializeObject(payload)}");

                await _publisher.Publish(new PaymentInstrumentSavingFailed {Error = JsonConvert.SerializeObject(e)},
                    cancellationToken);
                throw;
            }
        }

        public async Task<UpdatePaymentInstrumentResultDTO> UpdatePaymentMethodAsync(Guid mid,
            UpdatePaymentInstrumentDTO request,
            CancellationToken cancellationToken)
        {
            throw new NotImplementedException();

            using var workspan = Workspan.Start<PaymentInstrumentsService>();
            try
            {
                workspan.Log.Information(
                    $"ENTERED: PaymentInstrumentsService > UpdatePaymentMethodAsync > OwnerId: {mid} method: {JsonConvert.SerializeObject(request)}");

                var merchant = await GetMerchantAsync(mid);

                var paymentInstrument = await _context.PaymentInstruments
                    .Include(x => x.Merchant)
                    .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

                if (paymentInstrument == null)
                    throw new FlexChargeException("Payment instrument not found");

                var saved = await _context.SaveChangesAsync();
                if (saved == 0)
                {
                    workspan.Log.Information(
                        $"IN: PaymentInstrumentsService > UpdatePaymentMethodAsync > Payment instrument not saved. Mid: {mid}");
                    return new UpdatePaymentInstrumentResultDTO
                    {
                        Result = null,
                        Status = null,
                        StatusCode = null,
                        CustomProperties = null
                    };
                }

                return new UpdatePaymentInstrumentResultDTO
                {
                    Result = null,
                    Status = null,
                    StatusCode = null,
                    CustomProperties = null
                };
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: PaymentInstrumentsService > CreateBankAccount > OwnerId: {Mid} method: {SerializeObject}",
                    mid, JsonConvert.SerializeObject(request));
                throw;
            }
        }

        private async Task<EntityEntry<PaymentInstrument>> AddPaymentInstrumentToDatabase(Guid mid,
            PaymentInstrument mappedMethod, CancellationToken cancellationToken)
        {
            // //await DeactivateAllPayersPaymentMethodsButLastAsync(mid: mid, payerId: mappedMethod.OwnerId, cancellationToken);
            // // Deleting before adding new sow we always have no more than 1 PaymentInstrument (so SingleOrAsync() will not throw exception in other code in a parallel thread)
            // await DeleteAllPayersPaymentMethodsAsync(mid: mid, payerId: mappedMethod.OwnerId,
            //     cancellationToken);
            EntityEntry<PaymentInstrument> createdInstrument = null;
            mappedMethod.CardBrand = _cardBrandDetector.GetBrand(mappedMethod.Bin).ToString();
            createdInstrument = await _context.PaymentInstruments.AddAsync(mappedMethod, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);


            return createdInstrument;
        }


        public async Task DeactivateAllPayersPaymentMethodsButLastAsync(Guid mid, Guid payerId,
            CancellationToken cancellationToken)
        {
            var paymentMethods = await GetPaymentMethodsAsync(mid, payerId, cancellationToken);

            if (paymentMethods.Any())
            {
                var lastPaymentMethod = (from t in paymentMethods
                    where t.OwnerId == payerId && t.Merchant.Id == mid
                    orderby t.CreatedOn descending
                    select t.Id).FirstOrDefault();

                foreach (var method in paymentMethods)
                {
                    if (method.Id != lastPaymentMethod)
                    {
                        await DeleteAsync(method, cancellationToken);
                    }
                }
            }
        }

        public async Task DeleteAllPayersPaymentMethodsAsync(Guid mid, Guid payerId,
            CancellationToken cancellationToken)
        {
            var paymentMethods = await GetPaymentMethodsAsync(mid, payerId, cancellationToken);

            if (paymentMethods.Any())
            {
                var lastPaymentMethod = (from t in paymentMethods
                    where t.OwnerId == payerId && t.Merchant.Id == mid
                    orderby t.CreatedOn descending
                    select t.Id).FirstOrDefault();

                foreach (var method in paymentMethods)
                {
                    if (method.Id != lastPaymentMethod)
                    {
                        await DeleteAsync(method, cancellationToken);
                    }
                }
            }
        }

        private async Task<Merchant> GetMerchantAsync(Guid mid)
        {
            if (mid.Equals(Guid.Empty))
                throw new Exception($"Merchant id {mid} is empty");

            var merchant = await _context.Merchants.Include(x => x.RelatedGateways).SingleOrDefaultAsync(x =>
                x.Mid == mid);

            ArgumentNullException.ThrowIfNull(merchant, $"Merchant id {mid} Not found");
            return merchant;
        }

        public async Task<PaymentInstrument> AdminGetByTokenAsync(string token, CancellationToken cancellationToken)
        {
            using var workspan = Workspan.Start<PaymentInstrumentsService>()
                .Baggage("Token", token);

            try
            {
                workspan.Log.Information("Looking for payment instrument: Token:{token}", token);

                return await _context.PaymentInstruments
                    .OrderByDescending(x => x.CreatedOn)
                    .FirstOrDefaultAsync(x => x.Token == token);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }

        public async Task<PaymentInstrument> GetByTokenAsync(string token, Guid? mid, Guid? pid,
            CancellationToken cancellationToken)
        {
            using var workspan = Workspan.Start<PaymentInstrumentsService>()
                .Baggage("Mid", mid)
                .Baggage("Token", token);

            try
            {
                workspan.Log.Information("Looking for payment instrument: Token:{token}, Mid:{mid}", token, mid);

                var paymentInstruments = _context.PaymentInstruments
                    .Include(x => x.Merchant)
                    .OrderByDescending(x => x.CreatedOn)
                    .AsQueryable();

                if (mid != null && mid != Guid.Empty)
                {
                    paymentInstruments = paymentInstruments.Where(x => x.Merchant.Mid == mid);
                }

                if (pid != null && pid != Guid.Empty)
                {
                    paymentInstruments = paymentInstruments.Where(x =>
                        x.Merchant.Pid == pid || x.Merchant.IntegrationPartnerId == pid);
                }

                return await paymentInstruments.FirstOrDefaultAsync(x => x.Token == token);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                await _activityService.CreateActivityAsync(
                    PaymentInstrumentErrorActivities.PaymentInstrument_GetMethodByToken_Error,
                    set => set
                        .TenantId(mid)
                        .Meta(meta => meta
                            .SetValue("Token", token)));

                throw;
            }
        }
    }
}