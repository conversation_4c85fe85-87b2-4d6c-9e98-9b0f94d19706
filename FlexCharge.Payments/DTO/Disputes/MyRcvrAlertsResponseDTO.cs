using System.Collections.Generic;
using CsvHelper.Configuration.Attributes;

namespace FlexCharge.Payments.DTO;

public class MyRcvrAlertsResponseDTO
{
    public string Message { get; set; }
    public MyRcvrAlertsResponseDataDTO data { get; set; }
}

public class MyRcvrAlertsResponseDataDTO
{
    public int Count { get; set; }
    public List<MyRcvrAlertsResponseItemDTO> results { get; set; }
}

public class MyRcvrAlertsResponseItemDTO
{
    public string crm_transaction_id { get; set; }
    public string alert_time { get; set; }
    public string transaction_amount { get; set; }
    public string currency { get; set; }
    public string transaction_date { get; set; }
    public string alert_type { get; set; }
    public string card_type { get; set; }
    public string card_first6 { get; set; }
    public string card_last4 { get; set; }
}

