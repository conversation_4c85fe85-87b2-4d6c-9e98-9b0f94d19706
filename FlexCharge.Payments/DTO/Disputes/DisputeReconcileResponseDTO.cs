using System;
using System.Collections.Generic;

namespace FlexCharge.Payments.DTO;

public class DisputeReconcileResponseDTO
{
    public bool Success { get; set; }
    public string? Message { get; set; }
    public int SuccessCount { get; set; }
    public int FailedCount { get; set; }
    public int TotalCount { get; set; }
    public List<DisputeReconcileErrorsDTO> Errors { get; set; }
}

public class DisputeReconcileErrorsDTO
{
    public Guid DisputeQueueItemId { get; set; }
    public string Error { get; set; }
}