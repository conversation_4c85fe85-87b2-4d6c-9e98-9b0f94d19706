using System;

namespace FlexCharge.Payments.DTO;

public class PayoutQueryDTO
{
    public Guid MerchantId { get; set; }
    //public DateTime From { get; set; }
    //public DateTime To { get; set; }

    //public int PageSize { get; set; }
    //public int PageNumber { get; set; }
}

public class PayoutDTO
{
    public Guid PayoutId { get; set; }
    
    public DateTime PayoutDate { get; set; }
    public DateTime ScheduledFor { get; set; }
    public DateTime CapturePeriodFrom { get; set; }
    public DateTime CapturePeriodTo { get; set; }

    public bool IsPosted { get; set; }
    public bool CanUnpost { get; set; }
    public string Status { get; set; }
    public Guid Mid { get; set; }
    public decimal TotalValue { get; set; }
    public decimal FlexChargeFees { get; set; }
    // public decimal Tax { get; set; }
    public decimal Returns { get; set; }
    public decimal Chargebacks { get; set; }
    public decimal PayoutAmount { get; set; }

    public string CurrencyCode { get; set; }
    public string CurrencySymbol { get; set; }
}