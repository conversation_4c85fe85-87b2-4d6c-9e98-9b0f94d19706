using System;
using System.Collections.Generic;
using System.Linq;
using ShereSoft;

namespace FlexCharge.PaymentsUtils;

public class GeoHelper
{
    public static Dictionary<string, string> StateCodeToStateName = new(StringComparer.InvariantCultureIgnoreCase);
    public static Dictionary<string, string> StatenameToStateCode = new(StringComparer.InvariantCultureIgnoreCase);

    static GeoHelper()
    {
        StateCodeToStateName.Add("AL", "Alabama");
        StateCodeToStateName.Add("AK", "Alaska");
        StateCodeToStateName.Add("AZ", "Arizona");
        StateCodeToStateName.Add("AR", "Arkansas");
        StateCodeToStateName.Add("CA", "California");
        StateCodeToStateName.Add("CO", "Colorado");
        StateCodeToStateName.Add("CT", "Connecticut");
        StateCodeToStateName.Add("DE", "Delaware");
        StateCodeToStateName.Add("DC", "District of Columbia");
        StateCodeToStateName.Add("FL", "Florida");
        StateCodeToStateName.Add("GA", "Georgia");
        StateCodeToStateName.Add("HI", "Hawaii");
        StateCodeToStateName.Add("ID", "Idaho");
        StateCodeToStateName.Add("IL", "Illinois");
        StateCodeToStateName.Add("IN", "Indiana");
        StateCodeToStateName.Add("IA", "Iowa");
        StateCodeToStateName.Add("KS", "Kansas");
        StateCodeToStateName.Add("KY", "Kentucky");
        StateCodeToStateName.Add("LA", "Louisiana");
        StateCodeToStateName.Add("ME", "Maine");
        StateCodeToStateName.Add("MD", "Maryland");
        StateCodeToStateName.Add("MA", "Massachusetts");
        StateCodeToStateName.Add("MI", "Michigan");
        StateCodeToStateName.Add("MN", "Minnesota");
        StateCodeToStateName.Add("MS", "Mississippi");
        StateCodeToStateName.Add("MO", "Missouri");
        StateCodeToStateName.Add("MT", "Montana");
        StateCodeToStateName.Add("NE", "Nebraska");
        StateCodeToStateName.Add("NV", "Nevada");
        StateCodeToStateName.Add("NH", "New Hampshire");
        StateCodeToStateName.Add("NJ", "New Jersey");
        StateCodeToStateName.Add("NM", "New Mexico");
        StateCodeToStateName.Add("NY", "New York");
        StateCodeToStateName.Add("NC", "North Carolina");
        StateCodeToStateName.Add("ND", "North Dakota");
        StateCodeToStateName.Add("OH", "Ohio");
        StateCodeToStateName.Add("OK", "Oklahoma");
        StateCodeToStateName.Add("OR", "Oregon");
        StateCodeToStateName.Add("PA", "Pennsylvania");
        StateCodeToStateName.Add("RI", "Rhode Island");
        StateCodeToStateName.Add("SC", "South Carolina");
        StateCodeToStateName.Add("SD", "South Dakota");
        StateCodeToStateName.Add("TN", "Tennessee");
        StateCodeToStateName.Add("TX", "Texas");
        StateCodeToStateName.Add("UT", "Utah");
        StateCodeToStateName.Add("VT", "Vermont");
        StateCodeToStateName.Add("VA", "Virginia");
        StateCodeToStateName.Add("WA", "Washington");
        StateCodeToStateName.Add("WV", "West Virginia");
        StateCodeToStateName.Add("WI", "Wisconsin");
        StateCodeToStateName.Add("WY", "Wyoming");
        StatenameToStateCode =
            StateCodeToStateName.ToDictionary(x => x.Value, x => x.Key, StringComparer.InvariantCultureIgnoreCase);
    }

    public static string GetStateCode(string zipcode)
    {
        try
        {
            var cityAndState = Zip2City.GetClosestCityState(zipcode); // returns null when there's no match
            if (cityAndState == null)
            {
                return null;
            }

            //   var city = cityAndState[0];  // "BEVERLY HILLS"
            var state = cityAndState[1]; // "CA"
            return state;
        }
        catch (Exception e)
        {
            return null;
        }
    }

    public static string GetStateCode(string state, string zipcode)
    {
        if (state is not null && StateCodeToStateName.ContainsKey(state)) return state;
        if (state is not null && StatenameToStateCode.TryGetValue(state, out var statecode)) return statecode;
        if (string.IsNullOrWhiteSpace(zipcode)) return "";
        return GetStateCode(zipcode);
    }
}