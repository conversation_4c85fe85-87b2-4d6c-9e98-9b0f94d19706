using System;
using System.Collections.Generic;
using AutoMapper;
using FlexCharge.Common;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Activities;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Entities.JsonbModels;
using FlexCharge.Payments.Services.DisputeServices.Models;
using FlexCharge.Payments.Services.PaymentServices.Gateways;
using FlexCharge.Utils;
using Transaction = FlexCharge.Payments.Entities.Transaction;

namespace FlexCharge.Payments
{
    public class AutoMappings : Profile
    {
        public AutoMappings()
        {
            CreateMap<Transaction, TransactionCreateDTO>();
            CreateMap<TransactionCreateDTO, Transaction>();

            CreateMap<Gateway, QueryMerchantGatewaysDTO>()
                .ForMember(dest => dest.IsDefault, opt => opt.MapFrom(src => src.Default));


            CreateMap<CreateNewPaymentInstrumentDTO, PaymentInstrument>()
                .ForMember(dest => dest.Number, opt => opt.Ignore())
                .ForMember(dest => dest.CardHolderName, opt => opt.MapFrom(src => $"{src.First_Name} {src.Last_Name}"));

            CreateMap<IPagedList<SettlementDTO>, PagedDTO<SettlementDTO>>()
                .ConvertUsing(new PagedListTypeConverter<SettlementDTO, SettlementDTO>());

            CreateMap<TransactionDTO, TransactionDTO>()
                .ForMember(dest => dest.CurrencySymbol, opt => opt.MapFrom(src => GetCurrencySymbol(src.Currency)));
            CreateMap<IPagedList<TransactionDTO>, PagedDTO<TransactionDTO>>()
                .ConvertUsing(new PagedListTypeConverter<TransactionDTO, TransactionDTO>());

            CreateMap<UpdatePaymentProvidersDTO, SupportedGateway>();
            CreateMap<SupportedGateway, UpdatePaymentProvidersDTO>();

            //CreateMap<Activity, IActivity>().ReverseMap();
            CreateMap<PaymentInstrument, PaymentInstrumentByTokenDTO>().ReverseMap();

            CreateMap<DisputeActivity, DisputeActivityResponseDTO>();

            CreateMap<Dispute, DisputeResponseDTO.DisputeItemQueryDTO>()
                .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => Utils.Formatters.IntToDecimal(src.Amount)))
                .ForMember(dest => dest.DisputeAmount, opt => opt.MapFrom(src => Utils.Formatters.IntToDecimal(src.DisputeAmount)))
                .ForMember(dest => dest.IsRefunded, opt => opt.MapFrom(src => src.RefundTransactionId != Guid.Empty))
                .ForMember(dest => dest.CurrencySymbol, opt => opt.MapFrom(src => GetCurrencySymbol(src.Currency)));
            CreateMap<IPagedList<Dispute>, PagedDTO<DisputeResponseDTO.DisputeItemQueryDTO>>()
                .ConvertUsing(new PagedListTypeConverter<Dispute, DisputeResponseDTO.DisputeItemQueryDTO>());


            CreateMap<IPagedList<QueuesReponseDTO.DisputeQueueQueryDTO>,
                    PagedDTO<QueuesReponseDTO.DisputeQueueQueryDTO>>()
                .ConvertUsing(
                    new PagedListTypeConverter<QueuesReponseDTO.DisputeQueueQueryDTO,
                        QueuesReponseDTO.DisputeQueueQueryDTO>());

            CreateMap<DisputeQueueItem, DisputeResponseDTO.DisputeItemQueryDTO>()
                .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => Utils.Formatters.IntToDecimal(src.Amount)))
                .ForMember(dest => dest.DisputeAmount, opt => opt.MapFrom(src => Utils.Formatters.IntToDecimal(src.DisputeAmount)))
                .ForMember(dest => dest.IsRefunded, opt => opt.MapFrom(src => src.RefundTransactionId != Guid.Empty))
                .ForMember(dest => dest.CurrencySymbol, opt => opt.MapFrom(src => GetCurrencySymbol(src.Currency)));
            CreateMap<IPagedList<DisputeQueueItem>, PagedDTO<DisputeResponseDTO.DisputeItemQueryDTO>>()
                .ConvertUsing(new PagedListTypeConverter<DisputeQueueItem, DisputeResponseDTO.DisputeItemQueryDTO>());


            CreateMap<PotentialMatch, PotentialMatchDTO>();
            CreateMap<PotentialMatchDTO, PotentialMatch>();
            CreateMap<DisputeQueueItem, DisputeQueueItemDTO>()
                .ForMember(dest => dest.ExternalOrderId, opt => opt.MapFrom(src => src.OrderId))
                .ForMember(dest => dest.OrderId, opt => opt.Ignore())
                .ForMember(dest => dest.TransactionId, opt => opt.MapFrom(src => src.TransactionId.HasValue ? src.TransactionId.Value : Guid.Empty));
            
            CreateMap<DisputeQueueItemDTO, DisputeQueueItem>()
                .ForMember(dest => dest.OrderId, opt => opt.MapFrom(src => src.OrderId != null && src.OrderId != Guid.Empty ? src.OrderId.ToString() : src.ExternalOrderId));

            CreateMap<DisputeQueueItem, QueueItemsResponseDTO.QueueItemsQueryDTO>()
                .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => Utils.Formatters.IntToDecimal(src.Amount)))
                .ForMember(dest => dest.CurrencySymbol, opt => opt.MapFrom(src => GetCurrencySymbol(src.Currency)));
            CreateMap<IPagedList<DisputeQueueItem>, PagedDTO<QueueItemsResponseDTO.QueueItemsQueryDTO>>()
                .ConvertUsing(new PagedListTypeConverter<DisputeQueueItem, QueueItemsResponseDTO.QueueItemsQueryDTO>());
            
            CreateMap<IPagedList<QueueItemsResponseDTO.QueueItemsQueryDTO>, PagedDTO<QueueItemsResponseDTO.QueueItemsQueryDTO>>()
                .ConvertUsing(new PagedListTypeConverter<QueueItemsResponseDTO.QueueItemsQueryDTO, QueueItemsResponseDTO.QueueItemsQueryDTO>());

            CreateMap<IPagedList<QueueItemsResponseDTO.QueueItemsQueryDTO>,
                    PagedDTO<QueueItemsResponseDTO.QueueItemsQueryDTO>>()
                .ConvertUsing(
                    new PagedListTypeConverter<QueueItemsResponseDTO.QueueItemsQueryDTO,
                        QueueItemsResponseDTO.QueueItemsQueryDTO>());
            
            CreateMap<FinancialAccount, FinancialAccountDTO>()
                .ForMember(dest => dest.CurrencySymbol, opt => opt.MapFrom(src => GetCurrencySymbol(src.Currency)));
            CreateMap<IPagedList<FinancialAccountDTO>, PagedDTO<FinancialAccountDTO>>()
                .ConvertUsing(new PagedListTypeConverter<FinancialAccountDTO, FinancialAccountDTO>());
            
            CreateMap<AuditLog, AuditLogResponseDTO>();
            CreateMap<IPagedList<AuditLog>, PagedDTO<AuditLogResponseDTO>>()
                .ConvertUsing(new PagedListTypeConverter<AuditLog, AuditLogResponseDTO>());
            
            CreateMap<IPagedList<AuditLogResponseDTO>, PagedDTO<AuditLogResponseDTO>>()
                .ConvertUsing(new PagedListTypeConverter<AuditLogResponseDTO, AuditLogResponseDTO>());
        }
        
        private string GetCurrencySymbol(string currency)
        {
            if (string.IsNullOrEmpty(currency))
            {
                return string.Empty;
            }
            l18n.CurrencyTools.TryGetCurrencySymbol(currency, out var currencySymbol);
            return currencySymbol;
        }
    }
}