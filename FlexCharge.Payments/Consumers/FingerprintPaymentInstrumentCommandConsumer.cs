using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts.Commands;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.PaymentInstrumentsServices;
using FlexCharge.Payments.Services.PaymentServices;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace FlexCharge.Payments.Consumers;

public class FingerprintPaymentInstrumentCommandConsumer :
    IdempotentCommandConsumer<FingerprintPaymentInstrumentCommand, FingerprintPaymentInstrumentCommandResponse>
{
    private readonly PostgreSQLDbContext _dbContext;
    private IPaymentInstrumentsService _instruments;
    private readonly IPaymentOrchestrator _paymentOrchestrator;

    public FingerprintPaymentInstrumentCommandConsumer(
        PostgreSQLDbContext dbContext,
        IPaymentInstrumentsService instruments,
        IPaymentOrchestrator paymentOrchestrator,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _dbContext = dbContext;
        _instruments = instruments;
        _paymentOrchestrator = paymentOrchestrator;
    }

    protected override async Task<FingerprintPaymentInstrumentCommandResponse> ConsumeCommand(
        FingerprintPaymentInstrumentCommand command, CancellationToken cancellationToken)
    {
        try
        {
            Workspan
                .Baggage("Mid", command.Mid)
                .Baggage("PaymentInstrumentId", command.PaymentInstrumentID)
                .LogEnterAndExit();

            Workspan.Log.Information(
                $"ENTERED: PaymentsService > FingerprintPaymentInstrumentCommandConsumer > CorrelationID: {Context.CorrelationId} > Mid: {JsonConvert.SerializeObject(command.Mid)}");


            var fingerprintResponse = await _paymentOrchestrator.FingerprintPaymentInstrumentAsync(
                command.Mid,
                new FingerprintInstrumentRequest
                {
                    Token = command.PaymentInstrumentID,
                    UseLatestInstrument = command.UseLatestInstrument
                });

            /// prolong AccountUpdateExpiry if order expiry date ends later than the current one
            // var accountToUpdate = await _dbContext.AccountsToUpdate
            //     .FirstOrDefaultAsync(p => p.Token == command.PaymentInstrumentID);

            return new FingerprintPaymentInstrumentCommandResponse
            {
                ErrorCode = fingerprintResponse.StatusCode,
                ErrorMessage = fingerprintResponse.Status,
                Fingerprint = fingerprintResponse.Fingerprint,
                PaymentInstrumentIsActive = fingerprintResponse.PaymentInstrumentIsActive,
                //AccountUpdateExpiry=fingerprintResponse.AccountUpdateExpiry,
                AccountUpdaterResultPending = fingerprintResponse.AccountUpdaterResultPending,
                AccountUpdaterMessage = fingerprintResponse?.AccountUpdaterMessage
            };
        }
        catch (Exception e)
        {
            Workspan.RecordFatalException(e);
            throw;
        }
    }
}