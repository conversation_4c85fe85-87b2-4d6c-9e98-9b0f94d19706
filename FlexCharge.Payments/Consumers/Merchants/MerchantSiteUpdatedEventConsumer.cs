using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Payments.Consumers;

public class MerchantSiteUpdatedEventConsumer : ConsumerBase<MerchantSiteUpdatedEvent>
{
    private readonly PostgreSQLDbContext _dbContext;

    //private readonly IEmailSender _emailSender;
    private readonly IConfiguration _configuration;

    public MerchantSiteUpdatedEventConsumer(
        PostgreSQLDbContext dbContext,
        //IEmailSender emailSender, 
        IConfiguration configuration,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _dbContext = dbContext;
        //_emailSender = emailSender;
        _configuration = configuration;
    }


    protected override async Task ConsumeMessage(MerchantSiteUpdatedEvent message, CancellationToken cancellationToken)
    {
        // try
        // {
        //     _logger.LogInformation(
        //         "ENTERED: Correlation: {CorrelationId} > MerchantActivatedEventConsumer > {ApplicationId}",
        //         context.Message.Mid, context.CorrelationId);
        //
        //     JsonConvert.SerializeObject(context.Message);
        // }
        // catch (Exception e)
        // {
        //     _logger.LogError(e,
        //         $"EXCEPTION: Correlation: {context.CorrelationId} > Failed MerchantActivatedEventConsumer > {context.Message.Mid}˚");
        // }

        await Task.CompletedTask;
    }
}