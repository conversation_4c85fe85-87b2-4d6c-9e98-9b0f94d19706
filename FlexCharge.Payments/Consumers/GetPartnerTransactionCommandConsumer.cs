using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts.Commands;
using FlexCharge.Payments.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Payments.Consumers;

public class GetPartnerTransactionCommandConsumer :
    IdempotentCommandConsumer<GetPartnerTransactionStatusCommand, GetPartnerTransactionCommandStatusResponse>
{
    private readonly PostgreSQLDbContext _dbContext;

    public GetPartnerTransactionCommandConsumer(
        PostgreSQLDbContext dbContext,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _dbContext = dbContext;
    }

    protected override async Task<GetPartnerTransactionCommandStatusResponse> ConsumeCommand(
        GetPartnerTransactionStatusCommand statusCommand, CancellationToken cancellationToken)
    {
        try
        {
            if (statusCommand.TransactionId == Guid.Empty)
                throw new ArgumentException("TransactionId is empty");

            var transaction = await _dbContext.PartnerTransactions
                .FirstOrDefaultAsync(x => x.Id == statusCommand.TransactionId, cancellationToken);

            if (transaction == null)
                throw new FlexNotFoundException("Transaction not found");

            var relatedTransactions = await _dbContext.PartnerTransactions
                .Where(x => x.ParentId == transaction.Id)
                .OrderBy(x => x.CreatedOn)
                .ToListAsync(cancellationToken: cancellationToken);

            //check if relatedTransactions is DebitReturn or CreditReturn
            if (relatedTransactions.Any(x =>
                    x.Type == TransactionType.DebitReturn.ToString() ||
                    x.Type == TransactionType.CreditReturn.ToString()))
            {
                transaction.Status = TransactionStatus.Failed;
                transaction.ResponseCode = "Transaction returned";
            }

            return new GetPartnerTransactionCommandStatusResponse()
            {
                Id = transaction.Id,
                Status = transaction.Status.ToString(),
                ResponseCode = transaction.ResponseCode,
                ResponseMessage = transaction.ResponseMessage,
                ModifiedOn = transaction.ModifiedOn,
                CreatedOn = transaction.CreatedOn
            };
        }
        catch (ArgumentException e)
        {
            return new GetPartnerTransactionCommandStatusResponse()
            {
                Error = e.Message
            };
        }
        catch (FlexNotFoundException e)
        {
            return new GetPartnerTransactionCommandStatusResponse()
            {
                Error = e.Message
            };
        }
        catch (Exception e)
        {
            Workspan.RecordFatalException(e);
            throw;
        }
    }
}