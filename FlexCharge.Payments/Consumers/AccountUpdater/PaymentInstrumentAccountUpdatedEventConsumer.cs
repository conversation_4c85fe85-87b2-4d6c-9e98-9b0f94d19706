using System;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Vault;
using MassTransit;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Payments.Consumers.AccountUpdater;

public class PaymentInstrumentAccountUpdatedEventConsumer : IConsumer<PaymentInstrumentAccountUpdatedEvent>
{
    private readonly IActivityService _activityService;
    private readonly PostgreSQLDbContext _dbContext;

    public PaymentInstrumentAccountUpdatedEventConsumer(PostgreSQLDbContext dbContext,
        IActivityService activityService)
    {
        _dbContext = dbContext;
        _activityService = activityService;
    }

    public async Task Consume(ConsumeContext<PaymentInstrumentAccountUpdatedEvent> context)
    {
        await _dbContext.PaymentInstruments
            .Where(x => x.Token == context.Message.PaymentInstrumentId.ToString())
            .ExecuteUpdateAsync(statistics =>
                statistics
                    .SetProperty(s => s.Bin,
                        s => context.Message.Bin)
                    .SetProperty(s => s.Last4,
                        s => context.Message.Last4)
                    .SetProperty(s => s.CardNumberMasked,
                        s => context.Message.CardNumberMasked)
                    .SetProperty(s => s.ExpirationYear,
                        s => context.Message.ExpirationYear.ToString())
                    .SetProperty(s => s.ExpirationMonth,
                        s => context.Message.ExpirationMonth.ToString())
                    .SetProperty(s => s.ModifiedOn, s => DateTime.UtcNow)
                    //sets datetiem of last card update event
                    .SetProperty(s => s.AccountLastUpdatedAt, s =>
                        context.Message.CardIsUpdated ? DateTime.UtcNow : s.AccountLastUpdatedAt)
            );
    }
}