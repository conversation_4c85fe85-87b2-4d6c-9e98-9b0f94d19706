using System;
using System.Threading.Tasks;
using FlexCharge.Contracts.Commands;
using FlexCharge.Payments.Services.PaymentInstrumentsServices;
using FlexCharge.Payments.Services.PaymentServices;
using MassTransit;
using Microsoft.Extensions.Logging;

namespace FlexCharge.Payments.Consumers;

public class DummyCommandConsumer : IConsumer<DummyCommand>
{
    private readonly PostgreSQLDbContext _dbContext;
    private ILogger<DummyCommandConsumer> _logger;
    private IPaymentInstrumentsService _instruments;
    private readonly IPaymentOrchestrator _paymentOrchestrator;

    public DummyCommandConsumer(
        PostgreSQLDbContext dbContext,
        ILogger<DummyCommandConsumer> logger,
        IPaymentInstrumentsService instruments,
        IPaymentOrchestrator paymentOrchestrator)
    {
        _dbContext = dbContext;
        _logger = logger;
        _instruments = instruments;
        _paymentOrchestrator = paymentOrchestrator;
    }

    public async Task Consume(ConsumeContext<DummyCommand> context)
    {
        _logger.LogInformation(
            $"ENTERED: Dummy");
        try
        {
            await context.RespondAsync(new DummyCommandResponse
            {
                DummyLoad = context.Message.DummyLoad
            });
        }
        catch (Exception e)
        {
            _logger.LogError(e, $"EXCEPTION: Failed DUMMY");
            throw;
        }
    }
}