using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts.Commands;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Payments.Consumers;

public class GetPublicTokenizationKeyCommandConsumer :
    IdempotentCommandConsumer<GetPublicTokenizationKeyCommand, GetPublicTokenizationKeyCommandResponse>
{
    private readonly PostgreSQLDbContext _dbContext;

    public GetPublicTokenizationKeyCommandConsumer(
        PostgreSQLDbContext dbContext,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _dbContext = dbContext;
    }

    protected override async Task<GetPublicTokenizationKeyCommandResponse> ConsumeCommand(
        GetPublicTokenizationKeyCommand command, CancellationToken cancellationToken)
    {
        Workspan
            .Baggage("Mid", command.Mid)
            .LogEnterAndExit();

        try
        {
            var merchant = await _dbContext.Merchants.Include(x => x.RelatedGateways)
                .SingleOrDefaultAsync(x => x.Mid == command.Mid);

            if (merchant is null) Workspan.Log.Error("Merchant not found");

            return new GetPublicTokenizationKeyCommandResponse
            {
                TokenizationPublicKey = merchant?.SpreedlyEnvironmentKey
            };
        }
        catch (Exception e)
        {
            Workspan.RecordFatalException(e);
            throw;
        }
    }
}