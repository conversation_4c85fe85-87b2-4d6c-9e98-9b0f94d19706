// using System;
// using System.Threading;
// using System.Threading.Tasks;
// using FlexCharge.Contracts;
// using FlexCharge.Payments.DTO;
// using FlexCharge.Payments.Services.PaymentInstrumentsServices;
// using MassTransit;
// using Microsoft.Extensions.Logging;
// using Newtonsoft.Json;
//
// namespace FlexCharge.Payments.Consumers;
//
// public class EligibilityRequestedEventConsumer : IConsumer<EligibilityOfferInitiatedEvent>
// {
//     ILogger<EligibilityRequestedEventConsumer> _logger;
//     private readonly IPaymentInstrumentsService _piService;
//
//     public EligibilityRequestedEventConsumer(ILogger<EligibilityRequestedEventConsumer> logger,
//         IPaymentInstrumentsService piService)
//     {
//         _logger = logger;
//         _piService = piService;
//     }
//
//     /// <summary>
//     /// Saves payment method that was send with the eligibility request,
//     /// prior sending the request, a call to spreedly is made from the client and a tokenization is initiated
//     /// So card number should always be a TOKEN that was generated by the consumer
//     /// </summary>
//     /// <param name="context"></param>
//     public async Task Consume(ConsumeContext<EligibilityOfferInitiatedEvent> context)
//     {
//         try
//         {
//             _logger.LogInformation(
//                 $"ENTERED: PaymentsService > EligibilityRequestedEventConsumer > payload: {JsonConvert.SerializeObject(context)}");
//
//             var pi = await _piService.GetMethodByTokenAsync(context.Message.Mid,
//                 context.Message.PaymentMethod.CardNumber, CancellationToken.None);
//
//             string firstName, lastName;
//
//             Utils.NameHelpers.SplitToFirstAndLastName(context.Message.PaymentMethod.HolderName,
//                 out firstName, out lastName);
//             
//             if (pi == null)
//             {
//                 await _piService.SavePaymentMethodAsync(context.Message.Mid, new CreateNewPaymentInstrumentDTO
//                 {
//                     First_Name = firstName,
//                     Last_Name = lastName,
//                     Number = context.Message.PaymentMethod.CardNumber,
//                     Month = context.Message.PaymentMethod.ExpirationMonth,
//                     Year = context.Message.PaymentMethod.ExpirationYear
//                 }, true,false, CancellationToken.None);
//             }
//         }
//         catch (Exception e)
//         {
//             _logger.LogError(e,
//                 $"EXCEPTION: PaymentsService > EligibilityRequestedEventConsumer > payload: {JsonConvert.SerializeObject(context)}");
//         }
//     }
// }