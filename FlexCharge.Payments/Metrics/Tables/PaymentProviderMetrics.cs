// using System;
// using System.Threading.Tasks;
// using Amazon.DynamoDBv2;
// using FlexCharge.Common.NoSQL.DynamoDB;
// using FlexCharge.Common.Telemetry;
// using FlexCharge.Payments.Entities;
//
// namespace FlexCharge.Payments.Metrics.Tables;
//
// public sealed class PaymentProviderMetrics
// {
//     public static async Task ProcessDebitPaymentAsync(IAmazonDynamoDB dynamoDb, Gateway gateway,
//         PaymentInstrument paymentInstrument, int amount)
//     {
//         using var workspan = Workspan.Start<PaymentProviderMetrics>();
//
//         try
//         {
//             AtomicUpdatesBuilder metrics = new AtomicUpdatesBuilder();
//
//             // Update all time metrics
//             using (metrics.StartItemUpdate<ProvidersMetricsTable>(gateway.SupportedGatewayId,
//                        TimeRangeKeyFactory.CreateTimeRangeKey(TimeRangeKeyFactory.TimeRange.AllTime, DateTime.UtcNow)))
//             {
//                 UpdateMetrics(metrics);
//             }
//
//             // Update monthly metrics
//             using (metrics.StartItemUpdate<ProvidersMetricsTable>(gateway.SupportedGatewayId,
//                        TimeRangeKeyFactory.CreateTimeRangeKey(TimeRangeKeyFactory.TimeRange.Monthly, DateTime.UtcNow)))
//             {
//                 UpdateMetrics(metrics);
//             }
//
//             // Update daily metrics
//             using (metrics.StartItemUpdate<ProvidersMetricsTable>(gateway.SupportedGatewayId,
//                        TimeRangeKeyFactory.CreateTimeRangeKey(TimeRangeKeyFactory.TimeRange.Daily, DateTime.UtcNow)))
//             {
//                 UpdateMetrics(metrics);
//             }
//
//             await metrics.UpdateAsync(dynamoDb, false);
//         }
//         catch (Exception e)
//         {
//             workspan.RecordFatalException(e, "Failed to update provider metrics");
//         }
//
//         void UpdateMetrics(AtomicUpdatesBuilder metrics)
//         {
//             metrics.Add(
//                 nameof(ProvidersMetricsTable.TotalTransactionCount),
//                 +1);
//
//             metrics.Add(
//                 nameof(ProvidersMetricsTable.TotalTransactionAmount),
//                 +amount);
//
//             switch (paymentInstrument.Bin)
//             {
//                 case var bin when bin.StartsWith("5") || bin.StartsWith("2"): // MASTER CARD
//                 {
//                     metrics.Add(
//                         nameof(ProvidersMetricsTable.MasterCardTransactionsCount),
//                         +1);
//
//                     metrics.Add(
//                         nameof(ProvidersMetricsTable.MasterCardTransactionsAmount),
//                         +amount);
//                     break;
//                 }
//                 case var bin when bin.StartsWith("4"): // VISA
//                 {
//                     metrics.Add(
//                         nameof(ProvidersMetricsTable.VisaTransactionsCount),
//                         +1);
//
//                     metrics.Add(
//                         nameof(ProvidersMetricsTable.VisaTransactionsAmount),
//                         +amount);
//                     break;
//                 }
//                 default:
//                     // Other card network -> nothing to do 
//                     break;
//             }
//         }
//     }
// }

