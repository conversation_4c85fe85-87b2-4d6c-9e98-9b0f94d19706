ProviderResponseCode,ProviderResponseMessage,InternalResponseCode,InternalResponseMessage
200,,46,Bank decline
201,Decline,5,Do Not Honor
201,Do Not Honor,5,Do Not Honor
201,Do not try again,R0,Stop payment order
201,,5,Do Not Honor
202,Insufficient funds,51,Insufficient funds
202,Not sufficient funds,51,Insufficient funds
202,,51,Insufficient funds
203,Activity limit exceeded,65,Exceeds Withdrawal Frequency Limit
203,Allowable PIN tries exceeded,38,Allowable PIN tries exceeded
203,Count Exceeds Limit,N4,Cash request exceeds the issuer or approved limit
203,Daily threshold exceeded,GW01,Daily threshold exceeded
203,Exceeds withdrawal amount limit,61,Exceeds withdrawal limit
203,Exceeds withdrawal limit,61,Exceeds withdrawal limit
203,<PERSON>n tries exceeded,38,Allowable PIN tries exceeded
203,,65,Exceeds Withdrawal Frequency Limit
204,"Blocked, first used",78,"Blocked, first used (transaction is from a new card and the card wasn't properly unblocked)"
204,Issuer Declined MCC,46,Bank decline
204,No credit account,39,No credit account
204,Restricted card,36,"Restricted Card, Retain Card"
204,Transaction not permitted by acquirer,96,Gateway / Risk declines
204,Transaction not permitted by issuer,57,Transaction not permitted to cardholder
204,Transaction not permitted to cardholder,57,Transaction not permitted to cardholder
204,Violation of law,57,Transaction not permitted to cardholder
204,,57,Transaction not permitted to cardholder
220,Invalid account number,14,Invalid account number (no such number)
220,Invalid card number,14,Invalid account number (no such number)
220,Verification Data Failed,14,Invalid account number (no such number)
220,,14,Invalid account number (no such number)
221,,15,No such issuer
222,Account Closed,14,Invalid account number (no such number)
222,No account,14,Invalid account number (no such number)
222,No checking account,14,Invalid account number (no such number)
222,,15,No such issuer
223,,33,Expired card
224,,87,Bad track data (invalid CVV and/or expiry date)
225,CVV2 Declined,82,"Negative CAM, dCVV, iCVV, or CVV results"
225,CVV2 Mismatch,82,"Negative CAM, dCVV, iCVV, or CVV results"
225,Declined for CVV failure,82,"Negative CAM, dCVV, iCVV, or CVV results"
225,Incorrect CVV,82,"Negative CAM, dCVV, iCVV, or CVV results"
225,Invalid CVV,82,"Negative CAM, dCVV, iCVV, or CVV results"
225,,82,"Negative CAM, dCVV, iCVV, or CVV results"
226,,55,Incorrect PIN or PIN validation not possible
240,,1,Refer to card issuer
250,,4,Pick up card (no fraud)
251,,41,Lost card
252,,43,Stolen card
253,Pick up card - F,7,"Pick up card, special condition (fraud account)"
253,Pick up card - SF,34,Suspected fraud
253,,7,"Pick up card, special condition (fraud account)"
261,,17,Customer cancellation
262,,17,Customer cancellation
263,,48,Declined-Update cardholder data available
264,,19,Re-enter transaction
300,A card security code has never been passed for this account ,30,Format error
300,Custom descriptors are not allowed for this processor ,30,Format error
300,CVV must be 3 or 4 digits ,N7,Decline for CVV2 failure
300,Disabling Duplicate Check is not allowed for this processor ,-3,Provider exception error
300,Duplicate transaction ,94,Duplicate transmission / invoice
300,Error Processing Transaction. Please contact customer service.,-3,Provider exception error
300,Invalid Credit Card Number ,39,No credit account
300,Isracard is not accepted by this processor ,96,Gateway / Risk declines
300,Merchant descriptor URL is required for Keyed transactions with EMV-capable processors ,30,Format error
300,Specified API key not found ,-3,Provider exception error
300,The address1 field is required ,30,Format error
300,The cc payment type [Isracard] and/or currency [USD] is not accepted ,96,Gateway / Risk declines
300,The cc payment type [Mastercard] and/or currency [USD] is not accepted ,96,Gateway / Risk declines
300,The city field is required ,30,Format error
300,The Customer Name is required ,30,Format error
300,The state field is required ,30,Format error
300,The zipcode field is required ,30,Format error
300,,96,Gateway / Risk declines
400,General error,95,Generic soft decline error
400,General error: InsertUpdateCard - SqlException,48,Declined-Update cardholder data available
400,Invalid issuer.,15,No such issuer
400,Invalid routing information,12,An internal error occurred
400,Please retry,19,Re-enter transaction
400,"Please retry – Reasons for this error are one of the following: Format Error, Unable to route transaction, Switch or issuer unavailable, System Busy, Timeout",19,Re-enter transaction
400,System malfunction,12,An internal error occurred
400,,12,An internal error occurred
410,Bad Bin or Host Disconnect,3,Invalid merchant
410,Invalid bankcard merchant number,3,Invalid merchant
410,Invalid merchant ID,3,Invalid merchant
410,,3,Invalid merchant
411,File is temporarily unavailable,3,Invalid merchant
411,Invalid merchant. The merchant is not in the merchant database or the merchant is not permitted to use this particular card,3,Invalid merchant
411,,3,Invalid merchant
420,,68,Response received too late / Timeout
421,,91,Issuer unavailable or switch is inoperative
440,Format error.,30,Format error
440,Invalid amount,13,Invalid value/amount
440,Re-enter transaction,19,Re-enter transaction
440,Schema Validation Error,V01,Validation error
440,Security violation,63,Security violation
440,,30,Format error
441,Invalid transaction,57,Transaction not permitted to cardholder
441,"Invalid transaction. This card or terminal is not permitted to perform this transaction, or the transaction type is invalid, or First Data is unable to route a refund request to the network.",57,Transaction not permitted to cardholder
441,,57,Transaction not permitted to cardholder
460,Cash back service not available,12,An internal error occurred
460,Service not allowed,12,An internal error occurred
460,Terminal not programmed for service,12,An internal error occurred
460,"Violation, cannot complete",12,An internal error occurred
460,,12,An internal error occurred
461,,62,Restricted card