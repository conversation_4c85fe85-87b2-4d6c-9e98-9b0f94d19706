namespace FlexCharge.Eligibility.Authorization
{
    public class EligibilityClaims
    {
        public const string ELIGIBILITY_READ_ALL = "eligibility.read.all";
        public const string ELIGIBILITY_READ_ONE = "eligibility.read.one";
        public const string ELIGIBILITY_ADD = "eligibility.add";
        public const string ELIGIBILITY_UPDATE = "eligibility.update";
        public const string ELIGIBILITY_DELETE = "eligibility.delete";
        public const string ELIGIBILITY_MANAGE = "eligibility.manage";
    }
}
