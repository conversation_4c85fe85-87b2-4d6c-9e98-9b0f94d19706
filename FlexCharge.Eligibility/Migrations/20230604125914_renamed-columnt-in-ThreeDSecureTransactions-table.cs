using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Eligibility.Migrations
{
    /// <inheritdoc />
    public partial class renamedcolumntinThreeDSecureTransactionstable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "ThreeDSServerTransID",
                table: "ThreeDSecureTransactions",
                newName: "PreAuthenticate_ThreeDSServerTransID");

            migrationBuilder.RenameIndex(
                name: "IX_ThreeDSecureTransactions_ThreeDSServerTransID",
                table: "ThreeDSecureTransactions",
                newName: "IX_ThreeDSecureTransactions_PreAuthenticate_ThreeDSServerTrans~");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "PreAuthenticate_ThreeDSServerTransID",
                table: "ThreeDSecureTransactions",
                newName: "ThreeDSServerTransID");

            migrationBuilder.RenameIndex(
                name: "IX_ThreeDSecureTransactions_PreAuthenticate_ThreeDSServerTrans~",
                table: "ThreeDSecureTransactions",
                newName: "IX_ThreeDSecureTransactions_ThreeDSServerTransID");
        }
    }
}
