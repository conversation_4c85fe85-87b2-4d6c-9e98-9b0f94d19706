using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Eligibility.Migrations
{
    public partial class addfullyauthorizeandverifyzeroresultscolumnstoorderstable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "FullAutohrizationResult",
                table: "Orders",
                type: "jsonb",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsFullAuthorizationChecked",
                table: "Orders",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsZeroVerificationChecked",
                table: "Orders",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "ZeroVerificationResult",
                table: "Orders",
                type: "jsonb",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "FullAutohrizationResult",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "IsFullAuthorizationChecked",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "IsZeroVerificationChecked",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "ZeroVerificationResult",
                table: "Orders");
        }
    }
}
