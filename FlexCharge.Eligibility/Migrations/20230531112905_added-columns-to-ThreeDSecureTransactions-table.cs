using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Eligibility.Migrations
{
    /// <inheritdoc />
    public partial class addedcolumnstoThreeDSecureTransactionstable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AcsURL",
                table: "ThreeDSecureTransactions",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AuthenticationType",
                table: "ThreeDSecureTransactions",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EncodedCReq",
                table: "ThreeDSecureTransactions",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ThreeDSResults",
                table: "ThreeDSecureTransactions",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AcsURL",
                table: "ThreeDSecureTransactions");

            migrationBuilder.DropColumn(
                name: "AuthenticationType",
                table: "ThreeDSecureTransactions");

            migrationBuilder.DropColumn(
                name: "EncodedCReq",
                table: "ThreeDSecureTransactions");

            migrationBuilder.DropColumn(
                name: "ThreeDSResults",
                table: "ThreeDSecureTransactions");
        }
    }
}
