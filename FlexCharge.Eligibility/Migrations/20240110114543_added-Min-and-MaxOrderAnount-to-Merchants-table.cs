using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Eligibility.Migrations
{
    /// <inheritdoc />
    public partial class addedMinandMaxOrderAnounttoMerchantstable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "MaxOrderAmount",
                table: "Merchants",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MinOrderAmount",
                table: "Merchants",
                type: "integer",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON>OrderAmount",
                table: "Merchants");

            migrationBuilder.DropColumn(
                name: "MinOrderA<PERSON>",
                table: "Merchants");
        }
    }
}
