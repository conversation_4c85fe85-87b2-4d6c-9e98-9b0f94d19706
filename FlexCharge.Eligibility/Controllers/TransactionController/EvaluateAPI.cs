//#define ENABLE_ADDITIONAL_FIELDS_TESTING

using FlexCharge.Eligibility.DTO;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Security;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.SensitiveData.Guards;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.Telemetry.HttpRequests;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Utils;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Newtonsoft.Json;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.Controllers
{
    // Communication with the merchant/gateway/processor backend
    public partial class TransactionController : ITransmitAndEvaluateService
    {
        // //TODO: Must be set in Merchant Portal
        private static readonly TimeSpan MINIMUM_MIT_EXPIRATION_INTERVAL =
            TimeSpan.FromHours(24).Add(TimeSpan.FromMinutes(-1));


        [HttpPost("evaluate")]
        [SensitiveDataPublicApiTelemetry(typeof(EvaluateRequest), JwtTenantIdClaim = MyClaimTypes.MERCHANT_ID)]
        [ProducesResponseType(typeof(EvaluateResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> EvaluatePost(EvaluateRequest request,
            CancellationToken cancellationToken)
        {
            using var workspan = Workspan.StartEndpoint<TransactionController>(this, request, _globalData);

            Guid orderToCreateId = Guid.NewGuid();
            var midFromJwt = GetMID();

            _httpContextAccessor.AddCorrelation(midFromJwt, orderToCreateId);

            return await EvaluateAsync(request, midFromJwt, GetPID(), orderToCreateId,
                EvaluateProcessingType.AutoSelect, isInternalEvaluation: false,
                meta: null,
                cancellationToken);
        }


        [ApiExplorerSettings(IgnoreApi = true)]
        [NonAction]
        public async Task<IActionResult> EvaluateAsync(EvaluateRequest request,
            Guid midFromJWT, Guid? partnerId,
            Guid orderToCreateId,
            EvaluateProcessingType evaluateProcessingType,
            bool isInternalEvaluation,
            Dictionary<string, string> meta,
            CancellationToken cancellationToken)
        {
            using var workspan = Workspan.Start<TransactionController>()
                .Baggage("Mid", midFromJWT)
                .Baggage("Pid", partnerId)
                .Baggage("OrderId", orderToCreateId)
                .Tag("Meta", meta)
                .LogEnterAndExit();

            try
            {
                #region AdditionalFields Testing

#if ENABLE_ADDITIONAL_FIELDS_TESTING
                if (request.AdditionalFields == null)
                {
                    request.AdditionalFields = new List<AdditionalField>();

                    int fieldsToGenerate = 51;
                    for (int i = 0; i < fieldsToGenerate; i++)
                    {
                        request.AdditionalFields.Add(new AdditionalField()
                        {
                            Key = $"Field {i}",
                            Value = $"Value {i}"
                        });
                    }
                }
#endif

                #endregion


                // Only for Spreedly integration
                request = await ((ITransmitAndEvaluateService) this).OverrideEvaluateRequestByRequestInExtraDataAsync(
                    request, orderToCreateId, ModelState);

                var requestValidationResult =
                    await ValidateEvaluateRequestAsync(request, midFromJWT, partnerId, orderToCreateId);

                if (requestValidationResult.IsValid == false)
                {
                    //TODO: We want to replace code below with the ValidationProblem() in the next API version!!!
                    //return ValidationProblem();

                    //see: https://stackoverflow.com/questions/55289631/inconsistent-behaviour-with-modelstate-validation-asp-net-core-api
                    var traceId = System.Diagnostics.Activity.Current?.Id ?? HttpContext.TraceIdentifier;
                    ModelState.AddModelError("TraceId", traceId);

                    return BadRequest(ModelState);
                }

                if (request.OrderSessionKey == null)
                {
                    #region Run Evaluation Request

                    if (request.PaymentMethod?.Token == false)
                    {
                        await _eligibilityService.TokenizePaymentInstrumentAsync(request, orderToCreateId,
                            requestValidationResult.Merchant.Mid, cancellationToken);
                    }

                    var result = await _eligibilityService.RunEvaluateRequestAsync(request, partnerId,
                        orderToCreateId, evaluateProcessingType, EvaluateRequestType.EVALUATE,
                        cancellationToken, isInternalEvaluation, meta);

                    var evaluateResponse = result.EvaluateResponse;
                    evaluateResponse.OrderSessionKey = orderToCreateId;
                    evaluateResponse.OrderId = evaluateResponse.OrderSessionKey;
                    //evaluateResponse.AdditionalFields = request.AdditionalFields;

                    return SafeReturnResponse(evaluateResponse);

                    #endregion
                }
                else // OrderSessionKey is specified in request -> it's outcome request
                {
                    #region Outcome Request

                    var outcomeRequest = new OutcomeRequest()
                    {
                        OrderSessionKey = request.OrderSessionKey.Value
                    };

                    var response = await _eligibilityService.Outcome(outcomeRequest, midFromJWT);

                    response.Status = response.Status;

                    return SafeReturnResponse(response);

                    #endregion
                }
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(request.Mid)
                        .Meta(meta => meta
                            .SetValue("Request", JsonConvert.SerializeObject(request)))
                );

                return NotEligible<EvaluateResponse>(true);
            }
        }

        async Task<EvaluateRequest> ITransmitAndEvaluateService.OverrideEvaluateRequestByRequestInExtraDataAsync(
            EvaluateRequest request,
            Guid orderId, ModelStateDictionary modelState)
        {
            using var workspan = Workspan.Start<TransactionController>();

            if (string.IsNullOrWhiteSpace(request?.ExtraData))
                return request; //!!!


            workspan.Log.Information("Overriding EvaluateRequest by ExtraData");

            try
            {
                var requestInExtraData = JsonConvert.DeserializeObject<EvaluateRequest>(request.ExtraData);

                // We need to keep the PaymentMethod of the initial request as Merchant may have used external tokenization service
                // and we don't want to override it with the PaymentMethod from ExtraData (e.g. Spreedly integration)
                requestInExtraData.PaymentMethod = request.PaymentMethod;

                // We need to keep the TransactionType of the initial request as it can be specified by a payment orchestrator (e.g. Spreedly)
                requestInExtraData.TransactionType = request.TransactionType;

                // We don't need extra data in request anymore as it is used to override the request
                requestInExtraData.ExtraData = null;

                //TODO: Compare and validate mandatory fields in initial request and request in extra data (mid, etc.)

                //Clear and re-calculate ModelState as it's already populated with possible errors from initial request
                ModelState.Clear();
                TryValidateModel(requestInExtraData);

                return requestInExtraData;
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "Error while overriding EvaluateRequest by ExtraData");

                await _activityService.CreateActivityAsync(EligibilityErrorActivities.IncorrectRequestExtraData,
                    data: request.Mid,
                    set: set => set.TenantId(request.Mid).CorrelationId(orderId));

                modelState.AddModelError("ExtraData", "Incorrect data passed in ExtraData field");

                return null; //!!!
            }
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        [NonAction]
        public async Task<(bool IsValid, Merchant Merchant)> ValidateEvaluateRequestAsync(EvaluateRequest request,
            Guid midFromJWT, Guid? partnerId,
            Guid orderId)
        {
            using var workspan = Workspan.Start<TransactionController>();

            #region Checking ModelState Errors

            if (!ModelState.IsValid)
            {
                //workspan.Log.Information("Invalid model state {ModelState}", ModelState);
                workspan.RecordEndpointBadRequest(ModelState);

                // await _activityService.CreateActivityAsync(ApiErrorActivities.ModelStateError, ModelState,
                //     set => set.TenantId(midFromJWT).CorrelationId(transmitEvaluateCorrelationId));

                return (false, null);
            }

            #endregion

            // // Throws exception if not white-listed card is used in testing environment
            // _securityCheckService.EnsureInProductionOrTestCardIsWhitelisted(request.PaymentMethod?.CardNumber);

            if (!await CheckAndCorrectMidAsync(request, midFromJWT, partnerId))
            {
                return (false, null);
            }

            _securityCheckService.EnsureHasAccessToTransmitAndEvaluate(request.Mid, partnerId);

            #region Validations

            Merchant merchant = null;
            if (request.SiteId == null)
            {
                merchant = await _eligibilityService.GetMerchantOrDefaultAsync(request.Mid.Value);
            }
            else
            {
                var merchantAndSite =
                    await _eligibilityService.GetMerchantAndSiteOrDefaultAsync(request.Mid.Value, request.SiteId.Value);

                if (merchantAndSite.Merchant != null && merchantAndSite.Site == null)
                {
                    workspan.Log
                        .Fatal("Site {SiteId} is unknown for merchant {MerchantId}", request.SiteId, request.Mid);

                    await _activityService.CreateActivityAsync(EligibilityErrorActivities.Evaluate_UnknownSite,
                        data: request.SiteId.Value,
                        set: set => set.TenantId(request.Mid).CorrelationId(orderId));

                    ModelState.AddModelError(nameof(request.SiteId),
                        $"Site with ID {request.SiteId} not found");
                }

                merchant = merchantAndSite.Merchant;
            }


            if (merchant == null)
            {
                workspan.Log.Error("Unknown merchant");

                await _activityService.CreateActivityAsync(EligibilityErrorActivities.Evaluate_UnknownMerchant,
                    data: request.Mid,
                    set: set => set.TenantId(request.Mid).CorrelationId(orderId));

                ModelState.AddModelError(nameof(request.Mid),
                    $"Unknown merchant");
            }
            else
            {
                #region Check if Merchant is Active

                if (!merchant.IsActive)
                {
                    workspan.Log.Information("Merchant is not active");

                    await _activityService.CreateActivityAsync(SecurityActivities.MerchantIsNotActive,
                        data: request.Mid,
                        set: set => set.TenantId(request.Mid).CorrelationId(orderId));

                    ModelState.AddModelError(nameof(request.Mid),
                        $"Merchant is not active");
                }

                #endregion

                if (request.IsMIT == true)
                {
                    #region Check if MIT is enabled for this merchant

                    if (!merchant.IsMitEnabled)
                    {
                        workspan.Log.Information("MIT is not enabled for this merchant");

                        await _activityService.CreateActivityAsync(SecurityActivities.MerchantHasNoAccessToMITRequests,
                            data: request.Mid,
                            set: set => set.TenantId(request.Mid).CorrelationId(orderId));

                        ModelState.AddModelError(nameof(request.IsMIT),
                            $"MIT is not enabled for this merchant, contact support");
                    }

                    #endregion
                }

                OrderSource orderSource = OrderSource.ecommerce;
                if (!string.IsNullOrWhiteSpace(request.OrderSource))
                {
                    if (!Enum.TryParse<OrderSource>(request.OrderSource, true, out orderSource))
                    {
                        workspan.Log.Information("Invalid OrderSource: {OrderSource}", request.OrderSource);
                        ModelState.AddModelError(nameof(request.OrderSource),
                            $"Invalid OrderSource: {nameof(request.OrderSource)}");
                    }
                    else
                    {
                        if (orderSource is OrderSource.terminal or OrderSource.kiosk)
                        {
                            #region Checking if Terminal Mode is enabled for this merchant

                            if (merchant?.IntegrationType != nameof(MerchantIntegrationTypes.TERMINAL))
                            {
                                workspan.Log.Information("{OrderSource} mode is not enabled for this merchant",
                                    orderSource);

                                await _activityService.CreateActivityAsync(
                                    SecurityActivities.MerchantHasNoAccessToTerminalRequests,
                                    data: request.Mid,
                                    set: set => set.TenantId(request.Mid).CorrelationId(orderId));

                                ModelState.AddModelError(nameof(request.OrderSource),
                                    $"{orderSource} mode is not enabled for this merchant, contact support");
                            }

                            #endregion

                            if (request.IsMIT == true)
                            {
                                workspan.Log.Information("MIT is not allowed for terminal orders");

                                ModelState.AddModelError(nameof(request.IsMIT),
                                    $"MIT is not allowed for terminal orders");
                            }
                        }
                    }
                }

                #region HolderName Validation

                bool cardHolderNameRequired = false; // not required from now on
                if (request.PaymentMethod?.Token == false && cardHolderNameRequired)
                {
                    if (string.IsNullOrWhiteSpace(request.PaymentMethod.HolderName))
                    {
                        ModelState.AddModelError(
                            $"{nameof(request.PaymentMethod)}.{nameof(request.PaymentMethod.HolderName)}",
                            $"{nameof(request.PaymentMethod.HolderName)} field is required");
                    }
                }

                #endregion

                #region ExpiryDate Validation

                if (request.IsMIT == true)
                {
                    if (request.ExpiryDateUtc != null)
                    {
                        if (merchant.MITEvaluateAsync == false)
                        {
                            ModelState.AddModelError(nameof(request.ExpiryDateUtc),
                                $"{nameof(request.ExpiryDateUtc)} is not allowed for synchronous MIT orders");
                        }
                        else if (request.ExpiryDateUtc - DateTime.UtcNow < MINIMUM_MIT_EXPIRATION_INTERVAL)
                        {
                            // ModelState.AddModelError(nameof(request.ExpiryDateUtc),
                            //     $"{nameof(request.ExpiryDateUtc)} is earlier than allowed");
                            workspan
                                .Tag("ExpiryDateUtc", request.ExpiryDateUtc)
                                .Log
                                .Fatal($"{nameof(request.ExpiryDateUtc)} is earlier than allowed");
                        }
                    }
                    else
                    {
                        if (merchant.MITEvaluateAsync)
                        {
                            ModelState.AddModelError(nameof(request.ExpiryDateUtc),
                                $"{nameof(request.ExpiryDateUtc)} is required for asynchronous MIT orders");
                        }
                    }
                }

                #endregion

                #region Check if DynamicDescriptor is passed (for MIT orders and if it's required for this merchant)

                if (request.IsMIT == true &&
                    merchant?.MITGetSiteByDynamicDescriptorEnabled == true &&
                    string.IsNullOrWhiteSpace(request.Transaction.DynamicDescriptor))
                {
                    ModelState.AddModelError(
                        $"{nameof(request.Transaction)}.{nameof(request.Transaction.DynamicDescriptor)}",
                        $"{nameof(request.Transaction.DynamicDescriptor)} is required for this merchant");
                }

                #endregion

                #region If Order is in Terminal mode, check if order is CIT

                if ((request.OrderSource?.ToLower() is nameof(OrderSource.terminal) or nameof(OrderSource.kiosk))
                    && request.IsMIT == true)
                {
                    workspan.Log.Error("Order in terminal mode cannot be MIT");

                    await _activityService.CreateActivityAsync(
                        EligibilityErrorActivities.OrderInTerminalModeCannotBeMIT,
                        data: request.Mid,
                        set: set => set.TenantId(request.Mid).CorrelationId(orderId));

                    ModelState.AddModelError(
                        $"{nameof(request.IsMIT)}",
                        $"{nameof(request.IsMIT)} cannot be true for order in terminal mode");
                }

                #endregion

                if (!await _eligibilityService.CheckAndCorrectTransactionTypeAsync(request, merchant))
                {
                    ModelState.AddModelError(
                        $"{nameof(request.TransactionType)}",
                        $"{request.TransactionType} is not allowed for this merchant");
                }


                #region Ensure that no PCI data is sent to evaluate or merchant is PCI DSS

                // If merchant is not PCI DSS or PAN in request should be a token, ensure that no PCI data is sent
                if (merchant.IsPCIDSS() == false || request.PaymentMethod?.Token == true)
                {
                    #region For non-PCI merchants, ensure that PaymentMethod.Token is set to true

                    bool pciDataDetected = false;

                    if (merchant.IsPCIDSS() == false && request.PaymentMethod?.Token != true)
                    {
                        pciDataDetected = true;

                        await _activityService.CreateActivityAsync(SecurityActivities.MerchantHasNoAccessToPCIRequests,
                            data: request.Mid,
                            set: set => set.TenantId(request.Mid).CorrelationId(orderId));

                        ModelState.AddModelError(
                            $"{nameof(request.PaymentMethod)}.{nameof(request.PaymentMethod.Token)}",
                            "Cannot process PCI data for non-PCI merchant");
                    }

                    #endregion

                    #region Ensure that PaymentMethod does not contain PAN (PCI) data

                    if (!string.IsNullOrWhiteSpace(request.PaymentMethod?.CardNumber) &&
                        _securityCheckService.CanBeCardNumber(request.PaymentMethod.CardNumber))
                    {
                        pciDataDetected = true;

                        ModelState.AddModelError(
                            $"{nameof(request.PaymentMethod)}.{nameof(request.PaymentMethod.CardNumber)}",
                            "Cannot process PCI data for non-PCI merchant or request");
                    }

                    if (request.PaymentMethod?.ExpirationMonth != null)
                    {
                        pciDataDetected = true;

                        // Not returning error for now to not to break existing integrations 
                        // ModelState.AddModelError(
                        //     $"{nameof(request.PaymentMethod)}.{nameof(request.PaymentMethod.ExpirationMonth)}",
                        //     "Cannot process PCI data for non-PCI merchant or request");
                    }

                    if (request.PaymentMethod?.ExpirationYear != null)
                    {
                        pciDataDetected = true;

                        // Not returning error for now to not to break existing integrations
                        // ModelState.AddModelError(
                        //     $"{nameof(request.PaymentMethod)}.{nameof(request.PaymentMethod.ExpirationYear)}",
                        //     "Cannot process PCI data for non-PCI merchant or request");
                    }

                    if (!string.IsNullOrWhiteSpace(request.PaymentMethod?.VerificationValue))
                    {
                        pciDataDetected = true;

                        ModelState.AddModelError(
                            $"{nameof(request.PaymentMethod)}.{nameof(request.PaymentMethod.VerificationValue)}",
                            "Cannot process PCI data for non-PCI merchant or request");
                    }


                    if (pciDataDetected)
                    {
                        if (merchant.IsPCIDSS() == false)
                        {
                            workspan.Log.Warning("PaymentMethod contains PCI data for non-PCI merchant request");

                            await _activityService.CreateActivityAsync(
                                SecurityActivities.PCIDataDetectedInRequestFromNonPCIMerchant,
                                data: request.Mid,
                                set: set => set
                                    .TenantId(request.Mid)
                                    .CorrelationId(orderId));
                        }
                        else
                        {
                            workspan.Log.Warning("PaymentMethod contains PCI data in tokenized request");

                            await _activityService.CreateActivityAsync(
                                SecurityActivities.PCIDataDetectedInTokenizedRequest,
                                data: request.Mid,
                                set: set => set
                                    .TenantId(request.Mid)
                                    .CorrelationId(orderId));

                            // ModelState.AddModelError(
                            //     $"{nameof(request.PaymentMethod)}",
                            //     "PaymentMethod contains PCI data in tokenized request");
                        }
                    }

                    #endregion
                }


                if (merchant.IsPCIDSS()) // Validations for PCI DSS merchants only
                {
                    if (request.PaymentMethod?.Token == false)
                    {
                        if (!_securityCheckService.CanBeCardNumber(request.PaymentMethod.CardNumber))
                        {
                            ModelState.AddModelError(
                                $"{nameof(request.PaymentMethod)}.{nameof(request.PaymentMethod.CardNumber)}",
                                "Card number is not valid");
                        }


                        #region For non-production environments ensure that only test credit cards numbers are used

                        if (!EnvironmentHelper.IsInProduction)
                        {
                            try
                            {
                                SensitiveDataGuard.IsTestCreditCardGuard(request?.PaymentMethod?.CardNumber);
                            }
                            catch (SecurityException e)
                            {
                                workspan.Log.Error(e.Message);

                                ModelState.AddModelError(
                                    $"{nameof(request.PaymentMethod)}.{nameof(request.PaymentMethod.CardNumber)}",
                                    e.Message);
                            }
                            catch (Exception e)
                            {
                                workspan.RecordFatalException(e);

                                ModelState.AddModelError(
                                    $"{nameof(request.PaymentMethod)}.{nameof(request.PaymentMethod.CardNumber)}",
                                    e.Message);
                            }
                        }

                        #endregion
                    }
                }

                #endregion

                #region If BillingInformation.FullName is set, First and Last names should not be set

                if (request.BillingInformation?.FullName != null)
                {
                    if (request.BillingInformation.FirstName != null)
                    {
                        ModelState.AddModelError(
                            $"{nameof(request.BillingInformation)}.{nameof(request.BillingInformation.FullName)}",
                            $"{nameof(request.BillingInformation.FullName)} and {nameof(request.BillingInformation.FirstName)} cannot be set at the same time");
                    }

                    if (request.BillingInformation.LastName != null)
                    {
                        ModelState.AddModelError(
                            $"{nameof(request.BillingInformation)}.{nameof(request.BillingInformation.FullName)}",
                            $"{nameof(request.BillingInformation.FullName)} and {nameof(request.BillingInformation.LastName)} cannot be set at the same time");
                    }
                }

                #endregion

                #region If ShippingInformation.FullName is set, First and Last names should not be set

                if (request.ShippingInformation?.FullName != null)
                {
                    if (request.ShippingInformation.FirstName != null)
                    {
                        ModelState.AddModelError(
                            $"{nameof(request.ShippingInformation)}.{nameof(request.ShippingInformation.FullName)}",
                            $"{nameof(request.ShippingInformation.FullName)} and {nameof(request.ShippingInformation.FirstName)} cannot be set at the same time");
                    }

                    if (request.ShippingInformation.LastName != null)
                    {
                        ModelState.AddModelError(
                            $"{nameof(request.ShippingInformation)}.{nameof(request.ShippingInformation.FullName)}",
                            $"{nameof(request.ShippingInformation.FullName)} and {nameof(request.ShippingInformation.LastName)} cannot be set at the same time");
                    }
                }

                #endregion


                if (merchant.BillingInformationOptional != true)
                {
                    #region Billing Information Validation

                    if (request.BillingInformation == null)
                    {
                        ModelState.AddModelError(nameof(request.BillingInformation),
                            $"{nameof(request.BillingInformation)} field is required");
                    }
                    else
                    {
                        if (request.BillingInformation.FullName != null)
                        {
                            if (string.IsNullOrWhiteSpace(request.BillingInformation.FullName))
                            {
                                ModelState.AddModelError(
                                    $"{nameof(request.BillingInformation)}.{nameof(request.BillingInformation.FullName)}",
                                    $"{nameof(request.BillingInformation.FullName)} field is required");
                            }
                        }
                        else
                        {
                            #region Validating First and Last name fields

                            if (string.IsNullOrWhiteSpace(request.BillingInformation?.FirstName))
                            {
                                ModelState.AddModelError(
                                    $"{nameof(request.BillingInformation)}.{nameof(request.BillingInformation.FirstName)}",
                                    $"{nameof(request.BillingInformation.FirstName)} field is required");
                            }

                            if (string.IsNullOrWhiteSpace(request.BillingInformation?.LastName))
                            {
                                ModelState.AddModelError(
                                    $"{nameof(request.BillingInformation)}.{nameof(request.BillingInformation.LastName)}",
                                    $"{nameof(request.BillingInformation.LastName)} field is required");
                            }

                            #endregion
                        }

                        if (string.IsNullOrWhiteSpace(request.BillingInformation?.Country))
                        {
                            ModelState.AddModelError(
                                $"{nameof(request.BillingInformation)}.{nameof(request.BillingInformation.Country)}",
                                $"{nameof(request.BillingInformation.Country)} field is required");
                        }

                        if (string.IsNullOrWhiteSpace(request.BillingInformation?.CountryCode))
                        {
                            ModelState.AddModelError(
                                $"{nameof(request.BillingInformation)}.{nameof(request.BillingInformation.CountryCode)}",
                                $"{nameof(request.BillingInformation.CountryCode)} field is required");
                        }

                        if (request.BillingInformation?.CountryCode?.Trim() == "US" ||
                            request.BillingInformation?.Country?.Trim() == "US")
                        {
                            if (string.IsNullOrWhiteSpace(request.BillingInformation?.State))
                            {
                                ModelState.AddModelError(
                                    $"{nameof(request.BillingInformation)}.{nameof(request.BillingInformation.State)}",
                                    $"{nameof(request.BillingInformation.State)} field is required");
                            }
                        }

                        if (string.IsNullOrWhiteSpace(request.BillingInformation?.AddressLine1))
                        {
                            ModelState.AddModelError(
                                $"{nameof(request.BillingInformation)}.{nameof(request.BillingInformation.AddressLine1)}",
                                $"{nameof(request.BillingInformation.AddressLine1)} field is required");
                        }

                        if (string.IsNullOrWhiteSpace(request.BillingInformation?.City))
                        {
                            ModelState.AddModelError(
                                $"{nameof(request.BillingInformation)}.{nameof(request.BillingInformation.City)}",
                                $"{nameof(request.BillingInformation.City)} field is required");
                        }


                        if (string.IsNullOrWhiteSpace(request.BillingInformation?.Zipcode))
                        {
                            ModelState.AddModelError(
                                $"{nameof(request.BillingInformation)}.{nameof(request.BillingInformation.Zipcode)}",
                                $"{nameof(request.BillingInformation.Zipcode)} field is required");
                        }
                    }

                    #endregion
                }
            }

            if (request.BillingInformation != null)
            {
                if (request.BillingInformation.FullName != null)
                {
                    #region Extracting First and Last names from FullName

                    if (NameHelpers.TrySplitToFirstAndLastName(request.BillingInformation.FullName,
                            out var firstName, out var lastName))
                    {
                        request.BillingInformation.FirstName = firstName;
                        request.BillingInformation.LastName = lastName;
                    }
                    else
                    {
                        request.BillingInformation.FirstName = request.BillingInformation.FullName?.Trim();
                    }

                    #endregion
                }
                else
                {
                    #region Setting BillingInformationFullName from First and Last names

                    request.BillingInformation.FullName =
                        NameHelpers.ConcatenateFirstAndListNameOrDefault(request.BillingInformation.FirstName,
                            request.BillingInformation.LastName);

                    #endregion
                }
            }

            if (request.ShippingInformation != null)
            {
                if (request.ShippingInformation.FullName != null)
                {
                    #region Extracting First and Last names from FullName

                    if (NameHelpers.TrySplitToFirstAndLastName(request.ShippingInformation.FullName,
                            out var firstName, out var lastName))
                    {
                        request.ShippingInformation.FirstName = firstName;
                        request.ShippingInformation.LastName = lastName;
                    }
                    else
                    {
                        request.ShippingInformation.FirstName = request.ShippingInformation.FullName?.Trim();
                    }

                    #endregion
                }
                else
                {
                    #region Setting ShippingInformationFullName from First and Last names

                    request.ShippingInformation.FullName =
                        NameHelpers.ConcatenateFirstAndListNameOrDefault(request.ShippingInformation.FirstName,
                            request.ShippingInformation.LastName);

                    #endregion
                }
            }

            if (!await CheckModelStateAsync(request.Mid, orderId))
            {
                return (false, null);
            }

            #endregion

            return (true, merchant);
        }
    }
}