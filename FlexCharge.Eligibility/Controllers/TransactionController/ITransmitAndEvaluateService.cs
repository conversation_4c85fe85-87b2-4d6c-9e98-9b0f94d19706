using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.DTO;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.Controllers;

public enum EvaluateProcessingType
{
    AutoSelect,
    Synchronous,
    Asynchronous,
}

public interface ITransmitAndEvaluateService
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="request"></param>
    /// <param name="midFromJWT"></param>
    /// <param name="partnerId"></param>
    /// <param name="orderToCreateId"></param>
    /// <param name="evaluateProcessingType"></param>
    /// <param name="isInternalEvaluation"></param>
    /// <param name="externalAccountId">
    /// If order is processing on another system, it's account id in this system.
    /// For example, when recycling Stripe MIT orders
    /// </param>
    /// <param name="meta"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public Task<IActionResult> EvaluateAsync(EvaluateRequest request, Guid midFromJWT, Guid? partnerId,
        Guid orderToCreateId,
        EvaluateProcessingType evaluateProcessingType, bool isInternalEvaluation,
        Dictionary<string, string> meta,
        CancellationToken cancellationToken);

    Task<(bool IsValid, Merchant Merchant)> ValidateEvaluateRequestAsync(EvaluateRequest request, Guid midFromJWT,
        Guid? partnerId,
        Guid orderId);

    Task<EvaluateRequest> OverrideEvaluateRequestByRequestInExtraDataAsync(EvaluateRequest request, Guid orderId,
        ModelStateDictionary modelState);
}