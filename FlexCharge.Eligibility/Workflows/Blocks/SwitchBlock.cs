using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Workflows;
using FlexCharge.WorkflowEngine.Workflows;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.EligibilityChecks.Workflow.Steps;

public class SwitchBlock<TContext> : BlockBase<TContext>, ISwitchBlock<TContext>
    where TContext : IExecutionContext
{
    public override string Name => "SWITCH";
    public override string FullName => "Switch Block";

    public IList<(string? Name, Func<TContext, bool> Condition)> Conditions { get; } =
        new List<(string? Name, Func<TContext, bool> Condition)>();

    public IList<IBlock<TContext>> CaseLinks { get; } = new List<IBlock<TContext>>();
    public IBlock<TContext> DefaultLink { get; private set; }


    protected override async Task RunInternal()
    {
        for (var i = 0; i < Conditions.Count; i++)
        {
            if (Conditions[i].Condition(Context))
            {
                Context.Tracer.WriteInnerAction("CASE: " + (Conditions[i].Name ?? $"{i}"));
                await CaseLinks[i].Run();

                return; //!!!
            }
        }

        await DefaultLink.Run();
    }


    public void AddConditionCase(string name, Func<TContext, bool> condition)
    {
        AddConditionCase(name, condition, null);
    }

    public void AddConditionCase(string name, Func<TContext, bool> condition, IBlock<TContext> block)
    {
        Conditions.Add(new(name, condition));
        CaseLinks.Add(block);
    }

    public void AttachDefaultCase(IBlock<TContext> block)
    {
        block.AttachToPrevious(this);
        DefaultLink = block;
    }

    public void AttachCaseLink(int conditionIndex, IBlock<TContext> block)
    {
        block.AttachToPrevious(this);
        CaseLinks[conditionIndex] = block;
    }

    public override IEnumerable<IBlockLink> GetNextLinks()
    {
        yield return new BlockLink(this) {Target = DefaultLink, Name = "Default", ConnectorIndex = 0};

        int connectorIndex = 1;
        foreach (var link in CaseLinks)
        {
            yield return new BlockLink(this) {Target = link, Name = "", ConnectorIndex = connectorIndex++};
        }
    }

    public override void AttachNext(IBlock<TContext> nextBlock, int connectorIndex)
    {
        if (connectorIndex == 0)
        {
            AttachDefaultCase(nextBlock);
        }
        else if (connectorIndex <= CaseLinks.Count)
        {
            AttachCaseLink(connectorIndex - 1, nextBlock);
        }
        else
        {
            throw new ArgumentOutOfRangeException(nameof(connectorIndex));
        }
    }
}