using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Services;
using FlexCharge.Eligibility.Services.ConsumerNotificationsService;
using FlexCharge.Eligibility.Services.OffSessionRetryService;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.Consumers;

public class RetryOffSessionOfferCommandConsumer : IdempotentCommandConsumer<RetryOffSessionOfferCommand>
{
    private readonly IOffSessionRetryService _offSessionRetryService;
    private readonly IActivityService _activityService;


    public RetryOffSessionOfferCommandConsumer(
        IOffSessionRetryService offSessionRetryService,
        IActivityService activityService,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _offSessionRetryService = offSessionRetryService;
        _activityService = activityService;
    }


    protected override async Task ConsumeCommand(RetryOffSessionOfferCommand command,
        CancellationToken cancellationToken)
    {
        try
        {
            await _offSessionRetryService.RetryOffSessionOrderAsync(command.OfferId, command.ForceRetry,
                cancellationToken);
        }
        catch (Exception e)
        {
            Workspan.RecordFatalException(e);

            await _activityService.CreateActivityAsync(
                EligibilityErrorActivities.Eligibility_Error,
                data: e,
                set => set
                    //.TenantId(command.Mid)
                    .CorrelationId(command.OfferId));
        }
    }
}