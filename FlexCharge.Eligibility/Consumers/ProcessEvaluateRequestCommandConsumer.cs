using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Controllers;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using FlexCharge.Eligibility.Services.EligibilityService;
using MassTransit;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.Consumers;

public class ProcessEvaluateRequestCommandConsumer :
    IdempotentCommandConsumer<ProcessEvaluateRequestCommand, ProcessEvaluateRequestCommandResponse>
{
    private readonly IEligibilityService _eligibilityService;

    public ProcessEvaluateRequestCommandConsumer(
        IServiceScopeFactory serviceScopeFactory,
        IEligibilityService eligibilityService) : base(serviceScopeFactory)
    {
        _eligibilityService = eligibilityService;
    }

    protected override async Task<ProcessEvaluateRequestCommandResponse> ConsumeCommand(
        ProcessEvaluateRequestCommand command, CancellationToken cancellationToken)
    {
        Workspan
            .Baggage("Mid", command.MerchantId)
            .Baggage("Pid", command.PartnerId)
            .Baggage("OrderId", command.OfferId);


        var evaluateRequest = JsonConvert.DeserializeObject<EvaluateRequest>(command.Request);

        ProcessEvaluateRequestCommandResponse response = null;

        try
        {
            var result = await _eligibilityService.ExecuteRequestAsync(
                asynchronousEvaluation: true,
                evaluateRequest, command.PartnerId, command.OfferId,
                command.InternalEvaluation, command.RequestType,
                command.OrderMeta,
                cancellationToken);


            return new ProcessEvaluateRequestCommandResponse
            {
                Succeeded = true,
                Response = JsonConvert.SerializeObject(result.EvaluateResponse),
                ResponseCode = result.ResponseCode,
                ResponseMessage = result.ResponseMessage,
            };
        }
        catch (Exception e)
        {
            Workspan.RecordException(e);

            return new ProcessEvaluateRequestCommandResponse
            {
                Succeeded = false
            };
        }
    }
}