using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Services.OffSessionRetryService;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.Consumers;

public class SchedulerRunJobEventConsumer : IdempotentEventConsumer<SchedulerRunJobEvent>
{
    private readonly IOffSessionRetryService _offSessionRetryService;


    public SchedulerRunJobEventConsumer(
        IServiceScopeFactory serviceScopeFactory,
        IOffSessionRetryService offSessionRetryService) : base(serviceScopeFactory)
    {
        _offSessionRetryService = offSessionRetryService;
    }

    protected override async Task ConsumeEvent(SchedulerRunJobEvent message, CancellationToken cancellationToken)
    {
        if (message.Recipient == IOffSessionRetrySchedulerService.RetryJobName)
        {
            var orderId = message.ScheduledJobId;
            Workspan
                .Baggage("OrderId", message.ScheduledJobId);

            // We don't want to execute the retry job synchronously here to avoid scheduler retries on timeout,
            // caused by a long running evaluation
            await Publish(new RetryOffSessionOfferCommand(orderId));
        }
    }
}