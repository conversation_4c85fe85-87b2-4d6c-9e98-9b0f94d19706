using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.Consumers;

public class MerchantUnlockedEventConsumer : ConsumerBase<MerchantUnlockedEvent>
{
    private PostgreSQLDbContext _context;


    public MerchantUnlockedEventConsumer(
        PostgreSQLDbContext context,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _context = context;
    }

    protected override async Task ConsumeMessage(MerchantUnlockedEvent message, CancellationToken cancellationToken)
    {
        try
        {
            var merchant = await _context.Merchants.SingleOrDefaultAsync(x => x.Mid == message.Mid);

            ArgumentNullException.ThrowIfNull(merchant,"Merchant not found");

            merchant.IsLocked = false;
            merchant.ModifiedBy = message.ModifiedBy.ToString();
            
            _context.Merchants.Update(merchant);
            await _context.SaveChangesAsync();
        }
        catch (Exception e)
        {
            Workspan.RecordException(e,
                "Failed locking merchant {Mid}", message.Mid);
        }
    }

}