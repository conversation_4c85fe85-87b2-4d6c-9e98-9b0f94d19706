using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts;
using FlexCharge.Eligibility.Services.ExternalProviderDunningService;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.Consumers.Adapters;

public class ExternalProviderInvoiceDeletedEventConsumer : ConsumerBase<ExternalProviderInvoiceDeletedEvent>
{
    private readonly IExternalProviderDunningService _externalProviderDunningService;

    public ExternalProviderInvoiceDeletedEventConsumer(IServiceScopeFactory serviceScopeFactory,
        IExternalProviderDunningService externalProviderDunningService) : base(serviceScopeFactory)
    {
        _externalProviderDunningService = externalProviderDunningService;
    }

    protected override async Task ConsumeMessage(ExternalProviderInvoiceDeletedEvent message,
        CancellationToken cancellationToken)
    {
        try
        {
            await _externalProviderDunningService.ProcessDeletedInvoiceAsync(
                message.Mid, message.OrderId,
                message.InvoiceId);
        }
        catch (Exception e)
        {
            Workspan.RecordFatalException(e);
        }
    }
}