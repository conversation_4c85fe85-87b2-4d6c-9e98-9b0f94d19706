// using System;
// using System.Threading.Tasks;
// using FlexCharge.Contracts;
// using MassTransit;
// using Microsoft.Extensions.Logging;
// using Newtonsoft.Json;
//
// namespace FlexCharge.Eligibility.Consumers;
//
// public class ActivityCreatedEventConsumer : IConsumer<ActivityCreatedEvent>
// {
//     
//     ILogger<ActivityCreatedEventConsumer> _logger;
//     
//     public ActivityCreatedEventConsumer(ILogger<ActivityCreatedEventConsumer> logger)
//     {
//         _logger = logger;
//     }
//     
//     public async Task Consume(ConsumeContext<ActivityCreatedEvent> context)
//     {
//         try
//         {
//             _logger.LogInformation($"ActivityCreatedEventConsumer: {JsonConvert.SerializeObject(context)}");
//         }
//         catch (Exception e)
//         {
//             _logger.LogError(e, $"EXCEPTION: ActivityCreatedEventConsumer > FAILED");
//         }
//     }
// }