using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Cures;
using FlexCharge.Eligibility.Entities;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.Services.CureService;

public class CureService : ICureService
{
    private readonly ServiceCollectionExtensions.CureResolver _cureResolver;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly PostgreSQLDbContext _dbContext;

    public CureService(ServiceCollectionExtensions.CureResolver cureResolver, IServiceScopeFactory serviceScopeFactory,
        PostgreSQLDbContext dbContext)
    {
        _cureResolver = cureResolver;
        _serviceScopeFactory = serviceScopeFactory;
        _dbContext = dbContext;
    }

    public async Task<ICure> CreateCureOrDefaultAsync(IActivityService activityService,
        Merchant merchant, Order order,
        string cureIdWithRepeatCount, bool testCall)
    {
        using var workspan = Workspan.Start<CureService>();

        string cureId = cureIdWithRepeatCount;
        int indexOfRepeatChar = cureId.IndexOf(':');
        if (indexOfRepeatChar >= 0)
        {
            cureId = cureId.Substring(0, indexOfRepeatChar);
        }

        var cure = CreateUserChallengeCureOrDefault(order, cureId);

        if (cure != null)
        {
            cure.Initialize(_serviceScopeFactory, activityService, merchant, order);

            if (!testCall)
            {
                _dbContext.ExecutedCures.Add(new ExecutedCure()
                {
                    OrderId = order.Id,
                    Name = cureIdWithRepeatCount,
                    IsUserChallenge = cure.IsUserChallengeCure
                    //Payload
                });


                await _dbContext.SaveChangesAsync();
            }
        }


        return cure;
    }

    private ICure CreateUserChallengeCureOrDefault(Order order, string cureId)
    {
        var cure = _cureResolver(cureId, throwExceptionIfNotFound: false);

        IUserChallengeCure userChallengeCure = cure as IUserChallengeCure;
        if (cure == null ||
            (userChallengeCure != null && order.UseIFrames && !userChallengeCure.RequiresIFrame))
        {
            #region Trying to find corresponding cure for widget in iFrame mode

            var iFrameCure = _cureResolver(cureId + "i", false); // i at the end of cure name means iFrame
            if (iFrameCure != null)
            {
                cure = iFrameCure;
            }

            #endregion
        }

        return cure;
    }
}