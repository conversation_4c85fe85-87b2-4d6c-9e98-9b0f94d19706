using System.Text.RegularExpressions;
using FlexCharge.Eligibility.RiskManagement.Fingerprinting;
using FlexCharge.Utils;

namespace FlexCharge.Eligibility.Services.RiskManagement;

public class BlockableFingerprint
{
    public BlockableFingerprint(IFingerprint fingerprint, bool blocked, bool? networkLevel, string friendlyName)
    {
        Type = fingerprint.Type;
        Category = fingerprint.Category;
        Blocked = blocked;
        NetworkLevel = networkLevel;
        FriendlyName = friendlyName;
    }
    
    public FingerprintType Type { get; }
    public FingerprintCategory Category { get; }
    public bool Blocked { get; }
    public bool? NetworkLevel { get; }

    public string FriendlyName { get; }

    public string GetHumanReadableCategory()
    {
        var category = Category.ToString();

        category = ConvertPascalToSpaceDelimited(category);

        return category;
    }

    public string GetHumanReadableType()
    {
        var type = Type.ToString();
        //type = type.Replace("_", "+");
        type = type.Replace("_", " ");
        return type;
    }

    private static string ConvertPascalToSpaceDelimited(string input)
    {
        // Use regex to insert a space before every uppercase letter except the first one
        return Regex.Replace(input, "(?<!^)([A-Z])", " $1");
    }

}