using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Amazon.DynamoDBv2;
using FlexCharge.Common.Activities;
using FlexCharge.Common.NoSQL.DynamoDB;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.RiskManagement.Fingerprinting;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.Services.RiskManagement;

public class FingerprintService : IFingerprintService
{
    private readonly IActivityService _activityService;
    private readonly IAmazonDynamoDB _dynamoDb;
    private readonly IServiceScopeFactory _serviceScopeFactory;

    private IDictionary<string, IFingerprint> Fingerprints { get; set; }

    /// <summary>
    /// Important: FingerprintStatistics doesn't reflect latest changes and changes mad during curren order
    /// evaluation, as updated are done atomically and directly on databases 
    /// </summary>
    private Dictionary<string, FingerprintStatistics> FingerprintStatistics { get; set; }


    public FingerprintService(
        IActivityService activityService,
        IAmazonDynamoDB dynamoDb,
        IServiceScopeFactory serviceScopeFactory)
    {
        _activityService = activityService;
        _dynamoDb = dynamoDb;
        _serviceScopeFactory = serviceScopeFactory;
    }

    #region Fingerprinting Support Code

    public async Task ProcessFingerprintActivityAsync(FingerprintActivityType activityType, Order order,
        Func<AtomicUpdatesBuilder, Task> updateStatistics,
        Func<List<string>, PostgreSQLDbContext, Task> updateDbStatistics,
        bool addFingerprintActivity = true)
    {
        // Creating local context because of async processing
        // So Task.Run() can be used to run this method in background
        using var serviceScope = _serviceScopeFactory.CreateScope();
        using var dbContext = serviceScope.ServiceProvider.GetRequiredService<PostgreSQLDbContext>();

        if (Fingerprints != null)
        {
            if (addFingerprintActivity)
            {
                await AddFingerprintActivityAsync(dbContext, activityType, order.Amount, order);
                await dbContext.SaveChangesAsync();
            }

            await updateDbStatistics(Fingerprints.Keys.ToList(), dbContext);

            foreach (var fingerprint in Fingerprints.Keys)
            {
                AtomicUpdatesBuilder fingerprintsUpdate = new();
                using (fingerprintsUpdate.StartItemUpdate<Eligibility.RiskManagement.Fingerprinting.Tables
                           .FingerprintStatistics>(fingerprint))
                {
                    fingerprintsUpdate.Set(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                        .FingerprintStatistics.ModifiedOn), DateTime.UtcNow);

                    await updateStatistics(fingerprintsUpdate);

                    try
                    {
// #if DEBUG
//                         var updatedStatisticsFromDynamo =
//                             (await fingerprintsUpdate
//                                 .UpdateAndGetCurrentOrDefaultAsync<Eligibility.RiskManagement.Fingerprinting.Storage
//                                     .FingerprintStatistics>(_dynamoDb)).ToDictionary(x => x.Fingerprint, x => x);
//
//                         await _activityService.CreateActivityAsync(
//                             FingerprintingActivities.Fingerprinting_FingerprintStatisticsReceived_DynamoDB,
//                             data: updatedStatisticsFromDynamo, set => set
//                                 .TenantId(order?.Mid)
//                                 .CorrelationId(order?.Id));
//
//
// #else
                        //await fingerprintsUpdate.UpdateAsync(_dynamoDb);
//#endif
                    }
                    catch (Exception e)
                    {
                        Workspan.Current?.RecordException(e);

                        await _activityService.CreateActivityAsync(
                            FingerprintingErrorActivities.Fingerprinting_Error,
                            data: e);
                    }
                }
            }
        }
    }

    // private async Task CheckAndCorrectFingerprintStatistics(Order? order, FingerprintStatistics fingerprintStatistics,
    //     bool processingExpiredOffer = false)
    // {
    //     if (!fingerprintStatistics.CheckIfStatisticsIsCorrect(processingExpiredOffer))
    //     {
    //         Workspan.Current?.Log.Warning("Fingerprint statistics errors {FingerprintStatistics}",
    //             JsonConvert.SerializeObject(fingerprintStatistics));
    //
    //         await _activityService.CreateActivityAsync(FingerprintingErrorActivities.Fingerprinting_StatisticsError,
    //             data: fingerprintStatistics,
    //             set: set => set
    //                 .TenantId(order?.Mid)
    //                 .CorrelationId(order?.Id));
    //
    //         fingerprintStatistics
    //             .CorrectStatistics(order); // can be incorrect when debugging, shouldn't in production 
    //     }
    // }

    private async Task AddFingerprintActivityAsync(PostgreSQLDbContext dbContext, FingerprintActivityType activityType,
        int amount, Order? order)
    {
        // Adding this activity to all currently loaded fingerprints relevant to this transaction
        var fingerprintActivities = Fingerprints
            .Select(x => new FingerprintActivity(x.Value, activityType, amount,
                order?.Mid, order?.Id, order?.PaymentInstrumentFingerprint, order?.IsCIT,
                contactId: null))
            .ToList();

        dbContext.FingerprintActivities.AddRange(fingerprintActivities);

        // using var dynamoDbClient = new DynamoDbClient(_dynamoDb);
        // try
        // {
        //     await dynamoDbClient.WriteBatchAsync(
        //         fingerprintActivities.Select(x =>
        //             new Eligibility.RiskManagement.Fingerprinting.DTO.FingerprintActivity
        //             {
        //                 Fingerprint = x.Fingerprint,
        //                 ActivityType_Date_Time = $"{x.ActivityType}#{x.CreatedOn.ToString("yyyy-MM-dd")}#{x.CreatedOn.ToString("HH:mm:ss")}",
        //                 ActionTimestamp = x.CreatedOn, //.Ticks.ToString(),
        //                 FingerprintType = x.FingerprintType,
        //                 ActivityType = activityType.ToString(),
        //                 Amount = amount,
        //                 OrderId = order?.Id,
        //                 Mid = order?.Mid,
        //                 Meta = null
        //             }).ToList());
        // }
        // catch (Exception e)
        // {
        //     Workspan.Current?.RecordException(e);
        //
        //     await _activityService.CreateActivityAsync(FingerprintingErrorActivities.Fingerprinting_Error,
        //         data: e);
        // }
    }

    #region [Commented] Fingerprint Transmit Activity Related Code

    // private async Task UpdateFingerprintTransmitActivityHistoryAsync(
    //     FingerprintActivityType activityType, int amount, bool evaluateCalled, Order? order = null)
    // {
    //     using var workspan = Workspan.Start<FingerprintService>();
    //
    //     if (!ShouldStoreFingerprintActivityHistory(activityType)) return;
    //
    //     // Storing history only for exact persona fingerprints 
    //     var exactPersonaFingerprints = Fingerprints
    //         .Where(x => x.Value.Category == FingerprintCategory.ExactPersona)
    //         .Select(x => x.Value);
    //
    //     var utcNow = DateTime.UtcNow;
    //
    //     foreach (var exactPersonaFingerprint in exactPersonaFingerprints)
    //     {
    //         var historyCacheKey =
    //             CacheKeyFactory.CreateHistoricalTransmitsFingerprintActivityKey(exactPersonaFingerprint.ValueHash);
    //
    //         try
    //         {
    //             FingerprintActivityHistory history = null;
    //
    //             #region Loading history from cache or creating new one
    //
    //             var serializedHistory = await _distributedCache.GetValueAsync<byte[]>(historyCacheKey.Key);
    //             if (serializedHistory != null)
    //             {
    //                 history = DeserializeFingerprintActivityHistoryFromBSON(serializedHistory);
    //             }
    //
    //
    //             if (history == null)
    //             {
    //                 history = new FingerprintActivityHistory();
    //                 history.History = new List<HistoricalFingerprintActivity>();
    //             }
    //
    //             #endregion
    //
    //             const int TRANSMIT_HISTORY_LOOKUP_DAYS = 90;
    //             const int TRANSMIT_HISTORY_LOOKUP_MAX_COUNT = 99; // so after current transmit we have 100 items in history 
    //
    //             #region Truncating history by TRANSMIT_HISTORY_LOOKUP_DAYS days
    //
    //             var transmitLookupWindowStartTime = utcNow.AddDays(-TRANSMIT_HISTORY_LOOKUP_DAYS);
    //
    //             var historyCount = history.History.Count;
    //             if (historyCount > TRANSMIT_HISTORY_LOOKUP_MAX_COUNT)
    //             {
    //                 workspan.Log.Information("Truncating fingerprint transmit history from {HistoryCount} to {LimitResults} results", 
    //                     historyCount, TRANSMIT_HISTORY_LOOKUP_MAX_COUNT);
    //                 
    //                 await _activityService.CreateActivityAsync(
    //                     FingerprintingActivities.Fingerprinting_FingerprintTransmitHistory_HistoryTruncated,
    //                     data: new
    //                     {
    //                         Fingerprint = exactPersonaFingerprint.HumanReadableDescription, 
    //                         Count = historyCount,
    //                         NewCount = TRANSMIT_HISTORY_LOOKUP_MAX_COUNT,
    //                     },
    //                     set: set => set
    //                         .TenantId(order?.Mid)
    //                         .CorrelationId(order?.Id));
    //             }
    //
    //             history.History = history.History
    //                 .Where(x => x.Time >= transmitLookupWindowStartTime)
    //                 .Skip(historyCount - TRANSMIT_HISTORY_LOOKUP_MAX_COUNT) // take last TRANSMIT_HISTORY_LOOKUP_MAX_COUNT as they are in ascending order
    //                 .ToList();
    //
    //             #endregion
    //
    //             history.History.Add(new HistoricalFingerprintActivity(activityType.ToString(), utcNow, amount));
    //
    //             #region Saving history to cache
    //
    //             await _distributedCache.SetAsync<byte[]>(historyCacheKey.Key, SerializeToBSON(history, history.Version),
    //                 historyCacheKey.CacheOptions);
    //
    //             #endregion
    //         }
    //         catch (Exception ex)
    //         {
    //             workspan.RecordException(ex, "Cannot load transmit fingerprint history");
    //         }
    //     }
    // }

    // private bool ShouldStoreFingerprintActivityHistory(FingerprintActivityType activityType)
    // {
    //     return activityType is FingerprintActivityType.NonDeclineTransmitted ||
    //            activityType is FingerprintActivityType.DeclineTransmitted ||
    //            activityType is FingerprintActivityType.InitialNonDecline;
    // }

    // byte[] SerializeToBSON<T>(T obj, int versionBytes)
    // {
    //     using (var ms = new MemoryStream())
    //     {
    //         byte[] ver = new byte[] {(byte) versionBytes};
    //         ms.Write(ver);
    //
    //         using (var writer = new BsonDataWriter(ms))
    //         {
    //             writer.DateTimeKindHandling = DateTimeKind.Utc;
    //             
    //             var serializer = new JsonSerializer();
    //             serializer.Serialize(writer, obj);
    //         }
    //
    //         return ms.ToArray();
    //     }
    // }
    //
    // T DeserializeFromBSON<T>(byte[] bson, int versionBytesCount = 0)
    // {
    //     // Deserialize from BSON
    //     MemoryStream ms = new MemoryStream(bson);
    //     using (BsonDataReader reader = new BsonDataReader(ms))
    //     {
    //         reader.DateTimeKindHandling = DateTimeKind.Utc;
    //         
    //         JsonSerializer serializer = new JsonSerializer();
    //
    //         if (versionBytesCount > 0) ms.Position = versionBytesCount;
    //
    //         return serializer.Deserialize<T>(reader);
    //     }
    // }
    //
    // FingerprintActivityHistory DeserializeFingerprintActivityHistoryFromBSON(byte[] bsonWithVersion)
    // {
    //     using var workspan = Workspan.Start<FingerprintService>();
    //
    //     byte version = bsonWithVersion[0];
    //
    //     switch (version)
    //     {
    //         case 1:
    //             return DeserializeFromBSON<FingerprintActivityHistory>(bsonWithVersion, 1);
    //         default:
    //             workspan.RecordError("Unsupported version of fingerprint activity history: {Version}", version);
    //             return null;
    //     }
    // }

    // public async Task<List<(IFingerprint Fingerprint, FingerprintActivityHistory History)>>
    //     GetFingerprintTransmitActivityHistoryAsync(Order order, int? maxResults)
    // {
    //     // History is stored only for exact persona fingerprints 
    //     var exactPersonaFingerprints = Fingerprints
    //         .Where(x => x.Value.Category == FingerprintCategory.ExactPersona)
    //         .Select(x => x.Value.ValueHash);
    //
    //     List<(IFingerprint Fingerprint, FingerprintActivityHistory History)> allFingerprintHistory = new();
    //     foreach (var exactPersonaFingerprint in exactPersonaFingerprints)
    //     {
    //         await GetFingerprintTransmitActivityHistoryAsync(exactPersonaFingerprint, allFingerprintHistory, order, maxResults);
    //     }
    //     
    //
    //
    //     return allFingerprintHistory;
    // }

    // private async Task GetFingerprintTransmitActivityHistoryAsync(IFingerprint exactPersonaFingerprint,
    //     List<(IFingerprint Fingerprint, FingerprintActivityHistory History)> allFingerprintHistory,
    //     Order order,
    //     int? maxResults)
    // {
    //     using var workspan = Workspan.Start<FingerprintService>();
    //
    //     var historyCacheKey =
    //         CacheKeyFactory.CreateHistoricalTransmitsFingerprintActivityKey(exactPersonaFingerprint.ValueHash);
    //     var serializedHistory = await _distributedCache.GetValueAsync<byte[]>(historyCacheKey.Key);
    //     if (serializedHistory != null)
    //     {
    //         var history = DeserializeFingerprintActivityHistoryFromBSON(serializedHistory);
    //
    //         var historyCount = history.History.Count;
    //         if (maxResults != null && historyCount > maxResults.Value)
    //         {
    //             workspan.Log.Information(
    //                 "Truncating fingerprint transmit history from {HistoryCount} to {LimitResults} results",
    //                 historyCount, maxResults.Value);
    //
    //             await _activityService.CreateActivityAsync(
    //                 FingerprintingActivities.Fingerprinting_FingerprintTransmitHistory_ResultsTruncated,
    //                 data: new
    //                 {
    //                     Fingerprint = exactPersonaFingerprint.HumanReadableDescription,
    //                     Count = historyCount,
    //                     NewCount = maxResults.Value,
    //                 },
    //                 set: set => set
    //                     .TenantId(order.Mid)
    //                     .CorrelationId(order.Id));
    //
    //             history.History = history.History
    //                 .Skip(history.History.Count -
    //                       maxResults.Value) // take last maxResults as they are in ascending order
    //                 .ToList();
    //         }
    //
    //         if (history != null)
    //         {
    //             allFingerprintHistory.Add((exactPersonaFingerprint, history));
    //         }
    //     }
    // }

    #endregion


    public IList<IFingerprint> GetActualFingerprints()
    {
        return Fingerprints.Select(x => x.Value).Where(x => x.IsActualFingerprint).ToList();
    }


    private async Task LoadAllFingerprintStatisticsAsync(Order? order, bool countAsFingerprintMatch,
        bool countAsEvaluate, bool allowDuplicateFingerprints, bool processingExpiredOffer)
    {
        using var workspan = Workspan.Start<FingerprintService>();

        // Creating local context because of async processing
        // So Task.Run() can be used to run this method in background
        using var serviceScope = _serviceScopeFactory.CreateScope();
        using var dbContext = serviceScope.ServiceProvider.GetRequiredService<PostgreSQLDbContext>();


        var loadFingerprintStatisticsQuery =
            from fingerprintStatistics in dbContext.FingerprintStatistics
            where Fingerprints.Keys.Contains(fingerprintStatistics.Fingerprint)
            select fingerprintStatistics;

        loadFingerprintStatisticsQuery = loadFingerprintStatisticsQuery
            .AsNoTracking(); // Fingerprint Statistics updated only using ExecuteUpdateAsync() 

        var statisticsFromDb = new Dictionary<string, FingerprintStatistics>();

        var allFingerprintStatistics = await loadFingerprintStatisticsQuery.ToListAsync();

        if (countAsEvaluate)
        {
            // Fixing incorrect statistics (only on evaluate)

            await FixIncorrectStatisticsAsync(dbContext, allFingerprintStatistics);
        }

        foreach (var fingerprintStatistics in allFingerprintStatistics)
        {
            if (!statisticsFromDb.TryAdd(fingerprintStatistics.Fingerprint, fingerprintStatistics))
            {
                if (!allowDuplicateFingerprints) // duplicate fingerprints are expected during re-evaluation
                {
                    Workspan.Current?.Log.Warning("Duplicate Fingerprint Skipped: {Fingerprint}",
                        fingerprintStatistics.Fingerprint);
                    await _activityService.CreateActivityAsync(FingerprintingErrorActivities
                        .Fingerprinting_DuplicateFingerprintSkipped, data: fingerprintStatistics.Fingerprint, set =>
                        set
                            .TenantId(order?.Mid)
                            .CorrelationId(order?.Id));
                }
            }
        }

        AtomicUpdatesBuilder fingerprintsUpdate = new();

        FingerprintStatistics = new();

        foreach (var fingerprintStatistics in statisticsFromDb.Values)
        {
            if (!FingerprintStatistics.TryAdd(fingerprintStatistics.Fingerprint, fingerprintStatistics))
            {
                Workspan.Current?.Log.Warning("Duplicate Fingerprint Skipped: {Fingerprint}",
                    fingerprintStatistics.Fingerprint);
                await _activityService.CreateActivityAsync(FingerprintingErrorActivities
                    .Fingerprinting_DuplicateFingerprintSkipped, data: fingerprintStatistics.Fingerprint, set =>
                    set
                        .TenantId(order?.Mid)
                        .CorrelationId(order?.Id));
            }
        }

        await CreateMissingFingerprintStatisticsAsync(dbContext, Fingerprints);

        var utcNow = DateTime.UtcNow;

        if (countAsFingerprintMatch || countAsEvaluate)
        {
            await dbContext.FingerprintStatistics
                .Where(x => Fingerprints.Keys.Contains(x.Fingerprint))
                .ExecuteUpdateAsync(statistics => statistics
                    .SetProperty(s => s.TotalMatches, s => s.TotalMatches + (countAsFingerprintMatch ? 1 : 0))
                    .SetProperty(s => s.EvaluateMatches, s => s.EvaluateMatches + (countAsEvaluate ? 1 : 0))
                    .SetProperty(s => s.ModifiedOn, s => utcNow));
        }

        foreach (var fingerprint in Fingerprints)
        {
            #region Updating Statistics

            using (fingerprintsUpdate
                       .StartItemUpdate<Eligibility.RiskManagement.Fingerprinting.Tables.FingerprintStatistics>(
                           fingerprint.Value.ValueHash))
            {
                if (!processingExpiredOffer)
                {
                    fingerprintsUpdate.Initialize(
                        nameof(Eligibility.RiskManagement.Fingerprinting.Tables.FingerprintStatistics.FingerprintType),
                        fingerprint.Value.Type.ToString());

                    fingerprintsUpdate.Initialize(
                        nameof(Eligibility.RiskManagement.Fingerprinting.Tables.FingerprintStatistics.CreatedOn),
                        utcNow);
                }

                fingerprintsUpdate.Set(
                    nameof(Eligibility.RiskManagement.Fingerprinting.Tables.FingerprintStatistics.ModifiedOn), utcNow);


                if (countAsFingerprintMatch || countAsEvaluate)
                {
                    if (countAsFingerprintMatch)
                    {
                        fingerprintsUpdate.Add(
                            nameof(Eligibility.RiskManagement.Fingerprinting.Tables.FingerprintStatistics.TotalMatches),
                            +1);
                    }

                    if (countAsEvaluate)
                    {
                        fingerprintsUpdate.Add(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                            .FingerprintStatistics
                            .EvaluateMatches), +1);
                    }
                }
            }

            #endregion

            //await CheckAndCorrectFingerprintStatistics(order, fingerprintStatistics, processingExpiredOffer);
        }

        // Updated statistics will contain latest values from DynamoDB after update
        Dictionary<string, Eligibility.RiskManagement.Fingerprinting.Tables.FingerprintStatistics>
            updatedStatisticsFromDynamo = null;

        // try
        // {
        //     updatedStatisticsFromDynamo = (await fingerprintsUpdate
        //         .UpdateAndGetCurrentOrDefaultAsync<Eligibility.RiskManagement.Fingerprinting.DTO
        //             .FingerprintStatistics>(_dynamoDb)).ToDictionary(x => x.Fingerprint, x => x);
        //
        //     workspan.Log
        //         .Enrich("RDB", JsonConvert.SerializeObject(statisticsFromDb))
        //         .Enrich("Dynamo", JsonConvert.SerializeObject(updatedStatisticsFromDynamo))
        //         .Information("Fingerprint statistics loaded");
        //
        //     await _activityService.CreateActivityAsync(
        //         FingerprintingActivities.Fingerprinting_FingerprintStatisticsReceived_DynamoDB,
        //         data: updatedStatisticsFromDynamo, set => set
        //             .TenantId(order?.Mid)
        //             .CorrelationId(order?.Id));
        // }
        // catch (Exception e)
        // {
        //     workspan.RecordException(e);
        //     await _activityService.CreateActivityAsync(FingerprintingErrorActivities.Fingerprinting_Error,
        //         data: e);
        // }


        #region Comparing statistics from DynamoDB and PostgreSQL

        if (statisticsFromDb?.Count != updatedStatisticsFromDynamo?.Count)
        {
            // workspan.Log
            //     .Warning(
            //         "Fingerprint statistics count mismatch: {FromRdbCount} vs {FromDynamoCount}",
            //         statisticsFromDb?.Count, updatedStatisticsFromDynamo?.Count);
        }

        foreach (var fingerprint in Fingerprints)
        {
            var fingerprintStatisticsFromDynamo = updatedStatisticsFromDynamo?.GetValueOrDefault(fingerprint.Key);
            var fingerprintStatisticsFromDb = statisticsFromDb?.GetValueOrDefault(fingerprint.Key);

            var fingerprintStatisticsEntity = CreateFingerprintStatisticsEntity(fingerprintStatisticsFromDynamo);

            CompareStatistics(fingerprint.Key, fingerprint.Value.Type,
                fingerprintStatisticsEntity, fingerprintStatisticsFromDb);
        }

        #endregion
    }

    private async Task FixIncorrectStatisticsAsync(PostgreSQLDbContext dbContext,
        List<FingerprintStatistics> allFingerprintStatistics)
    {
        using var workspan = Workspan.Start<FingerprintService>();

        try
        {
            List<Guid> fingerprintsToResetOpenOffers = new();
            List<Guid> fingerprintsToResetOpenOrders = new();

            foreach (var fingerprintStatistics in allFingerprintStatistics)
            {
                CheckStatisticsAsync(fingerprintStatistics, out bool wrongOpenOffers, out bool wrongOpenOrders);

                if (wrongOpenOffers)
                {
                    fingerprintsToResetOpenOffers.Add(fingerprintStatistics.Id);

                    // Update in-memory statistics
                    fingerprintStatistics.SetTotalOpenOffers(0);
                    fingerprintStatistics.SetTotalOpenOffersAmount(0);
                }

                if (wrongOpenOrders)
                {
                    fingerprintsToResetOpenOrders.Add(fingerprintStatistics.Id);

                    // Update in-memory statistics
                    fingerprintStatistics.SetTotalOpenOrders(0);
                    fingerprintStatistics.SetTotalOpenOrdersAmount(0);
                }
            }

            await FixIncorrectStatisticsInDatabaseAsync(dbContext, allFingerprintStatistics,
                fingerprintsToResetOpenOffers,
                fingerprintsToResetOpenOrders);
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
        }
    }

    private void CheckStatisticsAsync(FingerprintStatistics fingerprintStatistics,
        out bool wrongOpenOffers, out bool wrongOpenOrders)
    {
        var workspan = Workspan.Current!
            .Tag("Fingerprint", fingerprintStatistics.Fingerprint);

        wrongOpenOffers = false;
        wrongOpenOrders = false;

        DateTime monthAgoUtc = DateTime.UtcNow.AddMonths(-1);

        #region Fixing stale statistics

        bool lastAuthorizationAttemptIsMoreThanMonthAgo =
            !fingerprintStatistics.LastAuthorizationAttempt.HasValue ||
            fingerprintStatistics.LastAuthorizationAttempt < monthAgoUtc;

        if (fingerprintStatistics.ModifiedOn <= monthAgoUtc || lastAuthorizationAttemptIsMoreThanMonthAgo
           )
        {
            // There couldn't be any open offers after a month
            if (fingerprintStatistics.TotalOpenOffers != 0)
            {
                workspan.Log.Warning("Stale statistics: OpenOffers reset");

                wrongOpenOffers = true;
            }
        }

        // old statistics can be broken
        if (fingerprintStatistics.CreatedOn < new DateTime(2024, 11, 12) &&
            fingerprintStatistics.ModifiedOn < new DateTime(2024, 11, 12))
        {
            if (fingerprintStatistics.TotalOpenOrders != 0)
            {
                workspan.Log.Warning("Stale statistics: OpenOrders reset");

                wrongOpenOrders = true;
            }
        }

        #endregion
    }


    private async Task FixIncorrectStatisticsInDatabaseAsync(PostgreSQLDbContext dbContext,
        List<FingerprintStatistics> allFingerprintStatistics,
        List<Guid> fingerprintsToResetOpenOffers, List<Guid> fingerprintsToResetOpenOrders)
    {
        var allStatisticsToFix =
            fingerprintsToResetOpenOffers
                .Union(fingerprintsToResetOpenOrders)
                .ToList();

        await dbContext.FingerprintStatistics
            .Where(x => allStatisticsToFix.Contains(x.Id))
            .ExecuteUpdateAsync(statistics =>
                statistics
                    .SetProperty(s => s.TotalOpenOffers,
                        s => fingerprintsToResetOpenOffers.Contains(s.Id) ? 0 : s.TotalOpenOffers)
                    .SetProperty(s => s.TotalOpenOffersAmount,
                        s => fingerprintsToResetOpenOffers.Contains(s.Id) ? 0 : s.TotalOpenOffersAmount)
                    .SetProperty(s => s.TotalOpenOrders,
                        s => fingerprintsToResetOpenOrders.Contains(s.Id) ? 0 : s.TotalOpenOrders)
                    .SetProperty(s => s.TotalOpenOrdersAmount,
                        s => fingerprintsToResetOpenOrders.Contains(s.Id) ? 0 : s.TotalOpenOrdersAmount)
                    .SetProperty(s => s.ModifiedOn, s => DateTime.UtcNow));
    }

    private async Task CreateMissingFingerprintStatisticsAsync(PostgreSQLDbContext dbContext,
        IDictionary<string, IFingerprint> fingerprints)
    {
        var fingerprintStatisticsToCreate = fingerprints.Keys
            .Except(FingerprintStatistics.Keys)
            .ToList();

        if (fingerprintStatisticsToCreate.Any())
        {
            foreach (var statisticToCreateFingerprint in fingerprintStatisticsToCreate)
            {
                var fingerprintToCreateStatisticsFor = fingerprints[statisticToCreateFingerprint];
                var fingerprintStatistics = new FingerprintStatistics(
                    fingerprintToCreateStatisticsFor.ValueHash,
                    fingerprintToCreateStatisticsFor.Type.ToString());

                dbContext.FingerprintStatistics.Add(fingerprintStatistics);

                FingerprintStatistics.Add(fingerprintStatistics.Fingerprint, fingerprintStatistics);
            }

            await dbContext.SaveChangesAsync();
        }
    }

    private FingerprintStatistics CreateFingerprintStatisticsEntity(
        Eligibility.RiskManagement.Fingerprinting.Tables.FingerprintStatistics? statistics)
    {
        if (statistics == null) return null;

        return new FingerprintStatistics(statistics);
    }

    private FingerprintStatistics CompareStatistics(string fingerprint,
        FingerprintType fingerprintType, FingerprintStatistics? statisticsFromDynamo,
        FingerprintStatistics? statisticsFromRdb)
    {
        var workspan = Workspan.Current!
            .Tag("Fingerprint", fingerprint)
            .Tag("FingerprintType", fingerprintType);

        if (statisticsFromDynamo == null && statisticsFromRdb == null)
        {
            return null;
        }
        else if (statisticsFromDynamo != null && statisticsFromRdb == null)
        {
            //workspan.Log.Information("Fingerprint statistics found only in DynamoDB");
            return statisticsFromDynamo;
        }
        else if (statisticsFromDynamo == null && statisticsFromRdb != null)
        {
            //workspan.Log.Information("Fingerprint statistics found only in RDB");
            return statisticsFromRdb;
        }

        //use greater value and log which one has been used
        var resultStatistics = new FingerprintStatistics(fingerprint, fingerprintType.ToString());

        resultStatistics.Id = statisticsFromRdb.Id;

        resultStatistics.CreatedOn = statisticsFromRdb.CreatedOn;
        resultStatistics.ModifiedOn = statisticsFromRdb.ModifiedOn;
        resultStatistics.SetMeta(statisticsFromDynamo?.Meta ?? statisticsFromRdb?.Meta);

        resultStatistics.SetTotalMatches(UseMaxAndLog(nameof(Entities.FingerprintStatistics.TotalMatches),
            statisticsFromDynamo.TotalMatches, statisticsFromRdb.TotalMatches));
        resultStatistics.SetEvaluateMatches(UseMaxAndLog(nameof(Entities.FingerprintStatistics.EvaluateMatches),
            statisticsFromDynamo.EvaluateMatches, statisticsFromRdb.EvaluateMatches));

        resultStatistics.SetTotalOpenOffers(UseMaxAndLog(nameof(Entities.FingerprintStatistics.TotalOpenOffers),
            statisticsFromDynamo.TotalOpenOffers, statisticsFromRdb.TotalOpenOffers));
        resultStatistics.SetTotalOpenOffersAmount(UseMaxAndLog(
            nameof(Entities.FingerprintStatistics.TotalOpenOffersAmount), statisticsFromDynamo.TotalOpenOffersAmount,
            statisticsFromRdb.TotalOpenOffersAmount));

        resultStatistics.SetTotalOpenOrders(UseMaxAndLog(nameof(Entities.FingerprintStatistics.TotalOpenOrders),
            statisticsFromDynamo.TotalOpenOrders, statisticsFromRdb.TotalOpenOrders));
        resultStatistics.SetTotalOpenOrdersAmount(UseMaxAndLog(
            nameof(Entities.FingerprintStatistics.TotalOpenOrdersAmount), statisticsFromDynamo.TotalOpenOrdersAmount,
            statisticsFromRdb.TotalOpenOrdersAmount));

        resultStatistics.SetTotalFullyPaidInOrders(UseMaxAndLog(
            nameof(Entities.FingerprintStatistics.TotalFullyPaidInOrders), statisticsFromDynamo.TotalFullyPaidInOrders,
            statisticsFromRdb.TotalFullyPaidInOrders));
        resultStatistics.SetTotalPaidInAmount(UseMaxAndLog(nameof(Entities.FingerprintStatistics.TotalPaidInAmount),
            statisticsFromDynamo.TotalPaidInAmount, statisticsFromRdb.TotalPaidInAmount));

        resultStatistics.SetTotalWrittenOffOrders(UseMaxAndLog(
            nameof(Entities.FingerprintStatistics.TotalWrittenOffOrders), statisticsFromDynamo.TotalWrittenOffOrders,
            statisticsFromRdb.TotalWrittenOffOrders));
        resultStatistics.SetTotalWrittenOffOrdersAmount(UseMaxAndLog(
            nameof(Entities.FingerprintStatistics.TotalWrittenOffOrdersAmount),
            statisticsFromDynamo.TotalWrittenOffOrdersAmount, statisticsFromRdb.TotalWrittenOffOrdersAmount));

        return resultStatistics;
    }

    private int UseMaxAndLog(string valueName, int valueFromDynamo, int valueFromDb)
    {
        if (valueFromDynamo > valueFromDb)
        {
            Workspan.Current?.Log.Information("{ValueName} from RDB is lower:  {ValueFromRdb} < {ValueFromDynamo}",
                valueName,
                valueFromDb, valueFromDynamo);
            return valueFromDynamo;
        }
        else if (valueFromDynamo != valueFromDb)
        {
            Workspan.Current?.Log.Information("{ValueName} from RDB is greater: {ValueFromRdb} > {ValueFromDynamo}",
                valueName,
                valueFromDb, valueFromDynamo);
            return valueFromDb;
        }
        else
        {
            Workspan.Current?.Log.Information("{ValueName} equals: {ValueFromDynamo}", valueName, valueFromDynamo);
            return valueFromDynamo;
        }
    }

    private long UseMaxAndLog(string valueName, long valueFromDynamo, long valueFromDb)
    {
        if (valueFromDynamo > valueFromDb)
        {
            Workspan.Current?.Log.Information("{ValueName} from DynamoDB is greater: {ValueFromDynamo}", valueName,
                valueFromDynamo);
            return valueFromDynamo;
        }
        else if (valueFromDynamo != valueFromDb)
        {
            Workspan.Current?.Log.Information("{ValueName} from RDB is greater: {ValueFromRdb}", valueName,
                valueFromDb);
            return valueFromDb;
        }
        else
        {
            Workspan.Current?.Log.Information("{ValueName} equals: {ValueFromDynamo}", valueName, valueFromDynamo);
            return valueFromDynamo;
        }
    }

    protected IEnumerable<IFingerprint> GetActualFingerprints(IFingerprintable fingerprintable,
        Dictionary<FingerprintType, IFingerprint> sourceFingerprints, bool populateFingerprintMeta)
    {
        return GetFingerprints(fingerprintable, sourceFingerprints, populateFingerprintMeta)
            .Where(x => x.IsActualFingerprint);
    }

    public IList<IFingerprint> GetFingerprints(IFingerprintable fingerprintable,
        Dictionary<FingerprintType, IFingerprint> sourceFingerprints, bool populateFingerprintMeta)
    {
        List<IFingerprint> fingerprints = new();

        var fingerprintsFromAttributes = fingerprintable.GetFingerprintsFromAttributes(populateFingerprintMeta);
        fingerprints.AddRange(fingerprintsFromAttributes);

        foreach (var fingerprintFromAttribute in fingerprintsFromAttributes)
        {
            sourceFingerprints.Add(fingerprintFromAttribute.Type, fingerprintFromAttribute);
        }

        fingerprints.AddRange(fingerprintable.CreateCustomFingerprints(sourceFingerprints));

        foreach (var fingerprint in fingerprints.Where(x => x.IsActualFingerprint))
        {
            // Each fingerprint can be the source for another fingerprint 
            sourceFingerprints.TryAdd(fingerprint.Type, fingerprint);
        }

        return fingerprints;
    }

    #endregion

    public async Task ProcessTransmitOrEvaluateRequestAsync(TransmitAndEvaluateRequestBase request,
        bool evaluateCalled, bool offSessionRetry, Order? order = null)
    {
        // Creating local context because of async processing
        // So Task.Run() can be used to run this method in background
        using var serviceScope = _serviceScopeFactory.CreateScope();
        using var dbContext = serviceScope.ServiceProvider.GetRequiredService<PostgreSQLDbContext>();

        //if (FingerprintsEnabler.IsFingerprintStatisticsEnabledForOrder(order))
        //{
        await LoadFingerprintsForRequest(request, order, true, evaluateCalled, true);

        FingerprintActivityType activityType;
        if (evaluateCalled)
        {
            activityType = FingerprintActivityType.InitialNonDecline;
        }
        else
        {
            activityType = request.IsDeclined == false
                ? FingerprintActivityType.NonDeclineTransmitted
                : FingerprintActivityType.DeclineTransmitted;
        }

        var transactionAmount = request.Transaction.Amount;

        await AddFingerprintActivityAsync(dbContext, activityType, transactionAmount, order);

        await dbContext.SaveChangesAsync();
        //}
    }

    private async Task LoadFingerprintsForRequest(IFingerprintable request, Order? order,
        bool countAsFingerprintMatch, bool countAsEvaluate,
        bool allowDuplicateFingerprints,
        bool processingExpiredOffer = false,
        bool populateFingerprintMeta = false,
        bool loadFingerprintStatistics = true)
    {
        Fingerprints = new Dictionary<string, IFingerprint>();

        Dictionary<FingerprintType, IFingerprint> sourceFingerprints = new();

        if (order != null) await AddActualFingerprintsAsync(order, sourceFingerprints, order, populateFingerprintMeta);

        await AddActualFingerprintsAsync(request, sourceFingerprints, order, populateFingerprintMeta);

        //if (FingerprintsEnabler.IsFingerprintStatisticsEnabledForOrder(order))
        //{
        if (loadFingerprintStatistics)
        {
            await LoadAllFingerprintStatisticsAsync(order, countAsFingerprintMatch, countAsEvaluate,
                allowDuplicateFingerprints, processingExpiredOffer);
        }
        //}
    }

    private async Task AddActualFingerprintsAsync(IFingerprintable fingerprintable,
        Dictionary<FingerprintType, IFingerprint> sourceFingerprints, Order order, bool populateFingerprintMeta)
    {
        using var workspan = Workspan.Start<FingerprintService>();

        try
        {
            var fingerprints = GetActualFingerprints(fingerprintable, sourceFingerprints, populateFingerprintMeta);
            var fingerprintsDictionary = fingerprints.ToDictionary(x => x.ValueHash, x => x);
            foreach (var fingerprint in fingerprintsDictionary)
            {
                Fingerprints.Add(fingerprint);
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            await _activityService.CreateActivityAsync(
                FingerprintingErrorActivities.Fingerprinting_UnableToLoadActualFingerprints, e,
                set: set => set
                    .CorrelationId(order?.Id)
                    .TenantId(order?.Mid));
        }
    }

    // public async Task ProcessExpiredOffersAsync(IEnumerable<Order> expiredOffers)
    // {
    //     using var workspan = Workspan.Start<FingerprintService>();
    //
    //     if (!expiredOffers.Any()) return;
    //
    //     foreach (var expiredOffer in expiredOffers)
    //     {
    //         workspan.Log.Information("Processing fingerprints for expired order: {OrderId}", expiredOffer.Id);
    //
    //         try
    //         {
    //             await ProcessExpiredOfferAsync(expiredOffer);
    //         }
    //         catch (Exception e)
    //         {
    //             workspan.RecordException(e);
    //             await _activityService.CreateActivityAsync(EligibilityErrorActivities.ExpiredOffers_Error, e,
    //                 set: set => set
    //                     .CorrelationId(expiredOffer.Id)
    //                     .TenantId(expiredOffer.Mid));
    //         }
    //     }
    //
    //     await _dbContext.SaveChangesAsync();
    // }

    private async Task LoadFingerprintsForOrderAsync(Order order, bool allowDuplicateFingerprints,
        bool processingExpiredOffer,
        bool populateFingerprintMeta,
        bool loadFingerprintStatistics = true)
    {
        var requestToFingerprint = JsonConvert.DeserializeObject<EvaluateRequest>(order.Payload);

        //if (FingerprintsEnabler.IsFingerprintStatisticsEnabledForOrder(order))
        //{
        await LoadFingerprintsForRequest(requestToFingerprint, order,
            false, false, allowDuplicateFingerprints, processingExpiredOffer,
            populateFingerprintMeta, loadFingerprintStatistics);
        //}
    }

    public async Task LoadFingerprintsForOrderAsync(Order order, bool populateFingerprintMeta,
        bool loadFingerprintStatistics = true)
    {
        await LoadFingerprintsForOrderAsync(order, false, false, populateFingerprintMeta, loadFingerprintStatistics);
    }

    public async Task UpdateFingerprintsForOrderAsync(Order order)
    {
        await LoadFingerprintsForOrderAsync(order, true, false, false);
    }

    public bool AreFingerprintsLoaded => Fingerprints != null;

    public async Task ProcessOfferCreatedAsync(Order order)
    {
        using var workspan = Workspan.Start<FingerprintService>()
            .Baggage("OrderId", order.Id)
            .Baggage("Mid", order.Mid);

        workspan.Log.Information("Processing offer created");

        if (Fingerprints == null)
        {
            workspan.Log.Information("Loading fingerprints");
            await LoadFingerprintsForOrderAsync(order, false);
        }

        await ProcessFingerprintActivityAsync(FingerprintActivityType.OfferCreated, order,
            async (statisticsUpdater) =>
            {
                statisticsUpdater.Add(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalOpenOffers), 1);

                statisticsUpdater.Add(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalOpenOffersAmount), order.Amount);
            },
            async (fingerprints, dbContext) =>
            {
                await dbContext.FingerprintStatistics
                    .Where(x => fingerprints.Contains(x.Fingerprint))
                    .ExecuteUpdateAsync(statistics => statistics
                        .SetProperty(s => s.TotalOpenOffers, s => s.TotalOpenOffers + 1)
                        .SetProperty(s => s.TotalOpenOffersAmount, s => s.TotalOpenOffersAmount + order.Amount)
                        .SetProperty(s => s.ModifiedOn, s => DateTime.UtcNow));
            });
    }

    public async Task ProcessOfferNotEligibleEverAsync(Order order)
    {
        using var workspan = Workspan.Start<FingerprintService>()
            .Baggage("OrderId", order.Id)
            .Baggage("Mid", order.Mid);

        workspan.Log.Information("Processing offer not eligible ever");

        // Fingerprints could be updated -> load them again
        await LoadFingerprintsForOrderAsync(order, false, loadFingerprintStatistics: false);

        // Workspan.Current?.Log.Information("Current fingerprint statistics: {Fingerprints}",
        //     JsonConvert.SerializeObject(this.FingerprintStatistics));

        await ProcessFingerprintActivityAsync(FingerprintActivityType.OfferNotEligible, order,
            async (statisticsUpdater) =>
            {
                statisticsUpdater.Subtract(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalOpenOffers), 1);

                statisticsUpdater.Subtract(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalOpenOffersAmount), order.Amount);
            },
            async (fingerprints, dbContext) =>
            {
                await dbContext.FingerprintStatistics
                    .Where(x => fingerprints.Contains(x.Fingerprint))
                    .ExecuteUpdateAsync(statistics => statistics
                        .SetProperty(s => s.TotalOpenOffers, s => s.TotalOpenOffers - 1)
                        .SetProperty(s => s.TotalOpenOffersAmount, s => s.TotalOpenOffersAmount - order.Amount)
                        .SetProperty(s => s.ModifiedOn, s => DateTime.UtcNow));
            });
    }

    public async Task ProcessOfferOnHoldAsync(Order order)
    {
        using var workspan = Workspan.Start<FingerprintService>()
            .Baggage("OrderId", order.Id)
            .Baggage("Mid", order.Mid);

        workspan.Log.Information("Processing offer on hold");

        // Fingerprints could be updated -> load them again
        await LoadFingerprintsForOrderAsync(order, false, loadFingerprintStatistics: false);

        // Workspan.Current?.Log.Information("Current fingerprint statistics: {Fingerprints}",
        //     JsonConvert.SerializeObject(this.FingerprintStatistics));

        await ProcessFingerprintActivityAsync(FingerprintActivityType.OfferOnHold, order,
            async (statisticsUpdater) =>
            {
                statisticsUpdater.Subtract(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalOpenOffers), 1);

                statisticsUpdater.Subtract(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalOpenOffersAmount), order.Amount);
            },
            async (fingerprints, dbContext) =>
            {
                await dbContext.FingerprintStatistics
                    .Where(x => fingerprints.Contains(x.Fingerprint))
                    .ExecuteUpdateAsync(statistics => statistics
                        .SetProperty(s => s.TotalOpenOffers, s => s.TotalOpenOffers - 1)
                        .SetProperty(s => s.TotalOpenOffersAmount, s => s.TotalOpenOffersAmount - order.Amount)
                        .SetProperty(s => s.ModifiedOn, s => DateTime.UtcNow));
            });
    }

    public async Task ProcessResumedOfferAsync(Order order)
    {
        using var workspan = Workspan.Start<FingerprintService>()
            .Baggage("OrderId", order.Id)
            .Baggage("Mid", order.Mid);

        workspan.Log.Information("Processing offer resumed");

        // Fingerprints could be updated -> load them again
        await LoadFingerprintsForOrderAsync(order, false, loadFingerprintStatistics: false);

        // Workspan.Current?.Log.Information("Current fingerprint statistics: {Fingerprints}",
        //     JsonConvert.SerializeObject(this.FingerprintStatistics));

        await ProcessFingerprintActivityAsync(FingerprintActivityType.OfferResumed, order,
            async (statisticsUpdater) =>
            {
                statisticsUpdater.Subtract(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalOpenOffers), 1);

                statisticsUpdater.Subtract(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalOpenOffersAmount), order.Amount);
            },
            async (fingerprints, dbContext) =>
            {
                await dbContext.FingerprintStatistics
                    .Where(x => fingerprints.Contains(x.Fingerprint))
                    .ExecuteUpdateAsync(statistics => statistics
                        .SetProperty(s => s.TotalOpenOffers, s => s.TotalOpenOffers + 1)
                        .SetProperty(s => s.TotalOpenOffersAmount, s => s.TotalOpenOffersAmount + order.Amount)
                        .SetProperty(s => s.ModifiedOn, s => DateTime.UtcNow));
            });
    }

    public async Task ProcessOrderCreatedAsync(Order order)
    {
        using var workspan = Workspan.Start<FingerprintService>()
            .Baggage("OrderId", order.Id)
            .Baggage("Mid", order.Mid);

        workspan.Log.Information("Processing order created");

        await LoadFingerprintsForOrderAsync(order, false,
            loadFingerprintStatistics: false); // don't need statistics current values to perform updates

        await ProcessFingerprintActivityAsync(FingerprintActivityType.OrderCreated, order,
            async (statisticsUpdater) =>
            {
                statisticsUpdater.Add(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalOpenOrders), 1);

                statisticsUpdater.Add(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalOpenOrdersAmount), order.Amount);

                statisticsUpdater.Subtract(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalOpenOffers), 1);

                statisticsUpdater.Subtract(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalOpenOffersAmount), order.Amount);
            },
            async (fingerprints, dbContext) =>
            {
                await dbContext.FingerprintStatistics
                    .Where(x => fingerprints.Contains(x.Fingerprint))
                    .ExecuteUpdateAsync(statistics => statistics
                        .SetProperty(s => s.TotalOpenOrders, s => s.TotalOpenOrders + 1)
                        .SetProperty(s => s.TotalOpenOrdersAmount, s => s.TotalOpenOrdersAmount + order.Amount)
                        .SetProperty(s => s.TotalOpenOffers, s => s.TotalOpenOffers - 1)
                        .SetProperty(s => s.TotalOpenOffersAmount, s => s.TotalOpenOffersAmount - order.Amount)
                        .SetProperty(s => s.ModifiedOn, s => DateTime.UtcNow));
            });
    }

    public async Task ProcessExpiredOfferAsync(Order order)
    {
        using var workspan = Workspan.Start<FingerprintService>()
            .Baggage("OrderId", order.Id)
            .Baggage("Mid", order.Mid);

        workspan.Log.Information("Processing offer expired");

        await LoadFingerprintsForOrderAsync(order,
            allowDuplicateFingerprints: false,
            processingExpiredOffer: true, populateFingerprintMeta: false,
            loadFingerprintStatistics: false);

        await ProcessFingerprintActivityAsync(FingerprintActivityType.OfferExpired, order,
            async (statisticsUpdater) =>
            {
                statisticsUpdater.Subtract(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalOpenOffers), 1);

                statisticsUpdater.Subtract(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalOpenOffersAmount), order.Amount);
            },
            async (fingerprints, dbContext) =>
            {
                await dbContext.FingerprintStatistics
                    .Where(x => fingerprints.Contains(x.Fingerprint))
                    .ExecuteUpdateAsync(statistics => statistics
                        .SetProperty(s => s.TotalOpenOffers, s => s.TotalOpenOffers - 1)
                        .SetProperty(s => s.TotalOpenOffersAmount, s => s.TotalOpenOffersAmount - order.Amount)
                        .SetProperty(s => s.ModifiedOn, s => DateTime.UtcNow));
            });
    }

    public async Task ProcessFullyPaidInOrderAsync(Order order)
    {
        using var workspan = Workspan.Start<FingerprintService>()
            .Baggage("OrderId", order.Id)
            .Baggage("Mid", order.Mid);

        workspan.Log.Information("Processing order fully paid in");

        await LoadFingerprintsForOrderAsync(order, false, loadFingerprintStatistics: false);

        await ProcessFingerprintActivityAsync(FingerprintActivityType.RepaymentProcessed, order,
            async (statisticsUpdater) =>
            {
                statisticsUpdater.Subtract(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalOpenOrders), 1);

                statisticsUpdater.Subtract(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalOpenOrdersAmount), order.Amount);

                statisticsUpdater.Add(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalFullyPaidInOrders), 1);

                statisticsUpdater.Add(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalPaidInAmount), order.Amount);
            },
            async (fingerprints, dbContext) =>
            {
                await dbContext.FingerprintStatistics
                    .Where(x => fingerprints.Contains(x.Fingerprint))
                    .ExecuteUpdateAsync(statistics => statistics
                        .SetProperty(s => s.TotalOpenOrders, s => s.TotalOpenOrders - 1)
                        .SetProperty(s => s.TotalOpenOrdersAmount, s => s.TotalOpenOrdersAmount - order.Amount)
                        .SetProperty(s => s.TotalFullyPaidInOrders, s => s.TotalFullyPaidInOrders + 1)
                        .SetProperty(s => s.TotalPaidInAmount, s => s.TotalPaidInAmount + order.Amount)
                        .SetProperty(s => s.ModifiedOn, s => DateTime.UtcNow)
                    );
            });
    }

    public async Task ProcessWrittenOffOrderAsync(Order order, int writtenOffAmount)
    {
        using var workspan = Workspan.Start<FingerprintService>()
            .Baggage("OrderId", order.Id)
            .Baggage("Mid", order.Mid);

        workspan.Log.Information("Processing order written off");

        await LoadFingerprintsForOrderAsync(order, false, loadFingerprintStatistics: false);

        await ProcessFingerprintActivityAsync(FingerprintActivityType.OrderWrittenOff, order,
            async (statisticsUpdater) =>
            {
                statisticsUpdater.Subtract(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalOpenOrders), 1);

                statisticsUpdater.Subtract(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalOpenOrdersAmount), order.Amount);

                statisticsUpdater.Add(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalWrittenOffOrders), 1);

                statisticsUpdater.Add(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.TotalWrittenOffOrdersAmount), order.Amount);
            },
            async (fingerprints, dbContext) =>
            {
                await dbContext.FingerprintStatistics
                    .Where(x => fingerprints.Contains(x.Fingerprint))
                    .ExecuteUpdateAsync(statistics => statistics
                        .SetProperty(s => s.TotalOpenOrders, s => s.TotalOpenOrders - 1)
                        .SetProperty(s => s.TotalOpenOrdersAmount, s => s.TotalOpenOrdersAmount - order.Amount)
                        .SetProperty(s => s.TotalWrittenOffOrders, s => s.TotalWrittenOffOrders + 1)
                        .SetProperty(s => s.TotalWrittenOffOrdersAmount,
                            s => s.TotalWrittenOffOrdersAmount + order.Amount)
                        .SetProperty(s => s.ModifiedOn, s => DateTime.UtcNow)
                    );
            });
    }

    public async Task ProcessAuthorizationAttemptedAsync(Order order)
    {
        using var workspan = Workspan.Start<FingerprintService>()
            .Baggage("OrderId", order.Id)
            .Baggage("Mid", order.Mid);

        workspan.Log.Information("Processing authorization attempted");

        if (Fingerprints == null)
            await LoadFingerprintsForOrderAsync(order, false, loadFingerprintStatistics: false);

        await ProcessFingerprintActivityAsync(FingerprintActivityType.AuthorizationAttempted, order,
            async (statisticsUpdater) =>
            {
                statisticsUpdater.Set(nameof(Eligibility.RiskManagement.Fingerprinting.Tables
                    .FingerprintStatistics.LastAuthorizationAttempt), DateTime.UtcNow);
            },
            async (fingerprints, dbContext) =>
            {
                await dbContext.FingerprintStatistics
                    .Where(x => fingerprints.Contains(x.Fingerprint))
                    .ExecuteUpdateAsync(statistics => statistics
                        .SetProperty(s => s.LastAuthorizationAttempt, s => DateTime.UtcNow)
                        .SetProperty(s => s.ModifiedOn, s => DateTime.UtcNow)
                    );
            },
            addFingerprintActivity: false); // only update statistics
    }

    public IDictionary<string, FingerprintStatistics> GetFingerprintStatistics()
    {
        return FingerprintStatistics;
    }
}