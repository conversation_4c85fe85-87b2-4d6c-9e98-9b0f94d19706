using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Services.EligibilityService;
using FlexCharge.Eligibility.Services.Orders;
using FlexCharge.Eligibility.Services.Orders.OrderStates;
using FlexCharge.Eligibility.Services.PaymentsService;
using FlexCharge.Eligibility.Services.RiskManagement;
using FlexCharge.Utils;
using FlexCharge.Utils.Concurrency;
using Microsoft.AspNetCore.Mvc.Diagnostics;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.Services.ProcessExpiredOrdersService;

public class ProcessExpiredOrdersService : IProcessExpiredOrdersService
{
    private static ConcurrentBoolean _isFirstExpiredOffersCheck = new(true);
    private static ConcurrentLong _inProcessingReenteranceLock = new(0);

    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly ReadOnlyPostgreSQLDbContext _readOnlyPostgreSqlDbContext;
    private readonly IOrderStateMachine _orderStateMachine;
    private readonly IPaymentsService _paymentsService;
    private readonly IFingerprintService _fingerprintService;
    private readonly IActivityService _activityService;

    public ProcessExpiredOrdersService(IServiceScopeFactory serviceScopeFactory,
        ReadOnlyPostgreSQLDbContext readOnlyPostgreSQLDbContext,
        IOrderStateMachine orderStateMachine,
        IPaymentsService paymentsService,
        IFingerprintService fingerprintService,
        IActivityService activityService)
    {
        _serviceScopeFactory = serviceScopeFactory;
        _readOnlyPostgreSqlDbContext = readOnlyPostgreSQLDbContext;
        _orderStateMachine = orderStateMachine;
        _paymentsService = paymentsService;
        _fingerprintService = fingerprintService;
        _activityService = activityService;
    }

    public async Task ProcessExpiredOrdersAsync(CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<ProcessExpiredOrdersService>();

        try
        {
            //workspan.Log.Information($"ProcessExpiredOffersAsync started");

            bool isAlreadyInProcessing = _inProcessingReenteranceLock.IncrementAndRead() > 1;
            if (isAlreadyInProcessing)
            {
                //workspan.Log.Information($"ProcessExpiredOffersAsync already in processing -> skipping");

                return; // to avoid parallel processing
            }

            bool firstCheck = _isFirstExpiredOffersCheck.ReadAndSetToFalse();

            //workspan.Log.Information($"Is first check {firstCheck}?");

            var merchantsGroupedByCITExpirationInterval = await GetMerchantMidsGroupedByCITExpirationIntervalAsync();

            foreach (var (expirationInterval, merchantsToProcess) in merchantsGroupedByCITExpirationInterval)
            {
                var citOrdersExpiresAfterInterval = TimeSpan.FromMinutes(expirationInterval);

                await ProcessExpiredOrdersAsync(firstCheck, merchantsToProcess, citOrdersExpiresAfterInterval,
                    cancellationToken);
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "Cannot process expired offers");
            await _activityService.CreateActivityAsync(EligibilityErrorActivities.ExpiredOffers_Error, e);
        }
        finally
        {
            _inProcessingReenteranceLock.DecrementAndRead();
        }
    }

    public async Task ProcessExpiredOrdersAsync(bool firstCheck,
        List<Guid> midsToProcess,
        TimeSpan citOrdersExpiresAfterInterval,
        CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<ProcessExpiredOrdersService>();

        try
        {
            List<Order> expiredOffers = new();

            using (var serviceScope = _serviceScopeFactory.CreateScope())
            using (var localDbContext = serviceScope.ServiceProvider.GetRequiredService<PostgreSQLDbContext>())
            using (await localDbContext.Database.BeginTransactionAsync())
            {
                List<Order> possibleExpiredOffers = new();

                #region Processing CIT Orders

                var citTerminalOrderStates = new string[]
                {
                    nameof(OrderState.ORDER_APPROVED),
                    nameof(OrderState.NOT_ELIGIBLE_EXPIRED),
                    nameof(OrderState.NOT_ELIGIBLE),
                    nameof(OrderState.NOT_ELIGIBLE_THRESHOLD_LIMIT),
                    nameof(OrderState.NOT_ELIGIBLE_DUPLICATE_ORDER),
                    nameof(OrderState.NOT_ELIGIBLE_CONSENT_REJECTED),
                    nameof(OrderState.NOT_ELIGIBLE_CANCELLED)
                };

                DateTime expirationLookupEndTime = DateTime.UtcNow.Subtract(citOrdersExpiresAfterInterval);

                var expiredOrdersQuery = localDbContext.Orders.AsNoTracking();


                DateTime? expirationLookupStartTime = null;
                if (!firstCheck)
                {
                    //we do not want to scan through all orders on each ProcessProcessExpiredOffersAsync call (only on first run)
                    expirationLookupStartTime = expirationLookupEndTime.AddMinutes(-2);
                }
                else
                {
                    if (EnvironmentHelper.IsInStagingOrDevelopment)
                    {
                        // For local/staging environments it's possible to have stalled orders due to debugging 
                        expirationLookupStartTime = null;
                    }
                    else
                    {
                        // Assuming in real environment we do not want to scan through all orders
                        // as it can be a lot of them and it's not necessary as it should be enough to check for one day
                        // on first run
                        expirationLookupStartTime =
                            DateTime.UtcNow.Subtract(citOrdersExpiresAfterInterval).AddDays(-1);
                    }
                }

                if (expirationLookupStartTime.HasValue)
                {
                    expiredOrdersQuery = expiredOrdersQuery.Where(x => x.ModifiedOn >= expirationLookupStartTime.Value);
                }

                expiredOrdersQuery = expiredOrdersQuery
                    .Where(x => x.ModifiedOn <= expirationLookupEndTime); //this orders cannot be expired

                expiredOrdersQuery = expiredOrdersQuery
                    .Where(x => citTerminalOrderStates.Contains(x.State) == false)
                    .Where(x => midsToProcess.Contains(x.Mid))
                    .Where(x => x.IsCIT);


                possibleExpiredOffers.AddRange(await expiredOrdersQuery.ToListAsync());

                #endregion


                #region Processing MIT Orders

                var mitTerminalOrderStates = new string[]
                {
                    nameof(OrderState.ORDER_APPROVED),
                    nameof(OrderState.NOT_ELIGIBLE_EXPIRED),
                    //nameof(OrderState.NOT_ELIGIBLE), // Can be eligible after off-session retry
                    nameof(OrderState.NOT_ELIGIBLE_THRESHOLD_LIMIT),
                    nameof(OrderState.NOT_ELIGIBLE_DUPLICATE_ORDER),
                    nameof(OrderState.NOT_ELIGIBLE_CONSENT_REJECTED),
                    nameof(OrderState.NOT_ELIGIBLE_CANCELLED),
                };

                var utcNow = DateTime.UtcNow;
                var expiredMITOrdersQuery = localDbContext.Orders.AsNoTracking();

                DateTime? mitExpirationLookupStartTime = utcNow.AddMonths(-3);

                if (mitExpirationLookupStartTime.HasValue)
                {
                    expiredMITOrdersQuery = expiredMITOrdersQuery
                        .Where(x => x.CreatedOn >= mitExpirationLookupStartTime.Value);
                }

                expiredMITOrdersQuery = expiredMITOrdersQuery
                    .Where(x => mitTerminalOrderStates.Contains(x.State) == false)
                    .Where(x => midsToProcess.Contains(x.Mid))
                    .Where(x => x.IsCIT == false && x.ExpiryDate != null && x.ExpiryDate.Value <= utcNow);

                possibleExpiredOffers.AddRange(await expiredMITOrdersQuery.ToListAsync());

                #endregion


                if (possibleExpiredOffers.Count > 0)
                {
                    workspan.Log
                        .Information("Processing {PossiblyExpiredOffersCount} possibly expired offers",
                            possibleExpiredOffers.Count);


                    bool saveChangesToDatabase = false;
                    int expiredOffersProcessedCount = 0;
                    foreach (var expiredOrder in possibleExpiredOffers)
                    {
                        if ((expiredOrder.IsMIT() && expiredOrder.IsExpiredMIT()) ||
                            expiredOrder.IsExpiredCIT((int) citOrdersExpiresAfterInterval.TotalMinutes))
                        {
                            try
                            {
                                workspan.Log
                                    .Information("Processing expired order {OrderId}", expiredOrder.Id);

                                localDbContext.Attach(expiredOrder);

                                await _orderStateMachine.SetOrderStateAsync(expiredOrder,
                                    StateOfOrder.NotEligibleExpired,
                                    saveChangesToDatabase: false, forceUpdateEvent: true);

                                await _paymentsService.VoidPaymentAuthorizationAndCancelACHDebitPaymentAsync(
                                    expiredOrder);

                                expiredOffers.Add(expiredOrder);

                                saveChangesToDatabase = true;

                                expiredOffersProcessedCount++;
                            }
                            catch (Exception e)
                            {
                                workspan.RecordException(e, "Cannot process expired offer: {OrderId}", expiredOrder.Id);
                            }
                        }
                    }


                    workspan.Log
                        .Information("Processed expired offers: {ExpiredOffersCount} ",
                            expiredOffersProcessedCount);

                    if (saveChangesToDatabase)
                    {
                        await localDbContext.SaveChangesAsync();
                    }

                    //await _fingerprintService.ProcessExpiredOffersAsync(expiredOffers);
                }

                await localDbContext.Database.CommitTransactionAsync();
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "Cannot process expired offers");
            await _activityService.CreateActivityAsync(EligibilityErrorActivities.ExpiredOffers_Error, e);
        }
    }

    private async Task<Dictionary<int, List<Guid>>> GetMerchantMidsGroupedByCITExpirationIntervalAsync()
    {
        using var workspan = Workspan.Start<ProcessExpiredOrdersService>();

        var merchantsGroupedByCITExpirationInterval = await _readOnlyPostgreSqlDbContext
            .Merchants.AsNoTracking()
            .Select(m => new
            {
                Mid = m.Mid,
                CITOrderExpirationInterval = m.CITOrderExpirationInterval
            })
            //.Where(x => x.CITOrderExpirationInterval != null)
            .GroupBy(x => x.CITOrderExpirationInterval ?? 5)
            .ToDictionaryAsync(x => x.Key, x => x.Select(y => y.Mid).ToList());

        return merchantsGroupedByCITExpirationInterval;
    }
}