using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
//using FlexCharge.Eligibility.Services.Cache;
using FlexCharge.Eligibility.Services.RequestRateLimiterService.RequestLimiters;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.DependencyInjection;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.Services.RequestRateLimiterService;

class RequestRateLimiterService : IRequestRateLimiterService
{
    private readonly IActivityService _activityService;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly IDistributedCache _distributedCache;
    private static ConcurrentDictionary<Guid, MerchantRequestStatistics> MerchantsRequestsStatistics = new();

    private static ConcurrentBag<IRequestLimiter> FirstPassLimiters { get; } = new();
    private static ConcurrentBag<IRequestLimiter> SecondPassLimiters { get; } = new();
    private static ConcurrentBag<IRequestLimiter> PaymentResponseProcessingLimiters { get; } = new();


    static RequestRateLimiterService()
    {
        FirstPassLimiters.Add(new RequestThrottlingLimiter());
        //FirstPassLimitters.Add(new MaxOfferRequestsPerDayLimiter());
        FirstPassLimiters.Add(new OfferRequestsRatePerTimeIntervalLimiter());
        FirstPassLimiters.Add(new MaxApprovedOfferRequestsPerDayLimiter());
        FirstPassLimiters.Add(new OrdersMaxMonthlyAmountLimiter());

        // //We want to apply RequestThrottlingLimiter first and than take only a fraction of NSF orders, according to Merchant settings
        // SecondPassLimiters.Add(new OfferNSFRequestsThrottlingLimiter());

        //Only limit based on payment response codes after we have full authorization results
        PaymentResponseProcessingLimiters.Add(new OfferNSFRequestsThrottlingLimiter());
    }

    public RequestRateLimiterService(IActivityService activityService,
        IDistributedCache distributedCache, IServiceScopeFactory serviceScopeFactory)
    {
        _activityService = activityService;
        _serviceScopeFactory = serviceScopeFactory;
        _distributedCache = distributedCache;
    }

    #region ShouldProcessRequest

    public async Task<bool> ShouldProcessRequestAsync(Merchant merchant, Order order, EvaluateRequest evaluateRequest)
    {
        return await ShouldProcessRequestAsync(DateTime.UtcNow, merchant, order, evaluateRequest,
            FirstPassLimiters, SecondPassLimiters);
    }

    private async Task<bool> ShouldProcessRequestAsync(DateTime currentTimeUtc, Merchant merchant, Order order,
        EvaluateRequest evaluateRequest,
        ConcurrentBag<IRequestLimiter> firstPassLimiters,
        ConcurrentBag<IRequestLimiter> secondPassLimiters
    )
    {
        using var workspan = Workspan.Start<RequestRateLimiterService>();

        MerchantRequestStatistics merchantRequestStatistics;
        if (!MerchantsRequestsStatistics.TryGetValue(merchant.Mid, out merchantRequestStatistics))
        {
            var newMerchantStatistics = new MerchantRequestStatistics(merchant.Mid);
            await newMerchantStatistics.LoadFromDistributedCacheAsync(_distributedCache, merchant.Mid,
                CancellationToken.None);
            merchantRequestStatistics =
                MerchantsRequestsStatistics.GetOrAdd(merchant.Mid, (mid) => newMerchantStatistics);
        }

        merchantRequestStatistics.PurgeOldStatistics(currentTimeUtc);

        //run all request limiters so they can populate statistics on every run
        List<(string limiterName, bool stop)> firstPassResults = RunLimitingStrategies(firstPassLimiters,
            currentTimeUtc, merchant, order,
            evaluateRequest, merchantRequestStatistics);

        //Request not accepted
        await AddLimitingResultsToActivities(firstPassResults, order.Id, evaluateRequest.Mid);

        bool denyRequest = true;

        List<(string limiterName, bool stop)> secondPassResults = null;
        if (firstPassResults.All(limiterResult => limiterResult.stop == false))
        {
            if (secondPassLimiters != null && secondPassLimiters.Any())
            {
                //second pass limiters are started only if first pass approves request
                secondPassResults = RunLimitingStrategies(secondPassLimiters, currentTimeUtc, merchant, order,
                    evaluateRequest,
                    merchantRequestStatistics);

                await AddLimitingResultsToActivities(secondPassResults, order.Id, evaluateRequest.Mid);
            }


            if ((secondPassLimiters == null || !secondPassLimiters.Any()) ||
                secondPassResults.All(limiterResult => limiterResult.stop == false))
            {
                workspan.Log.Information("REQUEST LIMITER: Request accepted");
                denyRequest = false;
            }
        }


        if (denyRequest)
        {
            workspan.Log.Information("REQUEST LIMITER: Request denied");
            await _activityService.CreateActivityAsync(Activities.RequestLimitingActivities.RequestLimited,
                set: set => set
                    .CorrelationId(order.Id)
                    .TenantId(evaluateRequest.Mid));
        }

        SaveToDistributedCache(merchantRequestStatistics);

        return !denyRequest;
    }


    public async Task<bool> ShouldProcessThisPaymentResponseCodeAsync(Merchant merchant, Order order,
        EvaluateRequest evaluateRequest)
    {
        return await ShouldProcessRequestAsync(DateTime.UtcNow, merchant, order, evaluateRequest,
            PaymentResponseProcessingLimiters, null);
    }

    private void SaveToDistributedCache(MerchantRequestStatistics merchantRequestStatistics)
    {
        Task.Run(async () =>
        {
            using (var scope = _serviceScopeFactory.CreateScope())
            {
                var distributedCache = scope.ServiceProvider.GetRequiredService<IDistributedCache>();

                await merchantRequestStatistics.SaveToDistributedCacheAsync(distributedCache,
                    merchantRequestStatistics.Mid,
                    CancellationToken.None);
                //await distributedCache.SaveAsync();
            }
        });
    }

    private static List<(string limiterName, bool stop)> RunLimitingStrategies(ConcurrentBag<IRequestLimiter> limiters,
        DateTime currentTimeUtc, Merchant merchant, Order order, EvaluateRequest evaluateRequest,
        MerchantRequestStatistics merchantRequestStatistics)
    {
        return limiters
            .Select(limiter => (limiter.GetType().Name,
                limiter.ShouldStop(currentTimeUtc, merchant, order, merchantRequestStatistics, evaluateRequest)))
            .ToList();
    }

    private async Task AddLimitingResultsToActivities(List<(string limiterName, bool stop)> limitersResults,
        Guid activityCorrelationId, Guid? mid)
    {
        for (var i = 0; i < limitersResults.Count; i++)
        {
            var limiterResult = limitersResults[i];

            if (limiterResult.stop == true)
            {
                await _activityService.CreateActivityAsync(
                    Activities.RequestLimitingActivities.RequestLimitedByStrategy, set => set
                        .Subcategory("First Pass Limiters")
                        .CorrelationId(activityCorrelationId)
                        .TenantId(mid)
                        .Data(limiterResult.limiterName)
                        .Meta(meta => meta
                            .SetValue("Limiter", limiterResult.limiterName)));
            }
        }
    }

    #endregion

    public void AddApprovedOrder(Order order)
    {
        var merchantRequestStatistics =
            MerchantsRequestsStatistics.GetOrAdd(order.Mid, mid => new MerchantRequestStatistics(order.Mid));
        merchantRequestStatistics.AddApprovedOrder(order);

        SaveToDistributedCache(merchantRequestStatistics);
    }
}