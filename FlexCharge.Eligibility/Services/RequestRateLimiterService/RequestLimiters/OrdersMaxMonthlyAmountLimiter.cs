using System;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.Services.RequestRateLimiterService.RequestLimiters;

class OrdersMaxMonthlyAmountLimiter : RequestLimiterBase
{
    protected override bool ShouldStopRequest(DateTime currentTimeUtc, Merchant merchant, Order order,
        MerchantRequestStatistics merchantRequestStatistics, EvaluateRequest evaluateRequest)
    {
        if (!merchant.Orders_MaxMonthlyAmount.HasValue) return false;

        //in cents
        var requestsThisMonthValue = merchantRequestStatistics.GetOrdersTotalAmountByMonth(currentTimeUtc);

        var ordersTotalAmountThisMonth = requestsThisMonthValue.Read();

        var ordersMaxMonthlyAmount = merchant.Orders_MaxMonthlyAmount.Value;
        if (ordersTotalAmountThisMonth <= Utils.Formatters.DecimalToLong(ordersMaxMonthlyAmount))
        {
            return false;
        }

        return true;
    }
}