using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Validation.FluentValidation;
using FlexCharge.Common.Settings.SharedSettings;
using FlexCharge.Common.Shared.Partners;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Entities;
using MassTransit;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Eligibility.Services.PartnerService;

public class PartnerService : IPartnerService
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IPublishEndpoint _publisher;
    private readonly IActivityService _activityService;
    private readonly ISettingsService<PartnerSettings> _partnerSettingsService;

    public PartnerService(PostgreSQLDbContext dbContext,
        IPublishEndpoint publisher,
        IActivityService activityService,
        ISettingsService<PartnerSettings> partnerSettingsService)
    {
        _dbContext = dbContext;
        _publisher = publisher;
        _activityService = activityService;
        _partnerSettingsService = partnerSettingsService;
    }

    public async Task<PartnerSettings> GetPartnerSettingsAsync(Merchant merchant, Order order,
        HashSet<string> requiredFields)
    {
        using var workspan = Workspan.Start<PartnerService>();

        if (!merchant.Pid.HasValue)
        {
            workspan.Log
                .Fatal("Merchant does not have a PID. Cannot get partner settings");

            await _activityService.CreateActivityAsync(
                ConsumerNotificationErrorActivities.ConsumerNotification_IncorrectPartnerSettings,
                data: "Merchant does not have a PID. Cannot get partner settings", set => set
                    .TenantId(merchant.Mid)
                    .CorrelationId(order.Id));

            return null;
        }

        var partnerSettings = await _partnerSettingsService.GetSettingsAsync(merchant.Pid.Value);

        PartnerSettingsValidator partnerSettingsValidator = new();
        var partnerSettingsValidationResult = partnerSettingsValidator.Validate(partnerSettings);
        if (!partnerSettingsValidationResult.IsValid)
        {
            try
            {
                partnerSettingsValidationResult.ThrowOnErrors(logErrors: false, filter:
                    error => requiredFields.Contains(error.PropertyName)
                );
            }
            catch (FlexValidationMultipleErrorsException e)
            {
                workspan.RecordFatalException(e, "Partner settings validation failed");

                await _activityService.CreateActivityAsync(
                    ConsumerNotificationErrorActivities.ConsumerNotification_IncorrectPartnerSettings,
                    data: e.ToString(), set => set
                        .TenantId(merchant.Mid)
                        .CorrelationId(order.Id));

                return null;
            }
        }

        return partnerSettings;
    }
}