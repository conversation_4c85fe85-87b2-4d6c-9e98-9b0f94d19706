using FlexCharge.Eligibility.DTO;

namespace FlexCharge.Eligibility.Services.EligibilityService;

public class EvaluationResult
{
    public EvaluationResult(EvaluateResponse evaluateEvaluateResponse)
    {
        EvaluateResponse = evaluateEvaluateResponse;
    }

    public EvaluateResponse EvaluateResponse { get; }

    public string? ResponseCode { get; set; }
    public string? ResponseMessage { get; set; }
}