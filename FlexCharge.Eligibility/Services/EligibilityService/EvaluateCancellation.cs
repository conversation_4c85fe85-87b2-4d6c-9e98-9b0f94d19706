// using System;
// using System.Threading;
// using FlexCharge.Eligibility.Exceptions.Eligibility;
//
// namespace FlexCharge.Eligibility.Services.EligibilityService;
//
// public class EvaluateCancellation
// {
//     public CancellationToken RequestCancellationToken { get; set; }
//     public CancellationTokenSource TimeoutCancellationTokenSource { get; set; } = new();
//
//     public EvaluateCancellation(CancellationToken requestCancellationToken)
//     {
//         RequestCancellationToken = requestCancellationToken;
//         TimeoutCancellationTokenSource.CancelAfter(TimeSpan.FromSeconds(19));
//     }
//     
//     public void ThrowIfCancelled()
//     {
//         DO NOTE THROW FOR APPROVED ORDERS!!!
//         
//         if (RequestCancellationToken.IsCancellationRequested ||
//             TimeoutCancellationTokenSource.IsCancellationRequested)
//         {
//             throw new NotEligibleEvaluationTimeoutException();
//         }
//     }
//
// }