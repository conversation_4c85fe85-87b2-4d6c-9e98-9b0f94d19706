using System;
using System.Threading.Tasks;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Implementations;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.Entities;

namespace FlexCharge.Eligibility.Services.EligibilityService.EligibilityWorkflow.Workflows;

public class Standard_NotEligibleOrderProcessingWorkflow : NotEligibleOrderProcessingWorkflowBase
{
    public override int Version => 1;

    public override Guid WorkflowId => _stableId;

    public override string Name => "Standard Eligible Order Processing Strategy";

    public override string Description => "";

    // This is temporary solution until Merchant-related workflow management is implemented
    private static Guid _stableId = new("ed70abe8-c671-4715-8240-31f3bd7d002d");
    public static Guid StableId => _stableId;


    public Standard_NotEligibleOrderProcessingWorkflow(Order order, Merchant merchant, bool offSessionRetry) : base(
        order,
        merchant,
        offSessionRetry)
    {
    }

    #region Workflow

    public override async Task<WorkflowBuilder<EligibilityCheckContext>> CreateWorkflowAsync()
    {
        var workflow = new WorkflowBuilder<EligibilityCheckContext>();

        workflow
            .INLINE_SUBFLOW(CreateProcessingWorkflow)
            .FINAL_END();
        ;

        return await Task.FromResult(workflow);
    }

    public void CreateProcessingWorkflow(WorkflowBuilder<EligibilityCheckContext> workflow)
    {
        workflow.Add(nameof(E0010_MIT_AskForDifferentCardForNotEligibleOffers));
        workflow.Add(nameof(E0008_ProcessNotEligibleOffer));

        workflow.Add(nameof(E3007_AccountUpdater));
    }

    #endregion
}