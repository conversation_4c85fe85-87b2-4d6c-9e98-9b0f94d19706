using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Implementations;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using FlexCharge.Eligibility.Services.ActivityService;
using FlexCharge.Eligibility.Services.EligibilityService.EligibilityWorkflow.Workflows;
using FlexCharge.Utils;
using FlexCharge.WorkflowEngine.Workflows;

namespace FlexCharge.Eligibility.Services.EligibilityService.EligibilityWorkflow;

public partial class EligibilityWorkflowFactory : IEligibilityWorkflowFactory
{
    private readonly IActivityService _activityService;

    public EligibilityWorkflowFactory(IActivityService activityService)
    {
        _activityService = activityService;
    }

    #region Observability

    async Task AddActivityAsync<TActivityNameEnum>(TActivityNameEnum activityNameEnum,
        Order order,
        string eventName = null,
        //object meta = null,
        object data = null,
        string subcategory = null,
        Action<IPayloadMetadataSetter> meta = null
    )
        where TActivityNameEnum : Enum
    {
        await _activityService.AddActivityAsync(activityNameEnum, order, eventName, data, subcategory, meta);
    }

    private async Task AddWorkflowCreatedActivityAsync(Order order, WorkflowBuilder<EligibilityCheckContext> workflow,
        WorkflowDescription workflowDescription)
    {
        await AddActivityAsync(WorkflowActivities.Workflow_Created,
            order,
            meta: meta => meta
                .SetValue("WorkflowId", workflowDescription.Id)
                .SetValue("WorkflowVersion", workflowDescription.Version)
                .SetValue("WorkflowDomain", workflowDescription.Domain)
                .SetValue("WorkflowName", workflowDescription.Name)
                .SetValue("WorkflowDescription", workflowDescription.Description)
        );
    }

    #endregion

    #region Evaluate Eligibility Sequences

    public async Task<(WorkflowBuilder<EligibilityCheckContext> Workflow, WorkflowDescription Description)>
        CreateEvaluateEligibilityWorkflowAsync(
            Entities.Merchant merchant,
            Order order, EvaluateRequest evaluateRequest, bool offSessionRetry)
    {
        using var workspan = Workspan.Start<EligibilityWorkflowFactory>()
            .Baggage("MerchantId", merchant.Mid)
            .Baggage("OrderId", order.Id);

        EligibilityStrategyWorkflowBase strategyWorkflow = null;

        if (evaluateRequest.OrderSource?.ToLower() == nameof(OrderSource.vterminal))
        {
            #region Virtual Terminal Sequence

            if (!merchant.VirtualTerminalEnabled)
            {
                #region Observability

                await AddActivityAsync(EligibilityErrorActivities.VirtualTerminalOrder_NotEnabledForMerchant, order);

                #endregion

                throw new NotEligibleException();
            }

            if (merchant.CITEvaluateAsync)
            {
                #region Observability

                await AddActivityAsync(
                    EligibilityErrorActivities.VirtualTerminalOrder_NotCompatibleWithMerchantAsyncMode, order);

                #endregion

                throw new NotEligibleException();
            }

            #region Observability

            await AddActivityAsync(EligibilityActivities.VirtualTerminalOrder_EnteredVirtualTerminalMode, order);

            #endregion

            strategyWorkflow = new VirtualTerminal_EligibilityStrategy(order, merchant, offSessionRetry);

            #endregion
        }
        else
        {
            if (IS_PRODUCTION_TEST_MODE_SEQUENCE(merchant, order))
            {
                // Always show challenge for test orders with amount of 2.00$ 
                if (order.Amount == 2_00)
                {
                    strategyWorkflow =
                        new ProductionTest_Challenge_Approve_EligibilityStrategy(order, merchant, offSessionRetry);
                }
                else
                {
                    strategyWorkflow =
                        new ProductionTest_Approve_EligibilityStrategy(order, merchant, offSessionRetry);
                }

                workspan.Log.Warning("Creating production test eligibility strategy workflow: {Name}",
                    strategyWorkflow.GetType().Name);
            }
            else if (!EnvironmentHelper.IsInProduction && IS_NON_PRODUCTION_TEST_MODE_SEQUENCE(merchant, order))
            {
                if (EnvironmentHelper.IsInProduction)
                {
                    workspan.Log.Fatal("Cannot create non-production test workflow in production environment");

                    throw new FlexChargeException(
                        "Cannot create non-production test workflow in production environment");
                }

                // Always show challenge for test orders with amount of 2.00$ 
                if (order.Amount == 2_00 ||
                    order.IsInKioskMode()) // for now - to always show some UI in kiosk mode
                {
                    strategyWorkflow =
                        new ProductionTest_Challenge_Approve_EligibilityStrategy(order, merchant, offSessionRetry);
                }
                else
                {
                    strategyWorkflow =
                        new ProductionTest_Approve_EligibilityStrategy(order, merchant, offSessionRetry);
                }

                workspan.Log.Warning("Creating non-production test eligibility strategy workflow: {Name}",
                    strategyWorkflow.GetType().Name);
            }
            else if (merchant.EligibilityStrategyWorkflowId != null)
            {
                workspan.Log.Information("Creating workflow: {WorkflowId}",
                    merchant.EligibilityStrategyWorkflowId);

                switch (merchant.EligibilityStrategyWorkflowId.ToString())
                {
                    case "fd1cf96d-6bb9-4af0-a37f-224a4b7f6d9a":
                        strategyWorkflow = new Standard_EligibilityStrategy(order, merchant, offSessionRetry);
                        break;
                    case "c4d39df4-01a8-406d-9c75-d283ad62a0e8":
                        strategyWorkflow =
                            new ExternalTokenDunning_EligibilityStrategy(order, merchant, offSessionRetry);
                        break;
                    default:
                        workspan.Log.Fatal("Unknown custom eligibility strategy workflow: {WorkflowId}",
                            merchant.EligibilityStrategyWorkflowId);
                        throw new NotImplementedException(
                            $"Unknown custom eligibility strategy workflow: {merchant.EligibilityStrategyWorkflowId}");
                }
            }
            else
            {
                workspan.Log.Information("Creating default eligibility strategy workflow");
                strategyWorkflow = new Standard_EligibilityStrategy(order, merchant, offSessionRetry);
            }
        }


        var workflow = await strategyWorkflow.CreateWorkflowAsync();
        var workflowDescription = strategyWorkflow.ToWorkflowDescription();

        await AddWorkflowCreatedActivityAsync(order, workflow, workflowDescription);

        return (workflow, workflowDescription);
    }


    // private async Task PopulateEligibilityStrategyWorkflowsAsync()
    // {
    //     if (_availableEligibilityStrategyWorkflows != null)
    //         return; //!!!
    //
    //     lock (_availableEligibilityStrategyWorkflowsLock)
    //     {
    //         if (_availableEligibilityStrategyWorkflows != null)
    //             return; //!!!
    //
    //
    //         _availableEligibilityStrategyWorkflows = new HashSet<Guid>();
    //     }
    // }

    #endregion

    #region Not Eligible Order Processing Workflow

    public async Task<(WorkflowBuilder<EligibilityCheckContext> Workflow, WorkflowDescription Description)>
        CreateNotEligibleOrderProcessingWorkflowAsync(Order order, Entities.Merchant merchant,
            EvaluateRequest evaluateRequest, bool offSessionRetry)
    {
        using var workspan = Workspan.Start<EligibilityWorkflowFactory>()
            .Baggage("MerchantId", merchant.Mid)
            .Baggage("OrderId", order.Id);

        NotEligibleOrderProcessingWorkflowBase notEligibleOrderProcessingWorkflow = null;

        if (merchant.NotEligibleOrderProcessingWorkflowId != null)
        {
            workspan.Log.Information("Creating workflow: {WorkflowId}",
                merchant.NotEligibleOrderProcessingWorkflowId);

            switch (merchant.NotEligibleOrderProcessingWorkflowId.ToString())
            {
                case "ed70abe8-c671-4715-8240-31f3bd7d002d":
                    notEligibleOrderProcessingWorkflow =
                        new Standard_NotEligibleOrderProcessingWorkflow(order, merchant, offSessionRetry);
                    break;
                case "17e9354b-f722-49de-ad42-14ee181a8ef2":
                    notEligibleOrderProcessingWorkflow =
                        new ExternalTokenDunning_NotEligibleOrderProcessingWorkflow(order, merchant, offSessionRetry);
                    break;
                default:
                    workspan.Log.Fatal("Unknown custom eligibility strategy workflow: {WorkflowId}",
                        merchant.NotEligibleOrderProcessingWorkflowId);
                    throw new NotImplementedException(
                        $"Unknown custom eligibility strategy workflow: {merchant.NotEligibleOrderProcessingWorkflowId}");
            }
        }
        else
        {
            workspan.Log.Information("Creating standard eligibility strategy workflow");
            notEligibleOrderProcessingWorkflow =
                new Standard_NotEligibleOrderProcessingWorkflow(order, merchant, offSessionRetry);
        }

        var workflow = await notEligibleOrderProcessingWorkflow.CreateWorkflowAsync();
        var workflowDescription = notEligibleOrderProcessingWorkflow.ToWorkflowDescription();

        await AddWorkflowCreatedActivityAsync(order, workflow, workflowDescription);

        return (workflow, workflowDescription);
    }

    #endregion
}