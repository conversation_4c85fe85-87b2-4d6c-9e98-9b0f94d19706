using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace FlexCharge.Eligibility.Services.Orders.OrderStates;

public abstract class StateMachineState<TState, TEnum> : IStateMachineState
    where TState : StateMachineState<TState, TEnum>
    where TEnum : Enum
{
    public string Name { get; init; }
    public TEnum Value { get; init; }

    protected abstract bool IsTerminalState { get; }

    protected StateMachineState(TEnum state, string? name = null) =>
        (Value, Name) = (state, name ?? state.ToString());

    public static void Initialize()
    {
        var values = typeof(TState).GetFields(BindingFlags.Public |
                                              BindingFlags.Static |
                                              BindingFlags.DeclaredOnly)
            .Select(f => f.GetValue(null))
            .Cast<TState>()
            .ToList();

        _allPossibleValues = values;

        #region Ensure all states are defined

        var enumValues = Enum.GetValues(typeof(TEnum)).Cast<TEnum>();
        var missingStates = enumValues.Except(values.Select(v => v.Value)).ToList();

        if (missingStates.Any())
        {
            throw new InvalidOperationException(
                $"Missing state machine state definitions for: {string.Join(", ", missingStates)}");
        }

        #endregion
    }


    public override string ToString() => Name;

    private static IEnumerable<TState> _allPossibleValues;

    public static IEnumerable<TState> GetAll() => _allPossibleValues;

    public override bool Equals(object obj)
    {
        if (obj is not StateMachineState<TState, TEnum> otherValue)
            return false;

        var typeMatches = GetType().Equals(obj.GetType());
        var valueMatches = Value.Equals(otherValue.Value);

        return typeMatches && valueMatches;
    }

    public int CompareTo(object other) => Value.CompareTo(((StateMachineState<TState, TEnum>) other).Value);

    public static bool operator ==(StateMachineState<TState, TEnum> a, StateMachineState<TState, TEnum> b)
    {
        if (ReferenceEquals(a, b))
            return true;

        if (a is null || b is null)
            return false;

        return a.Value.Equals(b.Value);
    }

    public static bool operator !=(StateMachineState<TState, TEnum> a, StateMachineState<TState, TEnum> b) => !(a == b);

    public static implicit operator StateMachineState<TState, TEnum>(TEnum state)
    {
        return GetAll().Single(s => s.Value.Equals(state));
    }

    public override int GetHashCode() => Value.GetHashCode();

    public virtual bool CanSwitchTo(TState state)
    {
        return !IsTerminalState;
    }
}

public interface IStateMachineState
{
    string Name { get; }
}