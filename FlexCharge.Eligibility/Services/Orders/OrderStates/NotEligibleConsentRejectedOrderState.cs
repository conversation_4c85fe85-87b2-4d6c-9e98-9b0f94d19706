using System;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;

namespace FlexCharge.Eligibility.Services.Orders.OrderStates;

public class NotEligibleConsentRejectedOrderState : NotEligibleOrderStateBase
{
    public NotEligibleConsentRejectedOrderState() : base(OrderState.NOT_ELIGIBLE_CONSENT_REJECTED)
    {
    }

    public override async Task CreateActivityAsync(IActivityService activityService, Order order)
    {
        await AddActivityAsync(EligibilityActivities.Offer_NotEligible, activityService, order);
    }
}