using System;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Services.ActivityService;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.Services.EligibilityService;

public abstract class OrderRelatedServiceBase
{
    protected IServiceProvider ServiceProvider { get; }
    protected readonly IActivityService ActivityService;

    protected OrderRelatedServiceBase(IServiceProvider serviceProvider)
    {
        ServiceProvider = serviceProvider;
        ActivityService = serviceProvider.GetRequiredService<IActivityService>();
    }

    protected async Task AddActivityAsync<TActivityNameEnum>(TActivityNameEnum activityNameEnum,
        Order order,
        string eventName = null,
        //object meta = null,
        object data = null,
        string subcategory = null,
        Action<IPayloadMetadataSetter> meta = null
    )
        where TActivityNameEnum : Enum
    {
        await ActivityService.AddActivityAsync(activityNameEnum, order, eventName, data, subcategory, meta);
    }
}