using System;
using System.Threading.Tasks;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Eligibility.Services.Workflows;

public interface IExternalControlsService
{
    public Task<ExternallyControlledParameterValues> LoadExternallyControlledParameterValuesAsync(Guid mid);
    Task SaveExternallyControlledParameterValuesAsync(Guid mid, ExternallyControlledParameterValues parameterValues);
}

public class ExternalControlsService : IExternalControlsService
{
    private readonly PostgreSQLDbContext _dbContext;

    public ExternalControlsService(PostgreSQLDbContext dbContext)
    {
        _dbContext = dbContext;
    }


    public async Task<ExternallyControlledParameterValues> LoadExternallyControlledParameterValuesAsync(Guid mid)
    {
        var merchant = await _dbContext.Merchants.SingleAsync(x => x.Mid == mid);

        var externallyControlledParameterValues = new ExternallyControlledParameterValues();
        externallyControlledParameterValues.DeserializeValues(merchant.WorkflowsExternalParameters);

        return externallyControlledParameterValues;
    }

    public async Task SaveExternallyControlledParameterValuesAsync(Guid mid,
        ExternallyControlledParameterValues parameterValues)
    {
        var merchant = await _dbContext.Merchants.SingleAsync(x => x.Mid == mid);

        merchant.WorkflowsExternalParameters = parameterValues.SerializeValues();

        await _dbContext.SaveChangesAsync();
    }
}