using System;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.Response;
using SendGrid;

namespace FlexCharge.Eligibility.DTO;

public class BlockListResponseDTO : BaseResponse
{
    public PagedDTO<BlockListQueryDTO> BlockList { get; set; }
        
    public class BlockListQueryDTO
    {
        public Guid Id  { get; set; }
        public string FingerprintType { get; set; }
        public DateTime? ValidUntil { get; set; }
        
        public string? Meta { get; set; }
        public string? Reason { get; set; }
    }
}