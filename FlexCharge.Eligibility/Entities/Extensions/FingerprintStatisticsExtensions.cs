using System;
using FlexCharge.Common.Telemetry;

namespace FlexCharge.Eligibility.Entities;

public static class FingerprintStatisticsExtensions
{
    // public static bool CheckIfStatisticsIsCorrect(this FingerprintStatistics statistics, bool processingExpiredOffer)
    // {
    //     // if (!processingExpiredOffer)
    //     // {
    //     //     // Ensuring that expired offers not counted as OpenOffers in fingerprint statistics
    //     //     if (CheckIfStatisticsIsOlderThanOrderExpiration(statistics))
    //     //     {
    //     //         // Every order related to this statistics is Eligible or expired
    //     //         if (statistics.TotalOpenOffers > 0 || statistics.TotalOpenOffersAmount > 0)
    //     //         {
    //     //             Workspan.Current?.Log.Warning(
    //     //                 "Incorrect fingerprint statistics: TotalOpenOffers statistics must be zero");
    //     //             return false;
    //     //         }
    //     //     }
    //     // }
    //
    //     return statistics.TotalOpenOffers >= 0 && statistics.TotalOpenOffersAmount >= 0 &&
    //            statistics.TotalOpenOrders >= 0 && statistics.TotalOpenOrdersAmount >= 0 &&
    //            statistics.TotalFullyPaidInOrders >= 0 && statistics.TotalPaidInAmount >= 0 &&
    //            statistics.TotalWrittenOffOrders >= 0 && statistics.TotalWrittenOffOrdersAmount >= 0;
    // }

    // private static bool CheckIfStatisticsIsOlderThanOrderExpiration(FingerprintStatistics statistics)
    // {
    //     return DateTime.UtcNow - statistics.ModifiedOn >= OrderExtensions.CIT_ORDER_EXPIRES_AFTER;
    // }

    // /// <summary>
    // /// Ensure statistics contains only positive numbers or zeros
    // /// </summary>
    // /// <param name="statistics"></param>
    // /// <param name="order"></param>
    // /// <exception cref="NotImplementedException"></exception>
    // public static void CorrectStatistics(this FingerprintStatistics statistics, Order? order)
    // {
    //     statistics.EnsureTotalOpenOffersStatisticsIsGreaterThanZero();
    //
    //     statistics.TotalOpenOrders = EnsureZeroOrGreater(statistics.TotalOpenOrders);
    //     statistics.TotalOpenOrdersAmount = EnsureZeroOrGreater(statistics.TotalOpenOrdersAmount);
    //
    //     statistics.TotalFullyPaidInOrders = EnsureZeroOrGreater(statistics.TotalFullyPaidInOrders);
    //     statistics.TotalPaidInAmount = EnsureZeroOrGreater(statistics.TotalPaidInAmount);
    //
    //     statistics.TotalWrittenOffOrders = EnsureZeroOrGreater(statistics.TotalWrittenOffOrders);
    //     statistics.TotalWrittenOffOrdersAmount = EnsureZeroOrGreater(statistics.TotalWrittenOffOrdersAmount);
    //
    //     // if (CheckIfStatisticsIsOlderThanOrderExpiration(order, statistics))
    //     // {
    //     //     // Every order related to this statistics is Eligible or expired
    //     //     statistics.TotalOpenOffers = 0;
    //     //     statistics.TotalOpenOffersAmount = 0;
    //     // }
    // }

    private static int EnsureZeroOrGreater(int value)
    {
        return Math.Max(value, 0);
    }

    private static long EnsureZeroOrGreater(long value)
    {
        return Math.Max(value, 0);
    }

    // public static void EnsureTotalOpenOffersStatisticsIsGreaterThanZero(this FingerprintStatistics statistics)
    // {
    //     // TotalOpenOffers statistics can already be zero if order statistics is old and 
    //     // TotalOpenOffers statistics has been reset in CheckAndCorrectFingerprintStatistics() method
    //     // And then on expired offers processing TotalOpenOffers is deducted
    //     statistics.TotalOpenOffers = EnsureZeroOrGreater(statistics.TotalOpenOffers);
    //     statistics.TotalOpenOffersAmount = EnsureZeroOrGreater(statistics.TotalOpenOffersAmount);
    // }
}