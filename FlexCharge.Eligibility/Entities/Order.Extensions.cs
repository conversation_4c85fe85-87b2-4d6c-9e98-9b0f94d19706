using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.Cures;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Implementations;
using FlexCharge.Eligibility.Entities.Enums;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Utils;
using Newtonsoft.Json;
using TransactionType = FlexCharge.Eligibility.Entities.Enums.TransactionType;

namespace FlexCharge.Eligibility.Entities;

public class PaymentTransactionResponse
{
    public PaymentTransactionResponse(
        DateTime time,
        TransactionType transactionType,
        string responseCodeGateway,
        string responseCodeProcessor,
        string responseCode,
        string cvv,
        string avs,
        int? usedGatewayOrder = null,
        string internalResponseCode = null,
        string internalResponseMessage = null,
        string internalResponseGroup = null,
        Guid? gatewayId = null,
        Guid? supportedGatewayId = null
    )
    {
        Time = time;

        TransactionType = transactionType.ToString();

        GatewayId = gatewayId;
        SupportedGatewayId = supportedGatewayId;

        ResponseCodeGateway = responseCodeGateway;
        ResponseCodeProcessor = responseCodeProcessor;
        ResponseCode = responseCode;
        NormalizedResponseCode = internalResponseCode;
        NormalizedResponseMessage = internalResponseMessage;
        NormalizedResponseCodeGroup = internalResponseGroup;
        CVV = cvv;
        AVS = avs;

        if (responseCodeGateway != null && transactionType != Enums.TransactionType.InitialDecline)
        {
            UsedGatewayOrder = usedGatewayOrder;
        }
    }

    public DateTime? Time { get; set; }

    public string TransactionType { get; set; }
    public string ResponseCodeGateway { get; set; }
    public string ResponseCodeProcessor { get; set; }
    public string ResponseCode { get; set; }

    public string NormalizedResponseCodeGroup { get; set; }
    public string NormalizedResponseCode { get; set; }
    public string NormalizedResponseMessage { get; set; }

    public string CVV { get; set; }

    public string AVS { get; set; }

    //public bool? ResponseCodeCertain { get; set; }
    public int? UsedGatewayOrder { get; set; }

    public Guid? GatewayId { get; set; }
    public Guid? SupportedGatewayId { get; set; }

    public CvvResponseType GetCvvResponseType()
    {
        switch (CVV)
        {
            case "M":
                return CvvResponseType.Match;
            case "N":
                return CvvResponseType.NoMatch;
            case "P":
                return CvvResponseType.NotProcessed;
            case "S":
                return CvvResponseType.NotProvided;
            default:
                return CvvResponseType.NotProcessed;
        }
    }

    public AvsResponseType GetAvsResponseType()
    {
        switch (AVS)
        {
            case "M":
                return AvsResponseType.FullMatch;
            case "X":
            case "Y":
            case "D":
                return AvsResponseType.PartialMatch;
            case "N":
                return AvsResponseType.NoMatch;
            default:
                return AvsResponseType.Unavailable;
        }
    }
}

public class SCAAuthenticationResponse
{
    public string? CAVV { get; }
    public bool InformationalOnly { get; set; }

    public SCAAuthenticationResponse(string? cavv, bool informationalOnlyAuthentication)
    {
        CAVV = cavv;
        InformationalOnly = informationalOnlyAuthentication;
    }


    //public string CavvResultCode => !string.IsNullOrWhiteSpace(CAVV) ? "Y" : "N";
}

public static class OrderExtensions
{
    //public static readonly TimeSpan CIT_ORDER_EXPIRES_AFTER = new TimeSpan(0, 0, 5, 0);

    // For safety reasons, we treat MIT orders as expired 1 minute before their actual expiration time
    static readonly TimeSpan MIT_ORDER_EXPIRATION_SAFE_ZONE = new TimeSpan(0, 0, 1, 0);

    public static bool IsExpired(this Order order, Merchant merchant)
    {
        if (order.IsCIT)
        {
            var orderExpirationInterval = merchant.GetCITOrderExpirationIntervalInMinutes();

            return order.IsExpiredCIT(orderExpirationInterval);
        }
        else
        {
            return order.IsExpiredMIT();
        }
    }

    public static bool IsExpiredCIT(this Order order, int orderExpirationIntervalInMinutes)
    {
        return (DateTime.UtcNow - order.ModifiedOn).TotalMinutes >= orderExpirationIntervalInMinutes;
    }

    public static bool IsExpiredMIT(this Order order)
    {
        return order.ExpiryDate != null &&
               order.ExpiryDate.Value - MIT_ORDER_EXPIRATION_SAFE_ZONE <= DateTime.UtcNow;
    }


    public static void AddPaymentTransactionResponse(this Order order,
        PaymentTransactionResponse paymentTransactionResponse)
    {
        Workspan.Current?.Log.Information("Payment transaction response: {paymentTransactionResponse}. Order {orderId}",
            JsonConvert.SerializeObject(paymentTransactionResponse), order.Id);

        var responseCodesList = GetPaymentTransactionsResponses(order);

        responseCodesList.Add(paymentTransactionResponse);

        order.PaymentTransactionsResponses = JsonConvert.SerializeObject(responseCodesList);
    }

    public static void UpdateLastPaymentTransactionResponse(this Order order,
        PaymentTransactionResponse paymentTransactionResponse)
    {
        var responseCodesList = GetPaymentTransactionsResponses(order);

        responseCodesList.RemoveAt(responseCodesList.Count - 1);
        responseCodesList.Add(paymentTransactionResponse);

        order.PaymentTransactionsResponses = JsonConvert.SerializeObject(responseCodesList);
    }

    public static List<PaymentTransactionResponse> GetPaymentTransactionsResponses(this Order order)
    {
        string responseCodes = order.PaymentTransactionsResponses;
        return responseCodes != null
            ? JsonConvert.DeserializeObject<List<PaymentTransactionResponse>>(responseCodes)
            : new List<PaymentTransactionResponse>();
    }

    public static void AddSCAAuthenticationResponse(this Order order,
        SCAAuthenticationResponse scaAuthenticationResponse)
    {
        Workspan.Current?.Log.Information("SCA authentication response: {scaAuthenticationResponse}. Order {orderId}",
            JsonConvert.SerializeObject(scaAuthenticationResponse), order.Id);

        var responseCodesList = GetSCAAuthenticationResponses(order);

        responseCodesList.Add(scaAuthenticationResponse);

        order.SCAAuthenticationResponses = JsonConvert.SerializeObject(responseCodesList);
    }

    public static List<SCAAuthenticationResponse> GetSCAAuthenticationResponses(this Order order)
    {
        string responseCodes = order.SCAAuthenticationResponses;
        return responseCodes != null
            ? JsonConvert.DeserializeObject<List<SCAAuthenticationResponse>>(responseCodes)
            : new List<SCAAuthenticationResponse>();
    }

    // public static PaymentTransactionResponse? GetLastTransactionResultOrDefault(this Order order
    //     //, bool preferCertainResponseCodes = false
    // )
    // {
    //     var response = GetLastTransactionResultOrDefault(order, false /*, preferCertainResponseCodes*/);
    //
    //     return response?.TransactionType != nameof(TransactionType.InitialDecline) ? response : null;
    // }

    public static PaymentTransactionResponse? GetLastTransactionResultOrDefault(this Order order,
        bool canReturnInitialDecline = false
        //, bool preferCertainResponseCodes = false
    )
    {
        //(string ResponseCode, string ResponseCodeGateway) transactionResult;

        var paymentTransactionsResponses = order.GetPaymentTransactionsResponses();

        PaymentTransactionResponse lastPaymentOperationResponse;

        lastPaymentOperationResponse = canReturnInitialDecline
            ? paymentTransactionsResponses.Last()
            : paymentTransactionsResponses.LastOrDefault(x =>
                x.TransactionType != nameof(TransactionType.InitialDecline));

        var lastVerifyAmountTransaction = paymentTransactionsResponses
            .Where(x =>
                x.TransactionType == nameof(TransactionType.VerifyAmount))
            .LastOrDefault();

        #region Commented

        // if (preferCertainResponseCodes)
        // {
        //     lastPaymentOperationResponse =
        //         paymentTransactionsResponses.LastOrDefault(x => x.ResponseCodeCertain == true);
        //
        //     if (lastPaymentOperationResponse == null)
        //     {
        //         // // TODO: remove this temporary fix can be removed after month passes from 2023-06-06 (all not expired orders will have ResponseCodeCertain)
        //         // lastPaymentOperationResponse =
        //         //     paymentTransactionsResponses
        //         //         .LastOrDefault(x =>
        //         //             (x.TransactionType == nameof(TransactionType.FullAuthorization) ||
        //         //              x.TransactionType == nameof(TransactionType.Sale))
        //         //             &&
        //         //             x.ResponseCodeGateway?.ToUpper() !=
        //         //             "NMI"); // NMI error codes can be incorrect - we can't rely on them if we don't have ResponseCodeCertain flag set
        //         
        //         // Couldn't find any transaction with ResponseCodeCertain flag set
        //         // Just returning last transaction 
        //         lastPaymentOperationResponse =
        //             paymentTransactionsResponses.LastOrDefault();
        //     }
        // }
        // else
        // {
        //    lastPaymentOperationResponse = paymentTransactionsResponses.Last();
        //}


        // var lastVerifyAmountTransaction = paymentTransactionsResponses
        //     .Where(x =>
        //         x.TransactionType == nameof(TransactionType.VerifyAmount) &&
        //         (preferCertainResponseCodes == false || x.ResponseCodeCertain == true))
        //     .LastOrDefault();
        //
        // if (lastVerifyAmountTransaction == null)
        // {
        // var lastVerifyAmountTransaction = paymentTransactionsResponses
        //     .Where(x =>
        //         x.TransactionType == nameof(TransactionType.VerifyAmount))
        //     .LastOrDefault();
        //}

        #endregion

        #region Processing of lastVerifyAmountTransaction (if any) to get the most accurate error code

        if (lastVerifyAmountTransaction != null && !string.IsNullOrWhiteSpace(lastVerifyAmountTransaction.ResponseCode))
        {
            if (lastPaymentOperationResponse.ResponseCode != "100")
            {
                // We use it only in case of error (otherwise we can have succeeded from Stripe, but no Authorization from NMI)
                if (lastVerifyAmountTransaction.ResponseCode.ToLower() != "succeeded")
                {
                    lastPaymentOperationResponse.ResponseCodeGateway = lastVerifyAmountTransaction.ResponseCodeGateway;
                    lastPaymentOperationResponse.ResponseCode = lastVerifyAmountTransaction.ResponseCode;
                }
            }

            if (string.IsNullOrWhiteSpace(lastPaymentOperationResponse.CVV) || lastPaymentOperationResponse.CVV == "P")
            {
                lastPaymentOperationResponse.CVV = lastVerifyAmountTransaction.CVV;
            }
        }

        #endregion


        // transactionResult = new(
        //     lastPaymentOperationResponse.ResponseCode,
        //     lastPaymentOperationResponse.ResponseCodeGateway);

        return lastPaymentOperationResponse;
    }

    public static PaymentTransactionResponse? GetLastPaymentTransactionResponse(this Order order /*,
        bool returnOnlyCertainResponseCodes = true*/)
    {
        return order.GetPaymentTransactionsResponses()
            .Where(x =>
                x.TransactionType == nameof(TransactionType.FullAuthorization) ||
                x.TransactionType == nameof(TransactionType.Sale) ||
                x.TransactionType == nameof(TransactionType.Charge)
            )
            .LastOrDefault();
    }

    public static PaymentTransactionResponse? GetFirstPaymentTransactionResponse(this Order order)
    {
        return order.GetPaymentTransactionsResponses()
            .Where(x =>
                x.TransactionType == nameof(TransactionType.FullAuthorization) ||
                x.TransactionType == nameof(TransactionType.Sale) ||
                x.TransactionType == nameof(TransactionType.Charge)
            )
            .FirstOrDefault();
    }

    public static bool AnyPaymentTransactionResponse(this Order order, Func<PaymentTransactionResponse, bool> match)
    {
        return order.GetPaymentTransactionsResponses().Any(x => match(x));
    }


    public static SCAAuthenticationResponse? GetCurrentSCAResponse(this Order order)
    {
        var currentSCAAuthenticationResponse = order.GetSCAAuthenticationResponses().LastOrDefault();

        return currentSCAAuthenticationResponse;
    }

    /// <summary>
    /// This method does not take into account that MIT orders can be retried until expiry date or order can be stopped.
    /// </summary>
    /// <param name="order"></param>
    /// <returns></returns>
    /// <remarks>If Order is MIT it can be potentially eligible on a retry before expiry date</remarks>
    public static bool CannotBeEligibleNow(this Order order)
    {
        return order.State.ToOrderState().CannotBeEligible();
    }

    /// <summary>
    /// This method takes into account that MIT orders can be retried until expiry date or order can be stopped.
    /// </summary>
    /// <param name="order"></param>
    /// <returns></returns>
    /// <remarks>If Order is MIT it can be potentially eligible on a retry before expiry date</remarks>
    public static bool CannotBeEligibleEver(this Order order)
    {
        if (order.IsMIT())
        {
            if (order.IsStoppedForever() || order.IsExpiredMIT()) return true;
            else return false;
        }
        else return CannotBeEligibleNow(order);
    }

    public static bool IsNotEligible(this Order order)
    {
        return order.State.ToOrderState().IsNotEligible();
    }


    public static bool IsApproved(this Order order)
    {
        return order.State.ToOrderState() == OrderState.ORDER_APPROVED;
    }

    public static bool IsConditionalConsumerInteraction(this Order order)
    {
        return order.State.ToOrderState() == OrderState.CONDITIONAL_CONSUMER_INTERACTION;
    }

    public static bool IsInCaptureRequiredState(this Order order)
    {
        return order.State.ToOrderState() == OrderState.CAPTURE_REQUIRED;
    }

    public static bool IsWaitingForApproval(this Order order)
    {
        return order.State.ToOrderState() == OrderState.WAITING_FOR_APPROVAL;
    }

    public static bool IsStoppedForever(this Order order)
    {
        return (order.State.ToOrderState()
                   is OrderState.NOT_ELIGIBLE_CANCELLED
                   or OrderState.NOT_ELIGIBLE_THRESHOLD_LIMIT
                   or OrderState.NOT_ELIGIBLE_DUPLICATE_ORDER
                   or OrderState.NOT_ELIGIBLE_CONSENT_REJECTED
                   or OrderState.NOT_ELIGIBLE_EXPIRED)
               || order.StopRetries == true;
    }

    public static bool IsOnHold(this Order order)
    {
        return (order.State.ToOrderState()
            is OrderState.ON_HOLD);
    }

    public static bool CanBeRetriedUntilExpired(this Order order)
    {
        return IsMIT(order) && !IsStoppedForever(order) && !IsOnHold(order);
    }

    // public static bool CanBeEvaluated(this Order order)
    // {
    //     if (order.IsApproved() || order.IsExpired() || order.IsStopped() || order.IsOnHold())
    //         return false;
    //
    //     if (order.IsCIT)
    //     {
    //         return !CannotBeEligibleNow(order);
    //     }
    //     else // MIT
    //     {
    //         return CanBeRetriedUntilExpired(order);
    //     }
    // }

    public static void SetMeta(this Order order, string key, string value)
    {
        var metaDictionary = order.Meta != null
            ? JsonConvert.DeserializeObject<Dictionary<string, string>>(order.Meta)
            : new Dictionary<string, string>();

        metaDictionary[key] = value;

        order.Meta = JsonConvert.SerializeObject(metaDictionary);
    }

    public static Dictionary<string, string> GetAllMetaReadOnly(this Order order)
    {
        var metaDictionary = order.Meta != null
            ? JsonConvert.DeserializeObject<Dictionary<string, string>>(order.Meta)
            : new Dictionary<string, string>();

        return new Dictionary<string, string>(metaDictionary);
    }

    public static string GetExternalAccountIdOrDefault(this Order order)
    {
        return order.GetAllMetaReadOnly().GetValueOrDefault("ExternalAccountId");
    }

    public static Guid? GetForwardedPaymentInstrumentIdOrDefault(this Order order)
    {
        var meta = order.GetAllMetaReadOnly();
        meta.TryGetValue("ForwardedPaymentInstrumentId", out var forwardedPaymentInstrumentString);

#if DEBUG
        if (forwardedPaymentInstrumentString == null)
        {
            // For now, for testing purposes, we can set a payment instrument)
            forwardedPaymentInstrumentString = "fe9341d8-2e77-40e5-8a05-4b05ba12c4cc";

            Workspan.Current?.Log.Warning("ForwardedPaymentInstrumentId is null. Using test value");
        }
#endif

        if (forwardedPaymentInstrumentString != null)
        {
            try
            {
                return Guid.Parse(forwardedPaymentInstrumentString);
            }
            catch (Exception e)
            {
                Workspan.Current?.Log.Fatal(e, "Failed to parse forwarded payment instrument ID");
            }
        }


        return null;
    }

    public static bool IsAnyUserChallengeExecuted(this Order order)
    {
        return order.ExecutedCures?.Any(x => x.IsUserChallenge) == true;
    }

    public static int NumberOfUserChallengesExecuted(this Order order)
    {
        return order.ExecutedCures?.Count(x => x.IsUserChallenge) ?? 0;
    }

    public static void AddPopupToPopupSequence(this Order order, string popupName, bool resetPopupSequence)
    {
        var popupId = CureBase.GetCureId(popupName);

        StringBuilder popupSequence = new(resetPopupSequence || order.PopupSequence == null ? "" : order.PopupSequence);

        if (popupSequence.Length > 0) popupSequence.Append(';');
        popupSequence.Append(popupId);

        order.PopupSequence = popupSequence.ToString();
    }

    public static bool HasCurrentPopup(this Order order)
    {
        return !string.IsNullOrWhiteSpace(order.PopupSequence);
    }

    public static string? RemoveCurrentPopupFromSequence(this Order order)
    {
        if (order.PopupSequence == null) return null;

        var popupSequenceSplit = order.PopupSequence.Split(';');

        StringBuilder newPopupSequence = new StringBuilder();
        for (int i = 1; i < popupSequenceSplit.Length; i++)
        {
            if (newPopupSequence.Length > 0) newPopupSequence.Append(';');
            newPopupSequence.Append(popupSequenceSplit[i]);
        }

        order.PopupSequence = newPopupSequence.ToString();

        return popupSequenceSplit.FirstOrDefault();
    }

    public static string? GetCurrentPopupId(this Order order)
    {
        if (order.PopupSequence == null) return null;

        var popupSequenceSplit = order.PopupSequence.Split(';');

        return popupSequenceSplit.FirstOrDefault();
    }

    public static bool IsMIT(this Order order)
    {
        return order.IsCIT == false;
    }


    public static EvaluateRequestType GetEvaluationRequestType(this Order order)
    {
        return order.EvaluationRequestType != null
            ? Utils.EnumHelpers.ParseEnum<EvaluateRequestType>(order.EvaluationRequestType)
            : EvaluateRequestType.EVALUATE;
    }

    public static DesiredPaymentTransactionType GetDesiredPaymentTransactionType(this Order order)
    {
        return order.DesiredPaymentTransactionType != null
            ? Utils.EnumHelpers.ParseEnum<DesiredPaymentTransactionType>(order.DesiredPaymentTransactionType)
            : DesiredPaymentTransactionType.Authorize;
    }

    public static bool IsUiWidgetAvailable(this Order order, Merchant merchant)
    {
        return order.SenseKeyIsMatched || merchant.UIWidgetOptional != true;
    }

    public static bool Is3dsEnabled(this Order order, Merchant merchant)
    {
        return
            string.IsNullOrEmpty(order.ACHToken) && // No ACH debit pending
            //Order.SenseKeyIsMatched &&
            merchant.Global3DSEnabled
            ;
    }

    public static bool Is3dsInformationOnlyEnabled(this Order order, Merchant merchant)
    {
        return
            Is3dsEnabled(order, merchant) &&
            merchant.InformationalOnly3DS
            ;
    }

    #region Payment Provider Black List

    public static HashSet<string> GetPaymentProviderBlackList(this Order order)
    {
        string responseCodes = order.PaymentProviderBlackList;
        return responseCodes != null
            ? JsonConvert.DeserializeObject<HashSet<string>>(responseCodes)
            : new HashSet<string>();
    }

    public static void AddPaymentProviderToBlackList(this Order order, string providerName)
    {
        var blackList = GetPaymentProviderBlackList(order);

        blackList.Add(providerName);

        order.PaymentProviderBlackList = JsonConvert.SerializeObject(blackList);
    }

    #endregion

    public static bool IsInTerminalOrKioskMode(this Order order)
    {
        return IsInTerminalMode(order) || IsInKioskMode(order);
    }

    public static bool IsInTerminalMode(this Order order)
    {
        return order.OrderSource is nameof(OrderSource.terminal) && order.IsCIT;
    }

    public static bool IsInKioskMode(this Order order)
    {
        return order.OrderSource is nameof(OrderSource.kiosk) && order.IsCIT;
    }

    public static DTO.TransactionType GetRequestedTransactionType(this Order order, EvaluateRequest evaluateRequest,
        Merchant merchant)
    {
        // If transaction type is not specified, we use merchant's default
        if (evaluateRequest.TransactionType == null)
        {
            if (merchant.CaptureRequired == true)
            {
                return DTO.TransactionType.authorization;
            }
            else
            {
                return DTO.TransactionType.purchase;
            }
        }

        if (EnumHelpers.TryParseEnum(evaluateRequest.TransactionType.ToLower(),
                out DTO.TransactionType transactionType))
            return transactionType;

        throw new ArgumentException($"Invalid transaction type: {evaluateRequest.TransactionType}");
    }

    public static bool IsInRequireCaptureMode(this Order order, Merchant merchant)
    {
        var evaluateRequest = JsonConvert.DeserializeObject<EvaluateRequest>(order.Payload);
        return IsInRequireCaptureMode(order, evaluateRequest, merchant);
    }

    public static bool IsInRequireCaptureMode(this Order order, EvaluateRequest evaluateRequest, Merchant merchant)
    {
        if (order.IsMIT())
            return false;

        var requestedTransactionType = GetRequestedTransactionType(order, evaluateRequest, merchant);
        return requestedTransactionType == DTO.TransactionType.authorization;
    }

    public static bool IsACHDebitAuthorized(this Order order) =>
        order.ACHTransactionId != null;

    public static bool IsOrderPaymentFullyAuthorized(this Order order) =>
        order.FullAuthorizationOrSaleResult()?.Success == true;

    public static PaymentTransactionResult? FullAuthorizationOrSaleResult(this Order order)
    {
        return order.PaymentTransactionResult != null
            ? JsonConvert.DeserializeObject<PaymentTransactionResult>(order.PaymentTransactionResult)
            : null;
    }

    public static bool IsPaymentAuthorizedOrCaptured(this Order order)
    {
        // Check if any successful transaction is active
        if (!string.IsNullOrWhiteSpace(order.PaymentTransactionResult))
            return true;

        if (order.IsPaymentCaptured == true)
            return true;

        if (order.IsACHDebitAuthorized())
            return true;

        return false;
    }
}