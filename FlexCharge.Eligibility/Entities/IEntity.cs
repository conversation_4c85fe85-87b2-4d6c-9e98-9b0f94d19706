using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FlexCharge.Eligibility.Entities
{
    public interface IEntity
    {
        System.Guid Id { get; set; }
        bool IsDeleted { get; set; }
    }

    public interface IAuditableEntity : IEntity
    {
        DateTime CreatedOn { get; set; }
        DateTime ModifiedOn { get; set; }
        string CreatedBy { get; set; }
        string ModifiedBy { get; set; }
    }

    public class Entity : IEntity
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Key]
        public System.Guid Id { get; set; }
        public bool IsDeleted { get; set; }
    }

    public class AuditableEntity : Entity, IAuditableEntity
    {
        public DateTime CreatedOn { get; set; } = DateTime.Now.ToUniversalTime();
        public DateTime ModifiedOn { get; set; } = DateTime.Now.ToUniversalTime();
        public string CreatedBy { get; set; }
        public string ModifiedBy { get; set; }
    }
}
