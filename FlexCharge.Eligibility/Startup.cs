//#define CREATE_CURES_LIST
//#define CREATE_ELIGIBILITY_CHECKS_LIST

using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using FlexCharge.Common;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Cache;
using FlexCharge.Common.Cloud.BI.Amazon;
using FlexCharge.Common.DataBase;
using FlexCharge.Common.Dependencies;
using FlexCharge.Common.DistributedLock.Implementations.RedLock;
using FlexCharge.Common.Emails;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.PostgreSql;
using FlexCharge.Common.Swagger;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.Cures;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Services.BinNumberValidationServices;
using FlexCharge.Eligibility.Services.CreditBureauServices;
using FlexCharge.Eligibility.Services.EligibilityService;
using FlexCharge.Eligibility.Services.FraudDeviceFingerprintingService;
using FlexCharge.Eligibility.Services.FraudServices;
using FlexCharge.Common.GeoServices;
using FlexCharge.Common.Grpc;
using FlexCharge.Eligibility.Services.SessionMatcherService;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.NoSQL.DynamoDB;
using FlexCharge.Common.Shared.Common;
using FlexCharge.Common.Shared.Merchants.Sites;
using FlexCharge.Common.Shared.Orders.GRPC;
using FlexCharge.Common.Shared.Partners;
using FlexCharge.Common.Shared.Payments.BinChecker.GRPC;
using FlexCharge.Common.Shared.Payments.GRPC;
using FlexCharge.Common.Shared.Tracking.DeviceDetection;
using FlexCharge.Common.Shared.Tracking.GRPC;
using FlexCharge.Common.Shared.UIBuilder;
using FlexCharge.Common.Shared.UrlShortener;
using FlexCharge.Eligibility.Controllers;
using FlexCharge.Common.Sms;
using FlexCharge.Common.Telemetry.HttpRequests;
using FlexCharge.Common.Telemetry.PerformanceCounters;
using FlexCharge.Contracts.Commands.Vault;
using FlexCharge.Eligibility.Adapters.Stripe;
using FlexCharge.Eligibility.Consumers.StripeMerchantInformationService;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Workflow.Steps;
using FlexCharge.Eligibility.Services;
using FlexCharge.Eligibility.Services.CureService;
using FlexCharge.Eligibility.Services.ConsumerNotificationsService;
using FlexCharge.Eligibility.Services.DataNormalization;
using FlexCharge.Eligibility.Services.DistributedLock;
using FlexCharge.Eligibility.Services.DistributedLock.Implementations.DistributedCache;
using FlexCharge.Eligibility.Services.EligibilityService.EligibilityWorkflow;
using FlexCharge.Eligibility.Services.ExternalProviderDunningService;
using FlexCharge.Eligibility.Services.MerchantsService;
using FlexCharge.Eligibility.Services.OffSessionRetryService;
using FlexCharge.Eligibility.Services.Orders;
using FlexCharge.Eligibility.Services.Orders.OrderStates;
using FlexCharge.Eligibility.Services.PartnerService;
using FlexCharge.Eligibility.Services.Payers;
using FlexCharge.Eligibility.Services.PaymentsService;
using FlexCharge.Eligibility.Services.ProcessExpiredOrdersService;
using FlexCharge.Eligibility.Services.RecyclingEngineService;
using FlexCharge.Eligibility.Services.RequestRateLimiterService;
using FlexCharge.Eligibility.Services.RiskManagement;
using FlexCharge.Eligibility.Services.SessionMatcherService.SessionMatchers;
using FlexCharge.Eligibility.Services.StripeReportsService;
using FlexCharge.Eligibility.Services.ThreeDSService;
using FlexCharge.Eligibility.Services.Workflows;
using FlexCharge.Eligibility.Workflows.SubFlowRegistry;
using FlexCharge.Eligibility.Workflows.SubFlows;
using FlexCharge.WorkflowEngine;
using FlexCharge.WorkflowEngine.Common;
using Microsoft.AspNetCore.Mvc;
using Npgsql.EntityFrameworkCore.PostgreSQL.Infrastructure;

namespace FlexCharge.Eligibility
{
    public class Startup
    {
        public IConfiguration Configuration { get; }

        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public void ConfigureServices(IServiceCollection services)
        {
            bool enableTelemetryConsoleExporter = false;

            #region For OpenTelemetry troubleshooting purposes only

            // if (EnvironmentHelper.IsInSandboxOrStagingOrDevelopment)
            // {
            //     enableTelemetryConsoleExporter = true;
            // }

            #endregion

            services.AddTelemetry(enableTelemetryConsoleExporter);
            services.AddCloudWatchPerformanceCountersTelemetry<Startup>();

            services.AddActivities();

            services.AddHttpClient();

            services.AddTransient<IOptimisticDistributedLockService, DistributedCacheBasedLockService>();

            services.AddTransient<ISecurityCheckService, SecurityCheckService>();
            services.AddTransient<IFingerprintService, FingerprintService>();
            services.AddTransient<IRiskManagementService, RiskManagementService>();
            services.AddTransient<IRequestRateLimiterService, RequestRateLimiterService>();
            services.AddTransient<INormalizeDataService, NormalizeDataService>();

            services.AddTransient<ITransmitAndEvaluateService, TransactionController>();

            services.AddTransient<ICureService, CureService>();
            services.AddTransient<IEligibilityWorkflowFactory, EligibilityWorkflowFactory>();

            services.AddTransient<IConsumerNotificationsService, ConsumerNotificationsService>();

            services.AddTransient<IOffSessionRetrySchedulerService, OffSessionRetrySchedulerService>();
            services.AddTransient<IOffSessionRetryService, OffSessionRetryService>();

            services.AddTransient<IFraudDeviceFingerprintingService, FraudDeviceFingerprintingService>();
            services.AddTransient<IOrderService, OrderService>();
            services.AddTransient<IOrderStateMachine, OrderStateMachine>();
            services.AddTransient<IPaymentsService, PaymentsService>();

            services.AddTransient<IGeoServices, GeoServices>();
            services.AddTransient<IEligibilityService, EligibilityService>();
            services.AddTransient<IProcessExpiredOrdersService, ProcessExpiredOrdersService>();
            services.AddTransient<I3DSService, ThreeDSService>();
            services.AddTransient<IPartnerService, PartnerService>();
            services.AddTransient<IMerchantsService, MerchantsService>();

            services.AddTransient<IBlockResolverService, BlockResolverService>();

            services.AddTransient<IRecyclingEngineService, RecyclingEngineService>();

            services.AddTransient<IPayersService, PayersService>();

            services.AddTransient<KinesisService>();

            services.AddTransient<IExternalProviderDunningService, ExternalProviderDunningService>();

            services.AddTransient<IHttpContextAccessor, HttpContextAccessor>();
            services.AddTransient(typeof(IOptions<>), typeof(OptionsManager<>));

            #region Workflows

            services.AddSingleton<ISubFlowRegistry, SubFlowRegistry>();
            services.AddTransient<IExternalControlsService, ExternalControlsService>();

            #endregion

            #region Session Matching Services

            services.AddTransient<ISessionMatcherService, SessionMatcherService>();

            //services.AddTransient<ISessionMatcherService, FuzzySessionMatcherService>();
            services.AddTransient<ISessionMatcher, ExternalOrderIdSessionMatcher>();
            services.AddTransient<ISessionMatcher, CustomerIpSessionMatcher>();

            services.AddTransient<ServiceCollectionExtensions.SessionMatcherResolver>(serviceProvider => key =>
            {
                var sessionMatcherServiceType = key switch
                {
                    SessionMatchersEnum.ExternalOrderIdSessionMatcher => typeof(ExternalOrderIdSessionMatcher),
                    SessionMatchersEnum.CustomerIpSessionMatcher => typeof(CustomerIpSessionMatcher),
                    SessionMatchersEnum.CustomerEmailSessionMatcher => typeof(CustomerEmailSessionMatcher),
                    _ => throw new KeyNotFoundException("Unknown session matcher type")
                };

                return (ISessionMatcher) ActivatorUtilities.CreateInstance(serviceProvider, sessionMatcherServiceType);
            });

            #endregion

            #region Fraud Services

            services.AddTransient<IFraudService, SeonService>();
            services.AddTransient<IFraudService, KountService>();
            services.AddTransient<ServiceCollectionExtensions.FraudServiceResolver>(serviceProvider => key =>
            {
                var fraudServices = serviceProvider.GetServices<IFraudService>();
                switch (key)
                {
                    case FraudProvidersEnum.SEON:
                        return fraudServices.First(x => x.GetType() == typeof(SeonService));
                    case FraudProvidersEnum.KOUNT:
                        return fraudServices.First(x => x.GetType() == typeof(KountService));
                    default:
                        throw new KeyNotFoundException();
                }
            });

            #endregion

            #region Bin Number Validation Services

            services.AddTransient<IBinNumberValidationService, SeonBinNumberValidationService>();
            services.AddTransient<IBinNumberValidationService, InternalBinNumberValidationService>();
            services.AddTransient<ServiceCollectionExtensions.BinNumberValidationServiceResolver>(serviceProvider =>
                key =>
                {
                    var binNumberValidationServices = serviceProvider.GetServices<IBinNumberValidationService>();
                    switch (key)
                    {
                        case BinNumberValidationProvidersEnum.SEON:
                            return binNumberValidationServices.First(x =>
                                x.GetType() == typeof(SeonBinNumberValidationService));
                        case BinNumberValidationProvidersEnum.INTERNAL:
                            return binNumberValidationServices.First(x =>
                                x.GetType() == typeof(InternalBinNumberValidationService));
                        default:
                            throw new KeyNotFoundException();
                    }
                });

            #endregion

            #region Credit Bureau Services

            services.AddTransient<ICreditBureauService, ExperianService>();
            //services.AddTransient<ICreditBureauService, TransunionService>();
            services.AddTransient<ServiceCollectionExtensions.CreditBureauServiceResolver>(serviceProvider => key =>
            {
                var creditBureauServices = serviceProvider.GetServices<ICreditBureauService>();

                switch (key)
                {
                    case BureauProvidersEnum.EXPERIAN:
                        return creditBureauServices.First(x => x.GetType() == typeof(ExperianService));
                    // case BureauProvidersEnum.TRANSUNION:
                    //     return creditBureauServices.First(x => x.GetType() == typeof(TransunionService));
                    default:
                        throw new KeyNotFoundException();
                }
            });

            #endregion

            #region Cures

            services.PopulateAndAddTransients<ICure>();
            services.AddTransient<ServiceCollectionExtensions.CureResolver>(serviceProvider =>
                (key, throwExceptionIfNotFound) =>
                {
                    var allCures = serviceProvider.GetServices<ICure>();

                    var cure = allCures.SingleOrDefault(x => x.CureId.ToUpper() == key.ToUpper());

#if DEBUG && CREATE_CURES_LIST
                StringBuilder allCuresList = new StringBuilder();
                foreach (var cureItem in allCures)
                {
                    allCuresList.AppendLine(cureItem.GetType().Name.Replace("_", " "));
                }
#endif

                    if (cure is null && throwExceptionIfNotFound)
                        throw new KeyNotFoundException($"Cure {key} not found");

                    return cure;
                });

            #endregion

            #region Eligibility Checks

            EligibilityChecksFactory.AddEligibilityChecks(services);
            services.AddTransient<ServiceCollectionExtensions.EligibilityCheckResolver<EligibilityCheckContext>>(
                serviceProvider => key =>
                {
                    IBlockImplementation<EligibilityCheckContext> eligibilityCheck = null;
                    try
                    {
                        eligibilityCheck =
                            EligibilityChecksFactory.CreateEligibilityCheckOrDefault(key, serviceProvider);
                    }
                    catch (Exception e)
                    {
                        Workspan.Current?.RecordFatalException(e, "Error while resolving eligibility check");
                    }

                    if (eligibilityCheck is null) throw new KeyNotFoundException($"Eligibility check {key} not found");

                    return eligibilityCheck;
                });

            #endregion

            #region Eligibility Adapters

            services.AddStripeAdapter();

            #endregion

            #region External Payment Providers

            services.AddTransient<IStripeReportsService, StripeReportsService>();
            services.AddTransient<IStripeMerchantInformationService, StripeMerchantInformationService>();

            #endregion

            services.Configure<AppOptions>(Configuration.GetSection("app"));
            services.Configure<ActivityServiceOptions>(Configuration.GetSection("activity"));
            services.Configure<EligibilityOptions>(Configuration.GetSection("eligibility"));
            services.Configure<ExperianOptions>(Configuration.GetSection("experian"));
            services.AddOptions();

            //Disable skip model validation on action filter
            services.Configure<ApiBehaviorOptions>(options => { options.SuppressModelStateInvalidFilter = true; });

            var connectionString =
                $@"Host={Environment.GetEnvironmentVariable("DB_HOST")};Port={Environment.GetEnvironmentVariable("DB_PORT")};Database={Environment.GetEnvironmentVariable("DB_DATABASE")};Username={Environment.GetEnvironmentVariable("DB_USERNAME")};Password='{Environment.GetEnvironmentVariable("DB_PASSWORD")}';";
#if DEBUG
            connectionString =
                "Host=localhost;Database=fc.eligibility;Username=eligibility-service-staging;Password=*****";
#endif

            NpgsqlDbContextOptionsBuilder MapEnums(NpgsqlDbContextOptionsBuilder options)
            {
                return options
                    //.MapEnum<OrderState>()
                    ;
            }

            services.AddEntityFrameworkNpgsql()
                .AddNpgsqlDbContext<PostgreSQLDbContext>(connectionString,
                    npgsqlOptionsAction: options => MapEnums(options))
                .AddNpgsqlDbContext<ReadOnlyPostgreSQLDbContext>(
                    ReadOnlyConnectionString.MakeReadOnly(connectionString),
                    npgsqlOptionsAction: options => MapEnums(options));
            //.AddNpgsqlDbContext<PostgreSQLDbContext>("Host=localhost;Database=fc.merchants;Username=merchant-service-staging;Password=*****");


            services.AddEntityFrameworkNpgsql()
                ////Temporary connection to JS server database to get Sense JS data (SenseKey)
                // .AddNpgsqlDbContext<SenseJsExternalPostgreSQLDbContext>(
                //         $@"Host={Environment.GetEnvironmentVariable("POSTGRES_DB_SENSEJS_HOST")};Port={Environment.GetEnvironmentVariable("POSTGRES_DB_SENSEJS_PORT")};Database={Environment.GetEnvironmentVariable("POSTGRES_DB_SENSEJS")};Username={Environment.GetEnvironmentVariable("POSTGRES_USER_SENSEJS")};Password='{Environment.GetEnvironmentVariable("POSTGRES_PASSWORD_SENSEJS")}';"
                // )
                .AddNpgsqlDbContext<WorkflowPostgreSQLDbContext>(
                    $@"Host={Environment.GetEnvironmentVariable("WORKFLOW_ENGINE_DB_HOST")};Port={Environment.GetEnvironmentVariable("WORKFLOW_ENGINE_DB_PORT")};Database={Environment.GetEnvironmentVariable("WORKFLOW_ENGINE_DB_DATABASE")};Username={Environment.GetEnvironmentVariable("WORKFLOW_ENGINE_DB_USERNAME")};Password='{Environment.GetEnvironmentVariable("WORKFLOW_ENGINE_DB_PASSWORD")}';");
            //.AddDbContext<PostgreSQLDbContext>(options => options.UseNpgsql("Host=localhost;Database=fc.merchants;Username=merchant-service-staging;Password=*****"));

            services.AddJwt();

            services.AddAuthorization(options =>
                {
                    options.AddPolicy(MyPolicies.SUPER_ADMINS_ONLY,
                        policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.SUPER_ADMIN));
                    options.AddPolicy(MyPolicies.ADMINS_ONLY,
                        policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.MERCHANT_ADMIN));
                    options.AddPolicy(MyPolicies.USERS,
                        policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.USER));

                    options.AddPolicy(MyPolicies.ADMINS_AND_MERACHANTS_ADMINS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN,
                                SuperAdminGroups.MERCHANT_ADMIN);
                        });
                    options.AddPolicy(MyPolicies.ADMINS_AND_ALL_MERCHANTS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN,
                                MerchantGroups.MERCHANT_ADMIN,
                                MerchantGroups.MERCHANT_SUPPORT,
                                MerchantGroups.MERCHANT_SUPPORT_ADMIN,
                                MerchantGroups.MERCHANT_FINANCE,
                                MerchantGroups.MERCHANT_DEVELOPER);
                        });

                    options.AddPolicy(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN,
                                SuperAdminGroups.PARTNER_ADMIN,
                                SuperAdminGroups.INTEGRATION_PARTNER_ADMIN,
                                MerchantGroups.MERCHANT_ADMIN,
                                MerchantGroups.MERCHANT_SUPPORT,
                                MerchantGroups.MERCHANT_SUPPORT_ADMIN,
                                MerchantGroups.MERCHANT_FINANCE,
                                MerchantGroups.MERCHANT_DEVELOPER);
                        });
                }
            );

            services.AddAutoMapper(typeof(Startup));
            services.AddControllers().AddNewtonsoftJson();
            services.AddSwaggerDocs();

            services.AddMassTransit<Startup>(
                x =>
                {
                    x.AddFastRequestClient<TokenizeInstrumentCommand>(Microservices.Vault);
                    x.AddFastRequestClient<GetByVaultIdCommand>(Microservices.Vault);
                });

            #region GRPC

            services.AddFlexGrpc();

            services.AddFlexGrpcClient<Grpc.Payments.GrpcGreeter.GrpcGreeterClient, PaymentsGrpcOptions>(
                nameof(PaymentsGrpcOptions.PaymentsEndpoint));

            services
                .AddFlexGrpcClient<Grpc.Payments.GrpcPaymentsService.GrpcPaymentsServiceClient, PaymentsGrpcOptions>(
                    nameof(PaymentsGrpcOptions.PaymentsEndpoint));

            // services.AddFlexGrpcClient<Grpc.Vault.GrpcVaultService.GrpcVaultServiceClient, VaultGrpcOptions>(
            //     nameof(VaultGrpcOptions.VaultEndpoint));

            services
                .AddFlexGrpcClient<Grpc.Orders.GrpcOrdersService.GrpcOrdersServiceClient, OrdersGrpcOptions>(
                    nameof(OrdersGrpcOptions.OrdersEndpoint));

            services
                .AddFlexGrpcClient<Grpc.Payments.BinChecker.GrpcBinCheckerService.GrpcBinCheckerServiceClient,
                    BinCheckerGrpcOptions>(nameof(BinCheckerGrpcOptions.BinCheckerEndpoint));

            services
                .AddFlexGrpcClient<Grpc.Tracking.GrpcTrackingService.GrpcTrackingServiceClient, TrackingGrpcOptions>(
                    nameof(TrackingGrpcOptions.TrackingEndpoint));

            #endregion

            services.AddCors(options =>
            {
                options.AddPolicy("CorsPolicy", cors =>
                    cors.AllowAnyMethod()
                        .AllowAnyOrigin()
                        .AllowAnyHeader());
            });

            services.AddBackgroundWorkerService(Configuration);

            //services.AddDistributedPostgreSqlCache();
            services.AddRedisCache();
            services.AddBigPayloadSupport();
            services.AddExternalRequestsCache();
            services.AddExternalRequestsDistributedMemoryDatabase();
            services.AddUrlShortener();
            services.AddAmazonSecretsManager();

            services.AddEmailClient();
            services.AddSmsClient();

            services.AddRedLockDistributedLock();

            services.AddUIBuilder();

            services.AddDynamoDBAsync();

            services.AddWorkflows();

            services.AddDeviceDetector();

            #region Registering Shared Flex Services

            services.AddSharedPartnerSettings();
            services.AddSharedMerchantSitesService();

            #endregion

            #region Validating Workflow Resources

            WorkflowResourcesManager.ValidateAndPreLoadWorkflowResources();

            #endregion

            #region Validation All State Machines States

            StateMachinesStatesValidator.ValidateAllStatesDefinedAndThrowAsync();

            #endregion
        }


        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IHostApplicationLifetime applicationLifetime,
            IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                // Enable middleware to serve generated Swagger as a JSON endpoint.
            }

            app.UseCors("CorsPolicy");
            //app.UseHttpsRedirection();
            app.UseSwaggerDocs();

            app.UseRouting();
            app.UseAuthorization();

            app.UseAutoMigrations<PostgreSQLDbContext>();

            app.UseEligibilityChecks();


            app.UsePublicHttpRequestsTelemetry(); // Should be right before UseEndpoints to work correctly
            app.UseEndpoints(endpoints => { endpoints.MapControllers(); });

            app.UseMassTransit();
        }
    }

    static class EligibilityChecksFactory
    {
        static Dictionary<string, Type> _eligibilityChecksMap;
        private static HashSet<Type> _eligibilityCheckTypes;

        public static void AddEligibilityChecks(this IServiceCollection services)
        {
            PopulateAndAddTransients<IBlockImplementation<EligibilityCheckContext>>(services);
        }


        public static void UseEligibilityChecks(this IApplicationBuilder app)
        {
            List<IBlockImplementation<EligibilityCheckContext>> allEligibilityChecks = new();
            foreach (var eligibilityCheckType in _eligibilityCheckTypes)
            {
                allEligibilityChecks.Add(
                    app.ApplicationServices.GetRequiredService(eligibilityCheckType) as
                        IBlockImplementation<EligibilityCheckContext>);
            }

            EnsureEligibilityCheckIdsAreUnique(allEligibilityChecks);

#if DEBUG && CREATE_ELIGIBILITY_CHECKS_LIST
                StringBuilder allEligibilityChecksList = new StringBuilder();
                foreach (var eligibilityCheck in allEligibilityChecks)
                {
                    allEligibilityChecksList.AppendLine(eligibilityCheck.GetType().Name.Replace("_", " "));
                }
#endif


            _eligibilityChecksMap = allEligibilityChecks.ToDictionary(
                x => x.BlockId.ToUpper(), x => x.GetType());
        }

        static Type? GetEligibilityCheckType(string eligibilityCheckId)
        {
            if (_eligibilityChecksMap.TryGetValue(eligibilityCheckId, out var type))
            {
                return type;
            }

            return null;
        }

        public static IBlockImplementation<EligibilityCheckContext>? CreateEligibilityCheckOrDefault(
            string eligibilityCheckId,
            IServiceProvider serviceProvider)
        {
            var eligibilityCheckType = GetEligibilityCheckType(eligibilityCheckId.ToUpper());

            if (eligibilityCheckType is null)
                return null;

            var eligibilityCheck =
                serviceProvider.GetRequiredService(eligibilityCheckType) as
                    IBlockImplementation<EligibilityCheckContext>;

            return eligibilityCheck;
        }

        static void PopulateAndAddTransients<TTransientBase>(IServiceCollection services,
            Type aTypeFromAssembly = null)
        {
            if (services == null)
            {
                throw new ArgumentNullException(nameof(services));
            }

            var assemblyToSearchForTransients = aTypeFromAssembly?.Assembly ?? Assembly.GetCallingAssembly();

            PopulateAndAddTransients<TTransientBase>(services, assemblyToSearchForTransients);
        }

        static void PopulateAndAddTransients<TTransientBase>(IServiceCollection services,
            Assembly assemblyToSearchForTransients)
        {
            var allTransients = assemblyToSearchForTransients.DefinedTypes.Where(typeInfo =>
                typeof(TTransientBase).IsAssignableFrom(typeInfo) && typeInfo.IsClass && !typeInfo.IsAbstract);

            _eligibilityCheckTypes = new HashSet<Type>();
            foreach (var typeInfo in allTransients)
            {
                // Add only unique types
                if (_eligibilityCheckTypes.Add(typeInfo))
                {
                    services.AddTransient(typeInfo);
                }
            }
        }

        private static void EnsureEligibilityCheckIdsAreUnique(
            List<IBlockImplementation<EligibilityCheckContext>> allEligibilityChecks)
        {
            using var workspan = Workspan.Start<Startup>();

            var duplicateIds = allEligibilityChecks
                .GroupBy(x => x.BlockId)
                .Where(g => g.Count() > 1)
                .Select(g => g.Key)
                .ToList();

            if (duplicateIds.Any())
            {
                workspan.Log.Fatal("Duplicate eligibility check ids found: {DuplicateIds}", duplicateIds);
                throw new FlexChargeException("Duplicate eligibility check ids found");
            }
        }
    }
}