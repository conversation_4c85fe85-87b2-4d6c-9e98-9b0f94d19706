using System;
using Amazon.DynamoDBv2.DataModel;

namespace FlexCharge.Eligibility.RiskManagement.Fingerprinting.Tables;

[DynamoDBTable("FingerprintStatistics")]
public class FingerprintStatistics
{
    [DynamoDBHashKey] public string Fingerprint { get; set; }

    [DynamoDBProperty] public string FingerprintType { get; set; }

    [DynamoDBProperty] public Guid? Mid { get; set; }

    [DynamoDBProperty] public DateTime CreatedOn { get; set; }

    [DynamoDBProperty] public DateTime ModifiedOn { get; set; }

    [DynamoDBProperty] public string Meta { get; set; }

    [DynamoDBProperty] public DateTime? LastAuthorizationAttempt;


    [DynamoDBProperty] public long TotalMatches { get; set; }

    [DynamoDBProperty] public long EvaluateMatches { get; set; }

    [DynamoDBProperty] public long TotalOpenOffers { get; set; }

    [DynamoDBProperty] public long TotalOpenOffersAmount { get; set; }

    [DynamoDBProperty] public long TotalOpenOrders { get; set; }

    [DynamoDBProperty] public long TotalOpenOrdersAmount { get; set; }

    [DynamoDBProperty] public long TotalPaidInAmount { get; set; }

    [DynamoDBProperty] public long TotalFullyPaidInOrders { get; set; }

    [DynamoDBProperty] public long TotalWrittenOffOrders { get; set; }

    [DynamoDBProperty] public long TotalWrittenOffOrdersAmount { get; set; }
}