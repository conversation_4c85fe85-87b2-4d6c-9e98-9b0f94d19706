using System;
using System.Threading.Tasks;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Common.Shared.UIBuilder;

namespace FlexCharge.Eligibility.Cures.Implementations;

public class C9992_TestComponentGroups : UserChallengeCureBase
{
    protected override async Task CreateAsync(EvaluateRequest evaluateRequest)
    {
        AddTitle(Order, evaluateRequest, "Complete your account");

        UI.HorizontalSeparator();

        UI.SubTitle()
            .Text("Please share your billing details:");

        using (UI.StartRow())
        {
            UI.SmallInput(InputType.Text, Guid.NewGuid())
                .Text("First Name");

            UI.SmallInput(InputType.Text, Guid.NewGuid())
                .Text("Last Name");
        }

        UI.FullRowInput(InputType.Text, Guid.NewGuid())
            .Text("Card Number");


        UI.SmallText()
            .Text("We’ll only ask you for this information once.");
    }

    protected override async Task<UserChallengeResult> ExecuteCureAsync(Order order,
        EvaluateRequest evaluateRequest, ChallengeReEvaluateRequest challengeReEvaluateRequest)
    {
        return UserChallengeResult.RE_EVALUATE;
    }
}