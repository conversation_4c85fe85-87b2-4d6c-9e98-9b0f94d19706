using System;
using System.Threading.Tasks;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Common.Shared.UIBuilder;

namespace FlexCharge.Eligibility.Cures.Implementations;

public class C9988_TestFullPopupUI : C0009_AskForDifferentCardInsteadOfBlocked
{
    // Ask for a new credit card - verify check digit on the fly
    // Error 78 - ask for a new card explaining that used one needs to be unblocked
    // Returns new payment instrument token

    private Guid _openBankingButtonId = new Guid("288B19F2-3F34-4A50-BCB8-C8978D590567");

    protected override async Task CreateAsync(EvaluateRequest evaluateRequest)
    {
        AddTitle(Order, evaluateRequest, "New payment method");

        UI.HorizontalSeparator();

        UI.SubTitle()
            .Text("To proceed, use a different payment card");

        UI.SmallText()
            .Text("You current card is not activated yet - to activate it contact your bank.");


        using (UI.StartRow())
        {
            UI.SmallInput(InputType.Text, Guid.NewGuid())
                .Text("First Name")
                .Placeholder("First Name")
                .Validations(v =>
                    v.Required("First Name is required"));

            UI.SmallInput(InputType.Text, Guid.NewGuid())
                .Text("Last Name")
                .Placeholder("Last Name")
                .Validations(v =>
                    v.Required("Last Name is required"));
        }

        UI.FullRowInput(InputType.Text, Guid.NewGuid())
            .Text("Card Number")
            .Placeholder("Card Number")
            .Validations(v => v
                .Required("Card number is required")
                .Regex(
                    "^(?:(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11}))$",
                    "Card number is incorrect",
                    stripWhitespaceBeforeValidation:
                    true // to support masks with whitespace between number groups
                ));


        using (UI.StartRow())
        {
            UI.SmallInput(InputType.Text, Guid.NewGuid())
                .Text("CVV")
                .Placeholder("CVV")
                .Attribute("maxLength", "4")
                .Validations(v => v
                    .Required("CVV is required")
                    .Regex("^[0-9]{3,4}$", "CVV is incorrect"));

            UI.SmallInput(InputType.Text, Guid.NewGuid())
                .Text("Expiry")
                .Placeholder("MM/YY")
                .Validations(v => v
                    .Required("Expiry date is required")
                    .Regex("^(0[1-9]|1[0-2])\\/?([0-9]{4}|[0-9]{2})$", "Expiry date is incorrect")
                );
        }
    }

    protected override void CreateFormButtons(EvaluateRequest evaluateRequest)
    {
        UI.SubmitFormButton(columnSpan: 6, Align.Left)
            .Text("Pay with card");

        UI.OptionButton(_openBankingButtonId, columnSpan: 6, Align.Right)
            .Text("Pay with bank account");
    }


    protected override async Task<UserChallengeResult> ExecuteCureAsync(Order order,
        EvaluateRequest evaluateRequest, ChallengeReEvaluateRequest challengeReEvaluateRequest)
    {
        if (IsButtonPressed(challengeReEvaluateRequest, _openBankingButtonId))
        {
        }

        return UserChallengeResult.RE_EVALUATE;
    }
}