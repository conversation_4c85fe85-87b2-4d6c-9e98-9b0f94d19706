using System;
using System.Threading.Tasks;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Common.Shared.UIBuilder;

namespace FlexCharge.Eligibility.Cures.Implementations;

public class C2001_AskForBillingAddress : UserChallengeCureBase
{
    // Ask for a new credit card - verify check digit on the fly
    // Returns new payment instrument token

    private Guid _addressLine1QuestionId = new Guid("4C1A40E1-5F2E-40CD-9118-7B47F90DF17F");
    private Guid _addressLine2QuestionId = new Guid("26FDAB95-3EA3-4DDB-B755-B42AAFEDFDCF");
    private Guid _zipQuestionId = new Guid("47644399-F035-466B-BB1F-F0A3F5C863B3");
    private Guid _cityQuestionId = new Guid("09AB5CB4-00DD-4FCD-A244-967146D0BC08");
    private Guid _stateQuestionId = new Guid("263F07E5-C13F-4378-9D9D-152046289EF5");

    protected override async Task CreateAsync(EvaluateRequest evaluateRequest)
    {
        if (!IsImplicitConsentFlow)
        {
            //AddTitle(order, evaluateRequest, "There is an issue with your billing address.");
            AddTitle(Order, evaluateRequest,
                "The billing address you have provided does not match with the address your bank has in record.");
        }
        else
            AddTitle(Order, evaluateRequest,
                "");

        UI.HorizontalSeparator();

        // UI.SubTitle()
        //     .Text("Please update your billing address:");

        UI.SubTitle()
            .Text("<b>Please confirm the home address</b> your bank has on file:");


        UI.FullRowInput(InputType.Text, _addressLine1QuestionId)
            .Text("Address Line 1:")
            .Placeholder("Address Line 1")
            .Validations(v =>
                v.Required("Address Line 1 is required"));

        UI.FullRowInput(InputType.Text, _addressLine2QuestionId)
            .Text("Address Line 2:")
            .Placeholder("Address Line 2");

        using (UI.StartRow())
        {
            UI.SmallInput(InputType.Text, _cityQuestionId)
                .Text("City:")
                .Placeholder("City")
                .Validations(v => v
                    .Required("City is required")
                    .MaxLength(29, "City must be less than 30 characters")
                );

            UI.SmallInput(InputType.Text, _zipQuestionId)
                .Text("Postcode:")
                .Placeholder("Postcode")
                .Validations(v => v
                    .Required("Postcode is required")
                    .Regex("^\\d{5}([\\-]?\\d{4})?$", "Postcode code is incorrect"));
        }

        UI.FullRowSelect(_stateQuestionId)
            .Text("State:")
            .Placeholder("State")
            .PotentialAnswers(x =>
                {
                    foreach (var state in StatePossibleAnswers)
                    {
                        x.AddAnswer(state.Id, state.Name);
                    }
                }
            )
            .Validations(v => v.Required("State is required"))
            .DefaultValue(null);

        // UI.FullRowInput(InputType.Text, Guid.NewGuid())
        //     .Text("Country");

        // UI.SmallText()
        //     .Text("We’ll only ask you for this information once.");
    }


    protected override async Task<UserChallengeResult> ExecuteCureAsync(Order order,
        EvaluateRequest evaluateRequest, ChallengeReEvaluateRequest challengeReEvaluateRequest)
    {
        string paymentToken;
        if (TryGetAnswerAsGuid(challengeReEvaluateRequest, _stateQuestionId, out var stateGuid)
            && StateAnswerHelper.TryToGetAnswer(stateGuid,
                out var stateAnswer) &&
            TryGetAnswerAsString(challengeReEvaluateRequest, _addressLine1QuestionId, out var addressLine1) &&
            TryGetAnswerAsString(challengeReEvaluateRequest, _addressLine2QuestionId, out var addressLine2,
                canBeWhitespace: true) &&
            TryGetAnswerAsString(challengeReEvaluateRequest, _cityQuestionId, out var city) &&
            TryGetAnswerAsString(challengeReEvaluateRequest, _zipQuestionId, out var zip))
        {
            evaluateRequest.BillingInformation.State = stateAnswer.Name;
            evaluateRequest.BillingInformation.City = city;
            evaluateRequest.BillingInformation.Zipcode = zip;
            evaluateRequest.BillingInformation.AddressLine1 = addressLine1;
            evaluateRequest.BillingInformation.AddressLine2 = addressLine2;

            return UserChallengeResult.RE_EVALUATE;
        }
        else return UserChallengeResult.NOT_CURED;
    }

    #region State Question Support

    class StateAnswer : Answer
    {
        public string Abbreviation { get; }

        public StateAnswer(Guid id, string abbreviation, string name) : base(id, name)
        {
            Abbreviation = abbreviation;
        }
    }


    private static readonly StateAnswer[] StatePossibleAnswers = new[]
    {
        new StateAnswer(new Guid("6563fbed-e9ed-4d38-a155-fddc0397d224"), "AL", "Alabama"),
        new StateAnswer(new Guid("ab0702d6-4e5a-484d-8e8c-7c7ec0302e21"), "AK", "Alaska"),
        new StateAnswer(new Guid("9fcbfbb6-3569-4c09-a233-************"), "AZ", "Arizona"),
        new StateAnswer(new Guid("f676bd21-070e-45eb-9419-951c09490946"), "AR", "Arkansas"),
        new StateAnswer(new Guid("8c5c09de-4a27-4ba5-a813-623780d26e7d"), "CA", "California"),
        new StateAnswer(new Guid("aa94aff9-9826-4f1a-a078-983aa6ef39cb"), "CO", "Colorado"),
        new StateAnswer(new Guid("f2e2b8a4-7993-4815-aadd-7a409f37d436"), "CT", "Connecticut"),
        new StateAnswer(new Guid("2439481d-823b-45a8-8982-d4be6caa4ab3"), "DE", "Delaware"),
        new StateAnswer(new Guid("c7483bd8-3c41-405e-b5b8-37593b3c5572"), "DC", "District Of Columbia"),
        new StateAnswer(new Guid("eeebc925-bf16-4ef5-a63c-91f45594a705"), "FL", "Florida"),
        new StateAnswer(new Guid("73756e55-af5d-4e61-96bb-10e1b75b44eb"), "GA", "Georgia"),
        new StateAnswer(new Guid("a668588d-26e5-4a41-8338-be65f0b52e8f"), "HI", "Hawaii"),
        new StateAnswer(new Guid("05a2231b-1a2f-4cff-be09-ef1099cccc88"), "ID", "Idaho"),
        new StateAnswer(new Guid("5a605e90-5989-4f5e-b303-c44dff753aae"), "IL", "Illinois"),
        new StateAnswer(new Guid("efad9b96-3f01-4e31-92d8-40e4fa634d78"), "IN", "Indiana"),
        new StateAnswer(new Guid("ca5f6ca9-b75d-4e02-8c64-0dd1026be2d6"), "IA", "Iowa"),
        new StateAnswer(new Guid("0ba70133-967e-405d-bf22-3d9b26e375df"), "KS", "Kansas"),
        new StateAnswer(new Guid("07a32beb-c9d7-41d4-a601-095da1bde06c"), "KY", "Kentucky"),
        new StateAnswer(new Guid("64e61d7d-dc16-4296-9021-b2c8eaa4cb30"), "LA", "Louisiana"),
        new StateAnswer(new Guid("8b8511ac-b21b-40a0-9d31-6a645f112941"), "ME", "Maine"),
        new StateAnswer(new Guid("92559922-dc1e-42bc-aac3-b3f5c913acf0"), "MD", "Maryland"),
        new StateAnswer(new Guid("87912b9c-a02a-4ab3-80a9-c5b551b40cbe"), "MA", "Massachusetts"),
        new StateAnswer(new Guid("7b8deb01-1b45-42f1-8a48-e4c8040bb494"), "MI", "Michigan"),
        new StateAnswer(new Guid("dde31dff-9465-4ef5-b8f8-64112c27b539"), "MN", "Minnesota"),
        new StateAnswer(new Guid("e56b9e1e-cfff-4c99-b966-cfc9746e8d6e"), "MS", "Mississippi"),
        new StateAnswer(new Guid("8c63cf7c-f7b5-41a9-abc1-adbd5d7c3b54"), "MO", "Missouri"),
        new StateAnswer(new Guid("337ef41c-0524-4ec1-b2ce-b8af9fd6a11a"), "MT", "Montana"),
        new StateAnswer(new Guid("d4a9361a-7116-4f7a-a511-10cd4eadacc9"), "NE", "Nebraska"),
        new StateAnswer(new Guid("876bf679-2e35-4d3f-8b1b-4170563725c1"), "NV", "Nevada"),
        new StateAnswer(new Guid("60688c82-79fc-442b-a41e-3aa66fac89fc"), "NH", "New Hampshire"),
        new StateAnswer(new Guid("*************-4b10-adca-f98f7349d74c"), "NJ", "New Jersey"),
        new StateAnswer(new Guid("3a731cd8-67af-4f1c-afe4-f71d9ccdf490"), "NM", "New Mexico"),
        new StateAnswer(new Guid("331a7202-c81a-415d-8dff-b754460e19a0"), "NY", "New York"),
        new StateAnswer(new Guid("d4241f88-2256-4cbf-bc67-79939a7b65a4"), "NC", "North Carolina"),
        new StateAnswer(new Guid("a1e9efa9-4a4b-4c2e-b4bd-fe13f182ae8a"), "ND", "North Dakota"),
        new StateAnswer(new Guid("6dc89c72-881b-4ff9-931e-b1a450ef4f05"), "OH", "Ohio"),
        new StateAnswer(new Guid("de602523-f350-49d8-ba4d-9f9b3c9db5bb"), "OK", "Oklahoma"),
        new StateAnswer(new Guid("c75b58a2-c092-4c36-a728-9865fd55b322"), "OR", "Oregon"),
        new StateAnswer(new Guid("06a88fa9-b40a-4102-a87f-ed52dd527b20"), "PA", "Pennsylvania"),
        new StateAnswer(new Guid("900e13e0-60fa-44dc-bebe-45c5117106c2"), "RI", "Rhode Island"),
        new StateAnswer(new Guid("d250039e-91c2-4149-a218-f6c4fbb9cdb1"), "SC", "South Carolina"),
        new StateAnswer(new Guid("94fbec0f-93e3-4432-b650-8f359955dfea"), "SD", "South Dakota"),
        new StateAnswer(new Guid("903e2ab0-ba38-4c6a-a3dd-9d974658b5a2"), "TN", "Tennessee"),
        new StateAnswer(new Guid("fb164f21-d184-42fc-b8b4-f009cd75cee9"), "TX", "Texas"),
        new StateAnswer(new Guid("c2133a1a-21da-43da-bc35-558b0f833901"), "UT", "Utah"),
        new StateAnswer(new Guid("0ec0846d-e74e-4494-aadd-e19924db9093"), "VT", "Vermont"),
        new StateAnswer(new Guid("93c611de-f514-4f2f-a1a5-39e0763f9a48"), "VA", "Virginia"),
        new StateAnswer(new Guid("186cb30c-46bd-4d1b-b29c-94d20976a4a7"), "WA", "Washington"),
        new StateAnswer(new Guid("7d4742a1-3596-4264-9b44-a575938a3784"), "WV", "West Virginia"),
        new StateAnswer(new Guid("12075ad0-e53f-4115-bc4c-bdc67c6bd267"), "WI", "Wisconsin"),
        new StateAnswer(new Guid("e954756e-06f0-44e8-9178-13b2200385b7"), "WY", "Wyoming")
    };

    private static SelectorAnswersHelper<StateAnswer> StateAnswerHelper = new(StatePossibleAnswers);

    #endregion
}