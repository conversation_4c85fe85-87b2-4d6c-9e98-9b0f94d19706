using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Common.Shared.UIBuilder;

namespace FlexCharge.Eligibility.Cures.Implementations;

public partial class C2002_AskForDateOfBirth : UserChallengeCureBase
{
    private Guid _monthQuestionId = new Guid("990C778E-B5B2-4402-8F3B-A30B21A50A05");
    private Guid _dayQuestionId = new Guid("4908DB91-D2AD-4E74-B235-E1C2C2D6A33E");
    private Guid _yearQuestionId = new Guid("8B32C210-4179-4B98-A975-281DB64E3995");

    protected override async Task CreateAsync(EvaluateRequest evaluateRequest)
    {
        AddTitle(Order, evaluateRequest, "Complete your account");

        UI.HorizontalSeparator();

        UI.SubTitle()
            .Text("Please enter your birth date:");

        using (UI.StartRow())
        {
            UI.Select(_monthQuestionId, columnSpan: 4)
                .Text("Month")
                .PotentialAnswers(x => x.AddAnswers(MonthPossibleAnswers))
                .Validations(v => v.Required("Month is required"))
                .DefaultValue(null);

            UI.Select(_dayQuestionId, columnSpan: 4)
                .Text("Day")
                .PotentialAnswers(x => x.AddAnswers(DayPossibleAnswers))
                .Validations(v => v.Required("Day is required"))
                .DefaultValue(null);

            UI.Select(_yearQuestionId, columnSpan: 4)
                .Text("Year")
                .Validations(v => v.Required("Year is required"))
                .PotentialAnswers(x => x.AddAnswers(YearPossibleAnswers))
                .DefaultValue(null);
        }

        UI.SmallText()
            .Text("We’ll only ask you for this information once.");
    }

    public override void ValidateAnswers(Order order,
        ChallengeReEvaluateRequest challengeReEvaluateRequest)
    {
        if (!TryGetDateTime(challengeReEvaluateRequest, out var birthDate))
        {
            AddAnswerError(_dayQuestionId, "Incorrect day selected");
        }
    }

    protected override async Task<UserChallengeResult> ExecuteCureAsync(Order order,
        EvaluateRequest evaluateRequest, ChallengeReEvaluateRequest challengeReEvaluateRequest)
    {
        if (TryGetDateTime(challengeReEvaluateRequest, out var birthDate))
        {
            evaluateRequest.Payer.Birthdate = birthDate;

            return UserChallengeResult.RE_EVALUATE;
        }


        return UserChallengeResult.NOT_CURED;
    }

    #region DateTime Helper

    bool TryGetDateTime(ChallengeReEvaluateRequest challengeReEvaluateRequest, out DateTime? dateTime)
    {
        dateTime = null;

        if (TryGetAnswerAsGuid(challengeReEvaluateRequest, _monthQuestionId, out var monthGuid) &&
            MonthAnswerHelper.TryToGetAnswer(monthGuid, out var monthAnswer) &&
            TryGetAnswerAsGuid(challengeReEvaluateRequest, _dayQuestionId, out var dayGuid) &&
            DayAnswerHelper.TryToGetAnswer(dayGuid, out var dayAnswer) &&
            TryGetAnswerAsGuid(challengeReEvaluateRequest, _yearQuestionId, out var yearGuid) &&
            YearAnswerHelper.TryToGetAnswer(yearGuid, out var yearAnswer)
           )
        {
            try
            {
                dateTime = new DateTime(yearAnswer.Value, monthAnswer.Value, dayAnswer.Value);
                return true;
            }
            catch (Exception e)
            {
                Log($"Customer entered incorrect birth date: {yearAnswer.Value}-{monthAnswer.Value}-{dayAnswer.Value}");
            }
        }

        return false;
    }

    #endregion

    #region Month Possible Answers

    private static readonly ValueAnswer<int>[] MonthPossibleAnswers = new[]
    {
        new ValueAnswer<int>(new Guid("a07023c0-5722-4630-95f8-c7eee5186e7f"), "January", 1),
        new ValueAnswer<int>(new Guid("26123b2a-b174-46a9-bc0a-a293459fa6c1"), "February", 2),
        new ValueAnswer<int>(new Guid("7cb53637-81e6-4a26-a645-ad03f4fb8ac0"), "March", 3),
        new ValueAnswer<int>(new Guid("572e1971-dcee-41b4-8c7f-93dbc6725868"), "April", 4),
        new ValueAnswer<int>(new Guid("2a86553d-23fb-46b8-b213-05ed7966df5d"), "May", 5),
        new ValueAnswer<int>(new Guid("9bb9d157-b97e-4f6c-9c31-6ef84bc19ab3"), "June", 6),
        new ValueAnswer<int>(new Guid("0d2bda78-cb8a-4ccc-9c47-479e26cebbbb"), "July", 7),
        new ValueAnswer<int>(new Guid("7d67c5dc-0d17-4344-8464-853b7fff7bc5"), "August", 8),
        new ValueAnswer<int>(new Guid("d5ff1bf0-9357-4f6a-881f-585e3ee965a2"), "September", 9),
        new ValueAnswer<int>(new Guid("0ef08a11-d1ef-4207-99ca-51ec42c108a5"), "October", 10),
        new ValueAnswer<int>(new Guid("092956b6-397a-4224-9d9b-0c263d075db8"), "November", 11),
        new ValueAnswer<int>(new Guid("f3e9e900-e56a-4d42-b103-4a38a1c7be0e"), "December", 12),
    };

    private static SelectorAnswersHelper<ValueAnswer<int>> MonthAnswerHelper = new(MonthPossibleAnswers);

    #endregion

    #region Day Possible Answers

    private static readonly ValueAnswer<int>[] DayPossibleAnswers = new[]
    {
        new ValueAnswer<int>(new Guid("d67753da-7e35-452e-b6d0-3730b7f2c8f6"), "01", 1),
        new ValueAnswer<int>(new Guid("ca614481-be72-4072-9fe3-39526b5d6643"), "02", 2),
        new ValueAnswer<int>(new Guid("1b0a4196-092e-43f5-8ee0-cff5480932c5"), "03", 3),
        new ValueAnswer<int>(new Guid("0568c533-067e-41df-8572-fa274105be24"), "04", 4),
        new ValueAnswer<int>(new Guid("3e0e1227-b3dc-4d32-bb9e-7dd1e3aea654"), "05", 5),
        new ValueAnswer<int>(new Guid("f3d236a8-ab3f-4eb6-91a0-789fc9ab7d39"), "06", 6),
        new ValueAnswer<int>(new Guid("8d816bb6-6e15-41aa-9027-79c987664b4a"), "07", 7),
        new ValueAnswer<int>(new Guid("5c1e2042-9f9c-4767-b229-f07aaa08a980"), "08", 8),
        new ValueAnswer<int>(new Guid("15588f70-669d-4599-a216-66f28a987163"), "09", 9),
        new ValueAnswer<int>(new Guid("ce7ad112-906d-4e63-b5cd-8df453ff1f8f"), "10", 10),
        new ValueAnswer<int>(new Guid("0a980ea0-0b85-413d-8278-e1c3b8db675d"), "11", 11),
        new ValueAnswer<int>(new Guid("0b2fb6f4-ad20-4e8b-8d5d-80c5b8680393"), "12", 12),
        new ValueAnswer<int>(new Guid("eceea4ca-f28a-49cd-a23e-b8eca3c5be25"), "13", 13),
        new ValueAnswer<int>(new Guid("7d5ec17b-ba0f-486a-8fc8-eb1fd8fd28b9"), "14", 14),
        new ValueAnswer<int>(new Guid("2d12ddf1-f2bb-4f31-b3cb-7bbc70a3b1f0"), "15", 15),
        new ValueAnswer<int>(new Guid("0d45436e-6e9a-41aa-a46f-8573421bf963"), "16", 16),
        new ValueAnswer<int>(new Guid("73a6713a-7944-47ab-86ab-653b70d511ba"), "17", 17),
        new ValueAnswer<int>(new Guid("8273cfc0-6370-4c89-92f5-ba355a366733"), "18", 18),
        new ValueAnswer<int>(new Guid("87e33872-e5b9-46f1-99e0-4196bf4f2b9f"), "19", 19),
        new ValueAnswer<int>(new Guid("abd2cbc1-8571-494a-a4d3-07e51bd12ae0"), "20", 20),
        new ValueAnswer<int>(new Guid("aeaa7cae-5fc4-4530-9600-d1fe46984d87"), "21", 21),
        new ValueAnswer<int>(new Guid("d72cf455-a5fc-45bd-96c1-982f2ef79203"), "22", 22),
        new ValueAnswer<int>(new Guid("f1d34f57-8dfc-4e67-b282-8efd3deeb7a4"), "23", 23),
        new ValueAnswer<int>(new Guid("3e56d638-23ee-4a55-b854-d5d8f9b6e301"), "24", 24),
        new ValueAnswer<int>(new Guid("fedf2f7d-6e8f-4c87-b560-79b20a7a4f9d"), "25", 25),
        new ValueAnswer<int>(new Guid("6c9a3fc4-9895-42d8-951f-23bd703c71da"), "26", 26),
        new ValueAnswer<int>(new Guid("a1b6494d-444a-4a24-b589-a974382ec358"), "27", 27),
        new ValueAnswer<int>(new Guid("9ed54f36-da60-49b0-abc5-0bc499c1a8d2"), "28", 28),
        new ValueAnswer<int>(new Guid("02a93ccb-14ea-413a-9bf9-01f6af3834e9"), "29", 29),
        new ValueAnswer<int>(new Guid("952ed2c6-2513-46e7-b1da-1ac0a3599cd0"), "30", 30),
        new ValueAnswer<int>(new Guid("9d94a593-2442-4e79-8e67-3c9c38240e80"), "31", 31),
    };

    private static SelectorAnswersHelper<ValueAnswer<int>> DayAnswerHelper = new(DayPossibleAnswers);

    #endregion

    #region Years Possible Answers

    private static IReadOnlyList<ValueAnswer<int>> _YearPossibleAnswers;

    private static IReadOnlyList<ValueAnswer<int>> YearPossibleAnswers
    {
        get
        {
            if (_YearPossibleAnswers == null)
            {
                var yearsPossibleAnswers = new List<ValueAnswer<int>>();
                for (int year = 1900; year < DateTime.Now.Year; year++)
                {
                    yearsPossibleAnswers.Add(new ValueAnswer<int>(Guid.NewGuid(), year.ToString(), year));
                }

                _YearPossibleAnswers = yearsPossibleAnswers;
            }

            return _YearPossibleAnswers;
        }
    }

    private static SelectorAnswersHelper<ValueAnswer<int>> YearAnswerHelper = new(YearPossibleAnswers);

    #endregion
}