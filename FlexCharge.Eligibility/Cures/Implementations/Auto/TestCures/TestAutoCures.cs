using System.Threading.Tasks;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.Entities;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.Cures.Implementations;

public class C0028_TestAutoCure : AutoCureBase
{
    protected override async Task<AutoCureResult> ExecuteCureAsync(EligibilityCheckContext context)
    {
        return AutoCureResult.RE_EVALUATE;
    }
}