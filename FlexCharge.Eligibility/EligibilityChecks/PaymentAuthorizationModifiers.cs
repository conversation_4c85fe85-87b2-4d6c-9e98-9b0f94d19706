using FlexCharge.Common.Shared.Payments;
using FlexCharge.Contracts.Commands.Common;

namespace FlexCharge.Eligibility.EligibilityChecks;

public class PaymentAuthorizationModifiers
{
    public bool PerformCascadingPayment { get; set; }
    public bool RedactCvv { get; set; }
    public bool RedactDevice { get; set; }
    public bool RedactIp { get; set; }
    public bool RedactAvs { get; set; }
    public bool Redact3ds { get; set; }
    public bool RedactSchemeTransactionId { get; set; }

    public bool UseBillingAsCardHolderIfMissing { get; set; }

    public int DynamicAmountDiscount { get; set; }

    public PaymentModifiersModel ToPaymentModifiersModel()
    {
        return new PaymentModifiersModel
        {
            RedactCvv = RedactCvv,
            RedactDevice = RedactDevice,
            RedactIp = RedactIp,
            RedactAvs = RedactAvs,
            Redact3ds = Redact3ds,
            RedactSchemeTransactionId = RedactSchemeTransactionId,
            UseBillingAsCardHolderIfMissing = UseBillingAsCardHolderIfMissing
        };
    }
}