using System;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Cures;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.EligibilityChecks.Workflow.Steps;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.EligibilityChecks;

public abstract class EligibilityCheckBase : BlockImplementationBase<EligibilityCheckContext>
{
    protected virtual bool IsProductionTestOrderBlock => false;

    protected Merchant Merchant => Context.Merchant;
    protected Site Site => Context.Site;
    protected Order Order => Context.Order;
    protected EvaluateRequest OrderPayload => Context.OrderPayload;
    protected DataChangeFlags ChangedData => Context.ChangedData;

    protected virtual bool CanProcessNotEligibleOffers => false;

    protected Guid Mid => Order.Mid;

    protected PostgreSQLDbContext DbContext { get; private set; }

    protected MerchantRiskProfiles MerchantRiskProfile =>
        Merchant.GetMerchantRiskProfile(OrderPayload.PaymentMethod);


    protected void ThrowIfOrderCannotBeEligible()
    {
        if (Order.State.ToOrderState().CannotBeEligible())
        {
            Log("Order cannot be eligible");
            throw new NotEligibleException();
        }
    }

    protected override async Task InitializeScopeAsync()
    {
        // Should be called at the start of the method
        await base.InitializeScopeAsync();

        DbContext = ServiceProvider.GetRequiredService<PostgreSQLDbContext>();
    }

    protected override async Task DisposeScopeAsync()
    {
        await DbContext.SaveChangesAsync(); //order can be changed in ExecuteStrategyAsync

        DbContext.Dispose();
        DbContext = null;

        // Should be called at the end of the method
        await base.DisposeScopeAsync();
    }


    public override void Initialize(EligibilityCheckContext context, IServiceScopeFactory serviceScopeFactory,
        ValueStorage valueStorage)
    {
        base.Initialize(context, serviceScopeFactory, valueStorage);

        ProcessDataChanges();
    }

    /// <summary>
    /// Override to reset stored eligibility check results, based on changed data
    /// <see cref="ChangedData"/>
    /// </summary>
    /// <remarks>Called for each <see cref="EligibilityCheckBase"/> during sequence Build phase</remarks>
    protected virtual void ProcessDataChanges()
    {
    }

    protected override void InitializeBeforeExecution()
    {
        base.InitializeBeforeExecution();

        DbContext.Attach(Order); //so we changes can be tracked and saved to database
    }

    protected override async Task ExecuteInternalAsync()
    {
        if (IsProductionTestOrderBlock)
        {
            if (!Order.IsTestOrder)
            {
                LogFatalError("Cannot execute production test order block on a non-test order.");

                throw new NotEligibleException("Order is not a test order.");
            }
        }

        if (Order.ExecutedCures == null)
        {
            await DbContext.Entry(Order).Collection(x => x.ExecutedCures).LoadAsync();
        }

        await ExecuteBlockAsync();
        Order.Payload = JsonConvert.SerializeObject(OrderPayload); //OrderPayload may be changed in Eligibility Check

        if (!CanProcessNotEligibleOffers)
        {
            ThrowIfOrderCannotBeEligible();
        }
    }


    #region Popups Support Code

    protected void AddPopup(string popupName, bool resetPopupSequence = true)
    {
        Order.AddPopupToPopupSequence(popupName, resetPopupSequence);
    }

    protected void ShowPopup(string popupName)
    {
        AddPopup(popupName, true);
    }

    #endregion

    protected async Task StopOrderRetriesAsync()
    {
        if (Order.IsMIT() != true) throw new FlexChargeException("Only MIT orders can be stopped from retries");

        throw new NotEligibleCancelledException();
    }

    protected void ResetStoredResult()
    {
        Workspan.Current?.Log.Information("Resetting stored result");
        Context.StoreEligibilityCheckResult(this, null);
    }

    protected void StoreResult<T>(T result)
    {
        var storedResult = JsonConvert.SerializeObject(result);
        Workspan.Current?.Log.Information("Storing result {StoredResult}", storedResult);

        Context.StoreEligibilityCheckResult(this, storedResult);
    }

    protected T? GetStoredResultOrDefault<T>()
    {
        using var workspan = Workspan.Start<EligibilityCheckBase>();

        var storedResult = Context.GetStoredEligibilityCheckResultOrDefault(this);

        if (storedResult == null)
            return default;

        try
        {
            return JsonConvert.DeserializeObject<T>(storedResult);
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "Cannot load stored result");
            return default;
        }
    }

    protected bool TryGetStoredResult<T>(out T result)
    {
        result = GetStoredResultOrDefault<T>()!;
        return result != null;
    }

    protected bool HasStoredResult => Context.GetStoredEligibilityCheckResultOrDefault(this) != null;

    #region Activity and Logging

    protected async Task AddDeclineActivityAsync(DeclineDescription declineDescription)
    {
        using var workspan = Workspan.Start<EligibilityCheckBase>()
            .Tag("Stage", declineDescription.Stage.ToString())
            .Tag("Category", declineDescription.Category.ToString())
            .Tag("RuleType", FormatRuleType(declineDescription.RuleType))
            .Tag("Rule", declineDescription.DeclineRule.ToString())
            .Tag("MIT", Order.IsMIT());

        workspan
            .Log.Information("DECLINE: {Message}", declineDescription.Message);

        await AddActivityAsync(EligibilityActivities.Eligibility_RequestDeclined, meta: meta => meta
            .SetValue("Stage", declineDescription.Stage.ToString())
            .SetValue("Group", declineDescription.Category.ToString())
            .SetValue("RuleType", FormatRuleType(declineDescription.RuleType))
            .SetValue("Rule", declineDescription.DeclineRule.ToString())
            .SetValue("Message", declineDescription.Message)
            .SetValue("MIT", Order.IsMIT()));

        string FormatRuleType(DeclineRuleType[] ruleTypes)
        {
            return string.Join("+", ruleTypes.Select(declineRuleType => declineRuleType.ToString()));
        }
    }

    #endregion
}