// using System;
// using FlexCharge.Eligibility.EligibilityChecks.Workflow.Steps;
// using FlexCharge.Eligibility.Workflows;
//
// namespace FlexCharge.Eligibility.EligibilityChecks.Implementations;
//
// public class IsCIT_Condition : PredefinedConditionIfBlockBase<EligibilityCheckContext>
// {
//     protected override bool PredefinedCondition()
//     {
//         return Context.Order.IsCIT;
//     }
// }

