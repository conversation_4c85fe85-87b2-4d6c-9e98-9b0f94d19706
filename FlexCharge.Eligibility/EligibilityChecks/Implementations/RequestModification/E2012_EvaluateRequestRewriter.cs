#if DEBUG
//#define TEST_REWRITER
#endif
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations.RequestModification;

public class E2012_EvaluateRequestRewriter : EligibilityCheckBase
{
    public override bool IsProductionBlock => true;

    static Func<object, bool> IsNull = (value) => value == null;
    static Func<string?, bool> IsNullOrEmpty = (string? value) => string.IsNullOrEmpty(value);
    static Func<string?, bool> IsNullOrWhiteSpace = (string? value) => string.IsNullOrWhiteSpace(value);

    static List<MerchantRewriteRule> _rewriteRules = new()
    {
        #region Test Code

#if DEBUG && TEST_REWRITER
        new
        (
            Guid.Parse("76e105a7-4d60-457b-82f2-648d46aba287"),
            new List<RewriteRuleBase>()
            {
                new RewriteRequestRule<BillingInformation>(
                    "Set default BillingInformation",
                    (request) => request.BillingInformation,
                    IsNull,
                    (request) => request.BillingInformation = new BillingInformation()),

                new RewriteRequestRule<string?>(
                    "Set default BillingInformation.Zipcode",
                    (request) => request.BillingInformation!.Zipcode,
                    IsNullOrWhiteSpace,
                    (request) => request.BillingInformation!.Zipcode = "76227"),

                new RewriteAuthorizationModifiersRule(
                    "Set UseBillingAsCardHolderIfMissing authorization modifier to True",
                    (order) => true,
                    (authorizationModifiers) => authorizationModifiers.UseBillingAsCardHolderIfMissing = true),
            }
        ),
#endif

        #endregion

        #region [Commented]Super.com Rewrite Rules

        // new
        // (
        //     MerchantSpecificOverrides.SUPER_COM_MERCHANT,
        //     new List<RewriteRuleBase>()
        //     {
        //         new RewriteRequestRule<BillingInformation>(
        //             "Set default BillingInformation",
        //             (request) => request.BillingInformation,
        //             IsNull,
        //             (request) => request.BillingInformation = new BillingInformation()),
        //
        //         new RewriteRequestRule<string?>(
        //             "Set default BillingInformation.Zipcode",
        //             (request) => request.BillingInformation!.Zipcode,
        //             IsNullOrWhiteSpace,
        //             (request) => request.BillingInformation!.Zipcode = "76227"),
        //
        //         new RewriteAuthorizationModifiersRule(
        //             "Set UseBillingAsCardHolderIfMissing authorization modifier to True",
        //             (order) => true,
        //             (authorizationModifiers) => authorizationModifiers.UseBillingAsCardHolderIfMissing = true),
        //     }
        // ),

        #endregion
    };

    static Dictionary<Guid, MerchantRewriteRule> _rewriteRulesByMerchant = _rewriteRules.ToDictionary(rule => rule.Mid);

    protected override async Task ExecuteBlockAsync()
    {
        using var workspan = Workspan.Start<E2012_EvaluateRequestRewriter>();

        if (_rewriteRulesByMerchant.TryGetValue(Mid, out var merchantRewriteRules))
        {
            Log($"Found {merchantRewriteRules.Rules.Count} rewrite rules for merchant");
            foreach (var rule in merchantRewriteRules.Rules)
            {
                try
                {
                    await rule.ApplyAsync(OrderPayload, Order, workspan, ActivityService);
                }
                catch (Exception e)
                {
                    workspan.RecordException(e);
                }
            }
        }
    }

    record MerchantRewriteRule(
        Guid Mid,
        List<RewriteRuleBase> Rules
    );

    abstract record RewriteRuleBase
    {
        public abstract Task ApplyAsync(EvaluateRequest request, Order order, Workspan workspan,
            IActivityService activityService);
    }

    record RewriteRequestRule<TField>(
        string RuleName,
        Func<EvaluateRequest, TField> Field,
        Func<TField, bool> Condition,
        Action<EvaluateRequest> Modifier
    ) : RewriteRuleBase
    {
        public override async Task ApplyAsync(EvaluateRequest request, Order order, Workspan workspan,
            IActivityService activityService)
        {
            var currentValue = Field(request);
            if (Condition(currentValue))
            {
                Modifier(request);

                var newValue = Field(request);

                workspan.Log
                    .Information("{RuleName}: Rewrote {Field} from {CurrentValue} to {NewValue}", RuleName,
                        typeof(TField).Name,
                        JsonSerializer.Serialize(currentValue), JsonSerializer.Serialize(newValue));

                await activityService.CreateActivityAsync(EligibilityActivities.EvaluateRequestRewriter_RewriteApplied,
                    new
                    {
                        Rule = RuleName,
                        Field = typeof(TField).Name,
                        OldValue = currentValue,
                        NewValue = newValue
                    }, set: set => set
                        .TenantId(order.Mid)
                        .CorrelationId(order.Id)
                        .Meta(meta => meta
                            .SetValue("Rule", RuleName)
                        )
                );
            }
        }
    };

    record RewriteAuthorizationModifiersRule(
        string RuleName,
        Func<Order, bool> Condition,
        Action<PaymentAuthorizationModifiers> Modifier
    ) : RewriteRuleBase
    {
        public override async Task ApplyAsync(EvaluateRequest request, Order order, Workspan workspan,
            IActivityService activityService)
        {
            if (Condition(order))
            {
                var paymentAuthorizationModifiers = order.GetPaymentAuthorizationModifiers();

                Modifier(paymentAuthorizationModifiers);

                order.SetPaymentAuthorizationModifiers(paymentAuthorizationModifiers);

                workspan.Log
                    .Information("{RuleName}: Authorization modifier applied", RuleName);

                await activityService.CreateActivityAsync(EligibilityActivities.EvaluateRequestRewriter_RewriteApplied,
                    new
                    {
                        Rule = RuleName,
                    }, set: set => set
                        .TenantId(order.Mid)
                        .CorrelationId(order.Id)
                        .Meta(meta => meta
                            .SetValue("Rule", RuleName)
                        )
                );
            }
        }
    };
}