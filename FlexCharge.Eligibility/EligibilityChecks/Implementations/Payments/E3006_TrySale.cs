using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using FlexCharge.Eligibility.Services.PaymentsService;
using FlexCharge.Eligibility.Services.ThreeDSService;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations;

public class E3006_TrySale : PaymentEligibilityCheckBase
{
    public override bool IsProductionBlock => true;

    protected override async Task ExecuteBlockAsync()
    {
        bool isReEvaluation = Context.IsReEvaluation;

        await TrySaleAsync(isReEvaluation);
    }

    private async Task TrySaleAsync(bool isReEvaluation)
    {
        using var workspan = Workspan.Start<E3006_TrySale>()
            .Tag(nameof(isReEvaluation), isReEvaluation);

        PaymentTransactionResult saleResult = null;

        Context.AuthorizationAttempts++;

        CreateAuthorizationAttemptedFingerprintActivity();

        // Skip payment processor's duplicate orders check
        bool skipDuplicateOrderCheck = true;

        var saleResponse = await SaleAsync(
            overrideDuplicateOrderCheckTimespan: skipDuplicateOrderCheck ? 0 : null);

        if (saleResponse?.Message != null)
        {
            Order.IsFullAuthorizationChecked = true;

            saleResult = new PaymentTransactionResult(saleResponse.Message);

            if (saleResponse.Message.GatewayFound)
            {
                Context.NextGateway = saleResponse.Message.NextGateway;
                Context.AlreadyUsedGateways.Add(saleResponse.Message.GatewayOrder);
            }
            else
            {
                await AddActivityAsync(EligibilityActivities.Sale_NoGatewayFound);

                throw new NotEligibleException();
            }
        }
        else
        {
            workspan.LogEligibility.Error($"Sale Error => Null is returned");
            await AddActivityAsync(EligibilityErrorActivities.Sale_NullResponseReturned);

            Order.IsFullAuthorizationChecked = false;

            throw new NotEligibleException();
        }

        if (!saleResponse.Message.Success)
        {
            workspan.LogEligibility.Information(
                $"Sale Failed => {saleResponse.Message}");
            await AddActivityAsync(EligibilityActivities.Sale_Failed,
                data: saleResponse.Message);
        }
        else
        {
            Order.IsPaymentCaptured = true;

            workspan.LogEligibility.Information(
                $"Sale Succeeded => {saleResponse.Message}");
            await AddActivityAsync(EligibilityActivities.Sale_Succeeded,
                data: saleResponse.Message);
        }
    }


    private async Task<Response<DebitPaymentCommandResponse>> SaleAsync(
        int? gatewayOrder = null,
        int? overrideDuplicateOrderCheckTimespan = null)
    {
        using var workspan = Workspan.Start<E3006_TrySale>();

        var paymentsService = ServiceProvider.GetRequiredService<IPaymentsService>();

        ThreeDSResult threeDSResult = null;
        // Is there any successful 3DS result?
        if (Order.SCAAuthenticationToken != null)
        {
            var threeDSecureTransaction = await DbContext.ThreeDSecureTransactions
                .OrderByDescending(x => x.CreatedOn).FirstOrDefaultAsync();

            if (threeDSecureTransaction?.ThreeDSResults != null)
            {
                threeDSResult = JsonConvert.DeserializeObject<ThreeDSResult>(threeDSecureTransaction?.ThreeDSResults);
            }
        }

        await VoidAuthorizationIfAnyAsync(Order, paymentsService);

        return await paymentsService.SaleAsync(Order, Merchant, Site, Context.PaymentInstrumentInformation,
            threeDSResult, gatewayOrder,
            overrideDuplicateOrderCheckTimespan,
            Order.GetPaymentAuthorizationModifiers());
    }
}