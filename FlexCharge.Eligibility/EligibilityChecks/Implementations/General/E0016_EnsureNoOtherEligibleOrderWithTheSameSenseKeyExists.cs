using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations.General;

public class E0016_EnsureNoOtherEligibleOrderWithTheSameSenseKeyExists : EligibilityCheckBase
{
    public override bool IsProductionBlock => true;

    protected override async Task<bool> CanBeExecutedAsync()
    {
        if (Order.IsMIT())
            return false;

        bool senseJsIsOptionalAndNoSenseKeyProvided = Merchant.IsSenseJSOptional(Order, OrderPayload) &&
                                                      string.IsNullOrWhiteSpace(Order.SenseKey);

        if (senseJsIsOptionalAndNoSenseKeyProvided)
            return false;

        return await base.CanBeExecutedAsync();
    }

    protected override async Task ExecuteBlockAsync()
    {
        await EnsureNoOtherActiveOrderWithTheSameSenseKeyExists(Order);
    }

    private async Task EnsureNoOtherActiveOrderWithTheSameSenseKeyExists(Order currentOrder)
    {
        #region Check if an order with the same SenseKey already exist

        var otherOrdersWithSameSenseKey = await DbContext.Orders.AsNoTracking().Where(x =>
            x.SenseKey == currentOrder.SenseKey &&
            x.Id != currentOrder.Id).ToListAsync();

        #endregion

        foreach (var order in otherOrdersWithSameSenseKey)
        {
            var orderState = order.State.ToOrderState();
            if (!order.CannotBeEligibleNow() || orderState.IsApproved() ||
                orderState == OrderState.NOT_ELIGIBLE_DUPLICATE_ORDER)
            {
                Log($"Duplicate active or approved Order with same SenseKey found");

                throw new NotEligibleDuplicateOrderException();
            }
        }
    }
}