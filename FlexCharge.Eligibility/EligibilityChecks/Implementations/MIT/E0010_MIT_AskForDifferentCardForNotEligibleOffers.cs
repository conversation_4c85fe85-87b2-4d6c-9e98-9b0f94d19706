using System.Threading.Tasks;
using FlexCharge.Eligibility.Cures;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Services.Orders.OrderStates;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations;

public class E0010_MIT_AskForDifferentCardForNotEligibleOffers : SetOfferStateCheckBase
{
    protected override bool CanProcessNotEligibleOffers => true;

    public override bool IsProductionBlock => true;

    protected override async Task<bool> CanBeExecutedAsync()
    {
        if (Order.IsMIT() &&
            Order.State.ToOrderState() == OrderState.NOT_ELIGIBLE &&
            !Order.IsExpiredMIT() &&
            Merchant.MITConsumerCuresEnabled)
        {
            return await base.CanBeExecutedAsync();
        }
        else return false;
    }

    protected override async Task ExecuteBlockAsync()
    {
        var isOpenBankingEnabled =
            await OpenBankingEnabler.IsOpenBankingEnabledForMerchantAsync(Merchant, ServiceScopeFactory);

        Order.CurrentCureId = CureBase.GetCureId(
            isOpenBankingEnabled
                ? nameof(Cures.Implementations.C1003_AskForNewCardOrInstantOpenBanking)
                : nameof(Cures.Implementations.C0003_AskForDifferentCard));

        await SetOrderStateAsync(StateOfOrder.ConditionalConsumerInteraction);
    }
}