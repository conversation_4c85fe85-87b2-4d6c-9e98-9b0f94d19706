using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Entities.Enums;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using FlexCharge.Eligibility.Migrations;
using FlexCharge.Eligibility.RiskManagement.Fingerprinting;
using FlexCharge.Eligibility.Services.RiskManagement;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Org.BouncyCastle.Crypto.Tls;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations.RiskManagement;

public class E2007_BlockByCardCountry : EligibilityCheckBase
{
    public override bool IsProductionBlock => true;

    protected override async Task<bool> CanBeExecutedAsync()
    {
        return await base.CanBeExecutedAsync();
    }

    protected override async Task ExecuteBlockAsync()
    {
        using var workspan = Workspan.Start<E2007_BlockByCardCountry>();

        string? cardCountryCode = OrderPayload.PaymentMethod?.CardCountry;

        if (!Merchant.IsCountrySupported(cardCountryCode))
        {
            await AddActivityAsync(EligibilityActivities.BinCheck_BlockByCountry_ElegibilityRequestDeclined,
                data: cardCountryCode);

            if (Order.IsMIT()) throw new NotEligibleCancelledException();
            throw new NotEligibleException();
        }
    }
}