{"containerDefinitions": [{"name": "core-eligibility", "image": "556663010871.dkr.ecr.us-east-1.amazonaws.com/fc-core-server-eligibility:a859edb84cc8f75c07290d82f1ef4dff3fefb065", "cpu": 0, "portMappings": [{"containerPort": 80, "hostPort": 80, "protocol": "tcp"}], "essential": true, "environment": [{"name": "DB_USERNAME", "value": "eligibility_service_prod"}, {"name": "DB_DATABASE", "value": "fc_eligibility"}, {"name": "DB_HOST", "value": "flexcharge-prod.ctwfhnhdjewu.us-east-1.rds.amazonaws.com"}, {"name": "DB_PORT", "value": "5432"}, {"name": "WORKFLOW_ENGINE_DB_USERNAME", "value": "workflowengine_service_prod"}, {"name": "WORKFLOW_ENGINE_DB_DATABASE", "value": "fc_workflowengine"}, {"name": "WORKFLOW_ENGINE_DB_HOST", "value": "flexcharge-prod.ctwfhnhdjewu.us-east-1.rds.amazonaws.com"}, {"name": "WORKFLOW_ENGINE_DB_PORT", "value": "5432"}, {"name": "POSTGRES_DB_SENSEJS_HOST", "value": "flexcharge-prod.ctwfhnhdjewu.us-east-1.rds.amazonaws.com"}, {"name": "POSTGRES_DB_SENSEJS_PORT", "value": "5432"}, {"name": "POSTGRES_USER_SENSEJS", "value": "sensejs_service_prod"}, {"name": "POSTGRES_DB_SENSEJS", "value": "fc_sensejs"}, {"name": "ASPNETCORE_ENVIRONMENT", "value": "Production"}, {"name": "SNS_IAM_REGION", "value": "us-east-1"}, {"name": "FRAUD_SEON_BASE_URL", "value": "https://api.seon.io/SeonRestService/fraud-api/v2"}, {"name": "BIN_SEON_BASE_URL", "value": "https://api.seon.io/SeonRestService/bin-api/v1"}, {"name": "EXPERIAN_PERMISSIBLE_PURPOSE", "value": "6H"}, {"name": "FRAUD_KOUNT_BASE_URL", "value": "https://risk.kount.net"}, {"name": "FRAUD_KOUNT_MERCHANT_ID", "value": "100488"}, {"name": "NEW_RELIC_APP_NAME", "value": "Eligibility-production"}, {"name": "OTEL_SERVICE_NAME", "value": "Eligibility-production"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PROD_DB_ELIGIBILITY_PASSWORD-OF7zfJ"}, {"name": "WORKFLOW_ENGINE_DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PROD_DB_WORKFLOW_PASSWORD-4hJHWZ"}, {"name": "POSTGRES_PASSWORD_SENSEJS", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PROD_SENSEJS_PASSWORD-ezMKpa"}, {"name": "SNS_IAM_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PROD_SNS_IAM_ACCESS_KEY-lbCS4b"}, {"name": "SNS_IAM_SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PROD_SNS_IAM_SECRET_KEY-a0ce7k"}, {"name": "FRAUD_SEON_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PROD_SEON_LICENSE_KEY-sPV5Su"}, {"name": "EXPERIAN_SUBSCRIBER_CODE", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:COMMON_EXPERIAN_SUBSCRIBER_CODE-lOSTuP"}, {"name": "EXPERIAN_USER_NAME", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PROD_EXPERIAN_USERNAME-3sNlmU"}, {"name": "EXPERIAN_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PROD_EXPERIAN_PASSWORD-oO6FEL"}, {"name": "EXPERIAN_CLIENT_ID", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PROD_EXPERIAN_CLIENT_ID-Lzolfb"}, {"name": "EXPERIAN_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PROD_EXPERIAN_CLIENT_SECRET-ZiNPZ4"}, {"name": "FRAUD_KOUNT_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PROD_FRAUD_KOUNT_API_KEY-IuUo4H"}, {"name": "API_CLIENT_JWT_SIGNING_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PRODUCTION-API-CLIENT-JWT-SIGNING-KEY-iGzRih"}, {"name": "STRIPE_APPS_WEBHOOK_SIGNING_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:prod/integrations/stripe-apps/webhook-signing-secret-NWzEDD"}, {"name": "STRIPE_APPS_STRIPE_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:prod/integrations/stripe-apps/stripe-api-key-pxlY0d"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/fc-core-eligibility-server-prod", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs", "awslogs-create-group": "true"}}}], "family": "fc-core-eligibility-server-prod", "taskRoleArn": "arn:aws:iam::556663010871:role/ecsTaskExecutionWithSecretAccess-Production-Role", "executionRoleArn": "arn:aws:iam::556663010871:role/ecsTaskExecutionWithSecretAccess-Production-Role", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "2048", "memory": "4096"}