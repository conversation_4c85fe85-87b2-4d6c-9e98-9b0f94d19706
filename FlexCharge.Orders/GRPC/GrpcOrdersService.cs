using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.DistributedLock;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using FlexCharge.Orders.DistributedLock;
using FlexCharge.Orders.DTO;
using FlexCharge.Orders.Entities;
using FlexCharge.Orders.Services;
using Grpc.Core;
using Newtonsoft.Json;

namespace FlexCharge.Orders.GRPC;

public class GrpcOrdersService : FlexCharge.Grpc.Orders.GrpcOrdersService.GrpcOrdersServiceBase
{
    private readonly IDistributedLockService _distributedLockService;
    private readonly IOrderService _orderService;

    public GrpcOrdersService(IDistributedLockService distributedLockService, IOrderService orderService)
    {
        _distributedLockService = distributedLockService;
        _orderService = orderService;
    }

    public override async Task<Grpc.Orders.SerializedResponse> CreateOrder(Grpc.Orders.SerializedRequest request,
        ServerCallContext context)
    {
        using var workspan = Workspan.Start<GrpcOrdersService>()
            .Request(request.Request)
            .LogEnterAndExit();

        try
        {
            var command = JsonConvert.DeserializeObject<CreateOrderCommand>(request.Request);

            workspan
                .Baggage("OrderId", command.OrderId)
                .Baggage("Mid", command.Mid)
                .Baggage("PayerId", command.PayerId)
                .LogEnterAndExit();

            await using var @lock = await _distributedLockService
                .AcquireLockAsync(LockKeyFactory.CreateOrderKey(command.OrderId),
                    TimeSpan.FromSeconds(15),
                    maxRetryDuration: TimeSpan.FromMinutes(1));


            string firstName, lastName;
            if (command.BillingInformation != null)
            {
                firstName = command.BillingInformation.FirstName;
                lastName = command.BillingInformation.LastName;
            }
            else
            {
                Utils.NameHelpers.TrySplitToFirstAndLastName(command.PaymentMethod.HolderName,
                    out firstName, out lastName);
            }

            var localMerchantEntity = await _orderService.GetMerchantAsync(command.Mid);

            var orderCreateDto = new OrderCreateDTO
            {
                Id = command.OrderId,
                PayerId = command.PayerId,
                IsMIT = command.IsMIT,
                IsGhostMode = command.IsGhostMode,
                IsTestOrder = command.IsTestOrder,
                MerchantId = command.Mid,
                MerchantName = localMerchantEntity?.Dba ?? command.Merchant?.Name,
                FirstName = firstName,
                LastName = lastName,
                Email = command.Payer?.Email,
                Phone = command.Payer?.Phone,
                CorrelationId = command.OrderId,
                Amount = command.Transaction.Amount,
                Currency = command.Transaction.Currency,
                PaymentInstrumentToken = command.PaymentMethod.CardNumber,
                ReferenceNumber = command.ExternalOrderId,
                StoreName = "null",
                StoreId = "",
                SiteId = command.SiteId,
                StatusCategory = OrderStatusCategory.draft,
                StatusSubCategory = "",
                StatusDescription = "",
                ExpiryDate = command.ExpiryDate,

                Bin = command.PaymentMethod.CardBinNumber,
                Last4 = command.PaymentMethod.CardLast4Digits,

                OrderItems = command.OrderItems?.Select(x => new OrderItemDTO
                {
                    Name = x.Name,
                    ReferenceId = x.Sku,
                    UnitPrice = x.Amount,
                    Discount = x.DiscountAmount,
                    Tax = x.Tax,
                    Quantity = x.Quantity,
                }).ToList(),
            };

            if (command.Meta != null)
            {
                orderCreateDto.Meta = new Dictionary<string, string>(command.Meta);
            }

            var billingInformation = command.BillingInformation;
            if (billingInformation != null)
            {
                orderCreateDto.BillingAddress = new AddressDTO
                {
                    Country = billingInformation.Country,
                    CountryCode = billingInformation.CountryCode,
                    Line1 = billingInformation.AddressLine1,
                    Line2 = billingInformation.AddressLine2,
                    City = billingInformation.City,
                    ZipCode = billingInformation.Zipcode,
                    State = billingInformation.State
                };
            }

            var shippingInformation = command.ShippingInformation;
            if (shippingInformation != null)
            {
                orderCreateDto.ShippingAddress = new AddressDTO()
                {
                    Country = shippingInformation.Country,
                    CountryCode = shippingInformation.CountryCode,
                    Line1 = shippingInformation.AddressLine1,
                    Line2 = shippingInformation.AddressLine2,
                    City = shippingInformation.City,
                    ZipCode = shippingInformation.Zipcode,
                    State = shippingInformation.State
                };
            }

            if (command.AdditionalFields != null)
            {
                orderCreateDto.AdditionalFields = command.AdditionalFields.ToDictionary(
                    x => x.Key, x => x.Value);
            }

            try
            {
                var subscription = command.Subscription;
                if (subscription != null)
                {
                    orderCreateDto.Subscription = JsonConvert.SerializeObject(command.Subscription);
                }
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "Error serializing subscription");
            }

            await _orderService.CreateAsync(orderCreateDto);

            var commandResponse = new CreateOrderCommandResponse
            {
                Succeeded = true
            };

            return new Grpc.Orders.SerializedResponse
            {
                Response = JsonConvert.SerializeObject(commandResponse)
            };
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }
}