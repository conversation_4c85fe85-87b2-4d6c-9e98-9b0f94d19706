using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Orders.Migrations
{
    /// <inheritdoc />
    public partial class OrderIndexRestore : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // migrationBuilder.CreateIndex(
            //     name: "IX_Orders_Email",
            //     table: "Orders",
            //     column: "Email");
            //
            // migrationBuilder.CreateIndex(
            //     name: "IX_Orders_FirstName",
            //     table: "Orders",
            //     column: "FirstName");
            //
            // migrationBuilder.CreateIndex(
            //     name: "IX_Orders_LastName",
            //     table: "Orders",
            //     column: "LastName");

            // migrationBuilder.CreateIndex(
            //     name: "IX_Orders_StatusCategory",
            //     table: "Orders",
            //     column: "StatusCategory");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // migrationBuilder.DropIndex(
            //     name: "IX_Orders_Email",
            //     table: "Orders");
            //
            // migrationBuilder.DropIndex(
            //     name: "IX_Orders_FirstName",
            //     table: "Orders");
            //
            // migrationBuilder.DropIndex(
            //     name: "IX_Orders_LastName",
            //     table: "Orders");
            //
            // migrationBuilder.DropIndex(
            //     name: "IX_Orders_StatusCategory",
            //     table: "Orders");
        }
    }
}
