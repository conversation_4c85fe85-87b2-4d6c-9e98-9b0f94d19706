using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Orders.Migrations
{
    /// <inheritdoc />
    public partial class removeonetimesubscriptionrelatedcolumns : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ExternalSubscriptionId",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "IsProcessingByExternalRecyclingEngine",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "RecyclingEngine",
                table: "Orders");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "ExternalSubscriptionId",
                table: "Orders",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsProcessingByExternalRecyclingEngine",
                table: "Orders",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "RecyclingEngine",
                table: "Orders",
                type: "text",
                nullable: true);
        }
    }
}
