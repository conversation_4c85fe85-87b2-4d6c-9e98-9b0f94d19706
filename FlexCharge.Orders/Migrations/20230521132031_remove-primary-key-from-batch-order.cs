using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Orders.Migrations
{
    /// <inheritdoc />
    public partial class removeprimarykeyfrombatchorder : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "<PERSON><PERSON>_BatchOrder",
                table: "BatchOrder");

            migrationBuilder.AddPrimaryKey(
                name: "PK_BatchOrder",
                table: "BatchOrder",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_BatchOrder_BatchId",
                table: "BatchOrder",
                column: "BatchId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "<PERSON><PERSON>_BatchOrder",
                table: "BatchOrder");

            migrationBuilder.DropIndex(
                name: "IX_BatchOrder_BatchId",
                table: "BatchOrder");

            migrationBuilder.AddPrimaryKey(
                name: "<PERSON><PERSON>_BatchOrder",
                table: "BatchOrder",
                columns: new[] { "BatchId", "OrderId" });
        }
    }
}
