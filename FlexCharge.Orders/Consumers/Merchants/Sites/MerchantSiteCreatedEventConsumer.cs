using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Orders.Entities;
using MassTransit;
using Microsoft.Extensions.Configuration;

namespace FlexCharge.Orders.Consumers.Merchants.Sites;

public class MerchantSiteCreatedEventConsumer : IConsumer<MerchantSiteCreatedEvent>
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IMapper _mapper;

    public MerchantSiteCreatedEventConsumer(PostgreSQLDbContext dbContext, IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;
    }


    public async Task Consume(ConsumeContext<MerchantSiteCreatedEvent> context)
    {
        using var workspan = Workspan.Start<MerchantSiteCreatedEventConsumer>()
            .Context(context);
            
        try
        {
            var message = context.Message;

            var site = _mapper.Map<Site>(message);
            site.Id = message.SiteId;
            await _dbContext.Sites.AddAsync(site);

            await _dbContext.SaveChangesAsync();
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
        }
    }
}

