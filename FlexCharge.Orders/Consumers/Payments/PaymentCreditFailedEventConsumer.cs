using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.DistributedLock;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Types;
using FlexCharge.Contracts;
using FlexCharge.Orders.Activities;
using FlexCharge.Orders.DistributedLock;
using FlexCharge.Orders.DTO;
using FlexCharge.Orders.Entities;
using FlexCharge.Orders.Services;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace FlexCharge.Orders.Consumers;

/// <summary>
/// Refund failed, but still should be can be saved on order as chargeback if it's pre-dispute refund
/// </summary>
public class PaymentCreditFailedEventConsumer : IdempotentEventConsumer<PaymentCreditFailedEvent>
{
    private readonly IOrderService _orderService;
    private readonly IActivityService _activityService;
    private readonly IDistributedLockService _distributedLockService;

    public PaymentCreditFailedEventConsumer(
        IOrderService orderService,
        IActivityService activityService,
        IServiceScopeFactory serviceScopeFactory,
        IDistributedLockService distributedLockService) : base(serviceScopeFactory)
    {
        _orderService = orderService;
        _activityService = activityService;
        _distributedLockService = distributedLockService;
    }

    protected override async Task ConsumeEvent(PaymentCreditFailedEvent message, CancellationToken cancellationToken)
    {
        Workspan
            .Context(Context)
            .Baggage("Mid", message.Mid)
            .Baggage("OrderId", message.OrderId)
            .LogEnterAndExit();

        try
        {
            await using var @lock = await _distributedLockService
                .AcquireLockAsync(LockKeyFactory.CreateOrderKey(message.OrderId),
                    TimeSpan.FromSeconds(15),
                    maxRetryDuration: TimeSpan.FromMinutes(1));

            var creditFailedEvent = message;

            // if (message.IsPreDisputeRefund.HasValue && message.IsPreDisputeRefund.Value)
            // {
            //     // For pre-dispute scenarios when refund is required (see ETHOCA) we need to still register a chargeback on the order
            //     // so we can get Chargeback fee from Merchant !!!
            //
            //     Workspan.Log.Information(
            //         $"OrdersService > PaymentCreditFailedEventConsumer > ProcessDisputeAsync: {JsonConvert.SerializeObject(Context)}");
            //
            //     var response = await _orderService.ApplyChargebackToOrderAsync(new OrderActivityDTO
            //     {
            //         OrderId = creditFailedEvent.OrderId,
            //         PaymentTransactionId = creditFailedEvent.TransactionId,
            //         Type = creditFailedEvent.TransactionType,
            //         Description = creditFailedEvent.Description,
            //         TotalAmount = creditFailedEvent.Amount,
            //         DiscountAmount = creditFailedEvent.DiscountAmount,
            //         PaymentDate = creditFailedEvent.PaymentDate,
            //         Bin = message.Bin,
            //         Last4 = message.Last4,
            //     });
            //
            //     await _orderService.ProcessDisputeAsync(message.Mid, message.OrderId,
            //         creditFailedEvent.Amount, message.Description, response.CreatedOn);
            // }

            await _activityService.CreateActivityAsync(PayInErrorActivities.PayIn_Payment_Credit_Error,
                set => set
                    .TenantId(creditFailedEvent.Mid)
                    .CorrelationId(creditFailedEvent.OrderId)
                    .Data(creditFailedEvent)
                    .Meta(meta => meta
                        .SetValue("Amount", creditFailedEvent.Amount)
                        .SetValue("DiscountAmount", creditFailedEvent.DiscountAmount)));

            await _orderService.UpdateOrderStateAsync(creditFailedEvent.Mid, creditFailedEvent.OrderId);
        }
        catch (Exception e)
        {
            Workspan.RecordException(e,
                $"EXCEPTION: OrdersService > PaymentCreditFailedEventConsumer");
        }
    }
}