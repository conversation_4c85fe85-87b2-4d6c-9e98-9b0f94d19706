using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.DistributedLock;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts;
using FlexCharge.Orders.Activities;
using FlexCharge.Orders.DistributedLock;
using FlexCharge.Orders.DTO;
using FlexCharge.Orders.Entities;
using FlexCharge.Orders.Services;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace FlexCharge.Orders.Consumers;

public class PaymentDebitedEventConsumer : IdempotentEventConsumer<PaymentDebitedEvent>
{
    private readonly IOrderService _orderService;
    private readonly IActivityService _activityService;
    private readonly IDistributedLockService _distributedLockService;

    public PaymentDebitedEventConsumer(IOrderService orderService, IActivityService activityService,
        IServiceScopeFactory serviceScopeFactory,
        IDistributedLockService distributedLockService) : base(serviceScopeFactory)
    {
        _orderService = orderService;
        _activityService = activityService;
        _distributedLockService = distributedLockService;
    }

    protected override async Task ConsumeEvent(PaymentDebitedEvent message, CancellationToken cancellationToken)
    {
        Workspan
            .Context(Context)
            .Baggage("Mid", message.Mid)
            .Baggage("OrderId", message.OrderId)
            .LogEnterAndExit();

        try
        {
            await using var @lock = await _distributedLockService
                .AcquireLockAsync(LockKeyFactory.CreateOrderKey(message.OrderId),
                    TimeSpan.FromSeconds(15),
                    maxRetryDuration: TimeSpan.FromMinutes(1));

            await _orderService.ApplyOrderActivityAsync(new OrderActivityDTO
            {
                OrderId = message.OrderId,
                PaymentTransactionId = message.TransactionId,
                Type = message.TransactionType,
                Description = message.Description,
                TotalAmount = message.Amount,
                DiscountAmount = message.DiscountAmount,
                PaymentDate = message.PaymentDate,
                Bin = message.Bin,
                Last4 = message.Last4,
            });

            await _activityService.CreateActivityAsync(PayInActivities.PayIn_Payment_Debit_Succeeded,
                set => set
                    .TenantId(message.Mid)
                    .CorrelationId(message.OrderId)
                    .Data(message)
                    .Meta(meta => meta
                        .SetValue("Amount", message.Amount)
                        .SetValue("DiscountAmount", message.DiscountAmount)));

            await _orderService.UpdateOrderStateAsync(message.Mid, message.OrderId);
        }
        catch (Exception e)
        {
            Workspan.RecordException(e,
                $"EXCEPTION: OrdersService > PaymentDebitedEventConsumer");
        }
    }
}