using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.DistributedLock;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts;
using FlexCharge.Orders.Activities;
using FlexCharge.Orders.DistributedLock;
using FlexCharge.Orders.DTO;
using FlexCharge.Orders.Entities;
using FlexCharge.Orders.Services;
using FlexCharge.Orders.Services.PayoutServices;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace FlexCharge.Orders.Consumers;

public class PaymentDisputeCreatedConsumer : IdempotentEventConsumer<PaymentDisputeCreatedEvent>
{
    private readonly IOrderService _orderService;
    private readonly IPayoutService _payoutService;
    private readonly IActivityService _activityService;
    private readonly IDistributedLockService _distributedLockService;

    public PaymentDisputeCreatedConsumer(IOrderService orderService, IActivityService activityService,
        IServiceScopeFactory serviceScopeFactory,
        IDistributedLockService distributedLockService, IPayoutService payoutService) : base(serviceScopeFactory)
    {
        _orderService = orderService;
        _activityService = activityService;
        _distributedLockService = distributedLockService;
        _payoutService = payoutService;
    }

    protected override async Task ConsumeEvent(PaymentDisputeCreatedEvent message,
        CancellationToken cancellationToken)
    {
        Workspan
            .Context(Context)
            .Baggage("Mid", message.Mid)
            .Baggage("OrderId", message.OrderId)
            .LogEnterAndExit();

        try
        {
            await using var @lock = await _distributedLockService
                .AcquireLockAsync(LockKeyFactory.CreateOrderKey(message.OrderId),
                    TimeSpan.FromSeconds(15),
                    maxRetryDuration: TimeSpan.FromMinutes(1));

            var response = await _orderService.ApplyChargebackToOrderAsync(new OrderActivityDTO
            {
                OrderId = message.OrderId,
                PaymentTransactionId = message.TransactionId,
                Type = message.Type,
                Description = message.Description + " Dispute ref: " + message.DisputeId,
                TotalAmount = message.Amount,
                DiscountAmount = message.DiscountAmount,
                PaymentDate = message.Date,
                Bin = message.Bin,
                Last4 = message.Last4,
            });

            //Need to move to payouts consumers 
            // if (response.Success)
            // {
            //     await _payoutService.AddOrderChargebackToPayoutAsync(message.Mid, message.OrderId, message.Description);
            // }
            // else
            //     throw new FlexChargeException($"Failed applying order activity ID:{message.OrderId} adding to payout skipped");
        }
        catch (Exception e)
        {
            Workspan.RecordException(e,
                "EXCEPTION: OrdersService > PaymentPreDisputeCreditedConsumer");

            await Context.Publish(new OrderNewDisputeActivityCreateFailedEvent
            {
                Mid = Context.Message.Mid,
                OrderId = Context.Message.OrderId,
                DisputeId = Context.Message.DisputeId,
                Error = e.Message
            }, cancellationToken);
        }
    }
}