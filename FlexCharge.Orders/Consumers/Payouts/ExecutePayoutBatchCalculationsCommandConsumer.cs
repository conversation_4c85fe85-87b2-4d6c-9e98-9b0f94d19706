using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Cache;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Commands;
using FlexCharge.Contracts.Common;
using FlexCharge.Contracts.Payouts;
using FlexCharge.Orders;
using FlexCharge.Orders.Contracts;
using FlexCharge.Orders.Entities;
using FlexCharge.Orders.Services.BatchServices.Batches;
using FlexCharge.Orders.Services.PayoutServices;
using FlexCharge.Utils;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace FlexCharge.Orders.Consumers;

public class
    ExecutePayoutBatchCalculationsCommandConsumer :
    IdempotentCommandConsumer<ExecutePayoutBatchCalculationsCommand>
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IBackgroundWorkerCommandQueue _backgroundWorkerCommandQueue;

    public ExecutePayoutBatchCalculationsCommandConsumer(
        PostgreSQLDbContext dbContext,
        IBackgroundWorkerCommandQueue backgroundWorkerCommandQueue,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _dbContext = dbContext;
        _backgroundWorkerCommandQueue = backgroundWorkerCommandQueue;
    }

    protected override async Task ConsumeCommand(ExecutePayoutBatchCalculationsCommand command,
        CancellationToken cancellationToken)
    {
        _backgroundWorkerCommandQueue.Enqueue(new CalculateFISCBatchesCommand());

        await Task.CompletedTask;
    }
}