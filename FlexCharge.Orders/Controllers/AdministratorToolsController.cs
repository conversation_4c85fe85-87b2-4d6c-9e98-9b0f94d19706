using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Threading.Tasks.Dataflow;
using AutoMapper;
using CsvHelper.Configuration;
using FlexCharge.Common;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.PostgreSql.Interceptors;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using FlexCharge.Orders.Activities;
using FlexCharge.Orders.Contracts;
using FlexCharge.Orders.DTO;
using FlexCharge.Orders.Entities;
using FlexCharge.Orders.Services.PayoutServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using FlexCharge.Orders.Entities.Extensions;
using FlexCharge.Orders.Services.AdministratorTools;
using FlexCharge.Orders.Services.PartnersServices.FeeCollectionServices;
using FlexCharge.Utils;
using MassTransit;
using Newtonsoft.Json;
using ActionResult = Microsoft.AspNetCore.Mvc.ActionResult;

namespace FlexCharge.Orders.Controllers;

[Route("[controller]")]
[ApiController]
[JwtAuth]
public class AdministratorToolsController : BaseController
{
    //private readonly AppOptions _globalData;
    private PostgreSQLDbContext _dbContext;
    private readonly IBatchService _batchService;
    private readonly IPayoutService _payoutService;
    private readonly IFeesCollectionService _feesCollectionService;
    private readonly IAdministratorToolsService _administratorToolsService;
    private readonly IPublishEndpoint _publisher;
    private readonly IRequestClient<PerformPartnerDebitCommand> _performPartnerDebitCommandRequestClient;

    private IActivityService _activityService { get; set; }


    public AdministratorToolsController(PostgreSQLDbContext dbContext, IBatchService batchService,
        IPayoutService payoutService, IAdministratorToolsService administratorToolsService,
        IActivityService activityService, IPublishEndpoint publisher,
        IRequestClient<PerformPartnerDebitCommand> performPartnerDebitCommandRequestClient,
        IFeesCollectionService feesCollectionService)
    {
        _dbContext = dbContext;
        _batchService = batchService;
        _payoutService = payoutService;
        _administratorToolsService = administratorToolsService;
        _activityService = activityService;
        _publisher = publisher;
        _performPartnerDebitCommandRequestClient = performPartnerDebitCommandRequestClient;
        _feesCollectionService = feesCollectionService;
    }

    internal class OrderGuidWrapper
    {
        public Guid orderId { get; set; }
    }

    [HttpGet("ResetPayoutBatch")]
    //[Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
    public async Task<IActionResult> ResetPayoutBatch(Guid pid, Guid mid, DateTime date,
        bool forceRecalculation = false)
    {
        using var workspan = Workspan.Start<AdministratorToolsController>().LogEnterAndExit();

        if (date.Kind != DateTimeKind.Utc)
            throw new ArgumentException("Date must be in UTC format", nameof(date));

        try
        {
            _administratorToolsService.CalculatePayoutBatches(pid, mid, date, forceRecalculation);
            return Ok();
        }
        catch (Exception e)
        {
            workspan.RecordException(e,
                $"EXCEPTION: Error");
            return BadRequest(e);
        }
    }

    [HttpGet("FixBatches")]
    [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
    //[AllowAnonymous]
    public async Task<IActionResult> FixBatches(CancellationToken token)
    {
        using var workspan = Workspan.Start<AdministratorToolsController>().LogEnterAndExit();

        #region get MasterAccount for fees collection

        var flexFeesCollectionAccount = await _dbContext.FinancialAccounts
            .FirstOrDefaultAsync(x =>
                    x.RelatedEntityType == FinancialAccountsRelatedEntityType.MasterAccount &&
                    x.AccountType == FinancialAccountsTypes.BankAccountAch,
                token);

        if (flexFeesCollectionAccount == null)
            throw new FlexChargeException("CalculateFeesAsync ERROR: Can't find flex financial account");

        #endregion

        try
        {
            var batches = await _dbContext
                .Batches
                .Include(x => x.Beneficiary)
                .ToListAsync(token);

            foreach (var batch in batches)
            {
                workspan.Log.Information("Processing batch {BatchId} type: {BatchType}", batch.Id, batch.BatchType);

                switch (batch.BatchType)
                {
                    case nameof(FIMovementType.FISC) when batch.SenderId == null && batch.ReceiverId == null:

                        //log merchant 
                        workspan.Log.Information("Processing merchant {MerchantId}", batch.Beneficiary.Mid);
                        workspan.Log.Information("Beneficiary Information {PartnerId}",
                            JsonConvert.SerializeObject(batch.Beneficiary));

                        if (batch.Beneficiary.Status == "Inactive")
                        {
                            workspan.Log.Fatal("Merchant {MerchantId} is inactive", batch.Beneficiary.Mid);
                            //continue;
                        }

                        if (batch.Beneficiary.Pid == Guid.Empty || batch.Beneficiary.Pid == null)
                        {
                            throw new FlexChargeException(
                                $"Partner ID not found for merchant {batch.Beneficiary.Dba}");
                        }

                        //get partnerFinancialAccount
                        var partnerFinancialAccount = await _dbContext.FinancialAccounts
                            .FirstOrDefaultAsync(
                                x => x.RelatedEntityId == batch.Beneficiary.Pid &&
                                     x.RelatedEntityType == FinancialAccountsRelatedEntityType.Partner, token);

                        workspan.Log.Information("Found partner financial account {PartnerFinancialAccountId}",
                            partnerFinancialAccount.Id);

                        batch.SenderId = partnerFinancialAccount.Id;

                        // get merchant financial account
                        var merchantFinancialAccount = await _dbContext.FinancialAccounts
                            .FirstOrDefaultAsync(
                                x => x.RelatedEntityId == batch.Beneficiary.Mid &&
                                     x.RelatedEntityType == FinancialAccountsRelatedEntityType.Merchant, token);


                        if (merchantFinancialAccount == null)
                        {
                            throw new FlexChargeException(
                                $"Merchant financial account not found for merchant {batch.Beneficiary.Mid}");
                        }

                        batch.ReceiverId = merchantFinancialAccount.Id;
                        _dbContext.Batches.Update(batch);

                        break;
                    case nameof(FIMovementType.FIPAD) when batch.ReceiverId == flexFeesCollectionAccount.Id &&
                                                           batch.SenderId == null:
                    {
                        //get partnerFinancialAccount
                        var pAccount = await _dbContext.FinancialAccounts
                            .FirstOrDefaultAsync(
                                x => x.RelatedEntityId == batch.Beneficiary.Pid &&
                                     x.RelatedEntityType == FinancialAccountsRelatedEntityType.Partner, token);

                        if (pAccount == null)
                        {
                            throw new FlexChargeException(
                                $"Partner financial account not found for partner {batch.Beneficiary.Pid}");
                        }

                        batch.SenderId = pAccount.Id;
                        batch.ReceiverId = flexFeesCollectionAccount.Id;

                        _dbContext.Batches.Update(batch);
                        break;
                    }
                }
            }

            await _dbContext.SaveChangesAsync(token);

            return Ok();
        }
        catch (Exception e)
        {
            workspan.RecordException(e,
                $"EXCEPTION: Error");
            return BadRequest(e);
        }
    }


    [HttpGet("CollectPartnerFees")]
    //[Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
    public async Task<IActionResult> CollectPartnerFees(Guid pid, DateTime start, DateTime end)
    {
        using var workspan = Workspan.Start<AdministratorToolsController>();

        if (end < start)
            throw new ArgumentException("End date must be greater than start date", nameof(end));

        if (start.Kind != DateTimeKind.Utc)
            throw new ArgumentException("Start date must be in UTC format", nameof(start));

        if (end.Kind != DateTimeKind.Utc)
            throw new ArgumentException("End date must be in UTC format", nameof(end));

        try
        {
            return Ok();
        }
        catch (Exception e)
        {
            workspan.RecordException(e,
                $"EXCEPTION: Error");
            return BadRequest(e);
        }
    }

    // [HttpGet("ResetPayoutBatch")]
    // [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
    // public async Task<IActionResult> ResetPayoutBatch(Guid mid, DateTime start, DateTime end)
    // {
    //     using var workspan = Workspan.Start<ActivitiesController>();
    //
    //     if (end < start)
    //         throw new ArgumentException("End date must be greater than start date", nameof(end));
    //
    //     if (start.Kind != DateTimeKind.Utc)
    //         throw new ArgumentException("Start date must be in UTC format", nameof(start));
    //
    //     if (end.Kind != DateTimeKind.Utc)
    //         throw new ArgumentException("End date must be in UTC format", nameof(end));
    //
    //     try
    //     {
    //         var merchants = await _dbContext.Merchants.ToListAsync();
    //
    //         if (mid != Guid.Empty)
    //             merchants = merchants.Where(x => x.Mid == mid).ToList();
    //
    //         foreach (var merchant in merchants)
    //         {
    //             var merchantFees =
    //                 await _dbContext.MerchantFees.Where(x => x.MerchantId == mid && x.IsActive).ToListAsync();
    //             //log merchant fees
    //             workspan.Log.Information("Merchant fees: {MerchantFees}", JsonConvert.SerializeObject(merchantFees));
    //
    //             var payoutFrequency = merchant.ToPayoutFrequencyType();
    //
    //             switch (payoutFrequency)
    //             {
    //                 case PayoutFrequencyType.DAILY:
    //
    //                     for (DateTime date = start; date <= end; date = date.AddDays(1))
    //                     {
    //                         var batchStartDate = payoutFrequency.GetCurrentBatchPayoutRangeStartDate(merchant, start);
    //                         var batchEndDate = payoutFrequency.GetCurrentBatchPayoutRangeEndDate(merchant, start);
    //
    //                         var batch = await _batchService.GetOrCreateBatch(merchant, date, date, true);
    //
    //                         var orders = await _dbContext
    //                             .Orders
    //                             .Include(x => x.ActivityItems)
    //                             .Where(x => x.MerchantId == mid && x.OrderPlacedDate >= batch.From &&
    //                                         x.OrderPlacedDate <= batch.To)
    //                             .ToListAsync();
    //
    //                         foreach (var order in orders)
    //                         {
    //                             batch.AddCompletedOrder(order, merchantFees);
    //                         }
    //                     }
    //
    //                     break;
    //                 case PayoutFrequencyType.WEEKLY:
    //                     break;
    //                 case PayoutFrequencyType.TWICEAMONTH:
    //                     break;
    //                 case PayoutFrequencyType.MONTHLY:
    //                     for (DateTime date = start; date <= end; date = date.AddMonths(1))
    //                     {
    //                         var batchStartDate = payoutFrequency.GetCurrentBatchPayoutRangeStartDate(merchant, date);
    //                         var batchEndDate = payoutFrequency.GetCurrentBatchPayoutRangeEndDate(merchant, date);
    //
    //                         var batch = await _batchService.GetOrCreateBatch(merchant, batchStartDate, batchEndDate,
    //                             includeBatchOrders: true,
    //                             overrideExistingBatch: true);
    //
    //                         await AddSettlementsToBatch(mid, batchStartDate, batchEndDate, workspan, batch,
    //                             merchantFees);
    //                         await AddDisputesToBatch(mid, batchStartDate, batchEndDate, workspan, batch, merchantFees);
    //                         await AddRefundsToBatch(mid, batchStartDate, batchEndDate, workspan, batch, merchantFees);
    //
    //                         //OLD CODE
    //                         //Get old batch orders
    //                         // var paidoutBatchRecords = await _dbContext.Batches
    //                         //     .Include(x => x.Beneficiary)
    //                         //     .Include(x => x.BatchRecords)
    //                         //     .Where(x => x.Beneficiary.Mid == mid && x.To.Date
    //                         //         < batchStartDate && x.PayoutStatus == nameof(BatchPayoutStatus.SUCCESS))
    //                         //     .ToListAsync();
    //                         //
    //                         // workspan.Log.Information(
    //                         //     "Found {PaidoutBatchRecordsCount} paidout batch records for batch {BatchId}",
    //                         //     paidoutBatchRecords?.Count, batch.Id);
    //                         //
    //                         // foreach (var order in orders)
    //                         // {
    //                         //     //log order 
    //                         //     workspan.Log.Information("Processing order {OrderId} status: {Status}", order.Id,
    //                         //         order.StatusCategory);
    //                         //
    //                         //     if (order.StatusCategory == OrderStatusCategory.Completed)
    //                         //     {
    //                         //         batch.AddCompletedOrder(order, merchantFees);
    //                         //     }
    //                         //
    //                         //     if (order.ActivityItems != null)
    //                         //     {
    //                         //         var returns = order.ActivityItems.Where(x =>
    //                         //             x.Type?.ToLower() == "refund" &&
    //                         //             x.CreatedOn.Date >= batchStartDate &&
    //                         //             x.CreatedOn.Date <= batchEndDate).ToList();
    //                         //
    //                         //         if (returns.Any())
    //                         //         {
    //                         //             foreach (var refund in returns)
    //                         //             {
    //                         //                 //log if return is already in any old batch
    //                         //                 workspan.Log.Information("Found {OrderId} in old batch {BatchId}", order.Id,
    //                         //                     refund.BatchId);
    //                         //
    //                         //                 //if return is already in any old batch, then skip
    //                         //                 if (paidoutBatchRecords.Any(x =>
    //                         //                         x.BatchRecords.Any(y => y.RelatedOrderId == order.Id) &&
    //                         //                         x.BatchRecords.Any(y => y.Type.ToLower() == "refund")))
    //                         //                     continue;
    //                         //
    //                         //                 batch.AddReturn(order, refund.CreatedOn, refund.TotalAmount,
    //                         //                     refund.Description,
    //                         //                     merchantFees);
    //                         //
    //                         //                 refund.BatchId = batch.Id;
    //                         //                 _dbContext.Transactions.Update(refund);
    //                         //             }
    //                         //         }
    //                         //
    //                         //         var chargebacks = order.ActivityItems.Where(x =>
    //                         //             x.Type?.ToLower() == "chargeback"
    //                         //             && x.CreatedOn.Date >= batchStartDate
    //                         //             && x.CreatedOn.Date <= batchEndDate).ToList();
    //                         //
    //                         //         if (chargebacks.Any())
    //                         //         {
    //                         //             foreach (var chargeback in chargebacks)
    //                         //             {
    //                         //                 //log if chargeback is already in any old batch
    //                         //                 workspan.Log.Information("Found {OrderId} in old batch {BatchId}", order.Id,
    //                         //                     chargeback.BatchId);
    //                         //
    //                         //                 if (paidoutBatchRecords.Any(x =>
    //                         //                         x.BatchRecords.Any(y => y.RelatedOrderId == order.Id) &&
    //                         //                         x.BatchRecords.Any(y => y.Type.ToLower() == "chargeback")))
    //                         //                     continue;
    //                         //
    //                         //                 batch.AddChargeback(order, chargeback.CreatedOn, chargeback.Description,
    //                         //                     merchantFees);
    //                         //
    //                         //                 chargeback.BatchId = batch.Id;
    //                         //                 _dbContext.Transactions.Update(chargeback);
    //                         //             }
    //                         //         }
    //                         //     }
    //                         // }
    //
    //                         await _dbContext.SaveChangesAsync();
    //                     }
    //
    //
    //                     break;
    //                 default:
    //                     throw new ArgumentOutOfRangeException();
    //             }
    //         }
    //
    //
    //         return Ok();
    //     }
    //     catch (Exception e)
    //     {
    //         workspan.RecordException(e,
    //             $"EXCEPTION: Error");
    //         return BadRequest(e);
    //     }
    // }

    // private async Task AddSettlementsToBatch(Guid mid, DateTime batchStartDate, DateTime batchEndDate,
    //     Workspan workspan,
    //     Batch batch, List<MerchantFee> merchantFees)
    // {
    //     var orders = await _dbContext
    //         .Orders
    //         //.Include(x => x.ActivityItems)
    //         .Where(x =>
    //             x.OrderPayoutStatus != OrderPayOutStatuses.Completed &&
    //             x.StatusCategory == nameof(OrderStatusCategory.completed) &&
    //             x.IsGhostMode == false &&
    //             x.MerchantId == mid &&
    //             x.OrderPlacedDate.Date >= batchStartDate && x.OrderPlacedDate.Date <= batchEndDate)
    //         .ToListAsync();
    //
    //     foreach (var order in orders)
    //     {
    //         //log order 
    //         workspan.Log.Information("Processing order {OrderId} status: {Status}", order.Id,
    //             order.StatusCategory);
    //
    //         batch.AddCompletedOrder(order, merchantFees);
    //     }
    //
    //     workspan.Log.Information("Found {OrdersCount} orders for batch {BatchId}", orders?.Count,
    //         batch.Id);
    // }
    //
    // private async Task AddDisputesToBatch(Guid mid, DateTime batchStartDate, DateTime batchEndDate, Workspan workspan,
    //     Batch batch, List<MerchantFee> merchantFees)
    // {
    //     var disputesToCollect = await _dbContext.Transactions.Include(x => x.Order)
    //         .Where(x =>
    //             x.Type == "chargeback" &&
    //             x.Order.MerchantId == mid &&
    //             x.Order.StatusCategory == nameof(OrderStatusCategory.completed) &&
    //             //x.BatchId == null &&
    //             x.CreatedOn.Date >= batchStartDate &&
    //             x.CreatedOn.Date <= batchEndDate).ToListAsync();
    //
    //     workspan.Log.Information("Found {DisputesToCollectCount} disputes for batch {BatchId}",
    //         disputesToCollect?.Count, batch.Id);
    //
    //     foreach (var dispute in disputesToCollect)
    //     {
    //         batch.AddChargeback(dispute.Order, dispute.CreatedOn, dispute.Description,
    //             merchantFees);
    //
    //         dispute.BatchId = batch.Id;
    //         _dbContext.Transactions.Update(dispute);
    //     }
    // }
    //
    // private async Task AddRefundsToBatch(Guid mid, DateTime batchStartDate, DateTime batchEndDate, Workspan workspan,
    //     Batch batch,
    //     List<MerchantFee> merchantFees)
    // {
    //     var refundsToCollect = await _dbContext.Transactions.Include(x => x.Order)
    //         .Where(x =>
    //             x.Type == "refund" &&
    //             x.Order.MerchantId == mid &&
    //             x.Order.StatusCategory == nameof(OrderStatusCategory.completed) &&
    //             //x.BatchId == null &&
    //             x.CreatedOn.Date >= batchStartDate &&
    //             x.CreatedOn.Date <= batchEndDate).ToListAsync();
    //
    //     workspan.Log.Information("Found {RefundsToCollectCount} refunds for batch {BatchId}", refundsToCollect?.Count,
    //         batch.Id);
    //
    //     foreach (var refund in refundsToCollect)
    //     {
    //         batch.AddReturn(refund.Order, refund.CreatedOn, refund.TotalAmount,
    //             refund.Description,
    //             merchantFees);
    //
    //         refund.BatchId = batch.Id;
    //         _dbContext.Transactions.Update(refund);
    //     }
    // }

    // [HttpGet("AuditOrder")]
    // [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
    // public async Task<IActionResult> ResetPayoutBatch(Guid orderId)
    // {
    //     using var workspan = Workspan.Start<ActivitiesController>();
    //
    //     try
    //     {
    //         
    //         
    //         return Ok();
    //     }
    //     catch (Exception e)
    //     {
    //         workspan.RecordException(e);
    //         return BadRequest(e);
    //     }
    // }

    [HttpPost("terminate-orders")]
    [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
    [ProducesResponseType(200)]
    public async Task<ActionResult> TerminateOrders(IFormFile file)
    {
        var workspan = Workspan.Start<AdministratorToolsController>()
            .LogEnterAndExit();

        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest("No file uploaded.");
            }

            var filePath = Path.Combine(Path.GetTempPath(), file.FileName);

            // Save the file to a temporary location or to a blob storage if the file is too large
            // Assuming large files are handled by storing them in a specific location like a blob storage or file server
            await using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);

                stream.Position = 0;

                var ordersIDs = Utils.CSVHelper.Parse<OrderGuidWrapper>(stream,
                    new CsvConfiguration(CultureInfo.InvariantCulture)
                    {
                        HasHeaderRecord = false
                    });

                var guidList = ordersIDs.Select(x => x.orderId).ToList();

                // var orderStatuses = await _dbContext.Orders
                //     .Where(x => guidList.Contains(x.Id))
                //     .GroupBy(x => x.StatusCategory)
                //     .Select(g => new
                //     {
                //         Status = g.Key.ToString(),
                //         Count = g.Count(),
                //         OldestOrderDate = g.Min(o => o.CreatedOn),
                //         NewestOrderDate = g.Max(o => o.CreatedOn)
                //     })
                //     .ToListAsync();
                //
                // //log order statuses
                // workspan.Log.Information("Order statuses: {OrderStatuses}", JsonConvert.SerializeObject(orderStatuses));


                _administratorToolsService.SilentlyTerminateMITOrders(guidList);


                return Ok( /*orderStatuses*/);
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
        }

        return Ok();
    }

    [HttpPost("cancel-mit-orders")]
    [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
    [ProducesResponseType(200)]
    public async Task<ActionResult> CancelCompletedMITOrders(IFormFile file)
    {
        var workspan = Workspan.Start<AdministratorToolsController>()
            .LogEnterAndExit();

        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest("No file uploaded.");
            }

            var filePath = Path.Combine(Path.GetTempPath(), file.FileName);

            // Save the file to a temporary location or to a blob storage if the file is too large
            // Assuming large files are handled by storing them in a specific location like a blob storage or file server
            await using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);

                stream.Position = 0;

                var ordersIDs = Utils.CSVHelper.Parse<OrderGuidWrapper>(stream,
                    new CsvConfiguration(CultureInfo.InvariantCulture)
                    {
                        HasHeaderRecord = false
                    });

                var guidList = ordersIDs.Select(x => x.orderId).ToList();

                foreach (var orderId in guidList)
                {
                    await _publisher.RunIdempotentCommandWithoutResponseAsync(
                        new StopMitOrderRetriesCommand(orderId, "Admin", Guid.Empty, "", GetMID(), true));
                }

                return Ok( /*orderStatuses*/);
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
        }

        return Ok();
    }


    [HttpGet("recalculate-partner-fees")]
    [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
    public async Task<IActionResult> RecalculatePartnerFees(Guid pid, Guid batchId)
    {
        using var workspan = Workspan.Start<AdministratorToolsController>();

        try
        {
            await _feesCollectionService.ReCalculateFeesAsync(pid, batchId, CancellationToken.None);
            return Ok();
        }
        catch (Exception e)
        {
            workspan.RecordException(e,
                $"EXCEPTION: Error");
            return BadRequest(e);
        }
    }

    [HttpPost("execute-partner-fee-debit-batch")]
    [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
    [ProducesResponseType(200)]
    public async Task<ActionResult> ExecutePartnerFeeDebitBatchPayout(Guid batchId)
    {
        var workspan = Workspan.Start<AdministratorToolsController>()
            .LogEnterAndExit();

        try
        {
            string responseMessage = "";

            await using (await _dbContext.Database.BeginTransactionAsync())
            {
                var batch = await _dbContext.Batches
                    .Include(x => x.Beneficiary)
                    .Include(x => x.Sender)
                    .Include(x => x.Receiver)
                    .Include(x => x.BatchRecords)
                    .TagWith(SelectForUpdateCommandInterceptor.SelectForUpdateBlockingTag)
                    .Where(x => x.Id == batchId)
                    .SingleOrDefaultAsync();

                if (batch == null)
                    return BadRequest("No batch found");

                if (batch.BatchType != nameof(FIMovementType.FIPAD))
                    throw new FlexChargeException($"Batch type is not supported: {batch.BatchType}");

                if (batch.Sender?.RelatedEntityType != nameof(FinancialAccountsRelatedEntityType.Partner))
                    throw new FlexChargeException("Sender is not a partner");

                if (batch.Receiver?.RelatedEntityType != nameof(FinancialAccountsRelatedEntityType.MasterAccount))
                    throw new FlexChargeException("Receiver is not a master account");

                if (batch.IsPosted != true)
                    throw new FlexChargeException("Batch is not posted");

                if (batch.PayoutStatus != BatchStatus.UNPROCESSED.ToString())
                    throw new FlexChargeException("Batch status is not unprocessed");

                bool payoutFailed = true;

                var transactionIdentificationNumber = UniqueIdsHelper.GeneratePseudoUniqueId(batch.Id);

                //Proceed with the ACH debit payment
                var performPayoutCommandResponse = await _performPartnerDebitCommandRequestClient
                    .RunCommandAsync<PerformPartnerDebitCommand, PerformPartnerDebitCommandResponse>(
                        new PerformPartnerDebitCommand
                        {
                            CompanyEntryDescription = "FLX ACH", //???
                            FundSenderPid = batch.Sender.RelatedEntityId,
                            Amount = batch.CalculatePayoutAmount(),
                            Currency = "USD",
                            TransactionIdentificationString = transactionIdentificationNumber,
                        }
                    );

                if (performPayoutCommandResponse.Message.Success)
                {
                    batch.RelatedTransaction = performPayoutCommandResponse.Message.TransactionId;
                    // Success status must be set after ACH debit payment is verified
                    payoutFailed = false;
                }
                else
                {
                    workspan.RecordError(
                        "PAYOUTS: Can't process fee debit payment for batchID: {BatchId} PID: {Pid} ERROR: {Error}",
                        batch.Id, batch.Sender.RelatedEntityId,
                        performPayoutCommandResponse.Message.ResponseMessage);
                }

                if (payoutFailed)
                {
                    batch.PayoutStatus = nameof(BatchStatus.FAILED);
                }
                else
                {
                    batch.PayoutStatus = nameof(BatchStatus.PROCESSING);
                }

                await _dbContext.SaveChangesAsync();
                await _dbContext.Database.CommitTransactionAsync();

                responseMessage = $"ACH TRANSFER RESULT: {performPayoutCommandResponse.Message.ResponseMessage}";
            }

            return Ok(responseMessage);
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            return base.BadRequest(e.Message);
        }
    }
}