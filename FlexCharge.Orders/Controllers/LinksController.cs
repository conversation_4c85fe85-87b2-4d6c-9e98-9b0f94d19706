using FlexCharge.Common;
using FlexCharge.Orders.DTO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Authentication.BasicAuthentication;
using FlexCharge.Common.HTTP;
using FlexCharge.Common.Recaptcha;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Controllers;
using FlexCharge.Orders.Activities;
using FlexCharge.Orders.Services.Links;
using MassTransit;

namespace FlexCharge.Orders.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class LinksController : BaseController
    {
        private readonly AppOptions _globalData;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IPublishEndpoint _publisher;
        private readonly ILinksService _linksService;
        private readonly IActivityService _activityService;
        private readonly IRecaptchaService _recaptchaService;

        public LinksController(
            IOptions<AppOptions> globalData,
            IHttpContextAccessor httpContextAccessor,
            IPublishEndpoint publisher,
            ILinksService linksService,
            IActivityService activityService,
            IRecaptchaService recaptchaService
        )
        {
            _globalData = globalData.Value;
            _httpContextAccessor = httpContextAccessor;
            _publisher = publisher;
            _linksService = linksService;
            _activityService = activityService;
            _recaptchaService = recaptchaService;
        }

        [HttpPost("next")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(NextPostResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> NextPost(NextPostRequest request, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<LinksController>(this, request, _globalData)
                .Baggage("LinkId", request?.Id);

            try
            {
                if (!await CheckModelStateAsync())
                    return ValidationProblem();

                var ipAddress = HttpContextHelper.GetConsumerIP(HttpContext);
                var userAgent = HttpContext.Request.Headers["User-Agent"].ToString();


                workspan.Log.Information("Looking for consumer link: {LinkId}; ipAddress: {ipAddress}", request?.Id,
                    ipAddress);


                var response = await _linksService.ProcessNextPostRequestAsync(request, userAgent, ipAddress);

                workspan
                    .Response(response);

                return ReturnResponse(response);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set.CorrelationId(request?.Id));

                return BadRequest();
            }
        }

        [HttpPut("next")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(NextPutResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> NextPut(NextPutRequest request,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<LinksController>(this, request, _globalData)
                .Baggage("LinkId", request?.Id);

            try
            {
                if (!await CheckModelStateAsync())
                    return ValidationProblem();

                var captchaResult = await _recaptchaService.CheckRecaptchaAsync(request.RecaptchaToken, null);

                if (!captchaResult)
                {
                    workspan.Log.Warning("Recaptcha failed");
                    ModelState.AddModelError("Captcha", "Captcha is not valid");
                    return ValidationProblem();
                }

                var response = await _linksService.ProcessNextPutRequestAsync(request);

                workspan.Response(response);

                return ReturnResponse(response);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set.CorrelationId(request?.Id));

                return BadRequest();
            }
        }

        #region Common

        private async Task<bool> CheckModelStateAsync(Guid? mid = null, Guid? orderId = null)
        {
            if (!ModelState.IsValid)
            {
                await _activityService.CreateActivityAsync(ApiErrorActivities.ModelStateError, ModelState,
                    set => set.TenantId(mid).CorrelationId(orderId));

                Workspan.Current?.RecordEndpointBadRequest(ModelState);

                return false;
            }

            return true;
        }

        #endregion

        #region GetIp

        #endregion
    }
}