using System;
using System.Collections.Generic;
using System.Linq;
using FlexCharge.Common.Activities.Attributes;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Shared.Activities;
using FlexCharge.Contracts.Activities;
using FlexCharge.Orders.Entities;
using FlexCharge.Utils;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using ActionResult = Microsoft.AspNetCore.Mvc.ActionResult;

namespace FlexCharge.Orders.Controllers;

[Route("[controller]")]
[ApiController]
[JwtAuth]
public class ListsController : ControllerBase
{
    public ListsController()
    {
    }


    [HttpGet]
    [ProducesResponseType(200)]
    public ActionResult Get()
    {
        var orderStatuses = new Dictionary<int, string>();

        foreach (OrderStatusCategory status in Enum.GetValues(typeof(OrderStatusCategory)))
        {
            var statusName = status.ToString();
            orderStatuses.Add((int) status, char.ToUpper(statusName[0]) + statusName.Substring(1));
        }

        return Ok(new
        {
            OrderStatuses = orderStatuses
        });
    }
}