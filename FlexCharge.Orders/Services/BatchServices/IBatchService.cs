using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Orders.DTO;
using FlexCharge.Orders.DTO.Batches;
using FlexCharge.Orders.Entities;

namespace FlexCharge.Orders.Services.PayoutServices;

public interface IBatchService
{
    //Task<List<SettlementDTO>> GetSettlementsAsync(Guid senderId, Guid receiverId, bool? isLatest);
    Task<List<SettlementDTO>> AdminGetSettlementsAsync(Guid senderId, Guid receiverId, Guid? partnerId,
        string? batchType, bool? isLatest);

    Task<List<SettlementDTO>> PartnerGetSettlementsAsync(Guid senderId, Guid receiverId, string? batchType,
        bool? isLatest);

    Task<List<SettlementDTO>> MerchantGetSettlementsAsync(Guid receiverId, string? batchType, bool? isLatest);

    // Task<List<SettlementDTO>> AdminGetLatestSettlementsAsync(Guid senderId, Guid receiverId);
    // Task<List<SettlementDTO>> PartnerGetLatestSettlementsAsync(Guid senderId, Guid receiverId);
    // Task<List<SettlementDTO>> MerchantGetLatestSettlementsAsync(Guid receiverId);

    Task<Batch> GetBatchByIdAsync(Guid id);
    Task<SettlementDTO> GetByIdAsync(Guid id);

    /// <summary>
    /// Approved by human batches must be marked as posted (IsPosted=true) to be processed by <see cref="IPayoutService.ExecuteBatchProcessingAsync"/>
    /// </summary>
    /// <returns></returns>
    Task PostBatch(Guid batchId);

    Task UnPostBatch(Guid? pid, Guid batchId);

    Task<Batch> GetOrCreateBatch(Merchant merchant, string batchType, DateTime batchStartDate, DateTime batchEndDate,
        bool includeBatchOrders);

    Task<Batch> GetOrCreateBatch(Merchant merchant, string batchType, DateTime batchStartDate, DateTime batchEndDate,
        bool includeBatchOrders, bool overrideExistingBatch);

    Task<Batch> GetOrCreateBatchV2(FinancialAccount sender, FinancialAccount receiver, string batchType,
        DateTime batchStartDate, DateTime batchEndDate, bool? isOffline, bool includeRecords,
        bool overrideExistingBatch, bool autoPosted = false);

    Task<List<SettlementDTO>> GetProcessedAsync(Guid senderId, Guid receiverId, Guid? partnerId, bool? isLatest,
        string? batchType);


    Task<Batch> GetCurrentBatch(Merchant merchant);

    Task<List<BatchDTO>> GetUnPostedAsync(Guid senderId, Guid receiverId, Guid? partnerId, string? batchType,
        DateTime? from, DateTime? to);

    Task<List<BatchDTO>> GetPendingAsync(Guid senderId, Guid receiverId, Guid? partnerId, string? batchType,
        DateTime? from, DateTime? to);

    Task<List<BatchDTO>> GetShelvedAsync(Guid senderId, Guid receiverId, Guid? partnerId, string? batchType,
        DateTime? from, DateTime? to);

    Task ShelveAsync(Guid batchId);
    Task UnshelveAsync(Guid batchId);
}