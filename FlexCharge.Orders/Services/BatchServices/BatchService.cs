using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Telemetry;
using FlexCharge.Orders.DTO;
using FlexCharge.Orders.DTO.Batches;
using FlexCharge.Orders.Entities;
using Microsoft.EntityFrameworkCore;
using FlexCharge.Orders.Entities.Extensions;
using FlexCharge.Utils;

namespace FlexCharge.Orders.Services.PayoutServices;

public class BatchService : IBatchService
{
    private PostgreSQLDbContext _dbContext;
    private readonly ReadOnlyPostgreSQLDbContext _readOnlyDbContext;

    public BatchService(PostgreSQLDbContext context, ReadOnlyPostgreSQLDbContext readOnlyDbContext)
    {
        _dbContext = context;
        _readOnlyDbContext = readOnlyDbContext;
    }

    public async Task<List<SettlementDTO>> AdminGetSettlementsAsync(Guid senderId, Guid receiverId, Guid? partnerId,
        string? batchType,
        bool? isLatest = null)
    {
        return await GetProcessedAsync(senderId, receiverId, partnerId, isLatest, batchType);
    }

    public async Task<List<SettlementDTO>> PartnerGetSettlementsAsync(Guid senderId, Guid receiverId, string? batchType,
        bool? isLatest = null)
    {
        if (senderId == Guid.Empty)
            throw new FlexChargeException("Invalid sender id");

        return await GetProcessedAsync(senderId, receiverId, null, isLatest, batchType);
    }

    public async Task<List<SettlementDTO>> MerchantGetSettlementsAsync(Guid receiverId, string? batchType,
        bool? isLatest = null)
    {
        if (receiverId == Guid.Empty)
            throw new FlexChargeException("Invalid receiver id");

        return await GetProcessedAsync(Guid.Empty, receiverId, null, isLatest, batchType);
    }

    public async Task<List<BatchDTO>> AdminGetUnPostedAsync(Guid receiverId, Guid? partnerId, string? batchType,
        DateTime? from, DateTime? to)
    {
        return await GetUnPostedAsync(Guid.Empty, receiverId, partnerId, batchType, from, to);
    }

    public async Task<List<SettlementDTO>> GetCompletedAsync(Guid senderId)
    {
        using var workspan = Workspan.Start<BatchService>()
            .Baggage(nameof(senderId), senderId);

        try
        {
            var query = _readOnlyDbContext.Batches
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .Where(x => x.PayoutStatus == nameof(BatchStatus.SUCCESS));

            if (senderId != Guid.Empty)
                query = query.Where(x => x.SenderId == senderId);

            return await query
                .Select(x =>
                    new SettlementDTO
                    {
                        PaymentRef = x.Id,
                        BatchType = x.BatchType,
                        PayoutDate = x.To.AddDays(1),
                        CapturePeriodFrom = x.From,
                        CapturePeriodTo = x.To,
                        Receiver = ToFinancialAccountDTO(x.Receiver),
                        Sender = ToFinancialAccountDTO(x.Sender),
                        Mid = x.Receiver.RelatedEntityId,
                        TotalValue = Formatters.IntToDecimal(x.TotalAmount),
                        FlexChargeFees = x.FlexChargeFeesAmount.ToSignedDecimal(MathHelpers.MathSign.Negative),
                        Returns = x.ReturnsAmount.ToSignedDecimal(MathHelpers.MathSign.Negative),
                        Chargebacks = x.ChargebacksAmount.ToSignedDecimal(MathHelpers.MathSign.Negative),
                        PayoutAmount = Formatters.IntToDecimal(x.CalculatePayoutAmount()),
                        CurrencyCode = x.CurrencyCode,
                        CurrencySymbol = x.CurrencySymbol,
                        Currency = x.Currency,
                        ReserveHold = x.Reserve.ToSignedDecimal(MathHelpers.MathSign.Negative),
                        ReserveRelease = x.ReserveRelease.ToSignedDecimal(MathHelpers.MathSign.Positive),
                        ReserveUtilization = x.ReserveUtilization.ToSignedDecimal(MathHelpers.MathSign.Positive),
                        Adjustment = Formatters.IntToDecimal(x.Adjustment),
                        FlexChargeChargebackFeesAmount = Formatters.IntToDecimal(x.FlexChargeChargebackFeesAmount)
                    }).ToListAsync();
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task<Batch> GetBatchByIdAsync(Guid id)
    {
        using var workspan = Workspan.Start<BatchService>()
            .LogEnterAndExit();

        try
        {
            var batch = await _readOnlyDbContext.Batches
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .Include(x => x.BatchRecords)
                .SingleOrDefaultAsync(x => x.Id == id);

            if (batch == null)
            {
                workspan.Log.Error("batch not found: {id}", id);
                throw new FlexChargeException("batch not found");
            }

            return batch;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }


    public async Task<SettlementDTO> GetByIdAsync(Guid id)
    {
        using var workspan = Workspan.Start<BatchService>().LogEnterAndExit();

        try
        {
            var batch = await _readOnlyDbContext.Batches
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .SingleOrDefaultAsync(x => x.Id == id);

            if (batch == null)
            {
                workspan.Log.Error("Settlement not found: {id}", id);
                throw new FlexNotFoundException("Not found", "Settlement not found");
            }

            return new SettlementDTO
            {
                PaymentRef = batch.Id,
                BatchType = batch.BatchType,
                PayoutDate = batch.To.AddDays(1),
                CapturePeriodFrom = batch.From,
                CapturePeriodTo = batch.To,
                Receiver = ToFinancialAccountDTO(batch.Receiver),
                Sender = ToFinancialAccountDTO(batch.Sender),

                TotalValue = Formatters.IntToDecimal(batch.TotalAmount),
                FlexChargeFees = batch.CalculateFlexProcessingFeesAmount()
                    .ToSignedDecimal(MathHelpers.MathSign.Negative),
                Returns = batch.ReturnsAmount.ToSignedDecimal(MathHelpers.MathSign.Negative),
                Chargebacks = batch.ChargebacksAmount.ToSignedDecimal(MathHelpers.MathSign.Negative),
                ReserveHold = batch.Reserve.ToSignedDecimal(MathHelpers.MathSign.Negative),
                ReserveRelease = batch.ReserveRelease.ToSignedDecimal(MathHelpers.MathSign.Positive),
                ReserveUtilization = batch.ReserveUtilization.ToSignedDecimal(MathHelpers.MathSign.Positive),
                Adjustment = Formatters.IntToDecimal(batch.Adjustment),
                FlexChargeChargebackFeesAmount =
                    batch.FlexChargeChargebackFeesAmount.ToSignedDecimal(MathHelpers.MathSign.Negative),
                PayoutAmount = Formatters.IntToDecimal(batch.CalculatePayoutAmount()),

                Currency = batch.Currency,
                CurrencyCode = batch.CurrencyCode,
                CurrencySymbol = batch.CurrencySymbol,
            };
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task PostBatch(Guid batchId)
    {
        using var workspan = Workspan.Start<BatchService>().LogEnterAndExit();

        await using (await _dbContext.Database.BeginTransactionAsync())
        {
            var batchToPostForPayout =
                await _dbContext.Batches
                    .Include(x => x.Sender)
                    .Include(x => x.Receiver)
                    .SingleOrDefaultAsync(x =>
                        x.Id == batchId && !x.IsPosted &&
                        x.PayoutStatus == nameof(BatchStatus.UNPROCESSED));

            if (batchToPostForPayout is null)
            {
                workspan.Log.Error("Batch not found: {BatchId}", batchId);
                throw new FlexValidationException("Batch not found");
            }

            //TODO: Need to move to a higher level service like PayoutService and than check for merchant status and payouts enabled 
            if (batchToPostForPayout.Receiver.RelatedEntityType == FinancialAccountsRelatedEntityType.Merchant)
            {
                var merchant =
                    await _dbContext.Merchants.SingleOrDefaultAsync(x =>
                        x.Mid == batchToPostForPayout.Receiver.RelatedEntityId);

                if (merchant is null)
                {
                    var message = $"Merchant not found: {batchToPostForPayout.Receiver.RelatedEntityId}";
                    workspan.Log.Error(message);
                    throw new FlexValidationException(message);
                }

                if (merchant.Locked)
                {
                    var message = $"Can't post batch {batchId}. Merchant {merchant.Mid} is locked";
                    workspan.Log.Information(message);
                    throw new FlexValidationException(message);
                }

                if (!merchant.PayoutsEnabled)
                {
                    var message = $"Can't post batch {batchId}. Merchant {merchant.Mid}  payouts disabled";

                    workspan.Log.Information(message);
                    throw new FlexValidationException(message);
                }
            }

            if (batchToPostForPayout.CalculatePayoutAmount() < 0)
            {
                var message = $"Can't post batch {batchId}. Payout amount is negative";
                workspan.Log.Information(message);
                throw new FlexValidationException(message);
            }

            batchToPostForPayout.IsPosted = true;

            _dbContext.Batches.Update(batchToPostForPayout);
            await _dbContext.SaveChangesAsync();


            await _dbContext.Database.CommitTransactionAsync();
        }
    }

    public async Task UnPostBatch(Guid? pid, Guid batchId)
    {
        using var workspan = Workspan.Start<BatchService>().LogEnterAndExit();

        await using (await _dbContext.Database.BeginTransactionAsync())
        {
            var batchToPostForPayout =
                await _dbContext.Batches
                    .SingleOrDefaultAsync(x =>
                        x.Id == batchId && x.IsPosted && x.PayoutStatus == nameof(BatchStatus.UNPROCESSED));

            if (batchToPostForPayout is null)
            {
                workspan.Log.Error("Batch not found: {batchId}", batchId);
                throw new FlexValidationException(-1, "Batch is already related to transaction");
            }
            else
            {
                if (batchToPostForPayout.RelatedTransaction != null)
                {
                    workspan.Log.Error(
                        "Can't unpost batch {BatchId}. Batch is already related to transaction {TransactionId}",
                        batchId, batchToPostForPayout.RelatedTransaction);

                    throw new FlexValidationException("Batch is already related to transaction");
                }

                if (batchToPostForPayout.BatchType == FIMovementType.FIPAD.ToString())
                {
                    workspan.Log.Error("Can't unpost batch {BatchId}. Batch is related to Partner Debit", batchId);
                    throw new FlexValidationException("Can't unpost a Partner Debit batch");
                }

                batchToPostForPayout.IsPosted = false;

                _dbContext.Batches.Update(batchToPostForPayout);
                await _dbContext.SaveChangesAsync();
            }

            await _dbContext.Database.CommitTransactionAsync();
        }
    }

    private async Task<List<BatchDTO>> GetAsync(
        Guid senderId,
        Guid receiverId,
        Guid? partnerId,
        BatchStatus? status,
        bool? isPosted,
        bool? isShelved,
        string? batchType,
        DateTime? from,
        DateTime? to)
    {
        using var workspan = Workspan.Start<BatchService>()
            .Baggage(nameof(receiverId), receiverId);

        try
        {
            var dbSet = _readOnlyDbContext.Batches
                .Include(x => x.Beneficiary)
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .OrderByDescending(x => x.CreatedOn)
                .Where(x => x.IsDeleted == false);

            if (senderId != Guid.Empty)
                dbSet = dbSet.Where(x => x.SenderId == senderId);

            if (receiverId != Guid.Empty)
                dbSet = dbSet.Where(x => x.ReceiverId == receiverId);

            if (partnerId.HasValue)
                dbSet = dbSet.Where(x => x.Sender.RelatedEntityId == partnerId);

            if (status.HasValue)
                dbSet = dbSet.Where(x => x.PayoutStatus == status.ToString());

            if (isPosted.HasValue)
                dbSet = dbSet.Where(x => x.IsPosted == isPosted);

            if (isShelved.HasValue && isShelved.Value)
                dbSet = dbSet.Where(x => x.IsShelved != null);
            else
                dbSet = dbSet.Where(x => x.IsShelved == null);

            if (from.HasValue && to.HasValue)
            {
                dbSet = dbSet.Where(x => x.From >= from.Value.ToUniversalTime());
                dbSet = dbSet.Where(x => x.To <= to.Value.ToUniversalTime());
            }

            if (!string.IsNullOrEmpty(batchType))
            {
                dbSet = dbSet.Where(x => x.BatchType == batchType);
            }

            dbSet = dbSet.OrderByDescending(x => x.To);

            return await dbSet.Select(x => ToBatchDto(x)).ToListAsync();
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task<List<BatchDTO>> GetUnPostedAsync(Guid senderId, Guid receiverId, Guid? partnerId,
        string? batchType, DateTime? from, DateTime? to)
    {
        return await GetAsync(senderId, receiverId, partnerId, null, false, false, batchType, from, to);
    }

    public async Task<List<BatchDTO>> GetPendingAsync(Guid senderId, Guid receiverId, Guid? partnerId,
        string? batchType, DateTime? from, DateTime? to)
    {
        using var workspan = Workspan.Start<BatchService>()
            .Baggage(nameof(receiverId), receiverId);

        try
        {
            var dbSet = _readOnlyDbContext.Batches
                .Include(x => x.Beneficiary)
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .Where(x =>
                    x.IsShelved == null &&
                    x.IsPosted &&
                    x.PayoutStatus != nameof(BatchStatus.SUCCESS));

            if (senderId != Guid.Empty)
                dbSet = dbSet.Where(x => x.SenderId == senderId);

            if (receiverId != Guid.Empty)
                dbSet = dbSet.Where(x => x.ReceiverId == receiverId);

            if (partnerId.HasValue)
                dbSet = dbSet.Where(x => x.Sender.RelatedEntityId == partnerId);

            if (from.HasValue && to.HasValue)
            {
                dbSet = dbSet.Where(x => x.From >= from.Value.ToUniversalTime());
                dbSet = dbSet.Where(x => x.To <= to.Value.ToUniversalTime());
            }

            if (!string.IsNullOrEmpty(batchType))
            {
                dbSet = dbSet.Where(x => x.BatchType == batchType);
            }

            return await dbSet.Select(x => ToBatchDto(x)).ToListAsync();
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task<List<BatchDTO>> GetShelvedAsync(Guid senderId, Guid receiverId, Guid? partnerId,
        string? batchType, DateTime? from, DateTime? to)
    {
        return await GetAsync(senderId, receiverId, partnerId, null, null, true, batchType, from, to);
    }

    public async Task ShelveAsync(Guid batchId)
    {
        using var workspan = Workspan.Start<PayoutService>();

        try
        {
            var batch = await _dbContext.Batches
                .SingleOrDefaultAsync(x => x.Id == batchId);

            if (batch == null)
            {
                workspan.Log.Error("Batch not found: {batchId}", batchId);
                throw new Exception("Not found");
            }
            else
            {
                batch.IsShelved = DateTime.UtcNow;
                _dbContext.Batches.Update(batch);
                await _dbContext.SaveChangesAsync();
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task UnshelveAsync(Guid batchId)
    {
        using var workspan = Workspan.Start<PayoutService>();

        try
        {
            var batch = await _dbContext.Batches
                .SingleOrDefaultAsync(x => x.Id == batchId);

            if (batch == null)
            {
                workspan.Log.Error("Batch not found: {batchId}", batchId);
                throw new Exception("Not found");
            }
            else
            {
                batch.IsShelved = null;
                _dbContext.Batches.Update(batch);
                await _dbContext.SaveChangesAsync();
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    private static BatchDTO ToBatchDto(Batch batch)
    {
        return new BatchDTO
        {
            BatchId = batch.Id,
            BatchType = batch.BatchType,
            PayoutId = batch.Id,
            PayoutDate = batch.To.AddDays(1),
            CapturePeriodFrom = batch.From,
            CapturePeriodTo = batch.To,
            Mid = batch.Beneficiary?.Mid ?? batch.Receiver.Id,
            MerchantName = batch.Beneficiary?.Dba ?? batch.Receiver.RelatedEntityDba,
            Sender = ToFinancialAccountDTO(batch.Sender),
            Receiver = ToFinancialAccountDTO(batch.Receiver),
            Status = batch.PayoutStatus,
            ScheduledFor = DateTime.Now.TimeOfDay >= DateTime.Today.AddHours(20).TimeOfDay
                ? DateTime.Today.AddHours(20)
                : DateTime.Today.AddHours(20).AddDays(1),
            CanUnpost = (batch.IsPosted && batch.PayoutStatus == nameof(BatchStatus.UNPROCESSED)) ||
                        batch.BatchType != FIMovementType.FIPAD.ToString(),
            CanPost = batch.IsFinished(),
            IsFinished = batch.IsFinished(),
            IsPosted = batch.IsPosted,

            TotalValue = Formatters.IntToDecimal(batch.TotalAmount),
            FlexChargeFees = batch.CalculateFlexProcessingFeesAmount().ToSignedDecimal(MathHelpers.MathSign.Negative),
            Returns = batch.ReturnsAmount.ToSignedDecimal(MathHelpers.MathSign.Negative),
            Chargebacks = batch.ChargebacksAmount.ToSignedDecimal(MathHelpers.MathSign.Negative),
            ReserveHold = batch.Reserve.ToSignedDecimal(MathHelpers.MathSign.Negative),
            ReserveRelease = batch.ReserveRelease.ToSignedDecimal(MathHelpers.MathSign.Positive),
            ReserveUtilization = batch.ReserveUtilization.ToSignedDecimal(MathHelpers.MathSign.Positive),
            FlexChargeChargebackFeesAmount =
                batch.FlexChargeChargebackFeesAmount.ToSignedDecimal(MathHelpers.MathSign.Negative),
            Adjustment = Formatters.IntToDecimal(batch.Adjustment),
            PayoutAmount = Formatters.IntToDecimal(batch.CalculatePayoutAmount()),

            TotalCount = batch.TotalCount,
            FlexChargeFeesCount = batch.FlexChargeFeesCount,
            ReturnsCount = batch.ReturnsCount,
            ChargebacksCount = batch.ChargebacksCount,
            Currency = batch.Currency,
            CurrencyCode = batch.CurrencyCode,
            CurrencySymbol = batch.CurrencySymbol,
            Comment = batch.Comment
        };
    }

    private static FinancialAccountDTO ToFinancialAccountDTO(FinancialAccount financialAccount)
    {
        if (financialAccount == null)
            return new FinancialAccountDTO();

        return new FinancialAccountDTO
        {
            Id = financialAccount.Id,
            RelatedEntityType = financialAccount.RelatedEntityType,
            RelatedEntityId = financialAccount.RelatedEntityId,
            RelatedEntityDba = financialAccount.RelatedEntityDba,
            AccountType = financialAccount.AccountType
        };
    }

    public async Task<List<SettlementDTO>> GetProcessedAsync(Guid senderId, Guid receiverId, Guid? partnerId,
        bool? isLatest,
        string? batchType)
    {
        using var workspan = Workspan.Start<BatchService>()
            .Baggage(nameof(receiverId), receiverId);

        try
        {
            var settlementsQuery = _readOnlyDbContext.Batches
                .Include(x => x.Beneficiary)
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .Where(x => x.PayoutStatus == nameof(BatchStatus.SUCCESS));

            if (senderId != Guid.Empty)
            {
                settlementsQuery = settlementsQuery.Where(x => x.SenderId == senderId);
            }

            if (receiverId != Guid.Empty)
            {
                settlementsQuery = settlementsQuery.Where(x => x.ReceiverId == receiverId);
            }

            if (partnerId.HasValue)
            {
                settlementsQuery = settlementsQuery.Where(x => x.Sender.RelatedEntityId == partnerId);
            }

            if (isLatest.HasValue && receiverId != Guid.Empty)
            {
                var mid = await _readOnlyDbContext.FinancialAccounts
                    .Where(x => x.Id == receiverId)
                    .Select(x => x.RelatedEntityId)
                    .FirstOrDefaultAsync();

                var merchant = await _readOnlyDbContext.Merchants
                    .FirstOrDefaultAsync(x => x.Mid == mid);

                var payoutFrequency = merchant.ToPayoutFrequencyType();
                var todayUtc = DateTime.UtcNow.ToUtcDate();

                var batchStartDate = payoutFrequency.GetCurrentBatchPayoutRangeStartDate(todayUtc).AddMonths(-1);
                var batchEndDate = payoutFrequency.GetCurrentBatchPayoutRangeEndDate(todayUtc).AddMonths(-1);

                settlementsQuery = isLatest == true
                    ? settlementsQuery.Where(x => x.To >= batchStartDate && x.To <= batchEndDate)
                    : settlementsQuery.Where(x => x.To < batchStartDate || x.To > batchEndDate);
            }
            else if (isLatest.HasValue)
            {
                DateTime todayDateUtc = DateTime.UtcNow.ToUtcDate();
                var batchStartDate = new DateTime(todayDateUtc.Year, todayDateUtc.Month, 1).AddMonths(-1).ToUtcDate();
                var batchEndDate = new DateTime(todayDateUtc.Year, todayDateUtc.Month, 1).AddDays(-1).ToUtcDate();

                settlementsQuery = isLatest == true
                    ? settlementsQuery.Where(x => x.To >= batchStartDate && x.To <= batchEndDate)
                    : settlementsQuery.Where(x => x.To < batchStartDate || x.To > batchEndDate);
            }

            if (!string.IsNullOrEmpty(batchType))
            {
                settlementsQuery = settlementsQuery.Where(x => x.BatchType == batchType);
            }

            var settlements = await settlementsQuery
                .Select(x =>
                    new SettlementDTO
                    {
                        Id = x.Id,
                        BatchType = x.BatchType,
                        PaymentRef = x.RelatedTransaction ?? x.Id,
                        IsOfflineSettlement = x.IsOffline,
                        PayoutDate = x.To.AddDays(1),
                        CapturePeriodFrom = x.From,
                        CapturePeriodTo = x.To,
                        Receiver = ToFinancialAccountDTO(x.Receiver),
                        Sender = ToFinancialAccountDTO(x.Sender),
                        Mid = x.Receiver != null ? x.Receiver.RelatedEntityId : x.Beneficiary.Mid,
                        TotalValue = x.TotalAmount.ToSignedDecimal(MathHelpers.MathSign.Positive),
                        FlexChargeFees = x.FlexChargeFeesAmount.ToSignedDecimal(MathHelpers.MathSign.Negative),
                        Returns = x.ReturnsAmount.ToSignedDecimal(MathHelpers.MathSign.Negative),
                        Chargebacks = x.ChargebacksAmount.ToSignedDecimal(MathHelpers.MathSign.Negative),
                        ReserveHold = x.Reserve.ToSignedDecimal(MathHelpers.MathSign.Negative),
                        ReserveRelease = x.ReserveRelease.ToSignedDecimal(MathHelpers.MathSign.Positive),
                        ReserveUtilization = x.ReserveUtilization.ToSignedDecimal(MathHelpers.MathSign.Positive),
                        Adjustment = Formatters.IntToDecimal(x.Adjustment),
                        FlexChargeChargebackFeesAmount =
                            x.FlexChargeChargebackFeesAmount.ToSignedDecimal(MathHelpers.MathSign.Negative),
                        PayoutAmount = Formatters.IntToDecimal(x.CalculatePayoutAmount()),
                        CurrencyCode = x.CurrencyCode,
                        CurrencySymbol = x.CurrencySymbol,
                        Currency = x.Currency,
                        Comment = x.Comment
                    }).ToListAsync();

            return settlements;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }


    #region Refactor (merchant related need to become sender receiver related

    public async Task<Batch> GetCurrentBatch(Merchant merchant)
    {
        var payoutFrequency = merchant.ToPayoutFrequencyType();
        var todayUtc = DateTime.UtcNow.ToUtcDate();

        var batchStartDate = payoutFrequency.GetCurrentBatchPayoutRangeStartDate(todayUtc);
        var batchEndDate = payoutFrequency.GetCurrentBatchPayoutRangeEndDate(todayUtc);
        var batchtype = nameof(FIMovementType.FISC);

        var batch = await GetOrCreateBatch(merchant, batchtype, batchStartDate, batchEndDate, includeBatchOrders: true);
        return batch;
    }

    public async Task<Batch> GetOrCreateBatch(Merchant merchant, string batchType,
        DateTime batchStartDate, DateTime batchEndDate, bool includeBatchOrders)
    {
        return await GetOrCreateBatch(merchant, batchType, batchStartDate, batchEndDate, includeBatchOrders, false);
    }

    public async Task<Batch> GetOrCreateBatch(Merchant merchant, string batchType,
        DateTime batchStartDate, DateTime batchEndDate, bool includeBatchOrders, bool overrideExistingBatch = false)
    {
        var concurrencyUniqueBatchId = CreateConcurrencyUniqueBatchId(merchant,
            batchStartDate,
            batchEndDate);

        var batchQuery = _dbContext.Batches.AsQueryable();
        if (includeBatchOrders)
        {
            batchQuery = batchQuery
                //.Include(x => x.BatchOrders)
                .Include(x => x.BatchRecords);
        }

        var batch = await batchQuery
            .SingleOrDefaultAsync(x =>
                x.ConcurrencyUniqueBatchId == concurrencyUniqueBatchId);

        if (batch == null)
        {
            batch = new Batch
            {
                // Unique key and its checked against a DB constraint to avoid adding duplicate batches
                ConcurrencyUniqueBatchId = concurrencyUniqueBatchId,
                Beneficiary = merchant,
                BatchType = batchType,
                PayoutStatus = nameof(BatchStatus.UNPROCESSED),
                From = batchStartDate,
                To = batchEndDate,
                CurrencyCode = 840,
                Currency = "USD",
                CurrencySymbol = "$",
                IsOffline = true, // TODO: Add support for ACH transactions (online)
                BatchRecords = new List<BatchRecord>()
            };
            _dbContext.Batches.Add(batch);
        }
        else
        {
            if (batch.IsPosted)
                throw new FlexChargeException("Can't override posted batch");

            if (overrideExistingBatch)
            {
                batch.ConcurrencyUniqueBatchId =
                    concurrencyUniqueBatchId + "_DELETED_" + DateTime.UtcNow.ToString("yyyyMMddHHmmssfff");

                //delete all batch records as well
                batch.BatchRecords.ForEach(x =>
                {
                    x.UniqueHash = x.UniqueHash + "_DELETED_" + DateTime.UtcNow.ToString("yyyyMMddHHmmssfff");
                });

                _dbContext.Batches.Remove(batch);
                await _dbContext.SaveChangesAsync();
                batch = null;
            }
        }

        return batch;
    }


    /// <summary>
    /// New version of GetOrCreateBatch method
    /// </summary>
    /// <param name="sender">Financial account id</param>
    /// <param name="receiver">Financial account id</param>
    /// <param name="batchType"></param>
    /// <param name="batchStartDate"></param>
    /// <param name="batchEndDate"></param>
    /// <param name="isOffline"></param>
    /// <param name="includeRecords"></param>
    /// <param name="overrideExistingBatch"></param>
    /// <returns></returns>
    /// <exception cref="FlexChargeException"></exception>
    public async Task<Batch> GetOrCreateBatchV2(FinancialAccount sender, FinancialAccount receiver, string batchType,
        DateTime batchStartDate, DateTime batchEndDate, bool? isOffline, bool includeRecords,
        bool overrideExistingBatch, bool autoPosted = false)
    {
        if (isOffline == null)
            throw new FlexChargeException("isOffline can't be null");

        var concurrencyUniqueBatchId =
            $"{batchType}_sndr_{sender.Id}_rcvr_{receiver.Id}_{batchStartDate.ToShortDateString()}_{batchEndDate.ToShortDateString()}";

        var batchQuery = _dbContext.Batches.AsQueryable();
        if (includeRecords)
        {
            batchQuery = batchQuery
                .Include(x => x.BatchRecords);
        }

        var batch = await batchQuery
            .SingleOrDefaultAsync(x =>
                x.ConcurrencyUniqueBatchId == concurrencyUniqueBatchId);

        if (batch == null)
        {
            batch = new Batch
            {
                // Unique key and its checked against a DB constraint to avoid adding duplicate batches
                ConcurrencyUniqueBatchId = concurrencyUniqueBatchId,
                ReceiverId = receiver.Id,
                SenderId = sender.Id,
                BatchType = batchType,
                PayoutStatus = nameof(BatchStatus.UNPROCESSED),
                From = batchStartDate,
                To = batchEndDate,
                CurrencyCode = 840,
                Currency = "USD",
                CurrencySymbol = "$",
                IsOffline = isOffline.Value,
                IsPosted = autoPosted,
                BatchRecords = new List<BatchRecord>()
            };
            _dbContext.Batches.Add(batch);
        }
        else
        {
            if (batch.IsPosted)
                throw new FlexChargeException("Can't override posted batch");

            if (overrideExistingBatch)
            {
                batch.ConcurrencyUniqueBatchId =
                    concurrencyUniqueBatchId + "_DELETED_" + DateTime.UtcNow.ToString("yyyyMMddHHmmssfff");

                //delete all batch records as well
                batch.BatchRecords?.ForEach(x =>
                {
                    x.UniqueHash = x.UniqueHash + "_DELETED_" + DateTime.UtcNow.ToString("yyyyMMddHHmmssfff");
                });

                //update all order records and remove any reference to this batch
                var orderTransactions = await _dbContext.Transactions
                    .Where(x => x.BatchId == batch.Id)
                    .ToListAsync();

                //remove batch id from all transactions (Order activity)
                foreach (var transaction in orderTransactions)
                    transaction.BatchId = null;

                _dbContext.Transactions.UpdateRange(orderTransactions);
                _dbContext.Batches.Remove(batch);
                await _dbContext.SaveChangesAsync();

                batch = await GetOrCreateBatchV2(sender, receiver, batchType, batchStartDate, batchEndDate,
                    isOffline, includeRecords, false);
            }
            else
            {
                Workspan.Current?.Log.Information("Batch already exists: {ConcurrencyUniqueBatchId}",
                    concurrencyUniqueBatchId);
            }
        }

        return batch;
    }


    private static string CreateConcurrencyUniqueBatchId(Merchant merchant, DateTime payoutRangeStartDateUtc,
        DateTime payoutRangeEndDateUtc)
    {
        return
            $"{merchant.Mid}_{payoutRangeStartDateUtc.ToShortDateString()}_{payoutRangeEndDateUtc.ToShortDateString()}";
    }

    #endregion
}