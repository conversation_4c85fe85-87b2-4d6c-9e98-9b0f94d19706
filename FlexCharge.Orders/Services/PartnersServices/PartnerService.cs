using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Validation.FluentValidation;
using FlexCharge.Common.Settings.SharedSettings;
using FlexCharge.Common.Shared.Partners;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using FlexCharge.Orders.Activities;
using FlexCharge.Orders.Services.ConsumerNotifications;
using MassTransit;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Orders.Services.PartnersServices;

public class PartnerService : IPartnerService
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IPublishEndpoint _publisher;
    private readonly IActivityService _activityService;
    private readonly ISettingsService<PartnerSettings> _partnerSettingsService;

    public PartnerService(PostgreSQLDbContext dbContext,
        IPublishEndpoint publisher,
        IActivityService activityService,
        ISettingsService<PartnerSettings> partnerSettingsService)
    {
        _dbContext = dbContext;
        _publisher = publisher;
        _activityService = activityService;
        _partnerSettingsService = partnerSettingsService;
    }

    #region Commented

    // public async Task UpsertPartnerAsync(Guid partnerId, string name)
    // {
    //     using var workspan = Workspan.Start<PartnerService>()
    //         .LogEnterAndExit();
    //
    //     try
    //     {
    //         //check if partner already exists
    //         var partner = await _dbContext.Partners.SingleOrDefaultAsync(x => x.Id == partnerId);
    //         if (partner != null)
    //         {
    //             partner.Name = name;
    //
    //             _dbContext.Update(partner);
    //         }
    //         else
    //         {
    //             partner = new Partner
    //             {
    //                 Id = partnerId,
    //                 Name = name
    //             };
    //             await _dbContext.Partners.AddAsync(partner);
    //         }
    //
    //         await _dbContext.SaveChangesAsync();
    //     }
    //     catch (Exception e)
    //     {
    //         throw;
    //     }
    // }
    //
    // public async Task UpdatePartnerAsync(Guid partnerId, string name)
    // {
    //     throw new NotImplementedException();
    // }

    #endregion

    public async Task<PartnerSettings> GetPartnerSettingsAsync(Guid mid, Guid orderId,
        HashSet<string> requiredFields)
    {
        using var workspan = Workspan.Start<PartnerService>();

        var merchant = await _dbContext.Merchants.SingleAsync(x => x.Mid == mid);

        if (!merchant.Pid.HasValue)
        {
            workspan.Log
                .Fatal("Merchant does not have a PID. Cannot get partner settings");

            await _activityService.CreateActivityAsync(
                ConsumerNotificationErrorActivities.ConsumerNotification_IncorrectPartnerSettings,
                data: "Merchant does not have a PID. Cannot get partner settings", set => set
                    .TenantId(mid)
                    .CorrelationId(orderId));

            return null;
        }

        var partnerSettings = await _partnerSettingsService.GetSettingsAsync(merchant.Pid.Value);

        PartnerSettingsValidator partnerSettingsValidator = new();
        var partnerSettingsValidationResult = partnerSettingsValidator.Validate(partnerSettings);
        if (!partnerSettingsValidationResult.IsValid)
        {
            try
            {
                partnerSettingsValidationResult.ThrowOnErrors(logErrors: false, filter:
                    error => requiredFields.Contains(error.PropertyName)
                );
            }
            catch (FlexValidationMultipleErrorsException e)
            {
                workspan.RecordFatalException(e, "Partner settings validation failed");

                await _activityService.CreateActivityAsync(
                    ConsumerNotificationErrorActivities.ConsumerNotification_IncorrectPartnerSettings,
                    data: e.ToString(), set => set
                        .TenantId(mid)
                        .CorrelationId(orderId));

                return null;
            }
        }

        return partnerSettings;
    }

    public async Task<PartnerSettings> GetPartnerSettingsAsync(Guid partnerId, HashSet<string> requiredFields)
    {
        using var workspan = Workspan.Start<PartnerService>();

        var partnerSettings = await _partnerSettingsService.GetSettingsAsync(partnerId);

        PartnerSettingsValidator partnerSettingsValidator = new();
        var partnerSettingsValidationResult = partnerSettingsValidator.Validate(partnerSettings);

        if (!partnerSettingsValidationResult.IsValid)
        {
            try
            {
                partnerSettingsValidationResult.ThrowOnErrors(logErrors: false, filter:
                    error => requiredFields.Contains(error.PropertyName)
                );
            }
            catch (FlexValidationMultipleErrorsException e)
            {
                workspan.RecordFatalException(e, "Partner settings validation failed");

                await _activityService.CreateActivityAsync(
                    ConsumerNotificationErrorActivities.ConsumerNotification_IncorrectPartnerSettings);

                return null;
            }
        }

        return partnerSettings;
    }
}