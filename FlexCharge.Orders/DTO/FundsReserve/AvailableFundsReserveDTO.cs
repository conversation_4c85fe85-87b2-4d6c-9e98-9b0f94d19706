using System;
using System.Collections.Generic;
using FlexCharge.Orders.Entities;
using FlexCharge.Orders.Services.FundsReserveService;

namespace FlexCharge.Orders.DTO;

public class AvailableFundsReserveDTO
{
    public List<FundsReserve> Records { get; set; }
    public int AmountInCents { get; set; }
    public decimal Amount => Utils.Formatters.IntToDecimal(AmountInCents);
    public string Currency { get; set; }
    public string CurrencySymbol { get; set; }
    public string CurrencyCode { get; set; }
}