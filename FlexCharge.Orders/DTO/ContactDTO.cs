using System;

namespace FlexCharge.Orders.DTO;

public class ContactDTO
{
        public Guid? UserId { get; set; }
        public Guid? ContactId { get; set; }

        public string FirstName { get; set; }
        public string LastName { get; set; }

        public string Email { get; set; }
        public string Phone { get; set; }
        public string ProfileUrl { get; set; }
        //public List<xContactAccounts> ContactAccounts { get; set; }
}