using System;

namespace FlexCharge.Orders.Entities
{
    public class Transaction : AuditableEntity
    {
        public string Type { get; set; }
        public Guid PaymentTransactionId { get; set; }
        public Guid? RelatedTransactionId { get; set; }
        public string Description { get; set; }
        public string TotalAmountFormatted { get; set; }
        public int TotalAmount { get; set; }

        public int DiscountAmount { get; set; }

        public DateTime PaymentDate { get; set; }

        public Guid OrderId { get; set; }
        public Order Order { get; set; }

        public Guid? BatchId { get; set; }
    }
}