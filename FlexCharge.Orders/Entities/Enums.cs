using System.ComponentModel;

namespace FlexCharge.Orders.Entities
{
    public enum OrderStatusCategory
    {
        [Description("Draft")] draft,
        [Description("OnHold")] onhold,
        [Description("Cancelled")] cancelled,
        [Description("Problem")] problem,
        [Description("Processing")] processing,
        [Description("CaptureRequired")] capturerequired,
        [Description("Returned")] returned,
        [Description("Completed")] completed,
        // [Description("Chargeback")]
        // Chargeback
    }
}