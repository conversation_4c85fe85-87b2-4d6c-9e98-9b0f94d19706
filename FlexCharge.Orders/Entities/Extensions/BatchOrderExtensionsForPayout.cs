using System;
using System.Collections.Generic;
using FlexCharge.Utils;

namespace FlexCharge.Orders.Entities.Extensions;

// public static class BatchOrderExtensionsForPayout
// {
//     public static BatchRecordTypes AsBatchOrderType(this BatchOrder batchOrder)
//     {
//         return EnumHelpers.ParseEnum<BatchOrderType>(batchOrder.Type);
//     }
// }

// public enum BatchOrderType
// {
//     ORDER,
//     CREDIT,
//     RETURN,
//     CHARGEBACK,
//     BATCH_ADJUSTMENT,
//     RESERVE_HOLD,
//     RESERVE_RELEASE,
// }