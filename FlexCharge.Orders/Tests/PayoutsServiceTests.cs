// using System;
// using System.Collections.Generic;
// using System.IO;
// using System.Linq;
// using System.Threading.Tasks;
// using AutoMapper;
// using FlexCharge.Common.Activities;
// using FlexCharge.Common.DistributedLock;
// using FlexCharge.Common.Telemetry;
// using FlexCharge.Contracts.Commands;
// using FlexCharge.Orders.Contracts;
// using FlexCharge.Orders.DTO;
// using FlexCharge.Orders.Entities;
// using FlexCharge.Orders.Services.AdministratorTools;
// using FlexCharge.Orders.Services.FundsReserveService;
// using FlexCharge.Orders.Services.PayoutServices;
// using MassTransit;
// using Microsoft.AspNetCore.Http;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.EntityFrameworkCore.Infrastructure;
// using Microsoft.EntityFrameworkCore.Storage;
// using Microsoft.Extensions.Configuration;
// using Microsoft.Extensions.DependencyInjection;
// using Microsoft.Extensions.Logging;
// using Moq;
// using NUnit.Framework;
//
// namespace FlexCharge.Orders.Tests;
//
// public class PayoutsServiceTests
// {
//     private readonly ServiceProvider _serviceProvider;
//
//     public IConfiguration _config { get; set; }
//
//     private readonly Mock<PostgreSQLDbContext> _mockDbContext;
//     private readonly Mock<ILogger<PayoutService>> _mockLogger;
//     public IPayoutService _payoutService { get; set; }
//     private readonly IAdministratorToolsService _administratorToolsService;
//
//
//     private PostgreSQLDbContext _dbContext;
//     private readonly IPublishEndpoint _publisher;
//     private readonly IBatchService _batchService;
//     private readonly IActivityService _activityService;
//     private readonly IDistributedLockService _distributedLockService;
//     private readonly IRequestClient<PerformPayoutCommand> _performPayoutCommandRequestClient;
//     private readonly IFundsReserveService _fundsReserveService;
//     private readonly IMapper _mapper;
//
//     public PayoutsServiceTests()
//     {
//         _config = new ConfigurationBuilder()
//             .SetBasePath(Directory.GetCurrentDirectory())
//             .AddJsonFile(@"appsettings.Development.json", false, false)
//             .AddEnvironmentVariables()
//             .Build();
//         
//         var serviceCollection = new ServiceCollection();
//         serviceCollection.AddSingleton(_config);
//         serviceCollection.AddTelemetry();
//         serviceCollection.AddDbContext<PostgreSQLDbContext>(options =>
//             options.UseInMemoryDatabase("TestDatabase"));
//
//         _publisher = new Mock<IPublishEndpoint>().Object;
//         _batchService = new Mock<IBatchService>().Object;
//         _activityService = new Mock<IActivityService>().Object;
//         _distributedLockService = new Mock<IDistributedLockService>().Object;
//         _performPayoutCommandRequestClient = new Mock<IRequestClient<PerformPayoutCommand>>().Object;
//         _fundsReserveService = new Mock<IFundsReserveService>().Object;
//         _mapper = new Mock<IMapper>().Object;
//
//         var mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
//         serviceCollection.AddSingleton(mockHttpContextAccessor.Object);
//         
//         // Step 2: Build the service provider to access the DbContextOptions
//         var tempProvider = serviceCollection.BuildServiceProvider();
//         var dbContextOptions = tempProvider.GetRequiredService<DbContextOptions<PostgreSQLDbContext>>();
//         
//         // Step 3: Mock the IDbContextTransaction
//         var mockTransaction = new Mock<IDbContextTransaction>();
//         mockTransaction.Setup(t => t.Commit()).Verifiable();
//         mockTransaction.Setup(t => t.Rollback()).Verifiable();
//
//         // Step 4: Mock the DatabaseFacade
//         var mockDatabaseFacade = new Mock<DatabaseFacade>(tempProvider.GetRequiredService<PostgreSQLDbContext>());
//         mockDatabaseFacade.Setup(db => db.BeginTransaction()).Returns(mockTransaction.Object);
//
//         // Step 5: Mock the DbContext with constructor parameters
//         var mockContext = new Mock<PostgreSQLDbContext>(
//             dbContextOptions, mockHttpContextAccessor.Object);
//         
//         mockContext.Setup(m => m.Database).Returns(mockDatabaseFacade.Object);
//
//         
//         serviceCollection.AddTransient<PayoutService>();
//
//         // _payoutService = new PayoutService(_mockDbContext.Object,
//         //     _publisher,
//         //     _activityService,
//         //     _performPayoutCommandRequestClient,
//         //     _distributedLockService,
//         //     _batchService,
//         //     _fundsReserveService,
//         //     _mapper);
//         
//         serviceCollection.AddSingleton(mockContext.Object);
//         
//         _serviceProvider = serviceCollection.BuildServiceProvider();
//     }
//
//     [SetUp]
//     public void Setup()
//     {
//         var ordersData = new List<Order>
//         {
//             new Order { Id = Guid.NewGuid(), OrderPayoutStatus = OrderPayOutStatuses.Completed, Amount = 100 },
//             new Order { Id = Guid.NewGuid(), OrderPayoutStatus = OrderPayOutStatuses.Completed, Amount = 100 },
//             new Order { Id = Guid.NewGuid(), OrderPayoutStatus = OrderPayOutStatuses.Completed, Amount = 100 },
//             new Order { Id = Guid.NewGuid(), OrderPayoutStatus = OrderPayOutStatuses.Completed, Amount = 100 },
//             new Order { Id = Guid.NewGuid(), OrderPayoutStatus = OrderPayOutStatuses.Completed, Amount = 100 }
//         }.AsQueryable();
//         
//         
//         
//         //Arrange
//         // newContext.Add(new Order
//         //     {Id = Guid.NewGuid(), OrderPayoutStatus = OrderPayOutStatuses.Completed, Amount = 100});
//         // newContext.Add(new Order
//         //     {Id = Guid.NewGuid(), OrderPayoutStatus = OrderPayOutStatuses.Completed, Amount = 100});
//         // newContext.Add(new Order
//         //     {Id = Guid.NewGuid(), OrderPayoutStatus = OrderPayOutStatuses.Completed, Amount = 100});
//         // newContext.Add(new Order
//         //     {Id = Guid.NewGuid(), OrderPayoutStatus = OrderPayOutStatuses.Completed, Amount = 100});
//         // newContext.Add(new Order
//         //     {Id = Guid.NewGuid(), OrderPayoutStatus = OrderPayOutStatuses.Completed, Amount = 100});
//         //
//         // newContext.Add(new Batch {Id = Guid.NewGuid(), IsPosted = true, TotalAmount = 500});
//         // newContext.Add(new BatchRecord {BatchId = Guid.NewGuid(), RelatedOrderId = Guid.NewGuid()});
//         //
//         // newContext.SaveChanges();
//         
//     }
//
//     //Write tests on payouts service
//     [Test]
//     public async Task Test1()
//     {
//         var newContext = _serviceProvider.GetService<PostgreSQLDbContext>();
//
//         var rrrr = await newContext.Orders.ToListAsync();
//
//         //Act
//         //await _payoutService.ExecutePayoutsAsync();
//
//         //Assert
//     }
// }

