using System;
using FlexCharge.Orders.Entities;
using FlexCharge.Orders.Entities.Extensions;
using FlexCharge.Utils;
using NUnit.Framework;
using NUnit.Framework.Legacy;

namespace FlexCharge.Orders.Tests
{
    [TestFixture]
    public class MerchantExtensionsForPayout_Tests
    {
        [Test]
        public void GetCurrentBatchPayoutRangeEndDate_Weekly_ReturnsMondayOfCurrentWeek()
        {
            // Arrange
            // Let's say "today" is Wednesday, December 25, 2024
            // DayOfWeek is Wednesday => (int)DayOfWeek.Wednesday = 3
            // The Sunday of this week should be December 22, 2024
            var today = new DateTime(2024, 12, 25);
            var payoutFrequencyType = PayoutFrequencyType.WEEKLY;

            // Act
            var result = payoutFrequencyType.GetCurrentBatchPayoutRangeEndDate(today);

            // Assert
            // We expect Sunday, December 22, 2024 in UTC
            var expected = new DateTime(2024, 12, 23).ToUtcDate();
            ClassicAssert.AreEqual(expected, result,
                $"Expected Sunday of the current week but got {result}");
        }

        [TestCase(2023, 10, 1, 14)] // If day < 15 => 14th
        [TestCase(2023, 10, 14, 14)] // Edge case: day == 14 => 14th
        [TestCase(2023, 10, 2, 14)] // day < 15 => 14th
        [TestCase(2023, 10, 15, 31)] // If day >= 15 => last day of month (Oct 31)
        [TestCase(2023, 10, 31, 31)] // day == 31 => last day of month
        public void GetCurrentBatchPayoutRangeEndDate_TwiceAMonth_ReturnsExpectedEndDate(
            int year, int month, int day, int expectedDay)
        {
            // Arrange
            var today = new DateTime(year, month, day);
            var payoutFrequencyType = PayoutFrequencyType.TWICEAMONTH;

            // Act
            var result = payoutFrequencyType.GetCurrentBatchPayoutRangeEndDate(today);

            // Assert
            var expectedDate = new DateTime(year, month, expectedDay).ToUtcDate();

            ClassicAssert.AreEqual(expectedDate, result,
                $"For day={day}, expected day {expectedDay}, but got {result}");
        }

        [TestCase(2023, 1, 1, 31)] // January has 31 days
        [TestCase(2023, 2, 5, 28)] // February 2023 has 28 days
        [TestCase(2024, 2, 5, 29)] // February 2024 has 29 days (leap year)
        [TestCase(2023, 4, 10, 30)] // April has 30 days
        public void GetCurrentBatchPayoutRangeEndDate_Monthly_ReturnsLastDayOfMonth(
            int year, int month, int day, int expectedDay)
        {
            // Arrange
            var today = new DateTime(year, month, day);
            var payoutFrequencyType = PayoutFrequencyType.MONTHLY;

            // Act
            var result = payoutFrequencyType.GetCurrentBatchPayoutRangeEndDate(today);

            // Assert
            var expectedDate = new DateTime(year, month, expectedDay).ToUtcDate();

            ClassicAssert.AreEqual(expectedDate, result,
                $"Expected the last day of month {month}/{year} => {expectedDay}, but got {result}");
        }


        [Test]
        public void GetCurrentBatchPayoutRangeEndDate_UnknownFrequency_ThrowsArgumentOutOfRangeException()
        {
            // Arrange
            var today = new DateTime(2023, 1, 1);
            var invalidFrequency = (PayoutFrequencyType) 999; // Not a valid enum value

            // Act & Assert
            Assert.Throws<ArgumentOutOfRangeException>(() =>
                invalidFrequency.GetCurrentBatchPayoutRangeEndDate(today));
        }

        // -------------------------------------------
        // Tests for GetCurrentBatchPayoutRangeStartDate
        // -------------------------------------------

        [Test]
        public void GetCurrentBatchPayoutRangeStartDate_Weekly_ReturnsMondayOfCurrentWeek()
        {
            // Arrange
            // Let's say "today" is Wednesday, October 4, 2023
            // DayOfWeek for Wednesday => (int)DayOfWeek.Wednesday = 3
            // The Monday of this week should be October 2, 2023
            var today = new DateTime(2023, 10, 4);
            var payoutFrequencyType = PayoutFrequencyType.WEEKLY;

            // Act
            var result = payoutFrequencyType.GetCurrentBatchPayoutRangeStartDate(today);

            // Assert
            // We expect Monday, October 2, 2023 in UTC
            var expected = new DateTime(2023, 10, 2).ToUtcDate();
            ClassicAssert.AreEqual(expected, result,
                $"Expected Monday of the current week but got {result}");
        }

        [TestCase(2023, 10, 1, 1)] // If day < 15 => 1st of month
        [TestCase(2023, 10, 14, 1)] // If day < 15 => 1st of month
        [TestCase(2023, 10, 2, 1)] // If day < 15 => 1st of month
        [TestCase(2023, 10, 15, 15)] // If day >= 15 => 15th of month
        [TestCase(2023, 10, 31, 15)] // day >= 15 => 15th of month
        public void GetCurrentBatchPayoutRangeStartDate_TwiceAMonth_ReturnsExpectedStartDate(
            int year, int month, int day, int expectedDay)
        {
            // Arrange
            var today = new DateTime(year, month, day);
            var payoutFrequencyType = PayoutFrequencyType.TWICEAMONTH;

            // Act
            var result = payoutFrequencyType.GetCurrentBatchPayoutRangeStartDate(today);

            // Assert
            var expectedDate = new DateTime(year, month, expectedDay).ToUtcDate();
            ClassicAssert.AreEqual(expectedDate, result,
                $"For day={day}, expected day {expectedDay}, but got {result}");
        }

        [TestCase(2023, 1, 1)] // January
        [TestCase(2023, 2, 5)] // February
        [TestCase(2024, 2, 29)] // 2024 leap year: If 'today' is 29th, the start date is 1st
        [TestCase(2023, 4, 10)] // April
        public void GetCurrentBatchPayoutRangeStartDate_Monthly_ReturnsFirstDayOfMonth(
            int year, int month, int day)
        {
            // Arrange
            var today = new DateTime(year, month, day);
            var payoutFrequencyType = PayoutFrequencyType.MONTHLY;

            // Act
            var result = payoutFrequencyType.GetCurrentBatchPayoutRangeStartDate(today);

            // Assert
            var expectedDate = new DateTime(year, month, 1).ToUtcDate();
            ClassicAssert.AreEqual(expectedDate, result,
                $"Expected the first day of month {month}/{year}, but got {result}");
        }

        [Test]
        public void GetCurrentBatchPayoutRangeStartDate_UnknownFrequency_ThrowsArgumentOutOfRangeException()
        {
            // Arrange
            var today = new DateTime(2023, 1, 1);
            var invalidFrequency = (PayoutFrequencyType) 999; // Not a valid enum value

            // Act & Assert
            Assert.Throws<ArgumentOutOfRangeException>(() =>
                invalidFrequency.GetCurrentBatchPayoutRangeStartDate(today));
        }

        [Test]
        public void ToPayoutFrequencyType_NullMerchant_ThrowsArgumentNullException()
        {
            // Arrange
            Merchant merchant = null;

            // Act & Assert
            // Since null merchant => ArgumentNullException
            Assert.Throws<ArgumentNullException>(() => merchant.ToPayoutFrequencyType(),
                "Expected an exception when merchant is null.");
        }

        [Test]
        public void ToPayoutFrequencyType_NullPayoutFrequency_ReturnsMonthly()
        {
            // Arrange
            var merchant = new Merchant {PayoutFrequency = null};

            // Act
            var result = merchant.ToPayoutFrequencyType();

            // Assert
            ClassicAssert.AreEqual(PayoutFrequencyType.MONTHLY, result,
                "Expected MONTHLY when merchant.PayoutFrequency is null.");
        }

        [TestCase("WEEKLY", PayoutFrequencyType.WEEKLY)]
        [TestCase("TWICEAMONTH", PayoutFrequencyType.TWICEAMONTH)]
        [TestCase("MONTHLY", PayoutFrequencyType.MONTHLY)]
        [TestCase("monthly", PayoutFrequencyType.MONTHLY)] // case-insensitive
        [TestCase("", PayoutFrequencyType.MONTHLY)] // Empty string => MONTHLY !!! This is temporary
        public void ToPayoutFrequencyType_ValidString_ReturnsCorrectEnum(
            string payoutFrequency, PayoutFrequencyType expected)
        {
            // Arrange
            var merchant = new Merchant {PayoutFrequency = payoutFrequency};

            // Act
            var result = merchant.ToPayoutFrequencyType();

            // Assert
            ClassicAssert.AreEqual(expected, result,
                $"Expected {expected} for merchant.PayoutFrequency = {payoutFrequency}");
        }

        [Test]
        public void ToPayoutFrequencyType_InvalidPayoutFrequency_ThrowsException()
        {
            // Arrange
            var merchant = new Merchant {PayoutFrequency = "INVALID_ENUM_VALUE"};

            // Act & Assert
            Assert.Throws<ArgumentOutOfRangeException>(() => merchant.ToPayoutFrequencyType(),
                "Expected ArgumentException when merchant.PayoutFrequency is invalid.");
        }

        [Test]
        public void IsPayoutDate_Weekly_TrueIfMonday()
        {
            // Arrange
            var merchant = new Merchant {PayoutFrequency = "WEEKLY"};
            var monday = new DateTime(2023, 10, 2); // Monday
            var tuesday = new DateTime(2023, 10, 3); // Tuesday

            // Act
            bool isMondayPayout = merchant.IsPayoutDate(monday);
            bool isTuesdayPayout = merchant.IsPayoutDate(tuesday);

            // Assert
            ClassicAssert.IsTrue(isMondayPayout, "Weekly payout on Monday should be true");
            ClassicAssert.IsFalse(isTuesdayPayout, "Weekly payout on Tuesday should be false");
        }

        [Test]
        public void IsPayoutDate_TwiceAMonth_TrueIfDay1OrDay15()
        {
            // Arrange
            var merchant = new Merchant {PayoutFrequency = PayoutFrequencyType.TWICEAMONTH.ToString()};
            var firstDay = new DateTime(2023, 10, 1);
            var fifteenthDay = new DateTime(2023, 10, 15);
            var secondDay = new DateTime(2023, 10, 2);
            var sixteenthDay = new DateTime(2023, 10, 16);

            // Act & Assert
            ClassicAssert.IsTrue(merchant.IsPayoutDate(firstDay), "Day=1 => true for TWICEAMONTH");
            ClassicAssert.IsTrue(merchant.IsPayoutDate(fifteenthDay), "Day=15 => true for TWICEAMONTH");
            ClassicAssert.IsFalse(merchant.IsPayoutDate(secondDay), "Day=2 => false for TWICEAMONTH");
            ClassicAssert.IsFalse(merchant.IsPayoutDate(sixteenthDay), "Day=16 => false for TWICEAMONTH");
        }

        [Test]
        public void IsPayoutDate_Monthly_TrueIfDay1()
        {
            // Arrange
            var merchant = new Merchant {PayoutFrequency = "MONTHLY"};
            var firstDay = new DateTime(2023, 10, 1);
            var secondDay = new DateTime(2023, 10, 2);

            // Act
            bool isFirstDayPayout = merchant.IsPayoutDate(firstDay);
            bool isSecondDayPayout = merchant.IsPayoutDate(secondDay);

            // Assert
            ClassicAssert.IsTrue(isFirstDayPayout, "For MONTHLY, day=1 => true");
            ClassicAssert.IsFalse(isSecondDayPayout, "For MONTHLY, day=2 => false");
        }

        [Test]
        public void IsPayoutDate_UnknownPayoutFrequency_ThrowsArgumentOutOfRangeException()
        {
            // Arrange
            var merchant = new Merchant {PayoutFrequency = "INVALID_ENUM_VALUE"};

            // Act & Assert
            // This will typically throw ArgumentException during the parse,
            // but if parsing somehow succeeded, the switch expression
            // would throw ArgumentOutOfRangeException. Either way, it's invalid.
            Assert.Throws<ArgumentOutOfRangeException>(() => merchant.IsPayoutDate(DateTime.UtcNow),
                "Expected an exception when merchant.PayoutFrequency is invalid.");
        }

        [Test]
        public void IsPayoutDate_NullMerchant_ReturnsArgumentNullException()
        {
            // Arrange
            Merchant merchant = null;

            // Assert
            // Since null merchant => ArgumentNullException
            Assert.Throws<ArgumentNullException>(() => merchant.IsPayoutDate(DateTime.UtcNow),
                "Expected an exception when merchant is null.");
        }


        #region Weekly Payout Tests

        [TestCase("2025-02-17", "2025-02-24")] // Monday to next Monday
        [TestCase("2025-02-18", "2025-02-24")] // Tuesday to next Monday
        [TestCase("2025-02-23", "2025-02-24")] // Sunday to next Monday
        [TestCase("2025-02-24", "2025-03-03")] // Monday to next Monday
        [TestCase("2024-02-29", "2024-03-04")] // Thursday (leap year) to next Monday
        public void GetNextPayoutDate_Weekly_Returns_Correct_Next_Payout(string todayStr, string expectedPayoutStr)
        {
            // Arrange
            var merchant = new Merchant {PayoutFrequency = "weekly"};
            DateTime today = DateTime.Parse(todayStr);
            DateTime expectedPayout = DateTime.Parse(expectedPayoutStr).ToUtcDate();

            // Act
            DateTime actualPayout = merchant.GetNextPayoutDate(today);

            // Assert
            ClassicAssert.AreEqual(expectedPayout, actualPayout);
        }

        #endregion

        #region Twice-a-Month Payout Tests

        [TestCase("2025-02-01", "2025-02-15")] // 1st to 15th
        [TestCase("2025-02-14", "2025-02-15")] // 14th to 15th
        [TestCase("2025-02-15", "2025-03-01")] // 15th to next month's 1st
        [TestCase("2025-02-16", "2025-03-01")] // 16th to next month's 1st
        [TestCase("2025-02-28", "2025-03-01")] // 28th to next month's 1st
        [TestCase("2024-02-29", "2024-03-01")] // Leap day to next month's 1st
        [TestCase("2025-04-01", "2025-04-15")] // 1st of April to 15th
        [TestCase("2025-04-15", "2025-05-01")] // 15th of April to May 1st
        public void GetNextPayoutDate_TwiceAMonth_Returns_Correct_Next_Payout(string todayStr,
            string expectedPayoutStr)
        {
            // Arrange
            var merchant = new Merchant {PayoutFrequency = "twiceamonth"};
            DateTime today = DateTime.Parse(todayStr);
            DateTime expectedPayout = DateTime.Parse(expectedPayoutStr).ToUtcDate();

            // Act
            DateTime actualPayout = merchant.GetNextPayoutDate(today);

            // Assert
            ClassicAssert.AreEqual(expectedPayout, actualPayout);
        }

        #endregion

        #region Monthly Payout Tests

        [TestCase("2025-02-01", "2025-03-01")] // 1st to next month's 1st
        [TestCase("2025-02-28", "2025-03-01")] // Last day to next month's 1st
        [TestCase("2025-03-01", "2025-04-01")] // 1st to next month's 1st
        [TestCase("2024-02-29", "2024-03-01")] // Leap day to next month's 1st
        [TestCase("2025-12-31", "2026-01-01")] // End of year to next year's 1st
        public void GetNextPayoutDate_Monthly_Returns_Correct_Next_Payout(string todayStr, string expectedPayoutStr)
        {
            // Arrange
            var merchant = new Merchant {PayoutFrequency = "monthly"};
            DateTime today = DateTime.Parse(todayStr);
            DateTime expectedPayout = DateTime.Parse(expectedPayoutStr).ToUtcDate();

            // Act
            DateTime actualPayout = merchant.GetNextPayoutDate(today);

            // Assert
            ClassicAssert.AreEqual(expectedPayout, actualPayout);
        }

        #endregion

        #region Invalid Payout Frequency Tests

        [TestCase("invalidFrequency")]
        [TestCase("")]
        [TestCase(null)]
        public void GetNextPayoutDate_InvalidFrequency_ThrowsException(string payoutFrequency)
        {
            // Arrange
            var merchant = new Merchant {PayoutFrequency = payoutFrequency};
            DateTime today = DateTime.UtcNow;

            // Act & Assert
            if (string.IsNullOrEmpty(payoutFrequency))
            {
                // Default to MONTHLY, as per ToPayoutFrequencyType implementation
                DateTime expectedPayout = new DateTime(today.Year, today.Month, 1).AddMonths(1).ToUtcDate();
                DateTime actualPayout = merchant.GetNextPayoutDate(today);
                ClassicAssert.AreEqual(expectedPayout, actualPayout);
            }
            else
            {
                Assert.Throws<ArgumentOutOfRangeException>(() => merchant.GetNextPayoutDate(today));
            }
        }

        #endregion
    }
}