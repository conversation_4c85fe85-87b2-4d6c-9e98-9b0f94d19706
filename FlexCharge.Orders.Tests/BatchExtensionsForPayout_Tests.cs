using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Contracts.Commands.Orders;
using FlexCharge.Orders.Entities;
using FlexCharge.Orders.Entities.Extensions;
using FlexCharge.Orders.Services.BatchServices.Batches;
using FlexCharge.Orders.Services.PayoutServices;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;

namespace FlexCharge.Orders.Tests;

[TestFixture]
public class BatchExtensionsForPayout_Tests
{
    private PostgreSQLDbContext _dbContext;
    private ReadOnlyPostgreSQLDbContext _readOnlyDbContext;
    private BatchService _batchService;
    private Mock<IHttpContextAccessor> _mockHttpContextAccessor;


    [Test]
    public void CalculatePayoutAmount_ReturnsCorrectValue()
    {
        // Arrange
        var batch = new Batch
        {
            TotalAmount = 10000,
            FlexChargeFeesAmount = 200,
            FlexChargeChargebackFeesAmount = 50,
            FlexChargeReturnFeesAmount = 75,
            ChargebacksAmount = 300,
            ReturnsAmount = 100,
            Reserve = 250,
            ReserveRelease = 100,
            ReserveUtilization = 200,
            Adjustment = 25
        };

        // Act
        var result = batch.CalculatePayoutAmount();

        ClassicAssert.AreEqual(9350, result);
    }

    [Test]
    public async Task AddCompletedOrder_OrderIsCompleted_UpdatesBatchCorrectly()
    {
        // Arrange
        var batch = new Batch()
        {
            PayoutStatus = nameof(BatchStatus.UNPROCESSED),
            BatchRecords = new List<BatchRecord>()
        };

        var order = new Order
            {Id = Guid.NewGuid(), Amount = 500, StatusCategory = nameof(OrderStatusCategory.completed)};
        var merchantFees = new List<MerchantFee>
        {
            new()
            {
                Name = "Percentage",
                FeeType = nameof(FeeType.PERCENTAGE),
                ChargeType = nameof(ChargeType.PER_TRANSACTION),
                Amount = 200,
                MinimumFeeAmount = 0,
                IsActive = true
            }
        };

        // Act
        batch.AddCompletedOrder(order, merchantFees);

        // Assert
        ClassicAssert.AreEqual(500, batch.TotalAmount);
        ClassicAssert.AreEqual(1, batch.TotalCount);
        ClassicAssert.AreEqual(10, batch.FlexChargeFeesAmount,
            "2% of 500 => 10 in fees");
        ClassicAssert.AreEqual(1, batch.FlexChargeFeesCount,
            "Fee > 0 => increments FlexChargeFeesCount");

        ClassicAssert.AreEqual(nameof(BatchStatus.UNPROCESSED), batch.PayoutStatus,
            "Should remain default. The extension sets FAILED only if error occurs.");
        // Actually, the code sets PayoutStatus to FAILED only if the order is not completed.
        // If everything is fine, it doesn't set PayoutStatus at all, but let's check what it started at.
        // If default is null, it should remain null. We'll check for null.
        // Adjust if your real code sets a default to something else.

        // Check the batch record existence
        var orderRecord = batch.BatchRecords.SingleOrDefault(r => r.Type == nameof(BatchRecordTypes.Order));
        ClassicAssert.IsNotNull(orderRecord, "Should have an 'Order' batch record");
        ClassicAssert.AreEqual(order.Id, orderRecord.RelatedOrderId);

        var feeRecord = batch.BatchRecords.SingleOrDefault(r => r.Type == nameof(BatchRecordTypes.FlexFee));
        ClassicAssert.IsNotNull(feeRecord, "Should have a 'FlexFee' record for the processing fee");
        ClassicAssert.AreEqual(10, feeRecord.Amount);
    }

    [Test]
    public void AddCompletedOrder_OrderIsNotCompleted_ThrowsException()
    {
        // Arrange
        var batch = new Batch()
        {
            BatchRecords = new List<BatchRecord>()
        };
        var order = new Order
            {Id = Guid.NewGuid(), Amount = 500, StatusCategory = nameof(OrderStatusCategory.cancelled)};

        // Act & Assert
        var ex = Assert.Throws<FlexChargeException>(() => batch.AddCompletedOrder(order, new List<MerchantFee>()));
        Assert.That(ex.Message, Does.Contain("because it is not completed"));
        ClassicAssert.AreEqual(nameof(BatchStatus.FAILED), batch.PayoutStatus);
    }

    [Test]
    public void AddReturn_UpdatesReturnsAndFees()
    {
        // Arrange
        var batch = new Batch()
        {
            BatchRecords = new List<BatchRecord>()
        };
        var order = new Order
            {Id = Guid.NewGuid(), Amount = 600, StatusCategory = nameof(OrderStatusCategory.completed)};
        var createdOn = DateTime.Now;
        var merchantFees = new List<MerchantFee>
        {
            new()
            {
                Name = "Percentage",
                FeeType = nameof(FeeType.PERCENTAGE),
                ChargeType = nameof(ChargeType.PER_CHARGEBACK_TRANSACTION),
                Amount = 5,
                MinimumFeeAmount = 0,
                IsActive = true
            },
            new()
            {
                Name = "Chargeback Fee",
                FeeType = nameof(FeeType.FIXED),
                ChargeType = nameof(ChargeType.PER_CHARGEBACK_TRANSACTION),
                Amount = 3500,
                MinimumFeeAmount = 0,
                IsActive = true
            },
            new()
            {
                Name = "Refund Fee",
                FeeType = nameof(FeeType.FIXED),
                ChargeType = nameof(ChargeType.PER_TRANSACTION),
                Amount = 500,
                MinimumFeeAmount = 0,
                IsActive = true
            }
        };

        // Act
        batch.AddReturn(order, createdOn, refundAmount: 200, message: "Test return", merchantFees);

        // Assert
        ClassicAssert.AreEqual(200, batch.ReturnsAmount);
        ClassicAssert.AreEqual(1, batch.ReturnsCount);

        ClassicAssert.AreEqual(500, batch.FlexChargeReturnFeesAmount, "$5 fee for the return");
        ClassicAssert.AreEqual(1, batch.FlexChargeFeesCount, "Fee > 0 => increments FlexChargeFeesCount");

        var returnRecord = batch.BatchRecords.SingleOrDefault(r => r.Type == nameof(BatchRecordTypes.Return));
        ClassicAssert.IsNotNull(returnRecord, "Return record was not created.");
        ClassicAssert.AreEqual(200, returnRecord.Amount);
        ClassicAssert.AreEqual("Test return", returnRecord.Description);

        var feeRecord = batch.BatchRecords.SingleOrDefault(r => r.Type == nameof(BatchRecordTypes.FlexReturnFee));
        ClassicAssert.IsNotNull(feeRecord, "Return fee record was not created.");
        ClassicAssert.AreEqual(500, feeRecord.Amount);
    }

    [Test]
    public void AddChargeback_UpdatesChargebackAndFees()
    {
        // Arrange
        var batch = new Batch()
        {
            BatchRecords = new List<BatchRecord>()
        };
        var order = new Order
            {Id = Guid.NewGuid(), Amount = 1000, StatusCategory = nameof(OrderStatusCategory.completed)};

        var merchantFees = new List<MerchantFee>
        {
            new()
            {
                Name = "Percentage",
                FeeType = nameof(FeeType.PERCENTAGE),
                ChargeType = nameof(ChargeType.PER_CHARGEBACK_TRANSACTION),
                Amount = 300,
                MinimumFeeAmount = 0,
                IsActive = true
            },
            new()
            {
                Name = "Chargeback Fee",
                FeeType = nameof(FeeType.FIXED),
                ChargeType = nameof(ChargeType.PER_CHARGEBACK_TRANSACTION),
                Amount = 3500,
                MinimumFeeAmount = 0,
                IsActive = true
            }
        };

        // Act
        batch.AddChargeback(order, createdOn: DateTime.Now, message: "CB", merchantFees);

        // Assert
        ClassicAssert.AreEqual(1000, batch.ChargebacksAmount);
        ClassicAssert.AreEqual(1, batch.ChargebacksCount);

        var cbRecord = batch.BatchRecords
            .SingleOrDefault(r => r.Type == nameof(BatchRecordTypes.Chargeback));
        ClassicAssert.IsNotNull(cbRecord, "Chargeback record was not created.");
        ClassicAssert.AreEqual(1000, cbRecord.Amount);

        ClassicAssert.AreEqual(3500, batch.FlexChargeChargebackFeesAmount, "Fixed chargeback fee of $35 was added.");

        var feeRecord = batch.BatchRecords
            .SingleOrDefault(r => r.Type == nameof(BatchRecordTypes.FlexChargebackFee));
        ClassicAssert.IsNotNull(feeRecord, "Chargeback fee record was not created.");
        ClassicAssert.AreEqual(3500, feeRecord.Amount);
    }

    [Test]
    public void AddChargeback_IfChargebackAlreadyExists_NoDuplicate()
    {
        // Arrange
        var batch = new Batch() {BatchRecords = new List<BatchRecord>()};
        var order = new Order
            {Id = Guid.NewGuid(), Amount = 500, StatusCategory = nameof(OrderStatusCategory.completed)};
        var merchantFees = new List<MerchantFee>
        {
            new()
            {
                Name = "Chargeback Fee",
                FeeType = nameof(FeeType.FIXED),
                ChargeType = nameof(ChargeType.PER_CHARGEBACK_TRANSACTION),
                Amount = 3500,
                MinimumFeeAmount = 0,
                IsActive = true
            }
        };

        // First time adding chargeback
        batch.AddChargeback(order, null, "First CB", merchantFees);

        // Act: try adding the same order's chargeback again
        batch.AddChargeback(order, null, "Second CB Attempt", merchantFees);

        // Assert
        var cbRecords = batch.BatchRecords
            .Where(r => r.Type == nameof(BatchRecordTypes.Chargeback)).ToList();

        // We expect only one record for the chargeback
        ClassicAssert.AreEqual(1, cbRecords.Count, "Should not add duplicate chargeback record if it already exists.");
    }

    [Test]
    public void ApplyAdjustment_WhenBatchTypeIsNotFISC_ThrowsException()
    {
        // Arrange
        var batch = new Batch
        {
            BatchRecords = new List<BatchRecord>(),
            BatchType = "NON_FISC"
        };

        // Act & Assert
        var ex = Assert.Throws<FlexChargeException>(() => batch.ApplyAdjustment(DirectionEnum.Credit, 100));
        Assert.That(ex.Message, Does.Contain("Adjustment is not supported for this batch type"));
    }

    [Test]
    public void ApplyAdjustment_WhenBatchTypeIsFISC_AddsOrUpdatesAdjustmentRecord()
    {
        // Arrange
        var batch = new Batch
        {
            BatchRecords = new List<BatchRecord>(),
            BatchType = nameof(FIMovementType.FISC)
        };

        // Act
        batch.ApplyAdjustment(DirectionEnum.Credit, 200);

        // Assert
        ClassicAssert.AreEqual(200, batch.Adjustment);
        var record = batch.BatchRecords.SingleOrDefault(r => r.Type == nameof(BatchRecordTypes.BatchAdjustment));
        ClassicAssert.IsNotNull(record);
        ClassicAssert.AreEqual(200, record.Amount);
        ClassicAssert.AreEqual(nameof(DirectionEnum.Credit), record.Direction);

        // Now apply a negative adjustment
        batch.ApplyAdjustment(DirectionEnum.Debit, 50);

        ClassicAssert.AreEqual(-50, batch.Adjustment, "ApplyAdjustment sets batch.Adjustment to last value set.");
        record = batch.BatchRecords.SingleOrDefault(r => r.Type == nameof(BatchRecordTypes.BatchAdjustment));
        ClassicAssert.AreEqual(50, record.Amount);
        ClassicAssert.AreEqual(nameof(DirectionEnum.Debit), record.Direction);
    }

    [Test]
    public void ApplyReserve_AddsReserveAndRecord()
    {
        // Arrange
        var batch = new Batch()
        {
            BatchType = nameof(FIMovementType.FISC),
            BatchRecords = new List<BatchRecord>(),
        };
        var orderId = Guid.NewGuid();

        // Act
        var reserveId = batch.ApplyReserve(orderId, 300, "Reserve 300");

        // Assert
        ClassicAssert.AreEqual(300, batch.Reserve);
        ClassicAssert.AreEqual(1, batch.ReserveCount);

        var record = batch.BatchRecords.SingleOrDefault(r => r.Id == reserveId);
        ClassicAssert.IsNotNull(record);
        ClassicAssert.AreEqual(nameof(BatchRecordTypes.ReserveHold), record.Type);
        ClassicAssert.AreEqual(nameof(DirectionEnum.Debit), record.Direction);
        ClassicAssert.AreEqual(300, record.Amount);
        ClassicAssert.AreEqual(orderId, record.RelatedOrderId);
        ClassicAssert.AreEqual("Reserve 300", record.Description);
    }

    [Test]
    public void ApplyReserve_IfAlreadyExists_ReturnsExistingId()
    {
        // Arrange
        var batch = new Batch()
        {
            BatchType = nameof(FIMovementType.FISC),
            BatchRecords = new List<BatchRecord>()
        };
        var orderId = Guid.NewGuid();

        var firstId = batch.ApplyReserve(orderId, 300, "First Reserve");
        var secondId = batch.ApplyReserve(orderId, 500, "Second Reserve Attempt");

        // Assert
        // Because an existing record for that orderId is found, it reuses the same record Id
        // Id isnt created because ctor is private
        ClassicAssert.AreEqual(firstId, secondId);

        // Also ensures that Reserve is not incremented again
        ClassicAssert.AreEqual(300, batch.Reserve, "Reserve should not be re-incremented if record already exists.");
        ClassicAssert.AreEqual(1, batch.ReserveCount);
    }

    [Test]
    public void UtilizeReserve_AddsCreditRecordAndUpdatesReserve()
    {
        // Arrange
        var batch = new Batch()
        {
            BatchType = nameof(FIMovementType.FISC),
            BatchRecords = new List<BatchRecord>()
        };
        var orderId = Guid.NewGuid();

        // Act
        var utilId = batch.AddReserveUtilization(orderId, 400, "Utilizing 400");

        // Assert
        ClassicAssert.AreEqual(400, batch.ReserveUtilization);
        ClassicAssert.AreEqual(1, batch.ReserveUtilizationCount);

        var record = batch.BatchRecords.SingleOrDefault(r => r.Id == utilId);
        ClassicAssert.IsNotNull(record);
        ClassicAssert.AreEqual(nameof(BatchRecordTypes.ReserveUtilization), record.Type,
            "Method uses 'ReserveUtilization' as the batchRecordTypes enum in the created record.");
        ClassicAssert.AreEqual(nameof(DirectionEnum.Credit), record.Direction);
        ClassicAssert.AreEqual(400, record.Amount);
    }

    [Test]
    public void AddReleaseReserve_AddsReleaseRecordAndUpdatesReserveRelease()
    {
        // Arrange
        var batch = new Batch()
        {
            BatchType = nameof(FIMovementType.FISC),
            BatchRecords = new List<BatchRecord>()
        };
        var orderId = Guid.NewGuid();

        // Act
        var releaseId = batch.AddReleaseReserve(orderId, 200, "Releasing 200");

        // Assert
        ClassicAssert.AreEqual(200, batch.ReserveRelease,
            "ReserveRelease accumulates the total release amounts.");
        ClassicAssert.AreEqual(1, batch.ReserveReleaseCount);

        var record = batch.BatchRecords.SingleOrDefault(r => r.Id == releaseId);
        ClassicAssert.IsNotNull(record);
        ClassicAssert.AreEqual(nameof(BatchRecordTypes.ReserveRelease), record.Type);
        ClassicAssert.AreEqual(nameof(DirectionEnum.Credit), record.Direction);
        ClassicAssert.AreEqual(200, record.Amount);
    }

    [Test]
    public void CancelReserve_AddsReserveCancelRecordAndIncrementsReserveRelease()
    {
        // Arrange
        var batch = new Batch()
        {
            BatchType = nameof(FIMovementType.FISC),
            BatchRecords = new List<BatchRecord>()
        };
        var orderId = Guid.NewGuid();

        // Act
        var cancelId = batch.CancelReserve(orderId, 150, "Cancel 150");

        // Assert
        ClassicAssert.AreEqual(150, batch.ReserveRelease);
        ClassicAssert.AreEqual(1, batch.ReserveReleaseCount);

        var record = batch.BatchRecords.SingleOrDefault(r => r.Id == cancelId);
        ClassicAssert.IsNotNull(record);
        ClassicAssert.AreEqual(nameof(BatchRecordTypes.ReserveCancel), record.Type);
        ClassicAssert.AreEqual(nameof(DirectionEnum.Credit), record.Direction);
        ClassicAssert.AreEqual(150, record.Amount);
    }

    [Test]
    public void UtilizeReserve_ExistingRecord_ReturnsExistingRecordId()
    {
        // Arrange
        var batch = new Batch()
        {
            BatchType = nameof(FIMovementType.FISC),
            BatchRecords = new List<BatchRecord>()
        };

        var orderId = Guid.NewGuid();

        // Create an existing ReserveUtilization record
        var existingRecord = BatchRecord.Create(
            batch.Id,
            BatchRecordTypes.ReserveUtilization,
            DirectionEnum.Credit,
            300,
            "Utilizing 300 reserve",
            orderId
        );

        batch.BatchRecords.Add(existingRecord);

        // Act
        var resultId = batch.AddReserveUtilization(orderId, 500, "Attempt to utilize again");

        // Assert
        // Because a record already exists, we expect the same record's Id
        ClassicAssert.AreEqual(existingRecord.Id, resultId,
            "Should return existing record's Id rather than creating a new one.");

        // Ensure no new records were added
        ClassicAssert.AreEqual(1, batch.BatchRecords.Count,
            "No additional records should be added if one already exists.");
        ClassicAssert.AreEqual(0, batch.Reserve,
            "Reserve should not change if we don't create a new record.");
        ClassicAssert.AreEqual(0, batch.ReserveCount,
            "ReserveCount should not increment if we don't create a new record.");
    }

    [Test]
    [TestCase(null, 150, "General utilization")]
    public void AddReserveUtilization_Should_Add_Record_With_No_RelatedOrderId(Guid? relatedOrderId, int reserveAmount,
        string description)
    {
        var batch = new Batch()
        {
            BatchType = nameof(FIMovementType.FISC),
            BatchRecords = new List<BatchRecord>()
        };

        // Act
        Guid recordId = batch.AddReserveUtilization(relatedOrderId, reserveAmount, description);

        // Assert
        ClassicAssert.AreEqual(reserveAmount, batch.ReserveUtilization,
            "ReserveUtilization should be increased by reserveAmount.");
        ClassicAssert.AreEqual(1, batch.ReserveUtilizationCount, "ReserveUtilizationCount should be incremented.");

        var record = batch.BatchRecords.Find(r => r.Id == recordId);
        ClassicAssert.IsNotNull(record, "ReserveUtilization record should be added to BatchRecords.");
        ClassicAssert.AreEqual(BatchRecordTypes.ReserveUtilization.ToString(), record.Type,
            "Record type should be ReserveUtilization.");
        ClassicAssert.AreEqual(DirectionEnum.Credit.ToString(), record.Direction, "Record direction should be Credit.");
        ClassicAssert.AreEqual(reserveAmount, record.Amount, "Record amount should match reserveAmount.");
        ClassicAssert.AreEqual(description, record.Description, "Record description should match.");
        ClassicAssert.AreEqual(Guid.Empty, record.RelatedOrderId, "Record should have Guid.Empty as RelatedOrderId.");
    }

    [Test]
    public void RemoveReserveUtilization_Should_Remove_All_ReserveUtilization_Records_And_Update_Reserve()
    {
        // Arrange
        var batch = new Batch()
        {
            BatchType = nameof(FIMovementType.FISC),
            BatchRecords = new List<BatchRecord>()
        };

        // Arrange
        Guid orderId1 = Guid.NewGuid();
        Guid orderId2 = Guid.NewGuid();

        batch.AddReserveUtilization(orderId1, 200, "Utilization for order 1");
        batch.AddReserveUtilization(orderId2, 300, "Utilization for order 2");

        // Act
        batch.RemoveReserveUtilization();

        // Assert
        ClassicAssert.AreEqual(0, batch.ReserveUtilization,
            "ReserveUtilization should be reset to zero after removal.");
        ClassicAssert.AreEqual(0, batch.ReserveUtilizationCount,
            "ReserveUtilizationCount should be reset to zero after removal.");
        ClassicAssert.IsFalse(batch.BatchRecords.Exists(r => r.Type == BatchRecordTypes.ReserveUtilization.ToString()),
            "All ReserveUtilization records should be removed.");
    }

    [Test]
    public void UtilizeReserve_NoExistingRecord_CreatesNewRecordAndUpdatesReserve()
    {
        // Arrange
        var batch = new Batch()
        {
            BatchType = nameof(FIMovementType.FISC),
            BatchRecords = new List<BatchRecord>()
        };

        var orderId = Guid.NewGuid();

        // No existing ReserveUtilization record for that orderId
        // Act
        var resultId = batch.AddReserveUtilization(orderId, 400, "Utilizing 400 reserve");

        // Assert
        // 1) A new record is created
        ClassicAssert.AreEqual(1, batch.BatchRecords.Count, "Should create a new record.");
        var record = batch.BatchRecords.Single();
        ClassicAssert.AreEqual(resultId, record.Id);
        ClassicAssert.AreEqual(nameof(BatchRecordTypes.ReserveUtilization), record.Type,
            "Method uses 'ReserveUtilization' as the batchRecordTypes enum in the created record.");
        ClassicAssert.AreEqual(nameof(DirectionEnum.Credit), record.Direction);
        ClassicAssert.AreEqual(400, record.Amount);
        ClassicAssert.AreEqual(orderId, record.RelatedOrderId);
        ClassicAssert.AreEqual("Utilizing 400 reserve", record.Description);

        // 2) Reserve and ReserveCount are updated
        ClassicAssert.AreEqual(400, batch.ReserveUtilization,
            "ReserveUtilization should increment by the 'reserveAmount' for a new record.");
        ClassicAssert.AreEqual(1, batch.ReserveUtilizationCount,
            "ReserveUtilizationCount should increment by 1 for a new record.");
    }

    [Test]
    public void RemoveReserveUtilization_Should_Handle_No_ReserveUtilization_Gracefully()
    {
        // Arrange
        var batch = new Batch()
        {
            BatchType = nameof(FIMovementType.FISC),
            BatchRecords = new List<BatchRecord>()
        };


        // Act & Assert
        Assert.DoesNotThrow(() => batch.RemoveReserveUtilization(),
            "Removing reserve utilization when none exist should not throw an exception.");

        ClassicAssert.AreEqual(0, batch.ReserveUtilization, "Reserve utilization should remain zero.");
        ClassicAssert.AreEqual(0, batch.ReserveUtilizationCount, "Reserve utilization count should remain zero.");
    }

    [Test]
    public void IsFinished_WhenBatchEndDateIsBeforeToday_ReturnsTrue()
    {
        // Arrange
        var batch = new Batch
        {
            BatchRecords = new List<BatchRecord>(),
            To = DateTime.Today.AddDays(-1) // ended yesterday
        };

        // Act
        var finished = batch.IsFinished();

        // Assert
        ClassicAssert.IsTrue(finished, "If batch 'To' is before today, it should be finished.");
    }

    [Test]
    public void IsFinished_WhenBatchEndDateIsTodayOrAfterToday_ReturnsFalse()
    {
        // Arrange
        var batch1 = new Batch {To = DateTime.Today}; // ends today
        var batch2 = new Batch {To = DateTime.Today.AddDays(1)}; // ends tomorrow

        // Act
        var finished1 = batch1.IsFinished();
        var finished2 = batch2.IsFinished();

        // Assert
        ClassicAssert.IsFalse(finished1, "If batch 'To' is today, it's not finished yet.");
        ClassicAssert.IsFalse(finished2, "If batch 'To' is in the future, it's not finished yet.");
    }
}