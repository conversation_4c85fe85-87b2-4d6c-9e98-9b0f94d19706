using FlexCharge.Common.Shared.Adapters;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Common;
using FlexCharge.Eligibility.Adapters.Stripe.EventHandlers;
using FlexCharge.Eligibility.Adapters.Stripe.Services.StripeService;
using Flexcharge.Eligibility.StripeAdapter.Ingestion;
using FluentValidation;
using MassTransit;
using Stripe;

namespace FlexCharge.Eligibility.Adapters.Stripe.Ingestion.EventHandlers.Invoices2;

class InvoiceDeletedEventHandler : StripeWebhookEventHandlerBase<Invoice>
{
    public override string EventType => EventTypes.InvoiceDeleted;

    protected override async Task HandleEventInternalAsync()
    {
        using var workspan = Workspan.Start<InvoiceDeletedEventHandler>();

        var invoice = EventData;

        var merchantConfiguration = await GetMerchantConfigurationAsync(OAuthProvider.Stripe, Context.StripeAccount);

        workspan
            .Baggage("Mid", merchantConfiguration)
            .Baggage("InvoiceId", invoice.Id);

        if (!invoice.Metadata.TryGetValue(FlexFactorMetadata.FlexFactorOrderId, out var orderIdString))
        {
            workspan.Log.Information("No OrderId found in Stripe metadata");
            return; //!!!
        }

        workspan
            .Baggage("OrderId", orderIdString);

        var orderId = Guid.Parse(orderIdString);

        var publisher = GetRequiredService<IPublishEndpoint>();

        await PublishInvoiceDeletedEvent(publisher, merchantConfiguration.Mid, orderId, invoice.Id);
    }

    private static async Task PublishInvoiceDeletedEvent(IPublishEndpoint publisher, Guid mid, Guid orderId,
        string invoiceId)
    {
        await publisher.Publish(new ExternalProviderInvoiceDeletedEvent(mid, orderId, invoiceId));
    }
}