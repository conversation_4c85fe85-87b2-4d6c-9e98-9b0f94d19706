using FlexCharge.Common.Shared.Adapters;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Common;
using FlexCharge.Eligibility.Adapters.Common.Ingestion.Webhooks;
using FlexCharge.Eligibility.Adapters.Stripe;
using MassTransit;
using Stripe;
using Event = Stripe.Event;

namespace Flexcharge.Eligibility.StripeAdapter.Ingestion;

abstract class StripeWebhookEventHandlerBase<TEventData>
    : AdapterWebhookEventHandlerBase<Event, TEventData, StripeEventContext> where TEventData : StripeEntity<TEventData>
{
    protected override string ProviderName => ProviderDescription.ProviderName;

    protected override Func<Event, string>? UniqueEventIdSelector => evt => evt.Id.ToString();
    protected override Func<Event, Guid?>? OrderIdSelector => evt => GetFlxOrderFromMetaId(evt);

    protected override TEventData? EventData => (TEventData) Event?.Data?.Object;

    private Guid? GetFlxOrderFromMetaId(Event evt)
    {
        IHasMetadata? metadata = evt.Data.Object as IHasMetadata;

        string orderIdString = metadata?.Metadata?.GetValueOrDefault(FlexFactorMetadata.FlexFactorOrderId);

        if (orderIdString == null)
            return null;

        if (!Guid.TryParse(orderIdString, out Guid orderId))
        {
            Workspan.Current?
                .Tag("OrderIdString", orderIdString)
                .Log.Fatal("Failed to parse OrderId from metadata");

            return null;
        }

        return orderId;
    }
}