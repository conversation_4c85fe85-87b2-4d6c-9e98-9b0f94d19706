{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:5091", "sslPort": 0}}, "$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": false, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "DB_HOST": "localhost", "DB_PORT": "5432", "DB_DATABASE": "fc.audit", "DB_USERNAME": "audit-service-staging", "DB_PASSWORD": "11111", "ACTIVITY_DB_HOST": "localhost", "ACTIVITY_DB_PORT": "5432", "ACTIVITY_DB_DATABASE": "fc.activity", "ACTIVITY_DB_USERNAME": "activity-service-staging", "ACTIVITY_DB_PASSWORD": "11111", "SNS_IAM_REGION": "us-east-1", "SNS_IAM_ACCESS_KEY": "********************", "SNS_IAM_SECRET_KEY": "uPO48OgTDXReduu6gz7QzqtuQalSgLnkB6ZHDRtW"}}, "FlexCharge.Audit": {"commandName": "Project", "launchBrowser": false, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "DB_HOST": "localhost", "DB_PORT": "5432", "DB_DATABASE": "fc.audit", "DB_USERNAME": "audit-service-staging", "DB_PASSWORD": "11111", "ACTIVITY_DB_HOST": "localhost", "ACTIVITY_DB_PORT": "5432", "ACTIVITY_DB_DATABASE": "fc.activity", "ACTIVITY_DB_USERNAME": "activity-service-staging", "ACTIVITY_DB_PASSWORD": "11111", "SNS_IAM_REGION": "us-east-1", "SNS_IAM_ACCESS_KEY": "********************", "SNS_IAM_SECRET_KEY": "uPO48OgTDXReduu6gz7QzqtuQalSgLnkB6ZHDRtW"}, "applicationUrl": "https://localhost:5120;http://localhost:5121"}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": false, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/swagger", "publishAllPorts": true, "environmentVariables": {"DB_HOST": "host.docker.internal", "DB_PORT": "5432", "DB_DATABASE": "fc.audit", "DB_USERNAME": "audit-service-staging", "DB_PASSWORD": "11111", "ACTIVITY_DB_HOST": "localhost", "ACTIVITY_DB_PORT": "5432", "ACTIVITY_DB_DATABASE": "fc.activity", "ACTIVITY_DB_USERNAME": "activity-service-staging", "ACTIVITY_DB_PASSWORD": "11111", "SNS_IAM_REGION": "us-east-1", "SNS_IAM_ACCESS_KEY": "********************", "SNS_IAM_SECRET_KEY": "uPO48OgTDXReduu6gz7QzqtuQalSgLnkB6ZHDRtW"}}}}