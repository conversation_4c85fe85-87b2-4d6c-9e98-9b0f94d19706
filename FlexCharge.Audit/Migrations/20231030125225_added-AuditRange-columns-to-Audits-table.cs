using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Audit.Migrations
{
    /// <inheritdoc />
    public partial class addedAuditRangecolumnstoAuditstable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Severity",
                table: "Audits",
                newName: "ProblemsSeverity");

            migrationBuilder.AddColumn<DateTime>(
                name: "AuditRangeEnd",
                table: "Audits",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "AuditRangeStart",
                table: "Audits",
                type: "timestamp with time zone",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AuditRangeEnd",
                table: "Audits");

            migrationBuilder.DropColumn(
                name: "AuditRangeStart",
                table: "Audits");

            migrationBuilder.RenameColumn(
                name: "ProblemsSeverity",
                table: "Audits",
                newName: "Severity");
        }
    }
}
