using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Audit.Migrations
{
    /// <inheritdoc />
    public partial class addedAuditstable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_AuditedCorrelationErrors_CorrelationId_Message",
                table: "AuditedCorrelationErrors");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "AuditedCorrelationErrors",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<Guid>(
                name: "CorrelationId",
                table: "AuditedCorrelationErrors",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AddColumn<Guid>(
                name: "AuditId",
                table: "AuditedCorrelationErrors",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateTable(
                name: "Audits",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CorrelationId = table.Column<Guid>(type: "uuid", nullable: true),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: true),
                    Severity = table.Column<int>(type: "integer", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Audits", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AuditedCorrelationErrors_AuditId",
                table: "AuditedCorrelationErrors",
                column: "AuditId");

            migrationBuilder.AddForeignKey(
                name: "FK_AuditedCorrelationErrors_Audits_AuditId",
                table: "AuditedCorrelationErrors",
                column: "AuditId",
                principalTable: "Audits",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AuditedCorrelationErrors_Audits_AuditId",
                table: "AuditedCorrelationErrors");

            migrationBuilder.DropTable(
                name: "Audits");

            migrationBuilder.DropIndex(
                name: "IX_AuditedCorrelationErrors_AuditId",
                table: "AuditedCorrelationErrors");

            migrationBuilder.DropColumn(
                name: "AuditId",
                table: "AuditedCorrelationErrors");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "AuditedCorrelationErrors",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "CorrelationId",
                table: "AuditedCorrelationErrors",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_AuditedCorrelationErrors_CorrelationId_Message",
                table: "AuditedCorrelationErrors",
                columns: new[] { "CorrelationId", "Message" },
                unique: true);
        }
    }
}
