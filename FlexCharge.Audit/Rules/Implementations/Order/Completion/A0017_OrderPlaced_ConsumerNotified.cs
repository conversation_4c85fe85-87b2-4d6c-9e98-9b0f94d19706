using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Eligibility;
using FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Eligibility.Evaluation;
using FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Eligibility.Processing;
using FlexCharge.Common.Shared.Activities;
using NUnit.Framework;

namespace FlexCharge.Audit.Rules.Implementations.Alerts;

public class A0017_OrderPlaced_ConsumerNotified : TestableRuleBase
{
    public override void Define()
    {
        // Matching both EligibilityError_Fact and EligibilityError_TriggerFact
        // just to make sure that the rule is fired when the EligibilityError_Fact is created
        // event if the EligibilityError_TriggerFact is not created somehow
        When()
            .Match<Order_Placed_Fact>()
            .And(x => x
                .Not<ConsumerNotification_NotificationSent_Fact>()
                .Not<ConsumerNotification_NotificationDisabled_Fact>()
                .Not<ConsumerNotification_NotificationFailed_Fact>()
            );

        Then()
            .CriticalError(() => "Order is placed without notifying the consumer");
    }

    #region Unit Testing

    [Test]
    [ExcludeFromCodeCoverage]
    public override async Task Test_Positive()
    {
        //Arrange
        await CreateActivityFromFactAsync<Order_Placed_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("Fee", 20)
            ));

        //Act
        Session.Fire();

        //Assert
        AssertFiredOnce();
    }

    [Test]
    [ExcludeFromCodeCoverage]
    public override async Task Test_Negative()
    {
        //Arrange

        //Act
        Session.Fire();

        //Assert
        AssertDidNotFire();
    }

    [Test]
    [ExcludeFromCodeCoverage]
    public async Task Test_Negative_NotificationSent()
    {
        //Arrange
        await CreateActivityFromFactAsync<Order_Placed_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("Fee", 20)
            ));

        await CreateActivityFromFactAsync<ConsumerNotification_NotificationSent_Fact>();

        //Act
        Session.Fire();

        //Assert
        AssertDidNotFire();
    }

    [Test]
    [ExcludeFromCodeCoverage]
    public async Task Test_Negative_NotificationDisabled()
    {
        //Arrange
        await CreateActivityFromFactAsync<Order_Placed_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("Fee", 20)
            ));

        await CreateActivityFromFactAsync<ConsumerNotification_NotificationDisabled_Fact>();

        //Act
        Session.Fire();

        //Assert
        AssertDidNotFire();
    }

    [Test]
    [ExcludeFromCodeCoverage]
    public async Task Test_Negative_NotificationFailed()
    {
        //Arrange
        await CreateActivityFromFactAsync<Order_Placed_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("Fee", 20)
            ));

        await CreateActivityFromFactAsync<ConsumerNotification_NotificationFailed_Fact>();

        //Act
        Session.Fire();

        //Assert
        AssertDidNotFire();
    }

    #endregion
}