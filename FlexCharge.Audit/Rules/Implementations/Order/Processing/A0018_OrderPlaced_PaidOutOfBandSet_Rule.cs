using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Eligibility;
using FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Eligibility.Evaluation;
using FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Eligibility.Processing;
using FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.ExternalProviders;
using FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Payments.Transactions;
using FlexCharge.Common.Shared.Activities;
using NUnit.Framework;

namespace FlexCharge.Audit.Rules.Implementations.Order.Processing;

public class A0018_OrderPlaced_PaidOutOfBandSet_Rule : TestableRuleBase
{
    Order_Placed_Fact _orderPlaced = null;

    IEnumerable<Payments_ChargePayment_Succeeded_Fact> _chargePayments = null;
    IEnumerable<ExternalProvider_InvoiceProcessing_InvoicePaid_Fact> _externallyPaidInvoices = null;

    private int _chargedOrPaidExternallyAmount = 0;

    public override void Define()
    {
        When()
            .Not<ProductionTestOrder_Fact>() // Production test orders can be captured without
            .Match(() => _orderPlaced)
            .Collect(() => _chargePayments)
            .Collect(() => _externallyPaidInvoices, x => x.PaidExternally)
            .Let(() => _chargedOrPaidExternallyAmount, () =>
                _chargePayments.Sum(a => a.Amount)
                + _externallyPaidInvoices.Sum(a => a.Amount))
            .Having(() =>
                _orderPlaced.PaidOutOfBand != (_chargedOrPaidExternallyAmount > 0)
            )
            ;

        Then()
            .CriticalError(() => "Paid out of band flag should be set if charge external token is successful");
    }

    #region Unit Testing

    [Test]
    [ExcludeFromCodeCoverage]
    public override async Task Test_Positive()
    {
        //Arrange
        await CreateActivityFromFactAsync<Payments_ChargePayment_Succeeded_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("DiscountAmount", 0)
            ));

        await CreateActivityFromFactAsync<Order_Placed_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 1000)
                .SetValue("Fee", 200)
                .SetValue("PaidOutOfBand", false)
            ));

        //Act
        Session.Fire();

        //Assert
        AssertFiredOnce();
    }

    [Test]
    [ExcludeFromCodeCoverage]
    public override async Task Test_Negative()
    {
        //Arrange
        await CreateActivityFromFactAsync<Payments_ChargePayment_Succeeded_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("DiscountAmount", 0)
            ));

        await CreateActivityFromFactAsync<Order_Placed_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 1000)
                .SetValue("Fee", 200)
                .SetValue("PaidOutOfBand", true)
            ));

        //Act
        Session.Fire();

        //Assert
        AssertDidNotFire();
    }

    [Test]
    [ExcludeFromCodeCoverage]
    public async Task Test_Positive_WithExternalProviderInvoicePaidExternally()
    {
        //Arrange
        await CreateActivityFromFactAsync<ExternalProvider_InvoiceProcessing_InvoicePaid_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 1000)
                .SetValue("PaidExternally", true)
            ));

        await CreateActivityFromFactAsync<Order_Placed_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 1000)
                .SetValue("Fee", 200)
                .SetValue("PaidOutOfBand", false)
            ));

        //Act
        Session.Fire();

        //Assert
        AssertFiredOnce();
    }

    [Test]
    [ExcludeFromCodeCoverage]
    public async Task Test_Negative_WithExternalProviderInvoicePaidExternally()
    {
        //Arrange
        await CreateActivityFromFactAsync<ExternalProvider_InvoiceProcessing_InvoicePaid_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 1000)
                .SetValue("PaidExternally", true)
            ));

        await CreateActivityFromFactAsync<Order_Placed_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 1000)
                .SetValue("Fee", 200)
                .SetValue("PaidOutOfBand", true)
            ));

        //Act
        Session.Fire();

        //Assert
        AssertDidNotFire();
    }

    [Test]
    [ExcludeFromCodeCoverage]
    public async Task Test_Positive_WithNoCharges()
    {
        //Arrange
        await CreateActivityFromFactAsync<Payments_AuthorizePayment_Succeeded_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("DiscountAmount", 6)
            ));

        await CreateActivityFromFactAsync<Payments_CapturePayment_Succeeded_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("DiscountAmount", 6)
            ));

        await CreateActivityFromFactAsync<Order_Placed_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("Fee", 200)
                .SetValue("PaidOutOfBand", true)
            ));

        //Act
        Session.Fire();

        //Assert
        AssertFiredOnce();
    }

    [Test]
    [ExcludeFromCodeCoverage]
    public async Task Test_Negative_WithNoCharges()
    {
        //Arrange
        await CreateActivityFromFactAsync<Payments_AuthorizePayment_Succeeded_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("DiscountAmount", 6)
            ));

        await CreateActivityFromFactAsync<Payments_CapturePayment_Succeeded_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("DiscountAmount", 6)
            ));

        await CreateActivityFromFactAsync<Order_Placed_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("Fee", 200)
                .SetValue("PaidOutOfBand", false)
            ));

        //Act
        Session.Fire();

        //Assert
        AssertDidNotFire();
    }

    #endregion
}