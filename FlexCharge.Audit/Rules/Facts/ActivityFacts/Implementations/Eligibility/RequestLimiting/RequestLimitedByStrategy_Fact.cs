using System.Collections.Generic;
using FlexCharge.Audit.DTO;
using FlexCharge.Common.Shared.Activities;

namespace FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Eligibility.Evaluation;

[ActivityFact(ActivityCategories.Eligibility_RequestLimiting,
    "RequestLimitedByStrategy")]
public class RequestLimitedByStrategy_Fact : ActivityWithMetadataFactBase
{
    public string Limiter { get; set; }

    public RequestLimitedByStrategy_Fact(ActivityToAuditDTO data) : base(data)
    {
    }

    public override void SetFactProperties(Dictionary<string, string> meta)
    {
        Limiter = meta["Limiter"];
    }
}