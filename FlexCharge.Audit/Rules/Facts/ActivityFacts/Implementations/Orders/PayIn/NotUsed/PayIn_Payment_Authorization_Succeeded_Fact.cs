// using System.Collections.Generic;
// using FlexCharge.Audit.DTO;
// using FlexCharge.Common.Shared.Activities;
//
// namespace FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Orders.PayIn;
//
// [ActivityFact(ActivityCategories.Orders_PayInProcessing, "PayIn_Payment_Authorization_Succeeded")]
// public class PayIn_Payment_Authorization_Succeeded_Fact : ActivityWithMetadataFactBase
// {
//     public int Amount { get; set; }
//     public int DiscountAmount { get; set; }
//
//     public PayIn_Payment_Authorization_Succeeded_Fact(ActivityToAuditDTO data) : base(data)
//     {
//     }
//
//     public override void SetFactProperties(Dictionary<string, string> meta)
//     {
//         Amount = int.Parse(meta["Amount"]);
//         DiscountAmount = int.Parse(meta["DiscountAmount"]);
//     }
// }

