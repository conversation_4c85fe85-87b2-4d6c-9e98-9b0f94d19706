// using System;
// using System.Collections.Generic;
// using FlexCharge.Audit.DTO;
//
// namespace FlexCharge.Audit.Rules;
//
// public class SessionMatchedSuccessfullyFact : ActivityWithMetadataFactBase
// {
//     public SessionMatchedSuccessfullyFact(
//         ActivityToAuditDTO data)
//         : base(data)
//     {
//     }
//
//     public override void SetFactProperties(Dictionary<string, string> meta)
//     {
//         //nothing
//     }
// }