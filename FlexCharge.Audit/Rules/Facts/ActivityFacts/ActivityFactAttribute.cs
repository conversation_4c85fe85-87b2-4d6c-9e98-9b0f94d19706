using System;

namespace FlexCharge.Audit.Rules
{
    [AttributeUsage(AttributeTargets.Class, Inherited = false, AllowMultiple = false)]
    public class ActivityFactAttribute : Attribute
    {
        public string ActivityCategory { get; }
        public string ActivityName { get; }

        public ActivityFactAttribute(object activityCategory, string activityName)
        {
            ActivityCategory = activityCategory.ToString();
            ActivityName = activityName;
        }
    }
}