using System.Threading;
using FlexCharge.Audit.Entities;
using NRules.RuleModel;

namespace FlexCharge.Audit.Rules.Extensions;

public static class ContextExtensions
{
    static int _linkedFactsCount;

    public static void Problem(this IContext context, Severity severity, string message)
    {
        var auditResultFact = new AuditResultFact(severity, message, 
            context.Rule);

        //Linked facts are facts that are retracted when the parent rule activation is removed.
        //Key should be unique within the rule.
        var uniqueLinkedFactNumber = Interlocked.Increment(ref _linkedFactsCount);

        context.InsertLinked($"__linked_key{uniqueLinkedFactNumber}__", auditResultFact);

        if (uniqueLinkedFactNumber == int.MaxValue)
            Interlocked.Exchange(ref _linkedFactsCount, 0);
    }
}