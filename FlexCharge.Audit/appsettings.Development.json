{"app": {"name": "audit-service", "version": "0.0.1"}, "jwt": {"Provider": "Cognito", "secretKey": "JLBMU2VbJZmt42sUwByUpJJF6Y5mG2gPNU9sQFUpJFcGFJdyKxskR3bxh527kax2UcXHvB", "expiryMinutes": 30, "issuer": "identity-service", "validateLifetime": true, "ValidateAudience": true, "ValidAudience": "4n0i8a4i9o1vk3g3tf64o6blg7", "Region": "us-east-1", "UserPoolId": "us-east-1_rCUpTgXY4", "AppClientId": "4n0i8a4i9o1vk3g3tf64o6blg7"}, "cache": {"connectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "instance": "", "schemaName": "dbo", "tableName": "<PERSON><PERSON>", "BigPayloadCacheConnectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "IdempotencyCacheConnectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "ExternalRequestsCacheConnectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379"}, "dataStream": {"provider": "kinesis"}, "email": {"provider": "sendgrid", "key": "*********************************************************************", "supportEmail": "<EMAIL>", "senderEmail": "<EMAIL>", "senderName": "FlexFactor"}, "sms": {"SID": "", "Token": "", "TwilioPhone": "", "WhatsappPhone": "", "TwilioCompanyName": "", "ServiceSid": "", "TwilioAuthyAPIKey": "", "TwilioVoiceSmsStartUrl": "https://api.authy.com/protected/json/phones/verification/start", "TwilioVoiceSmsChecktUrl": ""}, "serilog": {"consoleEnabled": true, "level": "Information", "path": "../logs/audit-{0}.txt"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "swagger": {"enabled": true, "commentsEnabled": true, "reDocEnabled": false, "name": "v1", "title": "activity-service", "version": "v1", "routePrefix": "", "includeSecurity": true}, "jaeger": {"agentHost": "localhost", "agentPort": 6831}, "backgroundWorkerService": {"executionInterval": 500}, "audit": {"AuditBatchSizeLimit": 10, "AuditEmailRecipientAddress": "<EMAIL>", "EmailSendPeriodInMinutes": 1, "EmailBatchLimit": 10, "AuditPollingIntervalInSeconds": 5, "EmailTemplateId": "d-3934c04fa5454c1397b60f8e3a996fda"}, "AllowedHosts": "*"}