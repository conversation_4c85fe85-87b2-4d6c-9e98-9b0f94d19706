using EntityFramework.Exceptions.Common;
using FlexCharge.Audit.Entities;
using FlexCharge.Audit.Rules;
using FlexCharge.Audit.Services.AuditRules.Config;
using FlexCharge.Common.Shared.Activities;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using NRules;
using NRules.Diagnostics;
using NRules.Extensibility;
using NRules.Fluent;
using NRules.RuleModel;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text.Json;
using System.Threading.Tasks;
using FlexCharge.Audit.Activities;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Logging.LogSuppression;
using FlexCharge.Common.PostgreSql.Interceptors;
using IActivity = FlexCharge.Contracts.Activities.IActivity;
using FlexCharge.Common.Shared.Serialization;
using FlexCharge.Contracts;
using FlexCharge.Utils;
using Serilog.Events;

namespace FlexCharge.Audit.Services.AuditRules
{
    public class AuditorService : IAuditorService

    {
        private PostgreSQLDbContext _dbContext;

        private IServiceScopeFactory _serviceScopeFactory;
        private readonly IPublishEndpoint _publisher;
        private readonly IActivityService _activityService;
        private readonly ExternalAggregatedActivitiesReadOnlyPostgreSQLDbContext _activityDbContext;
        private readonly IOptions<AuditOptions> _auditOptions;

        private static readonly Dictionary<string, (ISessionFactory Factory, List<IRuleDefinition> Rules)>
            _ruleSetToSessionDefinitionMap;

        static AuditorService()
        {
            _ruleSetToSessionDefinitionMap = new();

            CreateDefaultRuleSet();
        }

        public AuditorService(PostgreSQLDbContext dbContext,
            IOptions<AuditOptions> auditOptions,
            IServiceScopeFactory serviceScopeFactory,
            IPublishEndpoint publisher,
            IActivityService activityService,
            ExternalAggregatedActivitiesReadOnlyPostgreSQLDbContext activityDbContext
        )
        {
            _dbContext = dbContext;
            _auditOptions = auditOptions;
            _serviceScopeFactory = serviceScopeFactory;
            _publisher = publisher;
            _activityService = activityService;
            _activityDbContext = activityDbContext;
        }

        private static void CreateDefaultRuleSet()
        {
            //rule => rule.Name.StartsWith("Test") || rule.IsTagged("Test")
            CreateRuleSet("ARS1", rule => true);
        }

        #region Rule Sets

        private static void CreateRuleSet(string ruleSetName, Func<IRuleMetadata, bool> ruleSelectFilter)
        {
            var ruleRepository = new RuleRepository();
            ruleRepository.Load(x => x
                .From(Assembly.GetExecutingAssembly())
                .Where(ruleSelectFilter)
                .To(ruleSetName));

            CreateRuleSet(ruleRepository, ruleSetName);
        }

        private static void CreateRuleSet(RuleRepository ruleRepository, string ruleSetName)
        {
            var allRuleSets = ruleRepository.GetRuleSets();
            var compiler = new RuleCompiler();

            ConcurrentBag<string> errors = new();

            var applicableRuleSets = allRuleSets.Where(x => x.Name == ruleSetName).ToList();
            var sessionFactory = compiler.Compile(applicableRuleSets);

            sessionFactory.ActionInterceptor = new ActionInterceptor(errors);

            //sessionFactory.Events.RuleFiringEvent += (sender, e) => OnRuleFiringEvent(sender, e);
            sessionFactory.Events.AgendaExpressionFailedEvent +=
                (sender, e) => OnAgendaExpressionFailedEvent(sender, e, errors);
            sessionFactory.Events.LhsExpressionFailedEvent +=
                (sender, e) => OnLhsExpressionFailedEvent(sender, e, errors);
            sessionFactory.Events.RhsExpressionFailedEvent +=
                (sender, e) => OnRhsExpressionFailedEvent(sender, e, errors);

            List<IRuleDefinition> rules = new();
            applicableRuleSets.Select(x => x.Rules).ToList().ForEach(rule => rules.AddRange(rule));

            _ruleSetToSessionDefinitionMap.Add(ruleSetName, (sessionFactory, rules.DistinctBy(x => x.Name).ToList()));
        }

        #endregion

        #region Audit Scheduling

        public async Task RegisterActivity(IActivity activity)
        {
            using var workspan = Workspan.Start<AuditorService>()
                .Baggage("Id", activity.Id)
                .Baggage("CorrelationId", activity.CorrelationId)
                .Baggage("TenantId", activity.TenantId);

            await ScheduleAuditIfRequiredAsync(activity);
        }


        private async Task ScheduleAuditIfRequiredAsync(IActivity activity)
        {
            using var workspan = Workspan.Start<AuditorService>();

            if (ShouldScheduleAudit(activity, out var auditParameters))
            {
                var pendingAudit = activity.ToPendingAudit(auditParameters);

                workspan
                    .Baggage("CorrelationId", pendingAudit.CorrelationId)
                    .Baggage("RuleSet", pendingAudit.RuleSet)
                    .Baggage("NextAuditTime", pendingAudit.NextAuditTime)
                    .Baggage("IsRollingTime", pendingAudit.IsRollingTime)
                    .Baggage("AuditRangeStart", pendingAudit.AuditRangeStart)
                    .Baggage("AuditRangeEnd", pendingAudit.AuditRangeEnd);

                workspan.Log.Information("Scheduling audit");

                try
                {
                    using var _ = DatabaseLogSuppressor
                        .SuppressUniqueConstraintError<PostgreSQLDbContext>("PendingAudits",
                            "IX_PendingAudits_RuleSet_CorrelationId_AuditTimeRange_IsRollin~");

                    // Update or insert pending audit marker
                    var rowsUpdated = await ExecuteUpdateAsync(activity, auditParameters);

                    if (rowsUpdated == 0) // no pending audit for this correlationId
                    {
                        await _dbContext.PendingAudits.AddAsync(pendingAudit);
                        await _dbContext.SaveChangesAsync();
                    }

                    workspan.Log.Information("Audit scheduled");
                }
                catch (UniqueConstraintException e)
                {
                    // Retry if unique constraint exception (e.g. another thread or container is inserting the same correlationId)
                    try
                    {
                        await ExecuteUpdateAsync(activity, auditParameters);
                    }
                    catch (Exception ex)
                    {
                        workspan.RecordException(ex);
                    }
                }
                catch (Exception ex)
                {
                    workspan.RecordException(ex);
                }
            }
        }

        private async Task<int> ExecuteUpdateAsync(
            IActivity triggerActivity, AuditToScheduleParameters auditParameters)
        {
            return await _dbContext.PendingAudits
                .Where(ExistingPendingAuditSelector(auditParameters, triggerActivity.CorrelationId))
                .ExecuteUpdateAsync(s
                    => s.SetProperty(pendingAudit => pendingAudit.NextAuditTime,
                        // update only if next proposed audit time is less current
                        // or advance if rolling time
                        pa =>
                            (pa.NextAuditTime == null || // no pending audit
                             (auditParameters.IsRollingDelay == false &&
                              auditParameters.NextProposedAuditTime <
                              pa.NextAuditTime) || // update with closer audit time for non-rolling delay
                             (auditParameters.IsRollingDelay == true &&
                              auditParameters.NextProposedAuditTime >
                              pa.NextAuditTime) // update with later audit time for rolling delay
                            )
                                ? auditParameters.NextProposedAuditTime // update
                                : pa.NextAuditTime // do not update
                    )
                );
        }

        private static Expression<Func<PendingAudit, bool>> ExistingPendingAuditSelector(
            AuditToScheduleParameters auditToScheduleParameters, Guid auditCorrelationId)
        {
            return pa =>
                pa.RuleSet == auditToScheduleParameters.RuleSet &&
                pa.CorrelationId == auditCorrelationId &&
                pa.AuditTimeRange == auditToScheduleParameters.AuditTimeRange.ToString() &&
                pa.IsRollingTime == auditToScheduleParameters.IsRollingDelay;
        }

        private Dictionary<string, AuditToScheduleParameters> _auditScheduleParametersMap;

        private bool ShouldScheduleAudit(IActivity activity, out AuditToScheduleParameters auditToScheduleParameters)
        {
            using var workspan = Workspan.Start<AuditorService>();

            if (_auditScheduleParametersMap == null)
            {
                _auditScheduleParametersMap = _auditScheduleParameters.ToDictionary(
                    x => AuditToScheduleParameters.CreateKey(x.TriggerActivityCategory, x.TriggerActivityName));
            }

            if (_auditScheduleParametersMap.TryGetValue(
                    AuditToScheduleParameters.CreateKey(activity.Category, activity.Name),
                    out auditToScheduleParameters))
            {
                auditToScheduleParameters.UpdateFromActivity(activity);

                return true;
            }

            return false;
        }

        #region Commented

        // private static Expression<Func<PendingAudit, DateTime?>> UpdateNextAuditTime(
        //     AuditToScheduleParameters auditToScheduleParameters)
        // {
        //     return pa =>
        //         pa.NextAuditTime == null || auditToScheduleParameters.IsRollingDelay
        //             ? auditToScheduleParameters.NextProposedAuditTime
        //             : pa.NextAuditTime <
        //               auditToScheduleParameters
        //                   .NextProposedAuditTime // update only if next proposed audit time is less current
        //                 ? pa.NextAuditTime
        //                 : auditToScheduleParameters.NextProposedAuditTime;
        // }

        #endregion

        #endregion


#if DEBUG
        private const int ORDER_TRANSMITTED_AUDIT_DELAY_IN_SECONDS = 5 * 60;
        private const int ORDER_EVALUATION_STARTED_AUDIT_DELAY_IN_SECONDS = 4 * 60;
        private const int ORDER_PLACED_AUDIT_DELAY_IN_SECONDS = 30;
#else
        // we need to be sure that all activities to be received by Activity MS and
        // replicated to read-only database replica
        // it can take a few minutes

        private const int ORDER_TRANSMITTED_AUDIT_DELAY_IN_SECONDS = 10*60;
        private const int ORDER_EVALUATION_STARTED_AUDIT_DELAY_IN_SECONDS = 10*60;
        private const int ORDER_PLACED_AUDIT_DELAY_IN_SECONDS = 5*60;
#endif

        List<AuditToScheduleParameters> _auditScheduleParameters = new()
        {
            // new("ARS1", nameof(ActivityCategories.Eligibility_Transmit), AuditTimeRange.All,
            //     "Transmit_Evaluate", auditDelay: TimeSpan.FromSeconds(ORDER_PLACED_AUDIT_DELAY_IN_SECONDS)),

            // new("ARS1", nameof(ActivityCategories.Orders_Processing), AuditTimeRange.All,
            //     auditRollingDelay: TimeSpan.FromMinutes(10)),
            // new("ARS1", nameof(ActivityCategories.Eligibility_Transmit), AuditTimeRange.All,
            //     auditDelay: TimeSpan.FromMinutes(5)),

            new("ARS1", nameof(ActivityCategories.Eligibility_Transmit), AuditTimeRange.FromCheckpoint,
                "Transmit_Evaluate", auditDelay: TimeSpan.FromSeconds(ORDER_TRANSMITTED_AUDIT_DELAY_IN_SECONDS),
                parametersModifier: UpdateAuditTimeFromActivity),

            new("ARS1", nameof(ActivityCategories.Eligibility_Evaluation), AuditTimeRange.FromCheckpoint,
                "Evaluation_EvaluationStarted",
                auditDelay: TimeSpan.FromSeconds(ORDER_EVALUATION_STARTED_AUDIT_DELAY_IN_SECONDS),
                parametersModifier: UpdateAuditTimeFromActivity),

            new("ARS1", nameof(ActivityCategories.Orders_Processing), AuditTimeRange.All,
                "Order_Placed", auditDelay: TimeSpan.FromSeconds(ORDER_PLACED_AUDIT_DELAY_IN_SECONDS)),
        };

        public static void UpdateAuditTimeFromActivity(IAuditToScheduleParametersModifier parameters,
            IActivity activity)
        {
            using var workspan = Workspan.Start<AuditorService>();
            try
            {
                if (string.IsNullOrWhiteSpace(activity.Meta))
                    return;

                var meta = JsonSerializer.Deserialize<Dictionary<string, string>>(activity.Meta);

                if (meta.TryGetValue(KnownMetadataNames.AuditDelay, out var auditDelay))
                {
                    if (int.TryParse(auditDelay, out var auditDelayInSeconds) && auditDelayInSeconds >= 0)
                    {
                        parameters.SetAuditDelay(TimeSpan.FromSeconds(auditDelayInSeconds), false);
                    }
                    else
                    {
                        workspan.Log
                            .Warning("Invalid audit delay value in activity meta: {AuditDelay}", auditDelay);
                    }
                }
            }
            catch (Exception e)
            {
                workspan.Log.Error(e, "Error getting audit time from activity");
            }
        }

        #region Audit Execution

        List<AuditResultFact> ExecuteRules(string ruleSet, List<ActivityFactBase> facts,
            DateTime? lastActivityToAuditTime,
            out DateTime? nextTime,
            ref CheckPointFact checkpoint, out List<IRuleDefinition> firedRules,
            out List<IRuleDefinition> executedRules)
        {
            using var workspan = Workspan.Start<AuditorService>();

            executedRules = null;
            firedRules = null;

            using var serviceScope = _serviceScopeFactory.CreateScope();

            var ruleSetToSessionDefinition = _ruleSetToSessionDefinitionMap[ruleSet];
            var session = ruleSetToSessionDefinition.Factory.CreateSession();

            List<AuditResultFact>? results = null;
            nextTime = null;
            checkpoint = null;

            if (facts?.Count > 0 == false) return null;

            var utcNow = DateTime.UtcNow;

            // Input data (facts)
            session.InsertAll(facts);

            // Global options
            session.Insert(_auditOptions.Value);

            //session.Insert(new Merchant_Fact(merchant));

            // Checkpoint can be used to resume audit from the point where it was stopped
            // e.g. snapshot of some previous audit
            // Checkpoint prevents auditing facts twice
            if (checkpoint != null) session.Insert(checkpoint);

            // // Activities as set (Dictionary) input fact
            // session.Insert(new ActivitySet(facts));

            // ConcurrentBag<string> errors = new();
            //
            List<IRuleDefinition> firedRulesInternal = new();
            session.Events.RuleFiringEvent += (sender, e) => OnRuleFiringEvent(sender, e, firedRulesInternal);
            firedRules = firedRulesInternal;
            // session.Events.AgendaExpressionFailedEvent +=
            //     (sender, e) => OnAgendaExpressionFailedEvent(sender, e, errors);
            // session.Events.LhsExpressionFailedEvent += (sender, e) => OnLhsExpressionFailedEvent(sender, e, errors);
            // session.Events.RhsExpressionFailedEvent += (sender, e) => OnRhsExpressionFailedEvent(sender, e, errors);

            workspan.Log.Information("Starting audit");

            // Execute rules
            session.Fire();

            //HashSet<string> firedRulesHashSet = new(firedRules.Select(r => r.Name));
            // List<IRuleDefinition> notFiredRules = new();
            // ruleSetToSessionDefinition.Rules.ForEach(rule =>
            // {
            //     if (!firedRulesHashSet.Contains(rule.Name)) notFiredRules.Add(rule);
            // });

            executedRules = ruleSetToSessionDefinition.Rules;

            workspan.Log.Information("Audit completed");

            results = session.Query<AuditResultFact>().ToList();

            nextTime = session.Query<AuditTimer>()
                .Where(t => t.NextAuditTime > utcNow)
                .OrderBy(t => t.NextAuditTime)
                .FirstOrDefault()?.NextAuditTime;

            //checkpoint = session.Query<CheckPointFact>().FirstOrDefault();

            if (checkpoint == null && lastActivityToAuditTime.HasValue)
            {
                checkpoint = new CheckPointFact()
                {
                    CheckTime = lastActivityToAuditTime.Value
                };
            }

            return results.ToList();
        }

        #region Rule Events and Exceptions Processing

        public class ActionInterceptor : IActionInterceptor
        {
            private readonly ConcurrentBag<string> _errors;

            public ActionInterceptor(ConcurrentBag<string> errors)
            {
                _errors = errors;
            }

            public void Intercept(IContext context, IEnumerable<IActionInvocation> actions)
            {
                try
                {
                    foreach (var action in actions)
                    {
                        action.Invoke();
                    }
                }
                catch (Exception e)
                {
                    Workspan.Current?.Log.Fatal(e, "AUDIT ERROR: Rule actions execution failed. Name={RuleName}",
                        context.Rule.Name);

                    //Console.WriteLine($"Rule execution failed. Name={context.Rule.Name} Exception={e.Message}");
                    _errors.Add($"{context.Rule.Name}. Action error: {e.Message}");
                }
            }
        }

        private void OnRuleFiringEvent(object sender, AgendaEventArgs e, List<IRuleDefinition> firedRules)
        {
#if DEBUG
            Workspan.Current?.Log.Information("Firing rule: {RuleName}. Facts={Facts}", e.Rule.Name,
                string.Join(",", e.Match.Facts.Select(x => x.Value)));
#endif

            firedRules.Add(e.Rule);
        }


        private static void OnAgendaExpressionFailedEvent(object sender, AgendaExpressionErrorEventArgs e,
            ConcurrentBag<string> errors)
        {
            //Raised when an exception is thrown during the evaluation of an agenda expression (i.e. a rule's filter expression).
            e.IsHandled = true;

            Workspan.Current?.Log.Fatal(e.Exception, "AUDIT ERROR: Agenda > {Message}", e.Exception.Message);

            errors.Add($"{e.Rule.Name}. Agenda error: {e.Exception.Message}");
        }

        private static void OnLhsExpressionFailedEvent(object sender, LhsExpressionErrorEventArgs e,
            ConcurrentBag<string> errors)
        {
            //Raised when an exception is thrown during the evaluation of a left-hand side expression
            // (i.e. a rule's condition, binding expression or aggregation expression).
            e.IsHandled = true;

            foreach (var failedRule in e.Rules)
            {
                Workspan.Current?.Log.Fatal(e.Exception, "AUDIT ERROR: LHS > {Message}. Expression: {Expression}",
                    e.Exception.Message, e.Expression);

                errors.Add($"{failedRule.Name}. LHS error: {e.Exception.Message}. Expression: {e.Expression}");
            }
        }

        private static void OnRhsExpressionFailedEvent(object sender, RhsExpressionErrorEventArgs e,
            ConcurrentBag<string> errors)
        {
            //Raised when an exception is thrown during the evaluation of a right-hand side expression
            // (i.e. a rule's action).
            e.IsHandled = true;

            Workspan.Current?.Log.Fatal(e.Exception, "AUDIT ERROR: RHS > {Message}. Expression: {Expression}",
                e.Exception.Message, e.Expression);

            errors.Add($"RHS error: {e.Exception.Message}. Expression: {e.Expression}");
        }


        List<AuditedProblem> DoAudit(
            Guid auditId,
            string ruleSet, Guid correlationId, Guid? tenantId,
            List<ActivityFactBase> facts,
            DateTime? lastActivityToAuditTime,
            ref CheckPointFact checkpoint,
            out DateTime? nextTime,
            out List<IRuleDefinition>? firedRules,
            out List<IRuleDefinition>? executedRules,
            out Severity? maximumProblemSeverity
        )
        {
            using var workspan = Workspan.Start<AuditorService>();

            List<AuditedProblem>? result = null;

            var auditResults = ExecuteRules(ruleSet, facts,
                lastActivityToAuditTime,
                out nextTime, ref checkpoint,
                out firedRules, out executedRules);

            if (auditResults?.Count > 0)
            {
                result = auditResults
                    .Select(r => new AuditedProblem()
                    {
                        AuditId = auditId,
                        CorrelationId = correlationId,
                        TenantId = tenantId,
                        Severity = r.Result.ToString(),
                        CreatedOn = DateTime.UtcNow,
                        Message = r.Message,
                        RuleId = r.RuleId,
                        RuleName = r.RuleName
                    }).ToList();
            }

            if (auditResults?.Count > 0)
            {
                maximumProblemSeverity = (Severity?) auditResults?.Max(x => (int) x.Result);
            }
            else maximumProblemSeverity = null;

            return result;
        }

        #endregion

        public async Task PerformAuditAsync(Guid pendingAuditId, string ruleSet, Guid correlationIdToAudit)
        {
            using var workspan = Workspan.Start<AuditorService>()
                .Baggage("PendingAuditId", pendingAuditId)
                .Baggage("CorrelationId", correlationIdToAudit)
                .Baggage("RuleSet", ruleSet)
                .LogEnterAndExit();

            Guid? tenantId = null;
            Guid? auditId = null;
            try
            {
                workspan.Log.Information("Initializing audit");

                var pendingAudit = await _dbContext.PendingAudits
                    .AsNoTracking()
                    .SingleAsync(p => p.Id == pendingAuditId);

                workspan
                    .Baggage("CorrelationId", pendingAudit.CorrelationId)
                    .Baggage("TenantId", pendingAudit.TenantId);

                tenantId = pendingAudit.TenantId;

                // Note: activities loaded unsorted
                var activitiesToAudit = await LoadActivitiesToAuditAsync(pendingAudit);


                if (activitiesToAudit.Count == 0)
                {
                    workspan.Log.Fatal("No activities found for audit");

                    await _activityService.CreateActivityAsync(AuditErrorActivities
                            .Audit_NoActivitiesFound_Error,
                        set: set => set.Meta(meta => meta
                            .SetValue("CorrelationId", correlationIdToAudit)
                            .SetValue("TenantId", tenantId)));

                    return; //!!!
                }

                workspan.Log.Information("Loaded {ActivitiesCount} activities to audit",
                    activitiesToAudit.Count);

                var activityFacts = activitiesToAudit.Select(a =>
                    Auditor.ActivityToFact(a.ToFactDTO())).ToList();

                // Activities can be in incorrect order because they are delivered from different distributed sources
                // Calculate min and max time of activities to audit

                DateTime? firstActivityTime = null;
                DateTime? lastActivityTime = null;
                if (activitiesToAudit?.Any() == true)
                {
                    firstActivityTime = lastActivityTime = activitiesToAudit.First()?.ActionTimestamp;

                    foreach (var activity in activitiesToAudit)
                    {
                        if (activity.ActionTimestamp < firstActivityTime)
                            firstActivityTime = activity.ActionTimestamp;
                        if (activity.ActionTimestamp > lastActivityTime)
                            lastActivityTime = activity.ActionTimestamp;
                    }
                }

                var audit = _dbContext.Audits.Add(new Entities.Audit
                {
                    CorrelationId = correlationIdToAudit,
                    TenantId = tenantId,
                    Status = nameof(AuditStatus.Pending),
                    AuditTimeRange = pendingAudit.AuditTimeRange,
                    AuditRangeStart = firstActivityTime,
                    AuditRangeEnd = lastActivityTime,
                }).Entity;

                await _dbContext.SaveChangesAsync();

                auditId = audit.Id;

                workspan
                    .Baggage("AuditId", auditId)
                    .Baggage("AuditTimeRange", audit.AuditTimeRange);


                await _activityService.CreateActivityAsync(AuditActivities.Audit_Started,
                    data: new {ActivitiesToAudit = activitiesToAudit?.Count},
                    set: set => set
                        .CorrelationId(correlationIdToAudit)
                        .TenantId(tenantId)
                        .Meta(meta =>
                        {
                            meta
                                .SetValue("AuditId", auditId)
                                .SetValue("TimeRange", audit.AuditTimeRange)
                                .SetValue("FirstActivity", firstActivityTime?.ToString("O"))
                                .SetValue("LastActivity",
                                    lastActivityTime?.ToString("O"));
                        }));


                CheckPointFact checkpoint = pendingAudit.CheckPoint != null
                    ? new CheckPointFact() {CheckTime = pendingAudit.CheckPoint.Value}
                    : null;

                var corErrors =
                    DoAudit(auditId.Value, ruleSet, correlationIdToAudit, tenantId, activityFacts,
                        lastActivityTime,
                        ref checkpoint, out var nextTime, out var firedRules,
                        out var executedRules,
                        out var maximumProblemSeverity);

                if (checkpoint != null)
                {
                    try
                    {
                        // Update last audit time (Check Point) for all audits with the same correlationId
                        // and same rule set
                        await _dbContext.PendingAudits
                            .Where(p => p.CorrelationId == correlationIdToAudit && p.RuleSet == ruleSet)
                            .ExecuteUpdateAsync(s
                                => s.SetProperty(p => p.CheckPoint,
                                    p => checkpoint.CheckTime));
                    }
                    catch (Exception e)
                    {
                        workspan.RecordException(e);

                        await _activityService.CreateActivityAsync(AuditErrorActivities.Audit_Error,
                            data: e,
                            set: set => set
                                .CorrelationId(correlationIdToAudit)
                                .TenantId(tenantId)
                                .Meta(meta => meta
                                    .SetValue("AuditId", auditId)));
                    }
                }

                List<AuditedProblem> foundErrors = new List<AuditedProblem>();
                if (corErrors?.Count > 0) foundErrors.AddRange(corErrors);


                #region Commented

                // // process facts in parallel for each correlationId
                // ConcurrentBag<List<AuditedCorrelationError>> foundErrors = new();
                // ConcurrentBag<(Guid, DateTime)> nextTimes = new();
                // //ConcurrentBag<(Guid, CheckPointFact)> checkpoints = new();
                //
                // // var checkPoints = await _dbContext.PendingAudits
                // //     .AsNoTracking()
                // //     .Where(p => correlations.Contains(p.CorrelationId) && p.CheckPoint != null)
                // //     .Select(p => new {p.CorrelationId, p.CheckPoint}).ToListAsync();
                //
                //
                // // var checkFacts = checkPoints
                // //     .ToDictionary(x => x.CorrelationId, x => new CheckPointFact() {CheckTime = x.CheckPoint.Value});
                //
                // Parallel.ForEach(factGroups, f =>
                // {
                //     CheckPointFact checkpoint = null;
                //     //checkFacts.TryGetValue(f.Key.CorrelationId, out var checkpoint);
                //     var corErrors =
                //         DoAuditAsync(f.Key.CorrelationId, f.Key.TenantId, f.ToList(), ref checkpoint,
                //             out var nextTime);
                //
                //     if (corErrors?.Count > 0 == true) foundErrors.Add(corErrors);
                //
                //     if (nextTime.HasValue) nextTimes.Add((f.Key.CorrelationId, nextTime.Value));
                //
                //     //if (checkpoint != null) checkpoints.Add((f.Key.CorrelationId, checkpoint));
                // });

                #endregion

                #region Commented

                // //set next timers for pending audit
                // if (nextTimes.Count() > 0)
                // {
                //     var tasks = new List<Task>();
                //     foreach (var (key, time) in nextTimes)
                //     {
                //         var task = Task.Run(() => _dbContext.PendingAudits
                //             .Where(e => e.CorrelationId == key && e.NextAuditTimeOrLastSeen > time)
                //             .ExecuteUpdate(s
                //                 => s.SetProperty(p => p.NextAuditTimeOrLastSeen, p => time)));
                //         tasks.Add(task);
                //     }
                //
                //     await Task.WhenAll(tasks);
                // }

                // //set checkpoints
                // if (checkpoints.Count() > 0)
                // {
                //     var tasks = new List<Task>();
                //     foreach (var (key, check) in checkpoints)
                //     {
                //         var data = JsonSerializer.Serialize(check);
                //         
                //         CAN'T USE SAME CONTEXT IN PARALLEL THREADS???
                //         var task = Task.Run(() => _dbContext.PendingAudits
                //             .Where(e => e.CorrelationId == key)
                //             .ExecuteUpdate(s
                //                 => s.SetProperty(p => p.CheckPoint,
                //                     p => check.CheckTime)));
                //         
                //         tasks.Add(task);
                //     }
                //
                //     await Task.WhenAll(tasks);
                // }

                #endregion

                #region Commented

                // select existing errors in database for comparison with new ones, so that
                // only non-duplicate could errors are inserted to db. Duplication is calculated for
                // CorrelationId + RuleName
                //var newlyFoundErrors = foundErrors
                // //.SelectMany(a => a)
                // .GroupBy(e => new {e.CorrelationId, e.Message})
                // .SelectMany(g => g.Take(1))
                // .ToList();

                #endregion

                if (foundErrors.Count > 0)
                {
                    #region [Commented] Errors Deduplication

                    // var errorsCorrelationIds = foundErrors
                    //     .Select(e => e.CorrelationId)
                    //     .Distinct()
                    //     .ToList();

                    // var errorsInDb = await _dbContext
                    //     .AuditedProblems
                    //     .Where(e => errorsCorrelationIds.Contains(e.CorrelationId))
                    //     .ToListAsync();
                    //
                    // if (errorsInDb.Count > 0)
                    // {
                    //     var errorHashSet = new HashSet(errorsInDb.Select(e => (e.CorrelationId, e.Message)));
                    //
                    //     var errorsNotInDb = foundErrors
                    //         .Where(e => !errorHashSet.Contains((e.CorrelationId, e.Message))).ToList();
                    //
                    //     if (errorsNotInDb.Count > 0) _dbContext.AddRange(errorsNotInDb);
                    // }
                    // else
                    // {
                    //     _dbContext.AuditedProblems.AddRange(foundErrors);
                    // }

                    #endregion

                    _dbContext.AuditedProblems.AddRange(foundErrors);

                    #region Writing found errors to log

                    // Not aggregated logging of all found problems
                    // If there are no correlationId, then it is a global error and will not be logged with log severity
                    // otherwise it will be logged with Information log severity, because it will be logged as aggregated by correlationId below
                    foreach (var foundError in foundErrors)
                    {
                        LogProblem(foundError);
                    }


                    // Aggregated logging (by Correlation Id) of found errors (with log severity)
                    LogProblemsAggregatedByCorrelationId(Severity.Security, "Security problems found", foundErrors);
                    LogProblemsAggregatedByCorrelationId(Severity.Critical, "Critical problems found", foundErrors);
                    LogProblemsAggregatedByCorrelationId(Severity.Error, "Problems found", foundErrors);
                    LogProblemsAggregatedByCorrelationId(Severity.Warning, "Warnings found", foundErrors);

                    #endregion
                }

                try
                {
                    audit.Status = nameof(AuditStatus.Completed);
                    audit.ProblemsSeverity = maximumProblemSeverity?.ToString();
                    await _dbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    workspan.RecordException(ex);

                    await _activityService.CreateActivityAsync(AuditErrorActivities.Audit_Error,
                        data: ex,
                        set: set => set
                            .CorrelationId(correlationIdToAudit)
                            .TenantId(tenantId)
                            .Meta(meta => meta
                                .SetValue("AuditId", auditId)));
                }


                await _activityService.CreateActivityAsync(AuditActivities.Audit_Results,
                    data:
                    new
                    {
                        FoundErrors = foundErrors.Select(x => new
                        {
                            Rule = x.RuleName,
                            Message = x.Message,
                            Severity = x.Severity.ToString()
                        }),
                        ExecutedRules = executedRules?.Select(x => GetRuleClassName(x)),
                        FiredRules = firedRules?.Select(x => GetRuleClassName(x))
                    },
                    set:
                    set => set
                        .CorrelationId(correlationIdToAudit)
                        .TenantId(tenantId)
                        .Meta(meta => meta
                            .SetValue("AuditId", auditId)));

                string GetRuleClassName(IRuleDefinition x)
                {
                    return x.Name.Substring(x.Name.LastIndexOf('.') + 1);
                }

                if (foundErrors.Count == 0)
                {
                    await _activityService.CreateActivityAsync(AuditActivities.Audit_Succeeded,
                        set: set => set
                            .CorrelationId(correlationIdToAudit)
                            .TenantId(tenantId)
                            .Meta(meta => meta
                                .SetValue("AuditId", auditId)));
                }
                else if (foundErrors.Any(e => e.ToSeverity() > Severity.Warning))
                {
                    await _activityService.CreateActivityAsync(AuditActivities.Audit_Failed,
                        set: set => set
                            .CorrelationId(correlationIdToAudit)
                            .TenantId(tenantId)
                            .Meta(meta => meta
                                .SetValue("AuditId", auditId)));
                }
                else
                {
                    await _activityService.CreateActivityAsync(AuditActivities.Audit_Warning,
                        set: set => set
                            .CorrelationId(correlationIdToAudit)
                            .TenantId(tenantId)
                            .Meta(meta => meta
                                .SetValue("AuditId", auditId)));
                }

                #region Commented

                //var utcNow = DateTime.UtcNow;

                // await _dbContext.PendingAudits.Where(c => c.CorrelationId == correlationIdToAudit /*correlations.Contains(c.CorrelationId)*/)
                //     .ExecuteUpdateAsync(s => 
                //         s.SetProperty(e => e.AuditTime, e => utcNow));

                #endregion
            }
            catch (Exception ex)
            {
                workspan.RecordException(ex);
                await _activityService.CreateActivityAsync(AuditErrorActivities.Audit_Error,
                    data: ex,
                    set: set => set
                        .CorrelationId(correlationIdToAudit)
                        .TenantId(tenantId)
                        .Meta(meta => meta
                            .SetValue("AuditId", auditId)));
            }
        }

        private static void LogProblem(AuditedProblem foundError)
        {
            using var workspan = Workspan.Start<AuditorService>();

            workspan
                .Tag("Severity", foundError.Severity)
                .Tag("AuditId", foundError.AuditId)
                .Tag("RuleName", foundError.RuleName)
                .Tag("RuleId", foundError.RuleId)
                .Tag("AuditedCorrelationId", foundError.CorrelationId)
                .Tag("TenantId", foundError.TenantId)
                ;

            LogEventLevel logEventLevel;

            if (foundError.CorrelationId == null)
            {
                // If no correlationId, then it is a global error and will not be logged as aggregated by correlationId
                // So we need to log it with the severity

                switch (foundError.ToSeverity())
                {
                    case Severity.Critical:
                        logEventLevel = LogEventLevel.Fatal;
                        break;
                    case Severity.Security:
                        logEventLevel = LogEventLevel.Fatal;
                        break;
                    case Severity.Error:
                        logEventLevel = LogEventLevel.Error;
                        break;
                    case Severity.Warning:
                        logEventLevel = LogEventLevel.Warning;
                        break;
                    default:
                        workspan.RecordError("Unknown severity: {Severity}", foundError.Severity);

                        logEventLevel = LogEventLevel.Error;
                        break;
                }
            }
            else
            {
                // This is a correlationId related error and will be logged as aggregated by correlationId
                // So we should log it here as information just for the record
                logEventLevel = LogEventLevel.Information;
            }

            workspan.Log.Write(logEventLevel,
                "AUDIT | {Severity}: {Message}",
                foundError.Severity, foundError.Message);
        }

        private static void LogProblemsAggregatedByCorrelationId(Severity severity, string message,
            List<AuditedProblem> foundErrors)
        {
            var correlationIdsWithThisSeverity = CorrelationIdsWithProblems(severity, foundErrors);

            if (correlationIdsWithThisSeverity.Any())
            {
                foreach (var correlationId in correlationIdsWithThisSeverity)
                {
                    using var correlationIdRelatedWorkspan = Workspan.Start<AuditorService>();

                    var errorsRelatedToCorrelationId = foundErrors
                        .Where(x => x.CorrelationId == correlationId)
                        .Where(x => x.Severity == severity.ToString())
                        .ToList();

                    correlationIdRelatedWorkspan
                        .Tag("Severity", severity)
                        .Tag("RuleName", string.Join(',', errorsRelatedToCorrelationId.Select(x => x.RuleName)))
                        .Tag("RuleId", string.Join(',', errorsRelatedToCorrelationId.Select(x => x.RuleId)))
                        .Tag("RuleMessage", string.Join(',', errorsRelatedToCorrelationId.Select(x => x.Message)))
                        .Tag("TenantId",
                            string.Join(',',
                                errorsRelatedToCorrelationId.DistinctBy(x => x.TenantId).Select(x => x.TenantId)))
                        ;

                    if (correlationId != null)
                    {
                        correlationIdRelatedWorkspan
                            .Tag("AuditedCorrelationId", correlationId.Value.ToString("D"));
                    }


                    LogEventLevel logEventLevel;
                    switch (severity)
                    {
                        case Severity.Critical:
                            logEventLevel = LogEventLevel.Fatal;
                            break;
                        case Severity.Security:
                            logEventLevel = LogEventLevel.Fatal;
                            break;
                        case Severity.Error:
                            logEventLevel = LogEventLevel.Error;
                            break;
                        case Severity.Warning:
                            logEventLevel = LogEventLevel.Warning;
                            break;
                        default:
                            correlationIdRelatedWorkspan.Log.Fatal("Unknown severity: {Severity}", severity);

                            logEventLevel = LogEventLevel.Fatal;
                            break;
                    }

                    correlationIdRelatedWorkspan.Log.Write(logEventLevel,
                        $"AUDIT: {message} for CorrelationId");
                }
            }
        }

        private static List<Guid?> CorrelationIdsWithProblems(Severity severity, List<AuditedProblem> errors)
        {
            return errors
                .Where(e => e.Severity == severity.ToString() && e.CorrelationId != null)
                .Select(e => e.CorrelationId)
                .Distinct()
                .ToList();
        }

        private async Task<IList<ActivityDTO>> LoadActivitiesToAuditAsync(PendingAudit audit,
            bool includeActivityData = false)
        {
            var activitiesQuery = _activityDbContext
                .AggregatedActivities.AsNoTracking()
                .Where(a => a.CorrelationId == audit.CorrelationId);

            IQueryable<ActivityDTO> activityDTOsQuery;
            if (includeActivityData)
            {
                activityDTOsQuery = activitiesQuery
                    .Select(a =>
                        // Loading only required fields
                        new ActivityDTO
                        {
                            Name = a.Name,
                            CorrelationId = a.CorrelationId,
                            TenantId = a.TenantId,
                            ActionTimestamp = a.ActionTimestamp,
                            Meta = a.Meta,
                            Category = a.Category,
                            SubCategory = a.SubCategory,
                            InformationLevel = a.InformationLevel,
                            Data = a.Data
                        });
            }
            else
            {
                activityDTOsQuery = activitiesQuery
                    .Select(a =>
                        // Loading only required fields
                        new ActivityDTO
                        {
                            Name = a.Name,
                            CorrelationId = a.CorrelationId,
                            TenantId = a.TenantId,
                            ActionTimestamp = a.ActionTimestamp,
                            Meta = a.Meta,
                            Category = a.Category,
                            SubCategory = a.SubCategory,
                            InformationLevel = a.InformationLevel
                        });
            }

            if (audit.AuditRangeStart.HasValue)
                activityDTOsQuery = activityDTOsQuery.Where(a => a.ActionTimestamp >= audit.AuditRangeStart);

            if (audit.AuditRangeEnd.HasValue)
                activityDTOsQuery = activityDTOsQuery.Where(a => a.ActionTimestamp <= audit.AuditRangeEnd);

            switch (audit.ToAuditTimeRange())
            {
                case null:

                case AuditTimeRange.All:
                    break;

                case AuditTimeRange.FromCheckpoint:

                    if (audit.CheckPoint != null)
                        activityDTOsQuery = activityDTOsQuery.Where(a => a.ActionTimestamp > audit.CheckPoint);

                    break;
                default:
                    throw new ArgumentOutOfRangeException($"Unknown AuditTimeRange {audit.AuditTimeRange}");
            }

            return await activityDTOsQuery.ToListAsync().ConfigureAwait(false);
        }

        #endregion

        #region Run Pending Audits

        public record CorrelationToAudit(Guid CorrelationId, DateTime? AuditRangeStart, DateTime? AuditRangeEnd)
        {
            public override string ToString()
            {
                return
                    $"{{ CorrelationId = {CorrelationId}, AuditRangeStart = {AuditRangeStart}, AuditRangeEnd = {AuditRangeEnd} }}";
            }
        }

        public async Task<bool> RunPendingAuditsAsync()
        {
            using var workspan = Workspan.Start<AuditorService>();

            using var serviceScope = _serviceScopeFactory.CreateScope();
            var serviceProvider = serviceScope.ServiceProvider;

            using var localDbContext = serviceProvider.GetRequiredService<PostgreSQLDbContext>();


            bool commitAndSave = false;
            List<AuditActivitiesCommand> auditActivitiesCommandsToSend = new();

            await using var lockingTransaction = await localDbContext.Database.BeginTransactionAsync();
            try
            {
                var utcNow = DateTime.UtcNow;

                #region Selecting audits to process

                // This query uses postgres query hint : Select .. FOR UPDATE SKIP LOCKED
                // to lock rows for update - they won't be visible for other transactions until this transaction is committed or rolled back
                // hint is appended to sql command using SelectForUpdateCommandInterceptor
                // this hint is valid only inside transaction
                //https://www.2ndquadrant.com/en/blog/what-is-select-skip-locked-for-in-postgresql-9-5/
                //https://linuxhint.com/select-update-postgres/
                //https://learn.microsoft.com/en-us/ef/core/logging-events-diagnostics/interceptors
                var auditsToProcessBatch = localDbContext
                        .PendingAudits

                        // Skip-locking rows for update - they won't be visible for other transactions until this transaction is committed or rolled back
                        .TagWith(SelectForUpdateCommandInterceptor.SelectForUpdateSkipLockedTag)

                        // Select only audits that are ready for processing
                        .Where(p => p.NextAuditTime != null && p.NextAuditTime <= utcNow)
                        // p.NextAuditTimeOrLastSeen.AddSeconds(_auditOptions.Value.AuditIntervalInSeconds) < utcNow
                        // && (p.AuditDateTime == null || p.AuditDateTime < p.NextAuditTimeOrLastSeen) &&
                        // p.NextAuditTimeOrLastSeen <= utcNow)

                        // Limits number of audits to process at once and therefore limits number of activities to load from database
                        .Take(_auditOptions.Value.AuditBatchSizeLimit)
                    ;

                var audits = await auditsToProcessBatch.ToListAsync();

                if (audits.Count == 0)
                {
                    workspan.Log.Information("No audits to process");
                    return false;
                }

                workspan.Log.Information("Processing {AuditCount} audits", audits.Count);

                #endregion

                #region [Commented]Selecting Correlation Ids to Audit

                // var correlationIdsToAudit = audits
                //     // only correlationId-based audits are supported for now (TenantId-based requests must be limited by count and time!!!)
                //     .Where(a => a.CorrelationId != null)
                //     .Select(a => a.CorrelationId.Value)
                //     .Distinct()
                //     .ToList();

                // var correlationsToAudit = audits
                //     .Where(a => a.CorrelationId != null)
                //     .GroupBy(a => a.CorrelationId.Value)
                //     .Select(group => new CorrelationToAudit(group.Key,
                //         group.Min(a => a.CheckPoint),
                //         null //, group.Max(a => a.AuditRangeEnd)
                //     ))
                //     .Select(a =>
                //         new CorrelationToAudit(a.CorrelationId, a.AuditRangeStart ?? null, a.AuditRangeEnd ?? null))
                //     .ToList();
                //

                //
                // if (correlationsToAudit.Count == 0)
                // {
                //     workspan.Log.Information("No audits to process");
                //     return false;
                // }

                #endregion

                #region Commented

                // int halfProcessorCount = Environment.ProcessorCount / 2;
                //
                // // Set the maximum degree of parallelism to half of the available logical processors
                // // to avoid blocking other processes
                // ParallelOptions parallelOptions = new ParallelOptions
                // {
                //     MaxDegreeOfParallelism = halfProcessorCount
                // };

                // await Parallel.ForEachAsync(audits, parallelOptions, async (audit, cancellationToken) =>
                // {
                //     using var workspan = Workspan.Start<AuditorService>($"Processing Pending Audit")
                //         .Baggage("PendingAuditId", audit.Id)
                //         .Baggage("CorrelationId", audit.CorrelationId)
                //         .Baggage("AuditTime", audit.NextAuditTime)
                //         .Baggage("RuleSet", audit.RuleSet);
                //
                //     try
                //     {
                //         workspan.Log.Information("Initiating pending audit");
                //
                //
                //         await _publisher.RunIdempotentCommandWithoutResponseAsync(
                //             new AuditActivitiesCommand(
                //                 audit.Id,
                //                 audit.RuleSet,
                //                 audit.CorrelationId!.Value));
                //
                //         workspan.Log.Information("Audit requested");
                //         await _activityService.CreateActivityAsync(AuditActivities.Audit_Requested,
                //             data: new {Audit = audit},
                //             set: set => set
                //                 .CorrelationId(audit.CorrelationId)
                //                 .TenantId(audit.TenantId)
                //                 .Meta(meta => meta
                //                     .SetValue("RuleSet", audit.RuleSet)
                //                     .SetValue("NextAuditTime", audit.NextAuditTime?.ToString("yyyy-MM-dd HH:mm:ss"))
                //                 ));
                //
                //
                //         audit.LastAuditStartTime = utcNow;
                //         audit.NextAuditTime = null; // to avoid processing audits more then once
                //     }
                //     catch (Exception e)
                //     {
                //         workspan.RecordException(e, "Cannot initiate audit");
                //     }
                // });

                #endregion

                foreach (var audit in audits)
                {
                    auditActivitiesCommandsToSend.Add(new AuditActivitiesCommand(
                        audit.Id,
                        audit.RuleSet,
                        audit.CorrelationId!.Value));

                    await _activityService.CreateActivityAsync(AuditActivities.Audit_Requested,
                        data: new {Audit = audit},
                        set: set => set
                            .CorrelationId(audit.CorrelationId)
                            .TenantId(audit.TenantId)
                            .Meta(meta => meta
                                .SetValue("RuleSet", audit.RuleSet)
                                .SetValue("NextAuditTime", audit.NextAuditTime?.ToString("yyyy-MM-dd HH:mm:ss"))
                            ));

                    audit.LastAuditStartTime = utcNow;
                    audit.NextAuditTime = null; // to avoid processing audits more then once
                }

                commitAndSave = true;

                //return true;
            }
            catch (Exception ex)
            {
                workspan.RecordException(ex, "Cannot process pending audits");
            }
            finally
            {
                if (commitAndSave)
                {
                    await localDbContext.SaveChangesAsync();

                    // do not remove (see comments about lock for update hint above)
                    await lockingTransaction.CommitAsync();
                }
                else
                {
                    // do not remove (see comments about lock for update hint above)
                    await lockingTransaction.RollbackAsync();
                }
            }

            try
            {
                await _publisher.PublishBatch(auditActivitiesCommandsToSend);
            }
            catch (Exception e)
            {
                workspan.RecordException(e, "Cannot publish audit commands");
            }

            return auditActivitiesCommandsToSend.Count > 0;
        }

        #endregion
    }
}