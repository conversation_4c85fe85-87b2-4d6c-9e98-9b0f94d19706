using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.NotificationCenter.Migrations
{
    /// <inheritdoc />
    public partial class AddMergeFieldValidationToChannelTemplate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "DefaultValues",
                table: "ChannelTemplates",
                type: "jsonb",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "ChannelTemplates",
                type: "character varying(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "ChannelTemplates",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<List<string>>(
                name: "OptionalMergeFields",
                table: "ChannelTemplates",
                type: "jsonb",
                nullable: true);

            migrationBuilder.AddColumn<List<string>>(
                name: "RequiredMergeFields",
                table: "ChannelTemplates",
                type: "jsonb",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SubjectTemplate",
                table: "ChannelTemplates",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TemplateContent",
                table: "ChannelTemplates",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ValidationRules",
                table: "ChannelTemplates",
                type: "jsonb",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_ChannelTemplates_EventType_Channel_IsActive",
                table: "ChannelTemplates",
                columns: new[] { "EventType", "Channel", "IsActive" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ChannelTemplates_EventType_Channel_IsActive",
                table: "ChannelTemplates");

            migrationBuilder.DropColumn(
                name: "DefaultValues",
                table: "ChannelTemplates");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "ChannelTemplates");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "ChannelTemplates");

            migrationBuilder.DropColumn(
                name: "OptionalMergeFields",
                table: "ChannelTemplates");

            migrationBuilder.DropColumn(
                name: "RequiredMergeFields",
                table: "ChannelTemplates");

            migrationBuilder.DropColumn(
                name: "SubjectTemplate",
                table: "ChannelTemplates");

            migrationBuilder.DropColumn(
                name: "TemplateContent",
                table: "ChannelTemplates");

            migrationBuilder.DropColumn(
                name: "ValidationRules",
                table: "ChannelTemplates");
        }
    }
}
