using System;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.NotificationCenter.Entities;

[Index(nameof(SubscriberId), nameof(NotificationId), IsUnique = true)]
[Index(nameof(NotificationId))]
[Index(nameof(SubscriberId))]
[Index(nameof(DeliveredAt))]
[Index(nameof(ReadAt))]
public class SubscriberNotification : AuditableEntity
{
    public Guid SubscriberId { get; set; }
    public Subscriber Subscriber { get; set; }
    
    public Guid NotificationId { get; set; }
    public Notification Notification { get; set; }
    
    // Delivery tracking
    public DateTime? DeliveredAt { get; set; } // When the notification was delivered to the subscriber
    public DateTime? ReadAt { get; set; } // When the subscriber read/viewed the notification
    public DateTime? ClickedAt { get; set; } // When the subscriber clicked on the notification (if applicable)
    
    // Status specific to this subscriber
    public string DeliveryStatus { get; set; } = "Pending"; // Pending, Delivered, Failed, Bounced
    public string FailureReason { get; set; }
    
    // Subscriber-specific customization
    public bool IsRead { get; set; } = false;
    public bool IsArchived { get; set; } = false;
    public bool IsStarred { get; set; } = false;
}
