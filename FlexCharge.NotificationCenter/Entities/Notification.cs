using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.NotificationCenter.Entities;

[Index(nameof(EventType))]
[Index(nameof(CreatedOn))]
[Index(nameof(ScheduledAt))]
public class Notification : AuditableEntity
{
    public string EventType { get; set; } // Type of event that triggered this notification
    public string Title { get; set; }
    public string Message { get; set; }
    public string Channel { get; set; } // "email", "sms", "push", "in-app"
    
    // Scheduling
    public DateTime? ScheduledAt { get; set; } // When to send the notification
    public DateTime? SentAt { get; set; } // When the notification was actually sent
    
    // Status tracking
    public string Status { get; set; } = "Pending"; // Pending, Sent, Failed, Cancelled
    public string FailureReason { get; set; }
    public int RetryCount { get; set; } = 0;
    public int MaxRetries { get; set; } = 3;
    
    // Template and content
    public string TemplateKey { get; set; }
    [Column(TypeName = "jsonb")]
    public string TemplateData { get; set; } // JSON data for template variables
    
    // Priority and expiry
    public int Priority { get; set; } = 0; // Higher number = higher priority
    public DateTime? ExpiresAt { get; set; }
    
    // Navigation properties
    public ICollection<SubscriberNotification> SubscriberNotifications { get; set; } = new List<SubscriberNotification>();
}
