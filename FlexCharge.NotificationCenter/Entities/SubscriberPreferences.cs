using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.NotificationCenter.Entities;

[Index(nameof(SubscriberId), IsUnique = true)]
public class SubscriberPreferences : AuditableEntity
{
    public Guid SubscriberId { get; set; }
    public Subscriber Subscriber { get; set; }
    
    public string Language { get; set; } // Preferred language for notifications
    public List<string> Channels { get; set; } = new(); // "email", "sms", "push" , "in-app"
    public bool IsActive { get; set; } = true; // Indicates if the subscriber is active
    
    // Channel-specific preferences
    public bool EmailEnabled { get; set; } = true;
    public bool SmsEnabled { get; set; } = true;
    public bool PushEnabled { get; set; } = true;
    public bool InAppEnabled { get; set; } = true;
    
    // Frequency preferences
    public string EmailFrequency { get; set; } = "Immediate"; // Immediate, Daily, Weekly
    public string SmsFrequency { get; set; } = "Immediate";
    
    // Quiet hours
    public TimeSpan? QuietHoursStart { get; set; }
    public TimeSpan? QuietHoursEnd { get; set; }
    public string TimeZone { get; set; }
}
