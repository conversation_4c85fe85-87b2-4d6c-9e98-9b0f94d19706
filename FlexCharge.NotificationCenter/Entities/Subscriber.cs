using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.NotificationCenter.Entities;

[Index(nameof(Email), IsUnique = true)]
[Index(nameof(UserId), IsUnique = true)]
public class Subscriber : AuditableEntity
{
    public string Email { get; set; }
    public string PhoneNumber { get; set; } // E.164 format
    public string UserId { get; set; } // User ID in the system
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public bool IsActive { get; set; } = true; // Indicates if the subscriber is active

    // Navigation properties
    public SubscriberPreferences Preferences { get; set; }

    public ICollection<SubscriberNotification> SubscriberNotifications { get; set; } =
        new List<SubscriberNotification>();
}