using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace FlexCharge.NotificationCenter.Entities;

public class ChannelTemplate : AuditableEntity
{
    public string EventType { get; set; }
    public string TemplateKey { get; set; }
    public string Channel { get; set; }

    // Template content with merge field placeholders (e.g., "Hello {{name}}, your order {{orderId}} is confirmed")
    public string TemplateContent { get; set; }

    // Subject template for email notifications (e.g., "Order {{orderId}} Confirmation")
    public string SubjectTemplate { get; set; }

    // Required merge fields as JSON array (e.g., ["name", "orderId", "amount"])
    [Column(TypeName = "jsonb")] public List<string> RequiredMergeFields { get; set; } = new();

    // Optional merge fields as JSON array (e.g., ["customerPhone", "deliveryDate"])
    [Column(TypeName = "jsonb")] public List<string> OptionalMergeFields { get; set; } = new();

    // Template validation rules as JSON (e.g., {"orderId": {"type": "string", "minLength": 1}})
    [Column(TypeName = "jsonb")] public string ValidationRules { get; set; }

    // Default values for optional fields as JSON (e.g., {"deliveryDate": "TBD"})
    [Column(TypeName = "jsonb")] public string DefaultValues { get; set; }

    // Template description for documentation
    public string Description { get; set; }

    // Whether this template is active
    public bool IsActive { get; set; } = true;
}