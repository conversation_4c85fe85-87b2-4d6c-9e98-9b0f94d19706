-- Sample Channel Templates with Merge Field Validation
-- These examples show how to configure templates for different event types

-- Order Confirmation Email Template
INSERT INTO "ChannelTemplates" ("Id", "EventType", "Template<PERSON>ey", "Channel",
                                "TemplateContent", "SubjectTemplate",
                                "RequiredMergeFields", "OptionalMergeFields",
                                "ValidationRules", "DefaultValues",
                                "Description", "IsActive",
                                "CreatedOn", "ModifiedOn", "IsDeleted")
VALUES (gen_random_uuid(),
        'orders.created',
        'order_confirmation_email',
        'email',
        'Hello {{customerName}},
    
    Thank you for your order! Your order #{{orderId}} has been confirmed.
    
    Order Details:
    - Amount: {{amount}} {{currency}}
    - Payment Method: {{paymentMethod}}
    - Order Date: {{orderDate}}
    
    {{#deliveryDate}}
    Expected Delivery: {{deliveryDate}}
    {{/deliveryDate}}
    
    You can track your order at: {{trackingUrl}}
    
    Best regards,
    {{merchantName}} Team',
        'Order Confirmation #{{orderId}} - {{merchantName}}',
        '["customerName", "orderId", "amount", "currency", "paymentMethod", "orderDate", "merchantName", "trackingUrl"]'::jsonb,
        '["deliveryDate", "customerPhone", "specialInstructions"]'::jsonb,
        '{
            "customerName": {"type": "string", "minLength": 1, "maxLength": 100},
            "orderId": {"type": "string", "minLength": 1, "pattern": "^[A-Z0-9-]+$"},
            "amount": {"type": "number", "minValue": 0},
            "currency": {"type": "string", "allowedValues": ["USD", "EUR", "GBP", "CAD"]},
            "paymentMethod": {"type": "string", "allowedValues": ["credit_card", "debit_card", "paypal", "bank_transfer"]},
            "orderDate": {"type": "date"},
            "merchantName": {"type": "string", "minLength": 1, "maxLength": 100},
            "trackingUrl": {"type": "string", "pattern": "^https?://.*"}
        }'::jsonb,
        '{
            "deliveryDate": "TBD",
            "specialInstructions": "None"
        }'::jsonb,
        'Email template for order confirmation notifications',
        true,
        NOW(),
        NOW(),
        false);

-- Order Confirmation SMS Template
INSERT INTO "ChannelTemplates" ("Id", "EventType", "TemplateKey", "Channel",
                                "TemplateContent", "SubjectTemplate",
                                "RequiredMergeFields", "OptionalMergeFields",
                                "ValidationRules", "DefaultValues",
                                "Description", "IsActive",
                                "CreatedOn", "ModifiedOn", "IsDeleted")
VALUES (gen_random_uuid(),
        'orders.created',
        'order_confirmation_sms',
        'sms',
        'Hi {{customerName}}! Your order #{{orderId}} for {{amount}} {{currency}} has been confirmed. Track at: {{trackingUrl}}',
        NULL,
        '["customerName", "orderId", "amount", "currency", "trackingUrl"]'::jsonb,
        '[]'::jsonb,
        '{
            "customerName": {"type": "string", "minLength": 1, "maxLength": 50},
            "orderId": {"type": "string", "minLength": 1},
            "amount": {"type": "number", "minValue": 0},
            "currency": {"type": "string", "allowedValues": ["USD", "EUR", "GBP", "CAD"]},
            "trackingUrl": {"type": "string", "maxLength": 100}
        }'::jsonb,
        '{}'::jsonb,
        'SMS template for order confirmation notifications',
        true,
        NOW(),
        NOW(),
        false);

-- Payment Failed Email Template
INSERT INTO "ChannelTemplates" ("Id", "EventType", "TemplateKey", "Channel",
                                "TemplateContent", "SubjectTemplate",
                                "RequiredMergeFields", "OptionalMergeFields",
                                "ValidationRules", "DefaultValues",
                                "Description", "IsActive",
                                "CreatedOn", "ModifiedOn", "IsDeleted")
VALUES (gen_random_uuid(),
        'payments.failed',
        'payment_failed_email',
        'email',
        'Hello {{customerName}},
    
    We were unable to process your payment for order #{{orderId}}.
    
    Reason: {{failureReason}}
    Amount: {{amount}} {{currency}}
    Attempted on: {{attemptDate}}
    
    Please update your payment method and try again: {{retryUrl}}
    
    If you need assistance, please contact our support team.
    
    Best regards,
    {{merchantName}} Team',
        'Payment Failed for Order #{{orderId}} - {{merchantName}}',
        '["customerName", "orderId", "failureReason", "amount", "currency", "attemptDate", "retryUrl", "merchantName"]'::jsonb,
        '["supportEmail", "supportPhone"]'::jsonb,
        '{
            "customerName": {"type": "string", "minLength": 1, "maxLength": 100},
            "orderId": {"type": "string", "minLength": 1},
            "failureReason": {"type": "string", "minLength": 1, "maxLength": 200},
            "amount": {"type": "number", "minValue": 0},
            "currency": {"type": "string", "allowedValues": ["USD", "EUR", "GBP", "CAD"]},
            "attemptDate": {"type": "date"},
            "retryUrl": {"type": "string", "pattern": "^https?://.*"},
            "merchantName": {"type": "string", "minLength": 1, "maxLength": 100}
        }'::jsonb,
        '{
            "supportEmail": "<EMAIL>",
            "supportPhone": "1-800-SUPPORT"
        }'::jsonb,
        'Email template for payment failure notifications',
        true,
        NOW(),
        NOW(),
        false);

-- Welcome Email Template
INSERT INTO "ChannelTemplates" ("Id", "EventType", "TemplateKey", "Channel",
                                "TemplateContent", "SubjectTemplate",
                                "RequiredMergeFields", "OptionalMergeFields",
                                "ValidationRules", "DefaultValues",
                                "Description", "IsActive",
                                "CreatedOn", "ModifiedOn", "IsDeleted")
VALUES (gen_random_uuid(),
        'users.registered',
        'welcome_email',
        'email',
        'Welcome to {{platformName}}, {{firstName}}!
    
    Thank you for joining us. Your account has been successfully created.
    
    Account Details:
    - Email: {{email}}
    - Registration Date: {{registrationDate}}
    
    {{#verificationRequired}}
    Please verify your email address by clicking here: {{verificationUrl}}
    {{/verificationRequired}}
    
    Get started by exploring our features: {{dashboardUrl}}
    
    Best regards,
    The {{platformName}} Team',
        'Welcome to {{platformName}}, {{firstName}}!',
        '["firstName", "email", "registrationDate", "platformName", "dashboardUrl"]'::jsonb,
        '["lastName", "verificationRequired", "verificationUrl", "referralCode"]'::jsonb,
        '{
            "firstName": {"type": "string", "minLength": 1, "maxLength": 50},
            "email": {"type": "email"},
            "registrationDate": {"type": "date"},
            "platformName": {"type": "string", "minLength": 1, "maxLength": 100},
            "dashboardUrl": {"type": "string", "pattern": "^https?://.*"}
        }'::jsonb,
        '{
            "verificationRequired": false,
            "lastName": ""
        }'::jsonb,
        'Email template for user registration welcome notifications',
        true,
        NOW(),
        NOW(),
        false);
