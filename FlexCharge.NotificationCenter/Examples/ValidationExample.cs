using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading.Tasks;
using FlexCharge.NotificationCenter.Entities;
using FlexCharge.NotificationCenter.Services;
using FlexCharge.NotificationCenter.Services.Models;

namespace FlexCharge.NotificationCenter.Examples;

/// <summary>
/// Example demonstrating merge field validation usage
/// </summary>
public class ValidationExample
{
    private readonly IMergeFieldValidationService _validationService;

    public ValidationExample(IMergeFieldValidationService validationService)
    {
        _validationService = validationService;
    }

    public async Task<bool> DemonstrateValidation()
    {
        // Create a sample template
        var template = CreateSampleTemplate();

        // Test Case 1: Valid merge fields
        Console.WriteLine("=== Test Case 1: Valid Merge Fields ===");
        var validMergeFields = new Dictionary<string, object>
        {
            ["customerName"] = "John Doe",
            ["orderId"] = "ORD-12345",
            ["amount"] = 99.99,
            ["currency"] = "USD",
            ["paymentMethod"] = "credit_card",
            ["orderDate"] = "2024-01-15",
            ["merchantName"] = "Acme Store",
            ["trackingUrl"] = "https://track.acme.com/ORD-12345"
        };

        var result1 = await _validationService.ValidateAndProcessAsync(template, validMergeFields);
        Console.WriteLine($"Valid: {result1.IsValid}");
        if (result1.IsValid)
        {
            Console.WriteLine("Processed Content:");
            Console.WriteLine(result1.ProcessedContent);
            Console.WriteLine("\nProcessed Subject:");
            Console.WriteLine(result1.ProcessedSubject);
        }

        // Test Case 2: Missing required fields
        Console.WriteLine("\n=== Test Case 2: Missing Required Fields ===");
        var invalidMergeFields = new Dictionary<string, object>
        {
            ["customerName"] = "Jane Doe",
            ["orderId"] = "ORD-67890"
            // Missing: amount, currency, paymentMethod, orderDate, merchantName, trackingUrl
        };

        var result2 = await _validationService.ValidateAndProcessAsync(template, invalidMergeFields);
        Console.WriteLine($"Valid: {result2.IsValid}");
        if (!result2.IsValid)
        {
            Console.WriteLine("Missing Required Fields:");
            foreach (var field in result2.MissingRequiredFields)
            {
                Console.WriteLine($"  - {field}");
            }
        }

        // Test Case 3: Invalid field values
        Console.WriteLine("\n=== Test Case 3: Invalid Field Values ===");
        var invalidValueFields = new Dictionary<string, object>
        {
            ["customerName"] = "", // Too short
            ["orderId"] = "invalid-order-id", // Doesn't match pattern
            ["amount"] = -10, // Negative amount
            ["currency"] = "INVALID", // Not in allowed values
            ["paymentMethod"] = "bitcoin", // Not in allowed values
            ["orderDate"] = "invalid-date", // Invalid date format
            ["merchantName"] = "Acme Store",
            ["trackingUrl"] = "not-a-url" // Invalid URL format
        };

        var result3 = await _validationService.ValidateAndProcessAsync(template, invalidValueFields);
        Console.WriteLine($"Valid: {result3.IsValid}");
        if (!result3.IsValid)
        {
            Console.WriteLine("Validation Errors:");
            foreach (var error in result3.ValidationErrors)
            {
                Console.WriteLine($"  - {error}");
            }
        }

        // Test Case 4: With optional fields and defaults
        Console.WriteLine("\n=== Test Case 4: With Optional Fields and Defaults ===");
        var fieldsWithOptional = new Dictionary<string, object>
        {
            ["customerName"] = "Bob Smith",
            ["orderId"] = "ORD-11111",
            ["amount"] = 149.99,
            ["currency"] = "EUR",
            ["paymentMethod"] = "paypal",
            ["orderDate"] = "2024-01-16",
            ["merchantName"] = "Euro Store",
            ["trackingUrl"] = "https://track.eurostore.com/ORD-11111",
            ["deliveryDate"] = "2024-01-20", // Optional field provided
            // specialInstructions not provided - will use default
        };

        var result4 = await _validationService.ValidateAndProcessAsync(template, fieldsWithOptional);
        Console.WriteLine($"Valid: {result4.IsValid}");
        if (result4.IsValid)
        {
            Console.WriteLine("Processed Content (with optional fields):");
            Console.WriteLine(result4.ProcessedContent);
            
            Console.WriteLine("\nProcessed Merge Fields:");
            foreach (var field in result4.ProcessedMergeFields)
            {
                Console.WriteLine($"  {field.Key}: {field.Value}");
            }
        }

        return result1.IsValid && !result2.IsValid && !result3.IsValid && result4.IsValid;
    }

    private ChannelTemplate CreateSampleTemplate()
    {
        return new ChannelTemplate
        {
            Id = Guid.NewGuid(),
            EventType = "orders.created",
            Channel = "email",
            TemplateKey = "order_confirmation",
            TemplateContent = @"Hello {{customerName}},

                    Thank you for your order! Your order #{{orderId}} has been confirmed.

                    Order Details:
                    - Amount: {{amount}} {{currency}}
                    - Payment Method: {{paymentMethod}}
                    - Order Date: {{orderDate}}
                    - Delivery Date: {{deliveryDate}}
                    - Special Instructions: {{specialInstructions}}

                    You can track your order at: {{trackingUrl}}

                    Best regards,
                    {{merchantName}} Team",
            SubjectTemplate = "Order Confirmation #{{orderId}} - {{merchantName}}",
            RequiredMergeFields = new List<string> 
            { 
                "customerName", "orderId", "amount", "currency", 
                "paymentMethod", "orderDate", "merchantName", "trackingUrl" 
            },
            OptionalMergeFields = new List<string> { "deliveryDate", "specialInstructions" },
            ValidationRules = JsonSerializer.Serialize(new {
                customerName = new { type = "string", minLength = 1, maxLength = 100 },
                orderId = new { type = "string", pattern = "^[A-Z0-9-]+$" },
                amount = new { type = "number", minValue = 0 },
                currency = new { type = "string", allowedValues = new[] { "USD", "EUR", "GBP", "CAD" } },
                paymentMethod = new { type = "string", allowedValues = new[] { "credit_card", "debit_card", "paypal" } },
                orderDate = new { type = "date" },
                merchantName = new { type = "string", minLength = 1, maxLength = 100 },
                trackingUrl = new { type = "string", pattern = "^https?://.*" }
            }),
            DefaultValues = JsonSerializer.Serialize(new {
                deliveryDate = "TBD",
                specialInstructions = "None"
            }),
            Description = "Email template for order confirmation notifications",
            IsActive = true
        };
    }
}
