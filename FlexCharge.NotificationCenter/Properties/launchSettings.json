{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:5091", "sslPort": 0}}, "$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": false, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "DB_HOST": "localhost", "DB_PORT": "5432", "DB_DATABASE": "fc_notificationcenter", "DB_USERNAME": "notification_center_service_staging", "DB_PASSWORD": "11111", "SNS_IAM_REGION": "us-east-1", "SNS_IAM_ACCESS_KEY": "********************", "SNS_IAM_SECRET_KEY": "uPO48OgTDXReduu6gz7QzqtuQalSgLnkB6ZHDRtW"}}, "FlexCharge.NotificationCenter": {"commandName": "Project", "launchBrowser": false, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "DB_HOST": "localhost", "DB_PORT": "5432", "DB_DATABASE": "fc_notificationcenter", "DB_USERNAME": "notification_center_service_staging", "DB_PASSWORD": "11111", "SNS_IAM_REGION": "us-east-1", "SNS_IAM_ACCESS_KEY": "********************", "SNS_IAM_SECRET_KEY": "uPO48OgTDXReduu6gz7QzqtuQalSgLnkB6ZHDRtW", "REDSHIFT_CONNECTION_STRING": "arn:aws:secretsmanager:us-east-1:556663010871:secret:prod/redshift-sydKSu"}, "applicationUrl": "https://localhost:5210;http://localhost:5211"}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": false, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/swagger", "publishAllPorts": true, "environmentVariables": {"DB_HOST": "host.docker.internal", "DB_PORT": "5432", "DB_DATABASE": "fc_notificationcenter", "DB_USERNAME": "notification_center_service_staging", "DB_PASSWORD": "11111", "SNS_IAM_REGION": "us-east-1", "SNS_IAM_ACCESS_KEY": "********************", "SNS_IAM_SECRET_KEY": "uPO48OgTDXReduu6gz7QzqtuQalSgLnkB6ZHDRtW", "REDSHIFT_CONNECTION_STRING": "arn:aws:secretsmanager:us-east-1:556663010871:secret:prod/redshift-sydKSu"}}}}