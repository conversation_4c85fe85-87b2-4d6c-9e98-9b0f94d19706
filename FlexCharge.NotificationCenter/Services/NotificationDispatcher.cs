using System;
using System.Threading.Tasks;
using FlexCharge.Common.Emails;
using FlexCharge.Common.Sms;
using FlexCharge.NotificationCenter.Services.Models;
using SendGrid.Helpers.Mail;

namespace FlexCharge.NotificationCenter.Services;

public class NotificationDispatcher : INotificationDispatcher
{
    private readonly ISmsServices _smsServices;
    private readonly IEmailSender _emailSender;

    public NotificationDispatcher(ISmsServices smsServices, IEmailSender emailSender)
    {
        _smsServices = smsServices;
        _emailSender = emailSender;
    }

    public async Task SendAsync(string channel, NotificationTarget target, IChannelPayload payload)
    {
        switch (channel.ToLower())
        {
            case "sms":
                await SendSmsAsync(target, payload as SmsChannelPayload ?? new SmsChannelPayload { Message = payload.Message });
                break;
            case "email":
                await SendEmailAsync(target, payload as EmailChannelPayload ?? new EmailChannelPayload { Title = payload.Title, Message = payload.Message });
                break;
            case "push":
                await SendPushAsync(target, payload as PushChannelPayload ?? new PushChannelPayload { Title = payload.Title, Message = payload.Message });
                break;
            case "in-app":
                await SendInAppAsync(target, payload as InAppChannelPayload ?? new InAppChannelPayload { Title = payload.Title, Message = payload.Message });
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(channel), $"Invalid channel: {channel}");
        }
    }

    private async Task SendSmsAsync(NotificationTarget target, SmsChannelPayload payload)
    {
        var smsRequest = new SmsRequest
        {
            PhoneNumber = target.PhoneNumber,
            Message = payload.Message,
        };
        await _smsServices.SendSmsAsync(smsRequest);
    }

    private async Task SendEmailAsync(NotificationTarget target, EmailChannelPayload payload)
    {
        var emailRequest = new EmailRequestDTO
        {
            email = target.Email,
            subject = payload.Title,
            message = payload.Message,
        };
        // Implementation would depend on the email service configuration
        // await _emailSender.SendEmailAsync(emailRequest.email, emailRequest.subject, emailRequest.message);
    }

    private async Task SendPushAsync(NotificationTarget target, PushChannelPayload payload)
    {
        // Implementation for push notifications
        // This would typically integrate with a push notification service like Firebase, APNs, etc.
        await Task.CompletedTask;
    }

    private async Task SendInAppAsync(NotificationTarget target, InAppChannelPayload payload)
    {
        // Implementation for in-app notifications
        // This might store the notification in the database for the user to see when they log in
        await Task.CompletedTask;
    }
}
