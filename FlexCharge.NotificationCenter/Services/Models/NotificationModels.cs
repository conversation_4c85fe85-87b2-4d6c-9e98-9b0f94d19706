namespace FlexCharge.NotificationCenter.Services.Models;

public class NotificationTarget
{
    public string Email { get; set; }
    public string PhoneNumber { get; set; }
    public string UserId { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
}

public interface IChannelPayload
{
    string Title { get; set; }
    string Message { get; set; }
}

public class EmailChannelPayload : IChannelPayload
{
    public string Title { get; set; }
    public string Message { get; set; }
    public string Subject { get; set; }
    public string BodyHtml { get; set; }
    public string TemplateId { get; set; }
    public string SenderEmail { get; set; }
    
}

public class SmsChannelPayload : IChannelPayload
{
    public string Title { get; set; }
    public string Message { get; set; }
}

public class PushChannelPayload : IChannelPayload
{
    public string Title { get; set; }
    public string Message { get; set; }
    public string Icon { get; set; }
    public string Url { get; set; }
}

public class InAppChannelPayload : IChannelPayload
{
    public string Title { get; set; }
    public string Message { get; set; }
    public string ActionUrl { get; set; }
    public string Category { get; set; }
}
