using System.Collections.Generic;

namespace FlexCharge.NotificationCenter.Services.Models;

public class MergeFieldValidationResult
{
    public bool IsValid { get; set; }
    public List<string> MissingRequiredFields { get; set; } = new();
    public List<string> ValidationErrors { get; set; } = new();
    public Dictionary<string, object> ProcessedMergeFields { get; set; } = new();
    public string ProcessedContent { get; set; }
    public string ProcessedSubject { get; set; }
}

public class FieldValidationRule
{
    public string Type { get; set; } // "string", "number", "email", "date", "boolean"
    public int? MinLength { get; set; }
    public int? MaxLength { get; set; }
    public object MinValue { get; set; }
    public object MaxValue { get; set; }
    public string Pattern { get; set; } // Regex pattern
    public List<string> AllowedValues { get; set; } = new();
    public bool Required { get; set; } = true;
}

public class TemplateValidationRules
{
    public Dictionary<string, FieldValidationRule> Fields { get; set; } = new();
}

public class TemplateDefaultValues
{
    public Dictionary<string, object> Values { get; set; } = new();
}

public class MergeFieldProcessor
{
    public string ProcessTemplate(string template, Dictionary<string, object> mergeFields)
    {
        if (string.IsNullOrEmpty(template) || mergeFields == null)
            return template;

        var result = template;
        foreach (var field in mergeFields)
        {
            var placeholder = $"{{{{{field.Key}}}}}";
            var value = field.Value?.ToString() ?? "";
            result = result.Replace(placeholder, value);
        }

        return result;
    }

    public List<string> ExtractPlaceholders(string template)
    {
        var placeholders = new List<string>();
        if (string.IsNullOrEmpty(template))
            return placeholders;

        var startIndex = 0;
        while (true)
        {
            var start = template.IndexOf("{{", startIndex);
            if (start == -1) break;

            var end = template.IndexOf("}}", start + 2);
            if (end == -1) break;

            var fieldName = template.Substring(start + 2, end - start - 2).Trim();
            if (!placeholders.Contains(fieldName))
                placeholders.Add(fieldName);

            startIndex = end + 2;
        }

        return placeholders;
    }
}