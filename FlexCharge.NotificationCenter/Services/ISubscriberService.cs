using System.Threading.Tasks;
using FlexCharge.NotificationCenter.Entities;

namespace FlexCharge.NotificationCenter.Services;

public interface ISubscriberService
{
    Task<SubscriberPreferences> GetPreferencesAsync(string audience);
    Task<Subscriber> GetSubscriberAsync(string userId);

    Task<Subscriber> CreateSubscriberAsync(string email, string userId, string firstName = null,
        string lastName = null);

    Task UpdatePreferencesAsync(string userId, SubscriberPreferences preferences);
}