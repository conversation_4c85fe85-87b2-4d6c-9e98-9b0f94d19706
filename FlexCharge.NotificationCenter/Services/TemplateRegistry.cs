using System.Threading.Tasks;
using FlexCharge.NotificationCenter.Entities;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.NotificationCenter.Services;

public class TemplateRegistry : ITemplateRegistry
{
    private readonly PostgreSQLDbContext _dbContext;
    
    public TemplateRegistry(PostgreSQLDbContext dbContext)
    {
        _dbContext = dbContext;
    }
    
    public async Task<ChannelTemplate> GetTemplateAsync(string eventType, string channel)
    {
        return await _dbContext.ChannelTemplates
            .FirstOrDefaultAsync(t => t.EventType == eventType && t.Channel == channel);
    }
}
