using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.NotificationCenter.Entities;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.NotificationCenter.Services;

public class SubscriberService : ISubscriberService
{
    private readonly PostgreSQLDbContext _dbContext;

    public SubscriberService(PostgreSQLDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<SubscriberPreferences> GetPreferencesAsync(string audience)
    {
        // Try to find subscriber by email or userId
        var subscriber = await _dbContext.Subscribers
            .Include(s => s.Preferences)
            .FirstOrDefaultAsync(s => s.Email == audience || s.UserId == audience);

        if (subscriber?.Preferences != null)
        {
            return subscriber.Preferences;
        }

        // Return default preferences if subscriber not found
        return new SubscriberPreferences
        {
            Channels = new List<string> { "email" }, // Default to email notifications
            Language = "en",
            IsActive = true,
            EmailEnabled = true,
            SmsEnabled = false,
            PushEnabled = false,
            InAppEnabled = true,
            EmailFrequency = "Immediate",
            SmsFrequency = "Immediate"
        };
    }

    public async Task<Subscriber> GetSubscriberAsync(string userId)
    {
        return await _dbContext.Subscribers
            .Include(s => s.Preferences)
            .FirstOrDefaultAsync(s => s.UserId == userId);
    }

    public async Task<Subscriber> CreateSubscriberAsync(string email, string userId, string firstName = null, string lastName = null)
    {
        var subscriber = new Subscriber
        {
            Email = email,
            UserId = userId,
            FirstName = firstName,
            LastName = lastName,
            IsActive = true
        };

        _dbContext.Subscribers.Add(subscriber);
        await _dbContext.SaveChangesAsync();

        // Create default preferences
        var preferences = new SubscriberPreferences
        {
            SubscriberId = subscriber.Id,
            Channels = new List<string> { "email", "in-app" },
            Language = "en",
            IsActive = true,
            EmailEnabled = true,
            SmsEnabled = false,
            PushEnabled = false,
            InAppEnabled = true,
            EmailFrequency = "Immediate",
            SmsFrequency = "Immediate"
        };

        _dbContext.SubscriberPreferences.Add(preferences);
        await _dbContext.SaveChangesAsync();

        return subscriber;
    }

    public async Task UpdatePreferencesAsync(string userId, SubscriberPreferences preferences)
    {
        var subscriber = await GetSubscriberAsync(userId);
        if (subscriber == null)
        {
            throw new ArgumentException($"Subscriber with userId {userId} not found");
        }

        var existingPreferences = await _dbContext.SubscriberPreferences
            .FirstOrDefaultAsync(sp => sp.SubscriberId == subscriber.Id);

        if (existingPreferences != null)
        {
            // Update existing preferences
            existingPreferences.Channels = preferences.Channels;
            existingPreferences.Language = preferences.Language;
            existingPreferences.IsActive = preferences.IsActive;
            existingPreferences.EmailEnabled = preferences.EmailEnabled;
            existingPreferences.SmsEnabled = preferences.SmsEnabled;
            existingPreferences.PushEnabled = preferences.PushEnabled;
            existingPreferences.InAppEnabled = preferences.InAppEnabled;
            existingPreferences.EmailFrequency = preferences.EmailFrequency;
            existingPreferences.SmsFrequency = preferences.SmsFrequency;
            existingPreferences.QuietHoursStart = preferences.QuietHoursStart;
            existingPreferences.QuietHoursEnd = preferences.QuietHoursEnd;
            existingPreferences.TimeZone = preferences.TimeZone;
        }
        else
        {
            // Create new preferences
            preferences.SubscriberId = subscriber.Id;
            _dbContext.SubscriberPreferences.Add(preferences);
        }

        await _dbContext.SaveChangesAsync();
    }
}
