using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using FlexCharge.NotificationCenter.Entities;
using FlexCharge.NotificationCenter.Services.Models;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.NotificationCenter.Services;

public class MergeFieldValidationService : IMergeFieldValidationService
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly MergeFieldProcessor _processor;

    public MergeFieldValidationService(PostgreSQLDbContext dbContext)
    {
        _dbContext = dbContext;
        _processor = new MergeFieldProcessor();
    }

    public async Task<MergeFieldValidationResult> ValidateAndProcessAsync(
        ChannelTemplate template,
        Dictionary<string, object> mergeFields)
    {
        var result = new MergeFieldValidationResult();

        if (template == null)
        {
            result.ValidationErrors.Add("Template not found");
            return result;
        }

        // Parse validation rules
        TemplateValidationRules validationRules = null;
        if (!string.IsNullOrEmpty(template.ValidationRules))
        {
            try
            {
                validationRules = JsonSerializer.Deserialize<TemplateValidationRules>(template.ValidationRules);
            }
            catch (JsonException ex)
            {
                result.ValidationErrors.Add($"Invalid validation rules format: {ex.Message}");
            }
        }

        // Parse default values
        TemplateDefaultValues defaultValues = null;
        if (!string.IsNullOrEmpty(template.DefaultValues))
        {
            try
            {
                defaultValues = JsonSerializer.Deserialize<TemplateDefaultValues>(template.DefaultValues);
            }
            catch (JsonException ex)
            {
                result.ValidationErrors.Add($"Invalid default values format: {ex.Message}");
            }
        }

        // Apply default values for missing optional fields
        var processedMergeFields = new Dictionary<string, object>(mergeFields ?? new Dictionary<string, object>());
        if (defaultValues?.Values != null)
        {
            foreach (var defaultValue in defaultValues.Values)
            {
                if (!processedMergeFields.ContainsKey(defaultValue.Key))
                {
                    processedMergeFields[defaultValue.Key] = defaultValue.Value;
                }
            }
        }

        // Validate fields
        var validationResult = ValidateFields(
            template.RequiredMergeFields ?? new List<string>(),
            template.OptionalMergeFields ?? new List<string>(),
            processedMergeFields,
            validationRules);

        result.IsValid = validationResult.IsValid;
        result.MissingRequiredFields = validationResult.MissingRequiredFields;
        result.ValidationErrors.AddRange(validationResult.ValidationErrors);
        result.ProcessedMergeFields = processedMergeFields;

        // Process templates if validation passed
        if (result.IsValid)
        {
            result.ProcessedContent = ProcessTemplate(template.TemplateContent, processedMergeFields);
            result.ProcessedSubject = ProcessTemplate(template.SubjectTemplate, processedMergeFields);
        }

        return result;
    }

    public async Task<List<string>> GetRequiredFieldsAsync(string eventType, string channel)
    {
        var template = await _dbContext.ChannelTemplates
            .FirstOrDefaultAsync(t => t.EventType == eventType && t.Channel == channel && t.IsActive);

        return template?.RequiredMergeFields ?? new List<string>();
    }

    public async Task<List<string>> GetOptionalFieldsAsync(string eventType, string channel)
    {
        var template = await _dbContext.ChannelTemplates
            .FirstOrDefaultAsync(t => t.EventType == eventType && t.Channel == channel && t.IsActive);

        return template?.OptionalMergeFields ?? new List<string>();
    }

    public MergeFieldValidationResult ValidateFields(
        List<string> requiredFields,
        List<string> optionalFields,
        Dictionary<string, object> mergeFields,
        TemplateValidationRules validationRules = null)
    {
        var result = new MergeFieldValidationResult
        {
            ProcessedMergeFields = new Dictionary<string, object>(mergeFields ?? new Dictionary<string, object>())
        };

        // Check for missing required fields
        foreach (var requiredField in requiredFields ?? new List<string>())
        {
            if (!mergeFields.ContainsKey(requiredField) || mergeFields[requiredField] == null)
            {
                result.MissingRequiredFields.Add(requiredField);
            }
        }

        // Validate field values against rules
        if (validationRules?.Fields != null)
        {
            foreach (var field in mergeFields)
            {
                if (validationRules.Fields.TryGetValue(field.Key, out var rule))
                {
                    var validationError = ValidateFieldValue(field.Key, field.Value, rule);
                    if (!string.IsNullOrEmpty(validationError))
                    {
                        result.ValidationErrors.Add(validationError);
                    }
                }
            }
        }

        result.IsValid = result.MissingRequiredFields.Count == 0 && result.ValidationErrors.Count == 0;
        return result;
    }

    public string ProcessTemplate(string template, Dictionary<string, object> mergeFields)
    {
        return _processor.ProcessTemplate(template, mergeFields);
    }

    private string ValidateFieldValue(string fieldName, object value, FieldValidationRule rule)
    {
        if (value == null)
        {
            return rule.Required ? $"Field '{fieldName}' is required" : null;
        }

        var stringValue = value.ToString();

        // Type validation
        switch (rule.Type?.ToLower())
        {
            case "email":
                if (!IsValidEmail(stringValue))
                    return $"Field '{fieldName}' must be a valid email address";
                break;
            case "number":
                if (!double.TryParse(stringValue, out var numValue))
                    return $"Field '{fieldName}' must be a valid number";
                if (rule.MinValue != null && numValue < Convert.ToDouble(rule.MinValue))
                    return $"Field '{fieldName}' must be at least {rule.MinValue}";
                if (rule.MaxValue != null && numValue > Convert.ToDouble(rule.MaxValue))
                    return $"Field '{fieldName}' must be at most {rule.MaxValue}";
                break;
            case "date":
                if (!DateTime.TryParse(stringValue, out _))
                    return $"Field '{fieldName}' must be a valid date";
                break;
            case "boolean":
                if (!bool.TryParse(stringValue, out _))
                    return $"Field '{fieldName}' must be a valid boolean (true/false)";
                break;
        }

        // Length validation
        if (rule.MinLength.HasValue && stringValue.Length < rule.MinLength.Value)
            return $"Field '{fieldName}' must be at least {rule.MinLength.Value} characters long";
        if (rule.MaxLength.HasValue && stringValue.Length > rule.MaxLength.Value)
            return $"Field '{fieldName}' must be at most {rule.MaxLength.Value} characters long";

        // Pattern validation
        if (!string.IsNullOrEmpty(rule.Pattern))
        {
            if (!Regex.IsMatch(stringValue, rule.Pattern))
                return $"Field '{fieldName}' does not match the required pattern";
        }

        // Allowed values validation
        if (rule.AllowedValues?.Count > 0)
        {
            if (!rule.AllowedValues.Contains(stringValue))
                return $"Field '{fieldName}' must be one of: {string.Join(", ", rule.AllowedValues)}";
        }

        return null;
    }

    private bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}