using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.NotificationCenter.Entities;
using FlexCharge.NotificationCenter.Services.Models;

namespace FlexCharge.NotificationCenter.Services;

public interface IMergeFieldValidationService
{
    Task<MergeFieldValidationResult> ValidateAndProcessAsync(
        ChannelTemplate template,
        Dictionary<string, object> mergeFields);

    Task<List<string>> GetRequiredFieldsAsync(string eventType, string channel);

    Task<List<string>> GetOptionalFieldsAsync(string eventType, string channel);

    MergeFieldValidationResult ValidateFields(
        List<string> requiredFields,
        List<string> optionalFields,
        Dictionary<string, object> mergeFields,
        TemplateValidationRules validationRules = null);

    string ProcessTemplate(string template, Dictionary<string, object> mergeFields);
}