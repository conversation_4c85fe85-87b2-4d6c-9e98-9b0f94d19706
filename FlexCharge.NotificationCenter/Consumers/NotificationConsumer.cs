using System.Threading.Tasks;
using FlexCharge.Common.NotificationCenter;
using FlexCharge.NotificationCenter.Services;
using FlexCharge.NotificationCenter.Services.Models;
using MassTransit;

namespace FlexCharge.NotificationCenter.Consumers;

public class NotificationConsumer : IConsumer<INotificationMessage>
{
    private readonly INotificationPermissionsService _permissionsService;
    private readonly ISubscriberService _subscriberService;
    private readonly INotificationDispatcher _dispatcher;
    private readonly ITemplateRegistry _templateRegistry;
    

    public NotificationConsumer(
        INotificationPermissionsService permissionsService,
        ISubscriberService subscriberService,
        ITemplateRegistry templateRegistry,
        INotificationDispatcher dispatcher)
    {
        _permissionsService = permissionsService;
        _subscriberService = subscriberService;
        _templateRegistry = templateRegistry;
        _dispatcher = dispatcher;
    }

    public async Task Consume(ConsumeContext<INotificationMessage> context)
    {
        var message = context.Message;

        foreach (var audience in message.Audiences)
        {
            if (!await _permissionsService.IsAllowedAsync(audience, message.EventType))
                continue;

            var preferences = await _subscriberService.GetPreferencesAsync(audience);

            foreach (var channel in preferences.Channels)
            {
                var template = await _templateRegistry.GetTemplateAsync(message.EventType, channel);
                if (template == null) continue;

                // Extract title and message from merge fields or use defaults
                var title = message.Name ?? message.EventType;
                var messageText = message.Description ?? "Notification";
                
                // Try to get title and message from merge fields
                if (message.MergeFields != null)
                {
                    if (message.MergeFields.ContainsKey("title"))
                        title = message.MergeFields["title"]?.ToString() ?? title;
                    if (message.MergeFields.ContainsKey("message"))
                        messageText = message.MergeFields["message"]?.ToString() ?? messageText;
                    if (message.MergeFields.ContainsKey("subject"))
                        title = message.MergeFields["subject"]?.ToString() ?? title;
                }

                // Create the appropriate payload based on channel type
                IChannelPayload payload = channel.ToLower() switch
                {
                    "email" => new EmailChannelPayload 
                    { 
                        Title = title,
                        Message = messageText,
                        Subject = title
                    },
                    "sms" => new SmsChannelPayload 
                    { 
                        Title = title,
                        Message = messageText 
                    },
                    "push" => new PushChannelPayload 
                    { 
                        Title = title,
                        Message = messageText 
                    },
                    "in-app" => new InAppChannelPayload 
                    { 
                        Title = title,
                        Message = messageText 
                    },
                    _ => new SmsChannelPayload 
                    { 
                        Title = title,
                        Message = messageText 
                    }
                };

                // Create notification target
                var target = new NotificationTarget
                {
                    Email = audience, // Assuming audience is email for now
                    UserId = audience
                };
                
                await _dispatcher.SendAsync(channel, target, payload);
            }
        }
    }
}
