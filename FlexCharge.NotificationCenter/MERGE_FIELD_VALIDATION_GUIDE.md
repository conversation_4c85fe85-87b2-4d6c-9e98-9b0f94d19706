# Merge Field Validation System - Complete Guide

## Overview

The NotificationCenter now includes comprehensive merge field validation to ensure that notification publishers send all
required data and that the data meets validation criteria before processing.

## Key Components

### 1. Enhanced ChannelTemplate Entity

```csharp
public class ChannelTemplate : AuditableEntity
{
    // Basic template info
    public string EventType { get; set; }
    public string TemplateKey { get; set; }
    public string Channel { get; set; }
    
    // Template content with merge fields
    public string TemplateContent { get; set; }        // "Hello {{name}}, your order {{orderId}} is confirmed!"
    public string SubjectTemplate { get; set; }        // "Order Confirmation #{{orderId}}"
    
    // Validation configuration
    public List<string> RequiredMergeFields { get; set; }  // ["name", "orderId", "amount"]
    public List<string> OptionalMergeFields { get; set; }  // ["deliveryDate", "notes"]
    public string ValidationRules { get; set; }            // JSON validation rules
    public string DefaultValues { get; set; }              // JSON default values
    
    public string Description { get; set; }
    public bool IsActive { get; set; } = true;
}
```

### 2. Validation Rules Format

```json
{
  "orderId": {
    "type": "string",
    "minLength": 1,
    "maxLength": 50,
    "pattern": "^[A-Z0-9-]+$"
  },
  "amount": {
    "type": "number",
    "minValue": 0,
    "maxValue": 999999
  },
  "email": {
    "type": "email"
  },
  "status": {
    "type": "string",
    "allowedValues": [
      "pending",
      "confirmed",
      "cancelled"
    ]
  },
  "orderDate": {
    "type": "date"
  },
  "isVip": {
    "type": "boolean"
  }
}
```

### 3. Validation Service Interface

```csharp
public interface IMergeFieldValidationService
{
    Task<MergeFieldValidationResult> ValidateAndProcessAsync(
        ChannelTemplate template, 
        Dictionary<string, object> mergeFields);
    
    Task<List<string>> GetRequiredFieldsAsync(string eventType, string channel);
    Task<List<string>> GetOptionalFieldsAsync(string eventType, string channel);
    
    MergeFieldValidationResult ValidateFields(
        List<string> requiredFields,
        List<string> optionalFields,
        Dictionary<string, object> mergeFields,
        TemplateValidationRules validationRules = null);
        
    string ProcessTemplate(string template, Dictionary<string, object> mergeFields);
}
```

## Usage Examples

### Creating a Template

```csharp
var template = new ChannelTemplate
{
    EventType = "orders.created",
    Channel = "email",
    TemplateKey = "order_confirmation",
    TemplateContent = @"Hello {{customerName}},

Thank you for your order! Your order #{{orderId}} has been confirmed.

Order Details:
- Amount: {{amount}} {{currency}}
- Payment Method: {{paymentMethod}}
- Order Date: {{orderDate}}

You can track your order at: {{trackingUrl}}

Best regards,
{{merchantName}} Team",
    SubjectTemplate = "Order Confirmation #{{orderId}} - {{merchantName}}",
    RequiredMergeFields = new List<string> 
    { 
        "customerName", "orderId", "amount", "currency", 
        "paymentMethod", "orderDate", "merchantName", "trackingUrl" 
    },
    OptionalMergeFields = new List<string> { "deliveryDate", "specialInstructions" },
    ValidationRules = JsonSerializer.Serialize(new {
        customerName = new { type = "string", minLength = 1, maxLength = 100 },
        orderId = new { type = "string", pattern = "^[A-Z0-9-]+$" },
        amount = new { type = "number", minValue = 0 },
        currency = new { type = "string", allowedValues = new[] { "USD", "EUR", "GBP", "CAD" } },
        paymentMethod = new { type = "string", allowedValues = new[] { "credit_card", "debit_card", "paypal" } },
        orderDate = new { type = "date" },
        merchantName = new { type = "string", minLength = 1, maxLength = 100 },
        trackingUrl = new { type = "string", pattern = "^https?://.*" }
    }),
    DefaultValues = JsonSerializer.Serialize(new {
        deliveryDate = "TBD",
        specialInstructions = "None"
    }),
    Description = "Email template for order confirmation notifications",
    IsActive = true
};
```

### Publisher Sending Notification

```csharp
// Publisher sends this message
var notificationMessage = new NotificationMessage
{
    EventType = "orders.created",
    Audiences = new[] { "<EMAIL>" },
    MergeFields = new Dictionary<string, string>
    {
        ["customerName"] = "John Doe",
        ["orderId"] = "ORD-12345",
        ["amount"] = "99.99",
        ["currency"] = "USD",
        ["paymentMethod"] = "credit_card",
        ["orderDate"] = "2024-01-15",
        ["merchantName"] = "Acme Store",
        ["trackingUrl"] = "https://track.acme.com/ORD-12345"
        // deliveryDate and specialInstructions are optional - will use defaults
    }
};
```

### Validation Process Flow

1. **Template Lookup**: Find template for event type and channel
2. **Field Validation**: Check required fields are present
3. **Type Validation**: Validate field values against rules
4. **Default Application**: Apply default values for missing optional fields
5. **Template Processing**: Replace placeholders with actual values
6. **Notification Dispatch**: Send processed notification

### Validation Result

```csharp
public class MergeFieldValidationResult
{
    public bool IsValid { get; set; }
    public List<string> MissingRequiredFields { get; set; } = new();
    public List<string> ValidationErrors { get; set; } = new();
    public Dictionary<string, object> ProcessedMergeFields { get; set; } = new();
    public string ProcessedContent { get; set; }
    public string ProcessedSubject { get; set; }
}
```

## Validation Types Supported

| Type      | Description             | Validation Options                                   |
|-----------|-------------------------|------------------------------------------------------|
| `string`  | Text validation         | `minLength`, `maxLength`, `pattern`, `allowedValues` |
| `number`  | Numeric validation      | `minValue`, `maxValue`                               |
| `email`   | Email format validation | Built-in email validation                            |
| `date`    | Date format validation  | Built-in date parsing                                |
| `boolean` | Boolean validation      | Built-in boolean parsing                             |

## Error Handling

### Common Validation Errors

- "Field 'customerName' is required"
- "Field 'amount' must be a valid number"
- "Field 'email' must be a valid email address"
- "Field 'currency' must be one of: USD, EUR, GBP, CAD"
- "Field 'orderId' does not match the required pattern"
- "Field 'amount' must be at least 0"

### Consumer Error Handling

```csharp
var validationResult = await _mergeFieldValidationService.ValidateAndProcessAsync(template, mergeFields);

if (!validationResult.IsValid)
{
    _logger.LogError("Merge field validation failed for EventType={EventType}, Channel={Channel}. " +
                   "Missing fields: {MissingFields}, Validation errors: {ValidationErrors}",
        message.EventType, channel,
        string.Join(", ", validationResult.MissingRequiredFields),
        string.Join(", ", validationResult.ValidationErrors));
    continue; // Skip this notification
}

// Use processed content
var processedMessage = validationResult.ProcessedContent;
var processedSubject = validationResult.ProcessedSubject;
```

## Database Schema Changes

### New Migration: `AddMergeFieldValidationToChannelTemplate`

- Added `TemplateContent` (text)
- Added `SubjectTemplate` (varchar 500)
- Added `RequiredMergeFields` (jsonb)
- Added `OptionalMergeFields` (jsonb)
- Added `ValidationRules` (jsonb)
- Added `DefaultValues` (jsonb)
- Added `Description` (varchar 1000)
- Added `IsActive` (boolean)
- Added composite index on `(EventType, Channel, IsActive)`

## Sample Templates

See `Examples/SampleTemplates.sql` for complete examples:

- Order confirmation (email/SMS)
- Payment failure notifications
- User welcome emails
- With comprehensive validation rules

## Integration Steps

1. **Apply Migration**:
   ```bash
   cd FlexCharge.NotificationCenter
   dotnet ef database update
   ```

2. **Register Services** (in DI container):
   ```csharp
   services.AddScoped<IMergeFieldValidationService, MergeFieldValidationService>();
   ```

3. **Load Sample Templates**:
   ```bash
   psql -d your_database -f Examples/SampleTemplates.sql
   ```

4. **Test Validation**:
   ```csharp
   var result = await mergeFieldValidationService.ValidateAndProcessAsync(template, mergeFields);
   Assert.True(result.IsValid);
   Assert.Contains("Hello John Doe", result.ProcessedContent);
   ```

## Benefits

1. **Data Quality**: Ensures all required data is present and valid
2. **Error Prevention**: Catches validation errors before sending notifications
3. **Consistency**: Standardized template format across all channels
4. **Flexibility**: Support for optional fields with defaults
5. **Maintainability**: Centralized template and validation management
6. **Debugging**: Detailed validation error messages for troubleshooting
