using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace FlexCharge.NotificationCenter;

public class PostgreSQLDbContextFactory : IDesignTimeDbContextFactory<PostgreSQLDbContext>
{
    public PostgreSQLDbContext CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<PostgreSQLDbContext>();

        // Use a default connection string for migrations
        // In a real application, this would come from configuration
        optionsBuilder.UseNpgsql(
            "Host=localhost;Database=flexcharge_notifications;Username=postgres;Password=password");

        // Create a mock IHttpContextAccessor for design-time
        var httpContextAccessor = new MockHttpContextAccessor();

        return new PostgreSQLDbContext(optionsBuilder.Options, httpContextAccessor);
    }
}

// Mock implementation for design-time
public class MockHttpContextAccessor : IHttpContextAccessor
{
    public HttpContext HttpContext { get; set; }
}