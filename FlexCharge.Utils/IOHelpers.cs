using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace FlexCharge.Utils
{
    public static class IOHelper
    {
        public static void DirectoryCopy(string sourceDirName, string destDirName, bool copySubDirs, string searchPattern = null, bool deleteSource = false)
        {
            try
            {
                // Get the subdirectories for the specified directory.
                DirectoryInfo dir = new DirectoryInfo(sourceDirName);

                if (!dir.Exists)
                {
                    throw new DirectoryNotFoundException(
                        "Source directory does not exist or could not be found: "
                        + sourceDirName);
                }

                DirectoryInfo[] dirs = dir.GetDirectories();
                // If the destination directory doesn't exist, create it.
                if (!Directory.Exists(destDirName))
                {
                    Directory.CreateDirectory(destDirName);
                }

                // Get the files in the directory and copy them to the new location.
                FileInfo[] files = dir.GetFiles(searchPattern);
                foreach (FileInfo file in files)
                {
                    string temppath = Path.Combine(destDirName, file.Name);
                    if (!File.Exists(temppath))
                    {
                        file.CopyTo(temppath, false);
                    }
                }

                // If copying subdirectories, copy them and their contents to new location.
                if (copySubDirs)
                {
                    foreach (DirectoryInfo subdir in dirs)
                    {
                        string temppath = Path.Combine(destDirName, subdir.Name);
                        DirectoryCopy(subdir.FullName, temppath, copySubDirs, searchPattern, copySubDirs);
                    }
                }

                if (deleteSource)
                    DirectoryClear(sourceDirName);
            }
            catch (Exception e)
            {
                throw e;
            }

        }
        public static void DirectoryClear(string path, string searchPattern = null, bool deleteDirs = true)
        {
            DirectoryInfo directory = new DirectoryInfo(path);

            if (!directory.Exists)
                throw new DirectoryNotFoundException("Source directory does not exist or could not be found: " + path);

            directory.EnumerateFiles(searchPattern)
            .ToList().ForEach(f => f.Delete());

            if (deleteDirs)
            {
                directory.EnumerateDirectories()
                    .ToList().ForEach(d => d.Delete(true));
            }
        }
        public static FileInfo[] DirectoryGetFiles(string path, string searchPattern)
        {
            DirectoryInfo directory = new DirectoryInfo(path);

            if (!directory.Exists)
                throw new DirectoryNotFoundException("Source directory does not exist or could not be found: " + path);

            var files = directory.GetFiles(searchPattern);

            return files ?? throw new FileNotFoundException();
        }
        public static void DirectoryDelete(string path, bool deleteSubDirs = false)
        {
            DirectoryInfo directory = new DirectoryInfo(path);

            if (!directory.Exists)
                throw new DirectoryNotFoundException("Source directory does not exist or could not be found: " + path);

            directory.Delete(deleteSubDirs);
        }

        public static void DirectoryCreate(string path)
        {
            try
            {
                DirectoryInfo directory = new DirectoryInfo(path);

                if (!directory.Exists)
                    directory.Create();
            }
            catch (IOException ex)
            {
                throw ex;
            }

        }
    }
}
