using System;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace FlexCharge.Utils;

public static class LogExtensions
{
    public static T CopyAndRedact<T>(this T obj, Action<T> redactAction) where T : ISensitiveData
    {
        if (redactAction is null)
            return obj;
        JsonSerializerOptions options = new();
        options.ReferenceHandler = ReferenceHandler.IgnoreCycles;
        var json = JsonSerializer.Serialize(obj, options);
        var copy = JsonSerializer.Deserialize<T>(json, options);
        redactAction(copy);
        return copy;
    }
}

public interface ISensitiveData
{
}