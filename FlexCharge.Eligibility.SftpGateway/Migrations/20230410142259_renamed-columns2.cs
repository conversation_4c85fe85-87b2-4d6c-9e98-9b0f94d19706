using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Eligibility.SftpGateway.Migrations
{
    /// <inheritdoc />
    public partial class renamedcolumns2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "OrderSessionKey",
                table: "BatchRequests",
                newName: "RequestSessionKey");

            migrationBuilder.RenameColumn(
                name: "EvaluationStatus",
                table: "BatchRequests",
                newName: "ProcessingStatus");

            migrationBuilder.RenameColumn(
                name: "EvaluationStatus",
                table: "Batches",
                newName: "ProcessingStatus");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "RequestSessionKey",
                table: "BatchRequests",
                newName: "OrderSessionKey");

            migrationBuilder.RenameColumn(
                name: "ProcessingStatus",
                table: "BatchRequests",
                newName: "EvaluationStatus");

            migrationBuilder.RenameColumn(
                name: "ProcessingStatus",
                table: "Batches",
                newName: "EvaluationStatus");
        }
    }
}
