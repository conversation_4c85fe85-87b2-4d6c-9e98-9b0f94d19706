using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Eligibility.SftpGateway.Migrations
{
    /// <inheritdoc />
    public partial class renamedtables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BatchEvaluateRequests_EvaluateBatches_BatchId",
                table: "BatchEvaluateRequests");

            migrationBuilder.DropPrimaryKey(
                name: "PK_EvaluateBatches",
                table: "EvaluateBatches");

            migrationBuilder.DropPrimaryKey(
                name: "PK_BatchEvaluateRequests",
                table: "BatchEvaluateRequests");

            migrationBuilder.RenameTable(
                name: "EvaluateBatches",
                newName: "Batches");

            migrationBuilder.RenameTable(
                name: "BatchEvaluateRequests",
                newName: "BatchRequests");

            migrationBuilder.RenameIndex(
                name: "IX_BatchEvaluateRequests_BatchId",
                table: "BatchRequests",
                newName: "IX_BatchRequests_BatchId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Batches",
                table: "Batches",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_BatchRequests",
                table: "BatchRequests",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_BatchRequests_Batches_BatchId",
                table: "BatchRequests",
                column: "BatchId",
                principalTable: "Batches",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BatchRequests_Batches_BatchId",
                table: "BatchRequests");

            migrationBuilder.DropPrimaryKey(
                name: "PK_BatchRequests",
                table: "BatchRequests");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Batches",
                table: "Batches");

            migrationBuilder.RenameTable(
                name: "BatchRequests",
                newName: "BatchEvaluateRequests");

            migrationBuilder.RenameTable(
                name: "Batches",
                newName: "EvaluateBatches");

            migrationBuilder.RenameIndex(
                name: "IX_BatchRequests_BatchId",
                table: "BatchEvaluateRequests",
                newName: "IX_BatchEvaluateRequests_BatchId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_BatchEvaluateRequests",
                table: "BatchEvaluateRequests",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_EvaluateBatches",
                table: "EvaluateBatches",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_BatchEvaluateRequests_EvaluateBatches_BatchId",
                table: "BatchEvaluateRequests",
                column: "BatchId",
                principalTable: "EvaluateBatches",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
