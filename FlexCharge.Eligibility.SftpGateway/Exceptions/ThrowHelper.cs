using System;
using System.Diagnostics.CodeAnalysis;

namespace FlexCharge.Eligibility.SftpGateway.Exceptions;

public class ThrowHelper
{
    [DoesNotReturn]
    public static void ThrowSecurityException_MerchantHasNoAccess()
    {
        throw new SecurityException("Merchant has no access to SFTP processing");
    }
    
    [DoesNotReturn]
    public static void ThrowSecurityException_InvalidAuthorizationToken()
    {
        throw new SecurityException("AuthorizationToken is not valid");
    }

    [DoesNotReturn]
    public static void ThrowSecurityException_InvalidMerchantIdInToken()
    {
        throw new SecurityInvalidJwtTokenException("MerchantId provided in JWT token is not valid");
    }

    public static void ThrowException_UnprocessableFile()
    {
        throw new Exception("File is not processable");
    }
}