using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using FlexCharge.Common.Mvc;
using FlexCharge.Common.Mvc.Validators;

namespace FlexCharge.Eligibility.SftpGateway.DTO;

public class EvaluateRequest : RequestDTOBase, ITransmitAndEvaluateRequest
{
    [Required] public Guid? IdempotencyKey { get; set; }

    public Guid? SiteId { get; set; }
    public string? Descriptor { get; set; }


    //Unknown in case of Processor integration type - we'll try to find it by fuzzy matching
    public string? SenseKey { get; set; }

    [Required] public bool? IsDeclined { get; set; }

    public Guid? Mid { get; set; }


    //[UtcDateTime]
    public DateTime? ExpiryDateUtc { get; set; }

    ////Only for MIT. If passed, we'll try to find the order by this key and return it's current state (as Outcome endpoint)
    public Guid? OrderSessionKey { get; set; }

    [Required] public PaymentMethod PaymentMethod { get; set; }

    //External order Id
    [Required(ErrorMessage = "Merchant's orderId is required")]
    public string OrderId { get; set; }

    public string? SiteUrl { get; set; }

    public string? OrderSource { get; set; }

    [Required] public Transaction Transaction { get; set; }
    [Required] public Payer Payer { get; set; }

    public Merchant Merchant { get; set; }

    public List<OrderItem> OrderItems { get; set; }


    [Required] public BillingInformation BillingInformation { get; set; }
    public ShippingInformation? ShippingInformation { get; set; }

    /// <summary>
    /// Is Merchant-Initiated Transaction?
    /// </summary>
    public bool? IsMIT { get; set; }

    [RequiredIf(nameof(IsMIT), true)] public bool? IsRecurring { get; set; }

    [RequiredIf(nameof(IsRecurring), true)]
    public Subscription Subscription { get; set; }

    /// <summary>
    /// Only for MIT. If passed we'll try to connect to initial bank's recurring order
    /// </summary>
    public ThreeDsecure ThreeDsecure { get; set; }

    public List<AdditionalField> AdditionalFields { get; set; }
}