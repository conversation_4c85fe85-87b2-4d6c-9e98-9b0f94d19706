<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
		<UserSecretsId>99bbd081-7f55-4d77-9823-a712a2c8e7e0</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<!--<DockerComposeProjectPath>..\docker-compose.dcproj</DockerComposeProjectPath>-->
		<Configurations>Debug;Release;Staging</Configurations>
		<WarningsAsErrors>CS4014</WarningsAsErrors>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<DocumentationFile>bin\$(Configuration)\$(AssemblyName).xml</DocumentationFile>
		<NoWarn>1701;1702;1591;</NoWarn>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="Authorization\**" />
	  <Content Remove="Authorization\**" />
	  <EmbeddedResource Remove="Authorization\**" />
	  <None Remove="Authorization\**" />
	  <Compile Remove="Migrations\20230404124404_add-initial-tables.Designer.cs" />
	  <Compile Remove="DTO\TransmitAndEvaluate\**" />
	  <EmbeddedResource Remove="DTO\TransmitAndEvaluate\**" />
	  <None Remove="DTO\TransmitAndEvaluate\**" />
	  <Content Remove="DTO\TransmitAndEvaluate\**" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.7" />
		<PackageReference Include="AWSSDK.Transfer" Version="3.7.104.59" />
		<PackageReference Include="MediatR" Version="8.0.1" />
		<PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="8.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.2" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.10.8" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="SshKeyGenerator" Version="1.1.51" />
		<PackageReference Include="System.Net.Http" Version="4.3.4" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\FlexCharge.Common\FlexCharge.Common.csproj" />
		<ProjectReference Include="..\FlexCharge.Contracts\FlexCharge.Contracts.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Migrations" />
	</ItemGroup>

<!--	<Choose>-->
<!--		<When Condition=" '$(Configuration)'=='Staging' ">-->
<!--			<ItemGroup>-->
<!--				<Content Remove="appsettings.Development.json" />-->

<!--				&lt;!&ndash; Other files you want to update in the scope of Debug &ndash;&gt;-->
<!--				<None Update="other_files">-->
<!--					<CopyToOutputDirectory>Never</CopyToOutputDirectory>-->
<!--				</None>-->
<!--			</ItemGroup>-->
<!--		</When>-->
<!--		<When Condition=" '$(Configuration)'=='Development' ">-->
<!--			<ItemGroup>-->
<!--				<Content Remove="appsettings.Staging.json" />-->

<!--				&lt;!&ndash; Other files you want to update in the scope of Debug &ndash;&gt;-->
<!--				<None Update="other_files">-->
<!--					<CopyToOutputDirectory>Never</CopyToOutputDirectory>-->
<!--				</None>-->
<!--			</ItemGroup>-->
<!--		</When>-->
<!--	</Choose>-->

</Project>
