using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.SftpGateway.DTO;
using FlexCharge.Eligibility.SftpGateway.Entities;
using FlexCharge.Eligibility.SftpGateway.Services;
using FlexCharge.Eligibility.SftpGateway.Services.BatchProcessorBackgroundService;
using FlexCharge.Eligibility.SftpGateway.Services.SftpGatewayService;
using FlexCharge.Eligibility.SftpGateway.Services.SftpService;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.SftpGateway.Consumers;

public class InternalEvaluateCompletedEventConsumer : ConsumerBase<InternalEvaluateCompletedEvent>
{
    private readonly IBatchProcessorBackgroundService _batchProcessorBackgroundService;

    public InternalEvaluateCompletedEventConsumer(
        IServiceScopeFactory serviceScopeFactory,
        IBatchProcessorBackgroundService batchProcessorBackgroundService) : base(serviceScopeFactory)
    {
        _batchProcessorBackgroundService = batchProcessorBackgroundService;
    }

    protected override async Task ConsumeMessage(InternalEvaluateCompletedEvent message, CancellationToken cancellationToken)
    {
        await _batchProcessorBackgroundService.ProcessFinishedRequestAsync(message.RequestId, message.Result);
    }
}