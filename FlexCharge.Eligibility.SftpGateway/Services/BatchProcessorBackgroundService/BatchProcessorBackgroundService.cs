// Load balancer needs more testing:
// - Seems to stop processing if Evaluate returns validation errors -> no event on evaluation finished????
// - Cannot work if more than one container is running -> we need fan out InternalEvaluateCompletedEvent events to all containers to release processing slot 
//#define USE_LOAD_BALANCER

using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.SftpGateway.Entities;
using FlexCharge.Utils.Concurrency;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using FlexCharge.Common.MassTransit;
using FlexCharge.Eligibility.SftpGateway.Activities;

namespace FlexCharge.Eligibility.SftpGateway.Services.BatchProcessorBackgroundService;

public class BatchProcessorBackgroundService : BackgroundService, IBatchProcessorBackgroundService
{
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly IOptions<BatchProcessorOptions> _batchProcessorOptions;

    BlockingCollection<Guid> _requestsProcessingQueue = new();

    ParallelRequestsProcessingLimiter _parallelRequestsProcessingLimiter;


    public BatchProcessorBackgroundService(
        IServiceScopeFactory scopeFactory,
        IOptions<BatchProcessorOptions> batchProcessorOptions)
    {
        _scopeFactory = scopeFactory;
        _batchProcessorOptions = batchProcessorOptions;


        var maxConcurrentRequestsToProcess = _batchProcessorOptions.Value.MaxConcurrentRequestsToProcess;
        _parallelRequestsProcessingLimiter = new(maxConcurrentRequestsToProcess);
    }

    public string ProcessorUniqueId => Environment.MachineName;

    ConcurrentBoolean _IsShuttingDown = new(false);
    public bool IsShuttingDown => _IsShuttingDown.Value;

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        using var workspan = Workspan.Start<BatchProcessorBackgroundService>();

        workspan.Log.Information("Starting BatchProcessorBackgroundService");

        //Wait for database auto migrations in startup to complete
        //Need to run it in a separate thread to avoid deadlock
        //see: https://social.msdn.microsoft.com/Forums/en-US/8c1d7dba-9bbf-476c-9647-a07166529ed0/awaiting-waithandlewaitone?forum=winappswithcsharp
        await Task.Run(() => Startup.StartupCompleted.WaitOne());

        workspan.Log.Information("Started BatchProcessorBackgroundService");

        var newRequestsPollingTimeout = TimeSpan.FromSeconds(1);

        try
        {
            bool microserviceIsStopping = false;
            do
            {
                //blocks until there's any new request to process
                if (_requestsProcessingQueue.TryTake(out var requestToProcessId, 0,
                        stoppingToken))
                {
#if USE_LOAD_BALANCER
                    workspan.Log.Information("Waiting for processing slot to process request {requestToProcessId}",
                        requestToProcessId);

                    //waiting for a new processing slot to be available
                    await _parallelRequestsProcessingLimiter.WaitForAvailableProcessingSlotAndCaptureAsync(
                        stoppingToken);
#endif

                    workspan.Log.Information("Processing request {requestToProcessId}", requestToProcessId);

                    //processing slot is available - start processing the request
                    await StartRequestProcessingAsync(requestToProcessId, stoppingToken);
                }

                microserviceIsStopping =
                    stoppingToken.WaitHandle.WaitOne((int) newRequestsPollingTimeout.TotalMilliseconds);
            } while (!microserviceIsStopping);
        }
        catch (TaskCanceledException)
        {
        }

        await OnShutdownAsync();
    }

    private async Task StartRequestProcessingAsync(Guid requestToProcessId,
        CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<BatchProcessorBackgroundService>();

        using (var serviceScope = _scopeFactory.CreateScope())
        {
            var serviceProvider = serviceScope.ServiceProvider;

            try
            {
                using (var dbContext = serviceProvider.GetRequiredService<PostgreSQLDbContext>())
                {
                    var requestToProcess = await dbContext.BatchRequests.Include(x => x.Batch)
                        .SingleAsync(x => x.Id == requestToProcessId);


                    //double check if the request is not sent to evaluation yet
                    if (requestToProcess.EvaluationStarted == null)
                    {
                        requestToProcess.EvaluationStarted = DateTime.UtcNow;
                        await dbContext.SaveChangesAsync();

                        bool requestSucceeded = false;

                        try
                        {
                            workspan.Log.Warning("Sending request {requestToProcessId} to evaluation",
                                requestToProcessId);

                            requestSucceeded =
                                await InvokeEvaluationAsync(serviceProvider, requestToProcess, cancellationToken);
                        }
                        catch (Exception e)
                        {
                            workspan.RecordException(e);
                            throw;
                        }
                        finally
                        {
                            if (!requestSucceeded)
                            {
                                requestToProcess.ProcessingStatus = nameof(RequestProcessingStatus.Failed);
                                await dbContext.SaveChangesAsync();
                            }
                        }
                    }
                    else
                    {
                        workspan.Log.Warning("Request {requestToProcessId} already sent to evaluation",
                            requestToProcessId);
                    }
                }
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
            }
        }
    }

    public async Task ProcessFinishedRequestAsync(Guid processedRequestId, string result)
    {
        using var workspan = Workspan.Start<BatchProcessorBackgroundService>()
            .Baggage(nameof(processedRequestId), processedRequestId)
            .Baggage(nameof(result), result)
            .LogEnterAndExit();

        using (var serviceScope = _scopeFactory.CreateScope())
        {
            var serviceProvider = serviceScope.ServiceProvider;

            try
            {
                using (var dbContext = serviceProvider.GetRequiredService<PostgreSQLDbContext>())
                {
                    var processedRequest = await dbContext.BatchRequests.SingleAsync(x => x.Id == processedRequestId);

                    //processing started by this microservice instance?
                    if (processedRequest.ProcessorId == ProcessorUniqueId)
                    {
                        //release processing slot - so we can start processing another request
                        _parallelRequestsProcessingLimiter.ReleaseProcessingSlot();
                    }

                    //event not already handled by another microservice instance?
                    if (processedRequest.ProcessingStatus == nameof(RequestProcessingStatus.Processing))
                    {
                        processedRequest.ProcessingStatus = nameof(RequestProcessingStatus.Processed);
                        processedRequest.EvaluationCompleted = DateTime.UtcNow;
                        processedRequest.Result = result;

                        await dbContext.SaveChangesAsync();
                    }
                }
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
            }
        }
    }

    private static async Task<bool> InvokeEvaluationAsync(IServiceProvider serviceProvider,
        BatchRequest requestToEvaluate, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<BatchProcessorBackgroundService>();

        bool requestSucceeded = false;
        try
        {
            var internalEvaluateRequestClient =
                serviceProvider.GetRequiredService<IRequestClient<ExecuteInternalEvaluateRequestCommand>>();

            var evaluateResponse = await internalEvaluateRequestClient
                .RunIdempotentCommandAsync<ExecuteInternalEvaluateRequestCommand,
                    ExecuteInternalEvaluateRequestCommandResponse>(
                    new ExecuteInternalEvaluateRequestCommand()
                    {
                        MerchantId = requestToEvaluate.Mid,
                        PartnerId = requestToEvaluate.Batch.Pid,
                        RequestId = requestToEvaluate.RequestSessionKey,
                        Request = requestToEvaluate.Request
                    });

            requestSucceeded = evaluateResponse.Message.RequestSucceeded;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
        }

        return requestSucceeded;
    }

    private async Task OnShutdownAsync()
    {
        using var workspan = Workspan.Start<BatchProcessorBackgroundService>();

        workspan.Log.Information("Shutting down BatchProcessorBackgroundService");

        _IsShuttingDown.Write(true);


        #region Returning non-processed requests back to the database (setting status back to Submitted)

        List<Guid> unprocessedRequests = new();

        while (_requestsProcessingQueue.TryTake(out var requestId))
        {
            unprocessedRequests.Add(requestId);
        }

        if (unprocessedRequests.Count > 0)
        {
            workspan.Log.Information("Returning {UnprocessedRequestsCount} non-processed requests back to the database",
                unprocessedRequests.Count);

            using (var serviceScope = _scopeFactory.CreateScope())
            {
                using (var dbContext = serviceScope.ServiceProvider.GetRequiredService<PostgreSQLDbContext>())
                {
                    var requests = await dbContext.BatchRequests
                        .Where(x => unprocessedRequests.Contains(x.Id))
                        .ToListAsync();

                    foreach (var request in requests)
                    {
                        var processorId = ProcessorUniqueId;

                        if (request.ProcessorId == processorId &&
                            request.ProcessingStatus == nameof(RequestProcessingStatus.Processing))
                        {
                            request.ProcessingStatus = nameof(RequestProcessingStatus.Submitted);
                            request.ProcessingStarted = null;
                        }
                    }

                    await dbContext.SaveChangesAsync();
                }
            }
        }

        #endregion
    }

    public void Enqueue(List<BatchRequest> requests)
    {
        using var workspan = Workspan.Start<BatchProcessorBackgroundService>();

        var requestsQueue = GetSortedByPriorityRequestsQueue(requests);

        workspan.Log.Information("Enqueueing {RequestsCount} requests", requestsQueue.Count);

        foreach (var requestId in requestsQueue)
        {
            _requestsProcessingQueue.Add(requestId);
        }
    }

    /// <summary>
    /// Sorts requests by merchant and returns queue of requests to process
    /// so that each merchant requests are processed in a fair order
    /// </summary>
    /// <param name="requests"></param>
    /// <returns></returns>
    private static Queue<Guid> GetSortedByPriorityRequestsQueue(List<BatchRequest> requests)
    {
        using var workspan = Workspan.Start<BatchProcessorBackgroundService>();

        Queue<Guid> requestsToProcess = new();

        //NpgSQL does not support GroupBy correctly -> doing it in memory
        var unprocessedRequestsByMerchant = requests.GroupBy(x => x.Mid);

        var requestsPartitionedByMerchants = unprocessedRequestsByMerchant
            .Select(x => new Queue<Guid>(x.Select(y => y.Id))).ToList();

        workspan.Log.Information("Requests partitioned by merchants: {@RequestsPartitionedByMerchants}",
            requestsPartitionedByMerchants);

        while (requestsPartitionedByMerchants.Count > 0)
        {
            for (int i = 0; i < requestsPartitionedByMerchants.Count;)
            {
                if (requestsPartitionedByMerchants[i].TryDequeue(out var requestId))
                {
                    workspan.Log.Information("Request {RequestId} added to the queue", requestId);

                    requestsToProcess.Enqueue(requestId);
                    i++;
                }
                else
                {
                    requestsPartitionedByMerchants.RemoveAt(i);
                }
            }
        }

        return requestsToProcess;
    }
}