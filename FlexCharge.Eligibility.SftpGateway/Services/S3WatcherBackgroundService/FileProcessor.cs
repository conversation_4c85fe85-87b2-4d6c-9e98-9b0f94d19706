using System;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FlexCharge.Common.Cloud.Storage;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.SftpGateway.Enums;
using FlexCharge.Utils;
using Microsoft.Extensions.Primitives;

namespace FlexCharge.Eligibility.SftpGateway.Services.S3WatcherBackgroundService;

public class FileProcessor
{
    class InputFilePrefixes
    {
        public const string Evaluate = "evaluate_";
        public const string Transmit = "transmit_";
    }

    class OutputFilePrefixes
    {
        public const string Submitted = "submitted_";
        public const string Results = "results_";
    }

    private readonly string _initialFolder;
    private readonly string _initialFileName;

    public string BucketName { get; }

    private string RootFolderToWatch { get; }

    private string ProcessingRootFolder { get; }


    public string Folder { get; private set; }
    public string FileName { get; private set; }


    public string FilePath => $"{Folder}/{FileName}";

    public string InitialFilePath => $"{_initialFolder}{_initialFileName}";


    public string? MerchantName { get; }
    public Guid Mid { get; }


    public RequestType? RequestType =>
        FileName.StartsWith(InputFilePrefixes.Evaluate) ? Enums.RequestType.Evaluate
        : FileName.StartsWith(InputFilePrefixes.Transmit) ? Enums.RequestType.Transmit
        : null;


    public FileProcessor(string storageName, string rootFolderToWatch, string processingRootFolder,
        string initialFolder, string initialFileName)
    {
        RootFolderToWatch = rootFolderToWatch;
        ProcessingRootFolder = processingRootFolder;

        _initialFolder = initialFolder;
        _initialFileName = initialFileName;

        BucketName = storageName;

        SetCurrentPath(initialFolder, initialFileName);

        if (TryToExtractMerchantInfo(out var merchantName, out var merchantId))
        {
            MerchantName = merchantName;
            Mid = merchantId;
        }
    }

    private void SetCurrentPath(string path)
    {
        Folder = Path.GetDirectoryName(path);
        FileName = Path.GetFileName(path);
    }

    private void SetCurrentPath(string initialFolder, string initialFileName)
    {
        Folder = initialFolder;
        FileName = initialFileName;
    }

    private string GetMerchantProcessingRootFolder(string merchantName, Guid mid) =>
        $"{ProcessingRootFolder}{merchantName}_{mid.ToString("D")}";

    // private string GetMerchantInboundFolder(string merchantName, Guid mid) =>
    //     $"{RootFolderToWatch}{merchantName}_{mid.ToString("D")}/inbound/";

    private string GetMerchantOutboundFolder(string merchantName, Guid mid) =>
        $"{RootFolderToWatch}{merchantName}_{mid.ToString("D")}/outbound/";

    private string GetMerchantReportsFolder(string merchantName, Guid mid) =>
        $"{RootFolderToWatch}{merchantName}_{mid.ToString("D")}/reports/";

    private string GetProcessingFolder(string merchantName, Guid mid) =>
        $"{GetMerchantProcessingRootFolder(merchantName, mid)}/Processing/";

    private string GetQuarantineFolder(string merchantName, Guid mid) =>
        $"{GetMerchantProcessingRootFolder(merchantName, mid)}/Quarantine/";

    private string GetProcessedFolder(string merchantName, Guid mid) =>
        $"{GetMerchantProcessingRootFolder(merchantName, mid)}/Processed/";

    private string GetInvalidFolder(string merchantName, Guid mid) =>
        $"{GetMerchantProcessingRootFolder(merchantName, mid)}/Invalid/";


    private bool TryToExtractMerchantInfo(out string merchantName, out Guid merchantId)
    {
        string folder = _initialFolder;

        merchantName = null;
        merchantId = Guid.Empty;

        var merchantSubfolderNameSplit = folder.Split("/", 2).FirstOrDefault()?.Split("_", 2);

        if (merchantSubfolderNameSplit?.Length != 2) return false;

        merchantName = merchantSubfolderNameSplit.First();
        var mid = merchantSubfolderNameSplit.Last();

        return !string.IsNullOrWhiteSpace(mid) && Guid.TryParse(mid, out merchantId) &&
               !string.IsNullOrWhiteSpace(merchantName);
    }

    public async Task MoveToProcessingFolderAsync(ICloudStorage cloudStorage)
    {
        using var workspan = Workspan.Start<FileProcessor>();

        string sourceFolder = Folder;
        string sourceFileName = FileName;

        var filePathInProcessingFolder = await MoveFileAsync(cloudStorage, $"{Folder}{FileName}",
            GetProcessingFolder(MerchantName, Mid) + FileName, true);

        workspan.Log.Information(
            "File {FileName} in folder {Folder} for merchant {Mid} moved to processing folder {FilePathInProcessingFolder}",
            sourceFileName, sourceFolder, Mid, filePathInProcessingFolder);
    }

    public async Task MoveToQuarantineFolderAsync(ICloudStorage cloudStorage)
    {
        using var workspan = Workspan.Start<FileProcessor>();

        string sourceFolder = Folder;
        string sourceFileName = FileName;

        var quarantinedFileName = GetFileNameWithTimeAndRandomTextPostfix(FileName, Mid);
        var filePathInQuarantineFolder = await MoveFileAsync(cloudStorage, FilePath,
            GetQuarantineFolder(MerchantName, Mid) + quarantinedFileName, true);

        workspan.Log.Information(
            "File {FileName} in folder {Folder} for merchant {Mid} moved to -quarantine folder {FilePathInQuarantineFolder}",
            sourceFileName, sourceFolder, Mid, filePathInQuarantineFolder);
    }

    public async Task MoveToProcessedFolderAsync(ICloudStorage cloudStorage)
    {
        using var workspan = Workspan.Start<FileProcessor>();

        string sourceFolder = Folder;
        string sourceFileName = FileName;

        var filePathInProcessedFolder =
            await MoveFileAsync(cloudStorage, FilePath, GetFilePathInProcessedFolder(sourceFileName), true);

        workspan.Log.Information(
            "File {FileName} in folder {Folder} for merchant {Mid} moved to processed folder {FilePathInProcessedFolder}",
            sourceFileName, sourceFolder, Mid, filePathInProcessedFolder);
    }

    private string GetFilePathInProcessedFolder(string fileName)
    {
        var processedFileName = GetFileNameWithTimeAndRandomTextPostfix(fileName, Mid);
        var filePathInProcessedFolder = GetProcessedFolder(MerchantName, Mid) + processedFileName;
        return filePathInProcessedFolder;
    }

    public async Task MoveToInvalidFolderAsync(ICloudStorage cloudStorage)
    {
        using var workspan = Workspan.Start<FileProcessor>();

        string sourceFolder = Folder;
        string sourceFileName = FileName;

        var filePathInProcessedFolder =
            await MoveFileAsync(cloudStorage, FilePath, GetFilePathInInvalidFolder(sourceFileName), true);

        workspan.Log.Information(
            "File {FileName} in folder {Folder} for merchant {Mid} moved to invalid folder {FilePathInProcessedFolder}",
            sourceFileName, sourceFolder, Mid, filePathInProcessedFolder);
    }

    private string GetFilePathInInvalidFolder(string fileName)
    {
        var processedFileName = GetFileNameWithTimeAndRandomTextPostfix(fileName, Mid);
        var filePathInProcessedFolder = GetProcessedFolder(MerchantName, Mid) + processedFileName;
        return filePathInProcessedFolder;
    }

    private async Task<string> MoveFileAsync(ICloudStorage cloudStorage, string sourceFilePath,
        string destinationFilePath,
        bool setAsCurrentFilePath,
        bool overwrite = true,
        bool createTargetFolderIfMissing = true)
    {
        if (createTargetFolderIfMissing)
        {
            var folderPath = Path.GetDirectoryName(destinationFilePath);
            await cloudStorage.CreateFolderIfMissingAsync(BucketName, folderPath);
        }

        await cloudStorage.MoveFileAsync(BucketName, sourceFilePath, destinationFilePath, overwrite);

        if (setAsCurrentFilePath)
        {
            SetCurrentPath(destinationFilePath);
        }

        return destinationFilePath;
    }

    public async Task<string> CreateFileCopyInProcessedFolderAsync(ICloudStorage cloudStorage, string sourceFilePath)
    {
        using var workspan = Workspan.Start<FileProcessor>();

        string sourceFolder = Path.GetDirectoryName(sourceFilePath);
        string sourceFileName = Path.GetFileName(sourceFilePath);

        var filePathInProcessedFolder = GetFilePathInProcessedFolder(sourceFileName);
        await CopyFileAsync(cloudStorage, sourceFilePath, filePathInProcessedFolder, false);

        workspan.Log.Information(
            "File {FileName} in folder {Folder} for merchant {Mid} copied to processed folder {FilePathInProcessedFolder}",
            FileName, Folder, Mid, filePathInProcessedFolder);

        return filePathInProcessedFolder;
    }

    private async Task CopyFileAsync(ICloudStorage cloudStorage, string sourceFilePath, string destinationFilePath,
        bool setAsCurrentFilePath,
        bool overwrite = true,
        bool createTargetFolderIfMissing = true)
    {
        if (createTargetFolderIfMissing)
        {
            var folderPath = Path.GetDirectoryName(destinationFilePath);
            await cloudStorage.CreateFolderIfMissingAsync(BucketName, folderPath);
        }

        await cloudStorage.CopyFileAsync(BucketName, sourceFilePath, destinationFilePath, overwrite);

        if (setAsCurrentFilePath)
        {
            SetCurrentPath(destinationFilePath);
        }
    }

    readonly CultureInfo _invariantCulture = CultureInfo.InvariantCulture;

    private string _filePostfixWithTimeAndRandomPart = null;

    /// <summary>
    /// Used to add random postfix to the file name to avoid collisions
    /// </summary>
    /// <param name="fileName"></param>
    /// <param name="mid"></param>
    /// <returns></returns>
    private string GetFileNameWithTimeAndRandomTextPostfix(string fileName, Guid mid)
    {
        // Postfix should be the same for a given processed file
        if (_filePostfixWithTimeAndRandomPart == null)
        {
            var randomFileNamePostfix = GenerateRandomFileNamePostfix(fileName, mid);

            var utcNow = DateTime.UtcNow;

            _filePostfixWithTimeAndRandomPart = utcNow.ToString("_yyyy_MM_dd_HH_mm_ss_") + randomFileNamePostfix;
        }

        StringBuilder fileNameBuilder = new();
        fileNameBuilder.Append(Path.GetFileNameWithoutExtension(fileName));
        fileNameBuilder.Append(_filePostfixWithTimeAndRandomPart);
        fileNameBuilder.Append(Path.GetExtension(fileName));

        return fileNameBuilder.ToString();
    }

    private static string GenerateRandomFileNamePostfix(string fileName, Guid mid)
    {
        return UniqueIdsHelper.GeneratePseudoUniqueId($"{mid.ToString()}_{fileName}");
    }

    public string GetSubmittedFilePath()
    {
        var outboundFolder = GetMerchantOutboundFolder(MerchantName, Mid);
        var fileName = Path.GetFileName(FilePath);

        var submittedFileName = OutputFilePrefixes.Submitted + RemoveProcessingTypePrefixFromFileName(fileName);
        var submittedFilePath = outboundFolder + submittedFileName;

        return submittedFilePath;
    }

    private object RemoveProcessingTypePrefixFromFileName(string fileName)
    {
        return fileName.Split("_", 2).Last();
    }

    public string GetResultsFilePath()
    {
        var outboundFolder = GetMerchantOutboundFolder(MerchantName, Mid);
        var fileName = Path.GetFileName(FilePath);

        var resultsFileName = OutputFilePrefixes.Results + RemoveProcessingTypePrefixFromFileName(fileName);

        string todayDate = DateTime.UtcNow.ToString("_yyyy_MM_dd");

        resultsFileName = AddPostfixToFileName(resultsFileName, todayDate);
        var resultsFilePath = outboundFolder + resultsFileName;

        return resultsFilePath;
    }

    private string AddPostfixToFileName(string filePath, string postfix)
    {
        StringBuilder fileNameBuilder = new();

        string directoryName = Path.GetDirectoryName(filePath);
        if (!string.IsNullOrWhiteSpace(directoryName))
        {
            fileNameBuilder.Append(directoryName);
            fileNameBuilder.Append("/");
        }

        fileNameBuilder.Append(Path.GetFileNameWithoutExtension(filePath));
        fileNameBuilder.Append(postfix);
        fileNameBuilder.Append(Path.GetExtension(filePath));

        return fileNameBuilder.ToString();
    }

    public async Task<long> GetFileSizeAsync(ICloudStorage cloudStorage)
    {
        return await cloudStorage.GetFileSizeAsync(BucketName, FilePath);
    }
}